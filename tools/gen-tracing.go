package main

import (
	"gitlab.deepgate.io/apps/common/tools/trace"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/partnership"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
)

func getMongoRepoTrace() []trace.Trace {
	return []trace.Trace{
		// MongoDB
		trace.CreateTracingItem(repositories.NewRequestRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewSessionRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewBookingRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewAirportRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewSearchFlightsRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewListFlightRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewAircraftLayoutRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewSeatMapRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewPNRRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewAmadeusPNRRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewCurrencyExchangeRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewSupplierRouteRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewTFRoutingRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewCommissionRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewL2bRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewBaggageOptionRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewAmadeusISOCountryRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewAmaSSRTemplateRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),
		trace.CreateTracingItem(repositories.NewSalesforceConfigRepository, "repositories_tracing", "./internal/flights/tracing/adapter/mongodb/repositories", ""),

		// Redis
		// Skip redis repo tracing
	}
}

func getCommandTrace() []trace.Trace {
	return []trace.Trace{
		// command
		// trace.CreateTracingItem(command.NewCheckFareHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewAddPNRHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewModPNRHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewConfirmFareHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewCreateBookingHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewIssueTicketHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewProcessPendingTicketHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewProcessSaveMultipleSupplierRoutesDailyHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewProcessExpiredBookingHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewDeleteExpiredRoutingDataHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewBulkInsertL2bItemHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewUpdateTicketAndReservationCodeHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewManualCancelBookingHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewUpdateBookingTransferHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewRetrievePNRAmadeusForTransferredBookingHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewCreateAmaSSRTemplateHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewUpdateAmaSSRTemplateHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewSetBookingNotifiedHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		// trace.CreateTracingItem(command.NewPrepareBookingHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		trace.CreateTracingItem(command.NewCheckFareHandler, "command_tracing", "./internal/flights/tracing/app/command", ""),
		trace.CreateTracingItem(command.NewCheckFareV2Handler, "command_tracing", "./internal/flights/tracing/app/command", ""),
	}
}

func getQueryTrace() []trace.Trace {
	return []trace.Trace{
		// query
		// trace.CreateTracingItem(query.NewSearchFlightsHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewRetrieveBookingHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewLoginHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewTransactionHistoryHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewListBookingHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewListManualIssuingBookingsHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewListExpiredBookingHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetSeatMapHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetInMemL2bHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetL2bHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetBalanceHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetBookingByCodeHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetPNRBySessionHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetBaggagesHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetReportBookingHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewListUpcomingBookingsHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewGetOfficeInfoHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		// trace.CreateTracingItem(query.NewSearchFlightsV2Handler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		trace.CreateTracingItem(query.NewGetCurrencyExchangeDetailHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
		trace.CreateTracingItem(query.NewListBookingFilterHandler, "query_tracing", "./internal/flights/tracing/app/query", ""),
	}
}

func getServiceTrace() []trace.Trace {
	return []trace.Trace{
		// service
		// trace.CreateTracingItem(service.NewL2bService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewCommissionService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewFareService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewSessionService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		trace.CreateTracingItem(service.NewBookingService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewPNRService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		trace.CreateTracingItem(service.NewIssueTicketService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewCurrencyExchangeService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewSupplierRouteService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewAncillaryService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewProviderSearchHandler, "service_tracing", "./internal/flights/tracing/app/service", ""), // SKIP
		trace.CreateTracingItem(service.NewSearchFlightsService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewListFlightService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewAmadeusService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewReportService, "service_tracing", "./internal/flights/tracing/app/service", ""),
		// trace.CreateTracingItem(service.NewPriceService, "service_tracing", "./internal/flights/tracing/app/service", ""),
	}
}

func getGrpcClientTrace() []trace.Trace {
	return []trace.Trace{
		// Grpc clients
		// trace.CreateTracingItem(order.NewOrderServiceClient, "order_tracing", "./internal/flights/tracing/adapter/order", ""),
		// trace.CreateTracingItem(wallet.NewWalletClient, "wallet_tracing", "./internal/flights/tracing/adapter/wallet", ""),
		// trace.CreateTracingItem(payment.NewPaymentClient, "payment_tracing", "./internal/flights/tracing/adapter/payment", ""),
		// trace.CreateTracingItem(webhook.NewWebhookAdapter, "webhook_tracing", "./internal/flights/tracing/adapter/webhook", ""),
		// trace.CreateTracingItem(data_warehouse.NewDataWareHouseServiceClient, "data_warehouse_tracing", "./internal/flights/tracing/adapter/data_warehouse", ""),
		trace.CreateTracingItem(partnership.NewPartnershipClient, "partnership_tracing", "./internal/flights/tracing/adapter/partnership", ""),
	}
}

func getAdapterTrace() []trace.Trace {
	return []trace.Trace{
		// Adapter, client & other stuff
		// trace.CreateTracingItem(amadeus_client.NewAmadeusAdapter, "amadeus_client_tracing", "./internal/flights/tracing//adapter/amadeus_client", ""),
		// trace.CreateTracingItem(ev_international_client.NewEVInternationalAdapter, "ev_international_client_tracing", "./internal/flights/tracing/adapter/ev_international_client", ""),
		// trace.CreateTracingItem(ev_international_client.NewEVInternationalClient, "ev_international_client_tracing", "./internal/flights/tracing/adapter/ev_international_client", ""),
		// trace.CreateTracingItem(vna_client.NewVNAAdapter, "vna_client_tracing", "./internal/flights/tracing/adapter/vna_client", ""),
		// trace.CreateTracingItem(telegram.NewTelegramRepository, "telegram_tracing", "./internal/flights/tracing/adapter/telegram", ""), // SKIP
		// trace.CreateTracingItem(vietjet_client.NewVietjetAdapter, "vietjet_client_tracing", "./internal/flights/tracing/adapter/vietjet_client", ""),
		// trace.CreateTracingItem(ev_client.NewEVAdapter, "ev_client_tracing", "./internal/flights/tracing/adapter/ev_client", ""),
		// trace.CreateTracingItem(travel_fusion.NewTravelFusionClient, "travel_fusion_tracing", "./internal/flights/tracing/adapter/travel_fusion", ""),
		// trace.CreateTracingItem(travel_fusion.NewTravelFusionAdapter, "travel_fusion_tracing", "./internal/flights/tracing/adapter/travel_fusion", ""),
		// trace.CreateTracingItem(vna1a_client.NewVNA1AAdapter, "vna1a_client_tracing", "./internal/flights/tracing/adapter/vna1a_client", ""),
		// trace.CreateTracingItem(hnh_client.NewHNHAdapter, "hnh_client_tracing", "./internal/flights/tracing/adapter/hnh_client", ""),
		// trace.CreateTracingItem(pkfare_client.NewPkfareAdapter, "pkfare_client_tracing", "./internal/flights/tracing/adapter/pkfare_client", ""),
		// trace.CreateTracingItem(price.NewPriceClient, "pkfare_client_tracing", "./internal/flights/tracing/adapter/price", ""),
		trace.CreateTracingItem(tongcheng_client.NewTongChengAdapter, "tongcheng_client_tracing", "./internal/flights/tracing/adapter/tongcheng_client", ""),
	}
}

func main() {
	items := []trace.Trace{}

	genMongoRepo := false
	genCommand := false
	genQuery := false
	genService := true
	genGrpcClient := false
	genAdapter := true

	if genMongoRepo {
		items = append(items, getMongoRepoTrace()...)
	}
	if genCommand {
		items = append(items, getCommandTrace()...)
	}
	if genQuery {
		items = append(items, getQueryTrace()...)
	}
	if genService {
		items = append(items, getServiceTrace()...)
	}
	if genGrpcClient {
		items = append(items, getGrpcClientTrace()...)
	}
	if genAdapter {
		items = append(items, getAdapterTrace()...)
	}

	trace.GenTrace(items)
}
