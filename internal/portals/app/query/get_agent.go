package query

import (
	"context"
	"fmt"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/domain"
)

type GetAgentHandler interface {
	Handle(ctx context.Context, partnershipID, agentCode string) (*domain.Agent, error)
}

type getAgent struct {
	partnerClient partner.PartnerClient
}

func NewGetAgent(
	partnerClient partner.PartnerClient,
) GetAgentHandler {
	return &getAgent{partnerClient}
}

func (h *getAgent) Handle(ctx context.Context, partnershipID, agentCode string) (*domain.Agent, error) {
	agent, err := h.partnerClient.GetPartnerShopByCode(ctx, partnershipID, agentCode)
	if err != nil {
		log.Error("partnerClient.GetAgent error", log.Any("error", err),
			log.Any("agentCode", agentCode))
		return nil, errors.ErrSomethingOccurred
	}

	if agent == nil {
		return nil, errors.ErrNotFound
	}

	fmt.Println("agent", agent)
	return agent, nil
}
