package query

import (
	"context"

	"github.com/samber/lo"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/partnership"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/domain"
)

type GetTopupRequestsHandler interface {
	Handle(ctx context.Context, filter *domain.GetTopupRequestsFilter, dPaging *commonDomain.Pagination, partnershipID string, needFilteByAM, isAM bool, userID string) ([]*domain.TopupRequest, *commonDomain.Pagination, error)
}

type getTopupRequestHandler struct {
	repo              repositories.TopupRequestRepository
	partnershipClient partnership.PartnershipClient
}

func NewGetTopupRequestsHandler(repo repositories.TopupRequestRepository, pClient partnership.PartnershipClient) GetTopupRequestsHandler {
	return &getTopupRequestHandler{repo, pClient}
}

func (h *getTopupRequestHandler) Handle(ctx context.Context, filter *domain.GetTopupRequestsFilter, dPaging *commonDomain.Pagination, partnershipID string, needFilteByAM, isAM bool, userID string) ([]*domain.TopupRequest, *commonDomain.Pagination, error) {
	var pUserIDs []string
	if needFilteByAM {
		oID := userID
		if !isAM {
			psUser, err := h.partnershipClient.GetUserByID(ctx, partnershipID, userID)
			if err != nil {
				log.Error("h.partnershipClient.GetUserByID error", log.Any("error", err), log.Any("UserID", userID))
				return nil, nil, err
			}

			if psUser == nil {
				log.Error("h.partnershipClient.GetUserByID error", log.Any("error", err), log.Any("UserID", userID))
				return nil, nil, errors.ErrPermissionDenied
			}
			oID = psUser.OfficeManageId
		}
		filter.CreatedBy = append(filter.CreatedBy, oID)

		pUsers, err := h.partnershipClient.GetUsersByOfficeMangerID(ctx, partnershipID, oID)
		if err != nil {
			log.Error("h.partnershipClient.GetUserByID error", log.Any("error", err), log.Any("UserID", userID))
			return nil, nil, err
		}

		pUserIDs = lo.Map(pUsers, func(user *domain.Partnership, _ int) string {
			return user.Id
		})
	}

	filter.CreatedBy = append(filter.CreatedBy, pUserIDs...)

	result, paging, err := h.repo.GetList(ctx, filter, dPaging, partnershipID)
	if err != nil {
		log.Error("GetList error", log.Any("err", err))
		return nil, nil, err
	}

	return result, paging, nil
}
