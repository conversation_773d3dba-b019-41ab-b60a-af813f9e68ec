package roles

import (
	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/adapter/rabbitmq"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/airplane"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/mongodb/repositories"
	notificationclient "gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/notification-client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/partnership"
	skyhub_flights "gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/skyhub_flight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/telegram"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/app"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/app/service"
	grpcServer "gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/port/grpc"
	httpServer "gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/port/http"

	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

func New(cfg *config.Schema, db commonMongoDB.DB, mq rabbitmq.MQ, redis redis.IRedis) (*grpcServer.Server, *httpServer.Server) {
	repo := repositories.NewTopupRequestRepository(db)
	bookingNotiRepo := repositories.NewBookingNotificationRepository(db)

	repoHistory := repositories.NewTopupRequestHistoryRepository(db)
	partnerClient := partner.NewPartnerClient(cfg)
	walletClient := wallet.NewWalletClient(cfg, mq)
	airplaneClient := airplane.NewAirplaneClient(cfg)
	paymentClient := payment.NewPaymentClient(cfg)
	skyhubFlightClient := skyhub_flights.NewSkyhubFlightsClient(cfg)
	notiClient := notificationclient.NewNotificationClient(cfg)
	telegramClient := telegram.NewClient(cfg.TelegramURL, cfg.TelegramSSRNotifyBotToken)
	partnershipClient := partnership.NewPartnershipClient(cfg)

	topupService := service.NewTopupService(walletClient, partnerClient, paymentClient)
	operationService := service.NewOperationService(repo, repoHistory, partnerClient, walletClient, airplaneClient, topupService)

	application := app.Application{
		Commands: app.Commands{
			CreateTopupRequest:         command.NewCreateTopupRequestHandler(cfg, repo, partnerClient, telegramClient, notiClient),
			DeleteTopupRequest:         command.NewDeleteTopupRequestHandler(repo, repoHistory),
			UpdateTopupRequest:         command.NewUpdateTopupRequestHandler(repo, repoHistory),
			UpdateTopupStatus:          command.NewUpdateTopupStatusHandler(cfg, repo, repoHistory, topupService, operationService, walletClient, airplaneClient, redis),
			UpdateBooking:              command.NewUpdateBookingHandler(skyhubFlightClient),
			ManualCancelBooking:        command.NewManualCancelBookingHandler(skyhubFlightClient),
			AggregateTopUpManual:       command.NewAggregateTopUpManualHandler(repo, airplaneClient),
			SendRawBookingNotification: command.NewSendRawBookingNotificationHandler(bookingNotiRepo, notiClient, skyhubFlightClient),
		},
		Queries: app.Queries{
			GetTopupDetail:                   query.NewGetTopupDetailHandler(repo),
			GetTopupRequests:                 query.NewGetTopupRequestsHandler(repo, partnershipClient),
			GetLongCreatedTopupRequestAmount: query.NewGetLongCreatedTopupRequestAmountHandler(repo, partnershipClient),
			GetAgent:                         query.NewGetAgent(partnerClient),
			GetBookingByCode:                 query.NewGetBookingByCodeHandler(skyhubFlightClient),
			GetTransactionHistory:            query.NewGetTransactionHistory(walletClient),
			ListBookingByTicketStatuses:      query.NewListBookingByTicketStatusesHandler(skyhubFlightClient),
			ExportReportBooking:              query.NewExportReportBookingHandler(skyhubFlightClient),
			GetHistoryBookingNotifications:   query.NewGetHistoryBookingNotificationsHandler(bookingNotiRepo),
		},
	}

	return grpcServer.NewServer(application, cfg), httpServer.New(application, cfg)
}
