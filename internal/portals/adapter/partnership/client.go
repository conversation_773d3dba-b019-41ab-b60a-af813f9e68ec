package partnership

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	bPartnershipPb "gitlab.deepgate.io/apps/api/gen/go/partnership/backend"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"google.golang.org/grpc/metadata"
)

type PartnershipClient interface {
	GetUserByID(ctx context.Context, psId string, userId string) (*domain.Partnership, error)
	GetUsersByOfficeMangerID(ctx context.Context, pID string, officeManagerID string) ([]*domain.Partnership, error)
}

type partnershipClient struct {
	cfg *config.Schema
}

func NewPartnershipClient(cfg *config.Schema) PartnershipClient {
	return &partnershipClient{
		cfg: cfg,
	}
}

func (c *partnershipClient) GetUserByID(ctx context.Context, pId, userId string) (*domain.Partnership, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PartnershipServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "Cannot connect partner service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, pId)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	req := &bPartnershipPb.GetUserByIDReq{Id: userId}

	client := bPartnershipPb.NewPartnershipServiceClient(conn)

	res, err := client.GetUserByID(newCtx, req)
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}
	if res.Data == nil {
		return nil, fmt.Errorf("res info nil")
	}

	data := res.Data

	partnership := domain.Partnership{
		Id:             pId,
		FullName:       data.Username,
		OfficeManageId: data.OfficeManagerId,
		UserName:       data.Username,
	}

	return &partnership, nil
}

func (c *partnershipClient) GetUsersByOfficeMangerID(ctx context.Context, pID string, officeManagerID string) ([]*domain.Partnership, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PartnershipServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "Cannot connect partner service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, pID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	req := &bPartnershipPb.GetUsersByOfficeManagerIDReq{PartnershipId: pID, OfficeManagerId: officeManagerID}

	client := bPartnershipPb.NewPartnershipServiceClient(conn)

	res, err := client.GetUsersByOfficeManagerID(newCtx, req)
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	data := res.Users

	pUsers := make([]*domain.Partnership, 0, len(data))
	for _, user := range data {
		pUsers = append(pUsers, &domain.Partnership{
			Id:             user.Id,
			FullName:       user.Name,
			OfficeManageId: user.OfficeManagerId,
			UserName:       user.Username,
		})
	}
	return pUsers, nil
}
