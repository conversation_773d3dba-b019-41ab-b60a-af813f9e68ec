package models

import (
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BookingNotification struct {
	ID              primitive.ObjectID      `bson:"_id,omitempty"`
	CreatedAt       time.Time               `bson:"created_at,omitempty"`
	Sender          string                  `bson:"sender"`
	Method          enum.NotificationMethod `bson:"method"`
	Status          enum.NotificationStatus `bson:"status"`
	Content         string                  `bson:"content"`
	BookingCode     string                  `bson:"booking_code"`
	Title           string                  `bson:"title"`
	FileAttachments []*FileAttachment       `bson:"file_attachments"`
	Recipients      []string                `bson:"recipients"`
}

type FileAttachment struct {
	FileName string `bson:"file_name"`
	FileURL  string `bson:"file_url"`
}

func (b *BookingNotification) BeforeCreate() {

	if b.ID.IsZero() {
		b.ID = primitive.NewObjectID()
	}

	b.CreatedAt = time.Now()
}
