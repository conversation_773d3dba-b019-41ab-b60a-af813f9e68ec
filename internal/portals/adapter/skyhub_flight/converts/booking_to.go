package converts

import (
	"time"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	skyhubFlightsBasePb "gitlab.deepgate.io/apps/api/gen/go/skyhub"
	skyhubFlightsBackendPb "gitlab.deepgate.io/apps/api/gen/go/skyhub/backend"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/enum"
	appEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/enum"
)

func toDomainBookingBasicInfo(info *skyhubFlightsBackendPb.BookingBasicInfo) *domain.BookingBasicInfo {
	if info == nil {
		return nil
	}

	return &domain.BookingBasicInfo{
		BookingCode:       info.BookingCode,
		PNRCode:           info.PnrCode,
		OfficeID:          info.OfficeId,
		TotalNumberTicket: info.TotalNumberTicket,
		CreatedAt:         info.CreatedAt,
		TicketStatus:      appEnum.TicketStatusValue[int32(info.TicketStatus)],
	}
}

func ToDomainListBookingBasicInfo(items []*skyhubFlightsBackendPb.BookingBasicInfo) []*domain.BookingBasicInfo {
	res := make([]*domain.BookingBasicInfo, len(items))
	for index, info := range items {
		res[index] = toDomainBookingBasicInfo(info)
	}

	return res
}

func ToDomainBookingInfo(booking *skyhubFlightsBackendPb.BookingInfo) *domain.BookingInfo {
	if booking == nil {
		return nil
	}

	return &domain.BookingInfo{
		BookingCode:  booking.BookingCode,
		PNRCode:      booking.PnrCode,
		TicketStatus: appEnum.TicketStatusValue[int32(booking.TicketStatus)],
		Itineraries:  toDomainItineraries(booking.Itineraries),
	}
}

func ToDomainBookingInfoV2(in *skyhub.BookingDetails) *domain.BookingInfo {
	if in == nil {
		return nil
	}

	out := &domain.BookingInfo{
		BookingCode: in.BookingCode,
	}

	if in.ContactInfo != nil {
		out.ContactInfo = &domain.Contact{
			Surname:   in.ContactInfo.Surname,
			GivenName: in.ContactInfo.GivenName,
			PhoneCode: in.ContactInfo.PhoneCode,
			Phone:     in.ContactInfo.Phone,
			Email:     in.ContactInfo.Email,
			Gender:    commonEnum.GenderType(in.ContactInfo.Gender),
		}
	}

	return out
}

func toDomainItineraries(items []*skyhubFlightsBackendPb.Itinerary) []*domain.Itinerary {
	res := make([]*domain.Itinerary, len(items))

	for index, info := range items {
		res[index] = &domain.Itinerary{
			ID:              info.Id,
			DepartPlace:     info.DepartPlace,
			ArrivalPlace:    info.ArrivalPlace,
			DepartDate:      info.DepartDate,
			ReservationCode: info.ReservationCode,
			ListPaxInfo:     toDomainPaxInfo(info.PaxInfo),
		}
	}

	return res
}

func toDomainPaxInfo(items []*skyhubFlightsBackendPb.PaxInfo) []*domain.PaxInfo {
	res := make([]*domain.PaxInfo, len(items))
	for index, info := range items {
		res[index] = &domain.PaxInfo{
			ID:            info.Id,
			FirstName:     info.FirstName,
			LastName:      info.LastName,
			PassengerType: enum.PaxType(base.PassengerType_name[int32(info.PassengerType)]),
			TicketNumber:  info.TicketNumber,
		}
	}

	return res
}

func ToDomainReportBooking(items *skyhubFlightsBackendPb.GetReportBookingsRes) []*domain.SkyHubReport {
	if items == nil || len(items.Items) == 0 {
		return nil
	}

	res := make([]*domain.SkyHubReport, len(items.Items))
	for index, data := range items.Items {
		res[index] = &domain.SkyHubReport{
			Status:             data.Status,
			Provider:           data.Provider,
			AgentCode:          data.AgentCode,
			ContactLastName:    data.ContactLastName,
			ContactFirstName:   data.ContactFirstName,
			ContactCountryCode: data.ContactCountryCode,
			ContactPhoneNumber: data.ContactPhoneNumber,
			ContactEmail:       data.ContactEmail,
			BookingCode:        data.BookingCode,
			PNRCode:            data.PnrCode,
			TicketNumber:       data.TicketNumber,
			PassengerSurname:   data.PassengerSurname,
			PassengerGivenName: data.PassengerGivenName,
			DeparturePlace:     data.DeparturePlace,
			ArrivalPlace:       data.ArrivalPlace,
			DepartureDate:      time.UnixMilli(data.DepartureDate),
			FareBasic:          data.FareBasic,
			TaxAmount:          data.TaxAmount,
			SeatPrice:          data.SeatPrice,
			BaggagePrice:       data.BaggagePrice,
			HiddenFee:          data.HiddenFee,
			TotalFare:          data.TotalFare,
			CommissionRate:     data.CommissionRate,
			SurchargeRoute:     data.SurchargeRoute,
			Surcharges:         ToDomainSurcharges(data.Surcharges),
		}
	}

	return res
}

func ToDomainSurcharges(items []*skyhubFlightsBasePb.Surcharge) []*domain.Surcharge {

	res := make([]*domain.Surcharge, len(items))
	for index, info := range items {
		res[index] = &domain.Surcharge{
			SurchargeName:           info.SurchargeName,
			SurchargeBaseAmount:     info.SurchargeBaseAmount,
			SurchargeTaxAmount:      info.SurchargeTaxAmount,
			SurchargeDiscountAmount: info.SurchargeDiscountAmount,
			SurchargeTotalAmount:    info.SurchargeTotalAmount,
		}
	}

	return res
}
