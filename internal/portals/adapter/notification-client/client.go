package notificationclient

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/api/gen/go/notification"
	notiBE "gitlab.deepgate.io/apps/api/gen/go/notification/backend"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/portals/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
)

type SendMessageRequest struct {
	EntityType  string
	EntityID    string
	ServiceCode string
	Action      string
	Data        map[string]string
}

type notificationClient struct {
	cfg *config.Schema
}

type NotificationClient interface {
	SendRawNotification(ctx context.Context, req *domain.BookingNotification, recipients []string) error
	SendMessage(ctx context.Context, req *SendMessageRequest) error
}

func NewNotificationClient(cfg *config.Schema) NotificationClient {
	return &notificationClient{cfg: cfg}
}

func fromDomainFileAttachments(ins []*domain.FileAttachment) []*notiBE.FileAttachment {
	out := make([]*notiBE.FileAttachment, len(ins))
	for i, in := range ins {
		out[i] = &notiBE.FileAttachment{
			Name: in.FileName,
			Url:  in.FileURL,
		}
	}
	return out
}

func (c *notificationClient) SendRawNotification(ctx context.Context, req *domain.BookingNotification, recipients []string) error {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.NotificationServiceEndpoint)
	if err != nil {
		return errors.Wrap(err, "Cannot connect partner service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := notiBE.NewNotificationServiceClient(conn)

	clientReq := &notiBE.SendRawNotificationReq{
		SenderId:    req.SenderID,
		Title:       req.Title,
		Content:     req.Content,
		Method:      notification.NotificationMethod(req.Method),
		Recipients:  recipients,
		Attachments: fromDomainFileAttachments(req.FileAttachments),
	}

	r, err := client.SendRawNotification(
		newCtx,
		clientReq,
	)

	if err != nil {
		st, ok := status.FromError(err)
		if ok && st.Message() == commonErrors.ErrNotFound.Error() {
			return nil
		}

		return errors.Wrap(err, "Partner GetPartnerShopByCode failed")
	}

	if r != nil && !r.IsSuccess {
		return commonErrors.New(commonErrors.BadRequest, r.ErrorCode)
	}

	return nil
}

func (c *notificationClient) SendMessage(ctx context.Context, req *SendMessageRequest) error {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.NotificationServiceEndpoint)
	if err != nil {
		return errors.Wrap(err, "Cannot connect order service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, "")
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := notiBE.NewTelegramServiceClient(conn)
	res, err := client.SendMessage(
		newCtx,
		&notiBE.SendMessageRequest{
			EntityType:  req.EntityType,
			EntityId:    req.EntityID,
			ServiceCode: req.ServiceCode,
			Action:      req.Action,
			Data:        req.Data,
		},
	)
	if err != nil {
		return errors.Wrap(err, "CreateHotelOrder failed")
	}

	if res == nil {
		return nil
	}

	if !res.IsSuccess {
		return errors.New(res.ErrorCode)
	}

	return nil
}
