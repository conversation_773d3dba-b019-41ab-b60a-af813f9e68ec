package web

import (
	"context"

	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/web"
	"gitlab.deepgate.io/apps/common/auth"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
)

func (s *WebServer) GetTopupDetail(
	ctx context.Context,
	req *pb.GetTopupDetailReq,
) (*pb.GetTopupDetailRes, error) {
	if err := req.Validate(); err != nil {
		log.Error("validate error", log.Any("err", err))
		return &pb.GetTopupDetailRes{
			IsSuccess: false,
			ErrorCode: commonError.ErrInvalidInput.Error(),
		}, nil
	}

	usrContext, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, commonError.ErrPermissionDenied
	}

	response, err := s.app.Queries.GetTopupDetail.Handle(ctx, req.Id, usrContext.PartnershipId)
	if err != nil {
		if commonError.IsDirectError(err) {
			return nil, err
		}

		return &pb.GetTopupDetailRes{
			IsSuccess: false,
			ErrorCode: s.handleError(err),
		}, nil
	}

	return &pb.GetTopupDetailRes{
		IsSuccess: true,
		Data:      ToTopupRequestProto(response),
	}, nil
}
