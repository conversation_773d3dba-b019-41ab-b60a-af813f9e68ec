// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type createCurrencyExchangeHandlerTrace struct {
	command.CreateCurrencyExchangeHandler
}

func NewCreateCurrencyExchangeHandler(arg1 repositories.CurrencyExchangeRepository,

) command.CreateCurrencyExchangeHandler {
	return &createCurrencyExchangeHandlerTrace{
		CreateCurrencyExchangeHandler: command.NewCreateCurrencyExchangeHandler(
			arg1,
		),
	}
}

func (s *createCurrencyExchangeHandlerTrace) Handle(ctx context.Context, arg2 *domain.CurrencyExchange) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "createCurrencyExchangeHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.CreateCurrencyExchangeHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CreateCurrencyExchangeHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
