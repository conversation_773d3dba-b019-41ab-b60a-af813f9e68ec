// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/order"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type createBookingHandlerTrace struct {
	command.CreateBookingHandler
}

func NewCreateBookingHandler(arg1 *config.Schema,
	arg2 repositories.BookingRepository,
	arg3 repositories.PNRRepository,
	arg4 service.BookingService,
	arg5 service.SessionService,
	arg6 redisrepo.BookingRepository,
	arg7 order.OrderServiceClient,
	arg8 service.ReportService,
	arg9 service.L2bService,
	arg10 service.ListFlightService,

) command.CreateBookingHandler {
	return &createBookingHandlerTrace{
		CreateBookingHandler: command.NewCreateBookingHandler(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
		),
	}
}

func (s *createBookingHandlerTrace) Handle(ctx context.Context, arg2 *domain.CreateBookingRequest, arg3 auth.User) (*domain.CreateBookingResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "createBookingHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.CreateBookingHandler.Handle(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CreateBookingHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
