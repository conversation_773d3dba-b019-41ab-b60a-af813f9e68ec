// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type checkFareV2HandlerTrace struct {
	command.CheckFareV2Handler
}

func NewCheckFareV2Handler(arg1 service.FareService,
	arg2 service.SessionService,
	arg3 service.SearchFlightsService,
	arg4 service.BookingService,
	arg5 service.SupplierRouteService,
	arg6 service.L2bService,
	arg7 redisrepo.SearchFlightsRepository,
	arg8 service.ListFlightService,
	arg9 service.PriceService,
	arg10 config.Schema,

) command.CheckFareV2Handler {
	return &checkFareV2HandlerTrace{
		CheckFareV2Handler: command.NewCheckFareV2Handler(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
		),
	}
}

func (s *checkFareV2HandlerTrace) Handle(ctx context.Context, arg2 *domain.CheckFareRequestV2) (*domain.CheckFareResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "checkFareV2Handler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CheckFareV2Handler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CheckFareV2Handler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
