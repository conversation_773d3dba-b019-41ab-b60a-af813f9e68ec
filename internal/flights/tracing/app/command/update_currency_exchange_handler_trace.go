// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type updateCurrencyExchangeHandlerTrace struct {
	command.UpdateCurrencyExchangeHandler
}

func NewUpdateCurrencyExchangeHandler(arg1 repositories.CurrencyExchangeRepository,

) command.UpdateCurrencyExchangeHandler {
	return &updateCurrencyExchangeHandlerTrace{
		UpdateCurrencyExchangeHandler: command.NewUpdateCurrencyExchangeHandler(
			arg1,
		),
	}
}

func (s *updateCurrencyExchangeHandlerTrace) Handle(ctx context.Context, arg2 *domain.CurrencyExchange) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "updateCurrencyExchangeHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.UpdateCurrencyExchangeHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.UpdateCurrencyExchangeHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
