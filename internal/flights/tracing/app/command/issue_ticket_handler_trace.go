// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/order"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/payment"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type issueTicketHandlerTrace struct {
	command.IssueTicketHandler
}

func NewIssueTicketHandler(arg1 repositories.BookingRepository,
	arg2 repositories.PNRRepository,
	arg3 service.IssueTicketService,
	arg4 service.BookingService,
	arg5 redisrepo.TicketRepository,
	arg6 order.OrderServiceClient,
	arg7 service.ReportService,
	arg8 webhook.WebhookAdapter,
	arg9 payment.PaymentClient,
	arg10 wallet.WalletClient,
	arg11 internal_client.InternalClient,
	arg12 service.L2bService,
	arg13 service.ListFlightService,
	arg14 query.GetBookingByIDHandler,

) command.IssueTicketHandler {
	return &issueTicketHandlerTrace{
		IssueTicketHandler: command.NewIssueTicketHandler(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
			arg12,
			arg13,
			arg14,
		),
	}
}

func (s *issueTicketHandlerTrace) Handle(ctx context.Context, arg2 *domain.IssueTicketRequest, arg3 *domain.WebhookCfg) (*domain.IssueTicketResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "issueTicketHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.IssueTicketHandler.Handle(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.IssueTicketHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
