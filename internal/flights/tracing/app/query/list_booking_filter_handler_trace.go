// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/partnership"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type listBookingFilterHandlerTrace struct {
	query.ListBookingFilterHandler
}

func NewListBookingFilterHandler(arg1 repositories.BookingRepository,
	arg2 internal_client.InternalClient,
	arg3 partnership.PartnershipClient,

) query.ListBookingFilterHandler {
	return &listBookingFilterHandlerTrace{
		ListBookingFilterHandler: query.NewListBookingFilterHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *listBookingFilterHandlerTrace) Handle(ctx context.Context, arg2 *domain.ListBookingFilter) (*query.ListBookingFilterResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "listBookingFilterHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.ListBookingFilterHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ListBookingFilterHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
