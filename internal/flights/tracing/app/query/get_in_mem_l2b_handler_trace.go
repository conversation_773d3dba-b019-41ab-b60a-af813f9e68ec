// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.opentelemetry.io/otel/codes"
)

type getInMemL2bHandlerTrace struct {
	query.GetInMemL2bHandler
}

func NewGetInMemL2bHandler(arg1 redisrepo.L2bRepository,

) query.GetInMemL2bHandler {
	return &getInMemL2bHandlerTrace{
		GetInMemL2bHandler: query.NewGetInMemL2bHandler(
			arg1,
		),
	}
}

func (s *getInMemL2bHandlerTrace) Handle(ctx context.Context) ([]*domain.L2bTrackingItem, map[enum.L2bAPI]int, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "getInMemL2bHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1, res2, res3 := s.GetInMemL2bHandler.Handle(ctx)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.GetInMemL2bHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}
