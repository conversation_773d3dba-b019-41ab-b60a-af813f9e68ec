// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type getCurrencyExchangeDetailHandlerTrace struct {
	query.GetCurrencyExchangeDetailHandler
}

func NewGetCurrencyExchangeDetailHandler(arg1 repositories.CurrencyExchangeRepository,

) query.GetCurrencyExchangeDetailHandler {
	return &getCurrencyExchangeDetailHandlerTrace{
		GetCurrencyExchangeDetailHandler: query.NewGetCurrencyExchangeDetailHandler(
			arg1,
		),
	}
}

func (s *getCurrencyExchangeDetailHandlerTrace) Handle(ctx context.Context, arg2 *domain.GetCurrencyExchangeDetailReq) (*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "getCurrencyExchangeDetailHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.GetCurrencyExchangeDetailHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.GetCurrencyExchangeDetailHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
