package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type getBookingByIDHandlerTrace struct {
	query.GetBookingByIDHandler
}

func NewGetBookingByIDHandler(
	bookingRepo repositories.BookingRepository,
	pnrRepo repositories.PNRRepository,
) query.GetBookingByIDHandler {
	return &getBookingByIDHandlerTrace{
		GetBookingByIDHandler: query.NewGetBookingByIDHandler(
			bookingRepo,
			pnrRepo,
		),
	}
}

func (s *getBookingByIDHandlerTrace) Handle(ctx context.Context, req *domain.GetBookingByIDRequest) (*domain.GetBookingByIDResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "getBookingByIDHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, req)

	res, err := s.GetBookingByIDHandler.Handle(ctx, req)
	if err != nil {
		errValue, ok := reflect.ValueOf(err).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.GetBookingByIDHandler.Handle failed")
			span.RecordError(errValue)
		}
	}
	return res, err
}
