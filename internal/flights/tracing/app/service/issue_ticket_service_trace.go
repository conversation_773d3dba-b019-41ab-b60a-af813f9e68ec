// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/telegram"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna1a_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type issueTicketServiceTrace struct {
	service.IssueTicketService
}

func NewIssueTicketService(arg1 *config.Schema,
	arg2 vna_client.VNAAdapter,
	arg3 travel_fusion.TravelFusionAdapter,
	arg4 amadeus_client.AmadeusAdapter,
	arg5 vietjet_client.VietjetAdapter,
	arg6 repositories.BookingRepository,
	arg7 repositories.PNRRepository,
	arg8 redisrepo.BookingRepository,
	arg9 telegram.EVIssueTicketTelegramBotRepository,
	arg10 wallet.WalletClient,
	arg11 payment.PaymentClient,
	arg12 internal_client.InternalClient,
	arg13 webhook.WebhookAdapter,
	arg14 service.CommissionService,
	arg15 service.L2bService,
	arg16 ev_client.EVAdapter,
	arg17 service.AncillaryService,
	arg18 repositories.AmadeusPNRRepository,
	arg19 telegram.IssueEMDRepository,
	arg20 vna1a_client.VNA1AAdapter,
	arg21 hnh_client.HNHAdapter,
	arg22 service.ReportService,
	arg23 pkfare_client.PkfareAdapter,
	arg24 tongcheng_client.TongChengAdapter,

) service.IssueTicketService {
	return &issueTicketServiceTrace{
		IssueTicketService: service.NewIssueTicketService(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
			arg12,
			arg13,
			arg14,
			arg15,
			arg16,
			arg17,
			arg18,
			arg19,
			arg20,
			arg21,
			arg22,
			arg23,
			arg24,
		),
	}
}

func (s *issueTicketServiceTrace) IssueTicket(ctx context.Context, arg2 enum.FlightProvider, arg3 *domain.BookingSession, arg4 *domain.PNR) (*domain.IssueTicketServiceResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "issueTicketService.IssueTicket")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.IssueTicketService.IssueTicket(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.IssueTicketService.IssueTicket failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *issueTicketServiceTrace) RefundFailedTicket(ctx context.Context, arg2 *domain.PaymentMethod, arg3 string, arg4 *domain.WebhookCfg, arg5 string, arg6 string, arg7 string, arg8 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "issueTicketService.RefundFailedTicket")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6, arg7, arg8)

	res1 := s.IssueTicketService.RefundFailedTicket(ctx, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.IssueTicketService.RefundFailedTicket failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *issueTicketServiceTrace) ResolvePendingTicket(ctx context.Context, arg2 *domain.BookingSession) (enum.TicketStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "issueTicketService.ResolvePendingTicket")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.IssueTicketService.ResolvePendingTicket(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.IssueTicketService.ResolvePendingTicket failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
