// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type amadeusServiceTrace struct {
	service.AmadeusService
}

func NewAmadeusService(arg1 amadeus_client.AmadeusAdapter,

) service.AmadeusService {
	return &amadeusServiceTrace{
		AmadeusService: service.NewAmadeusService(
			arg1,
		),
	}
}

func (s *amadeusServiceTrace) RetrievePNRForTransferredBooking(ctx context.Context, arg2 *domain.BookingSession, arg3 *domain.PNR) (*domain.PNRAmadeusTransferred, []*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "amadeusService.RetrievePNRForTransferredBooking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3, res4 := s.AmadeusService.RetrievePNRForTransferredBooking(ctx, arg2, arg3)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AmadeusService.RetrievePNRForTransferredBooking failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}
