// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/price"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.opentelemetry.io/otel/codes"
)

type priceServiceTrace struct {
	service.PriceService
}

func NewPriceService(arg1 price.PriceClient,

) service.PriceService {
	return &priceServiceTrace{
		PriceService: service.NewPriceService(
			arg1,
		),
	}
}

func (s *priceServiceTrace) CalculateFarePricing(ctx context.Context, arg2 *domain.TotalFareInfo, arg3 *domain.SearchFlightsRequest, arg4 []*domain.FlightItinerary, arg5 string, arg6 enum.FlightProvider) (*domain.TotalFareInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceService.CalculateFarePricing")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1, res2 := s.PriceService.CalculateFarePricing(ctx, arg2, arg3, arg4, arg5, arg6)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceService.CalculateFarePricing failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *priceServiceTrace) CalculatePenaltySearchFlight(ctx context.Context, arg2 string, arg3 string, arg4 []*domain.ResponseFlight) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceService.CalculatePenaltySearchFlight")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.PriceService.CalculatePenaltySearchFlight(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceService.CalculatePenaltySearchFlight failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *priceServiceTrace) CalculateSearchFlightsPricing(ctx context.Context, arg2 []*domain.ResponseFlight, arg3 *domain.PaxRequest, arg4 enum.FlightType, arg5 string, arg6 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceService.CalculateSearchFlightsPricing")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1 := s.PriceService.CalculateSearchFlightsPricing(ctx, arg2, arg3, arg4, arg5, arg6)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceService.CalculateSearchFlightsPricing failed")
			span.RecordError(err)
		}
	}
	return res1

}
