// DO NOT EDIT: code generated from 'gen-tracing.go'
package pkfare_client_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/price"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type priceClientTrace struct {
	price.PriceClient
}

func NewPriceClient(arg1 *config.Schema,

) price.PriceClient {
	return &priceClientTrace{
		PriceClient: price.NewPriceClient(
			arg1,
		),
	}
}

func (s *priceClientTrace) CalculateFlightPricing(ctx context.Context, arg2 *domain.PricingCalculationRequest) (*domain.PricingCalculationResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceClient.CalculateFlightPricing")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PriceClient.CalculateFlightPricing(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceClient.CalculateFlightPricing failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *priceClientTrace) CalculateFlightPricingBatch(ctx context.Context, arg2 *domain.PricingBatchServiceRequest) (*domain.PricingBatchServiceResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "priceClient.CalculateFlightPricingBatch")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PriceClient.CalculateFlightPricingBatch(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PriceClient.CalculateFlightPricingBatch failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
