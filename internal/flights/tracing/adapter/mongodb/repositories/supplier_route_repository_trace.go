// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.opentelemetry.io/otel/codes"
)

type supplierRouteRepositoryTrace struct {
	repositories.SupplierRouteRepository
}

func NewSupplierRouteRepository(arg1 commonMongoDb.DB,

) repositories.SupplierRouteRepository {
	return &supplierRouteRepositoryTrace{
		SupplierRouteRepository: repositories.NewSupplierRouteRepository(
			arg1,
		),
	}
}

func (s *supplierRouteRepositoryTrace) DeleteByProvider(ctx context.Context, arg2 enum.FlightProvider) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "supplierRouteRepository.DeleteByProvider")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.SupplierRouteRepository.DeleteByProvider(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SupplierRouteRepository.DeleteByProvider failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *supplierRouteRepositoryTrace) FindByRoute(ctx context.Context, arg2 string) ([]*domain.SupplierRoute, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "supplierRouteRepository.FindByRoute")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.SupplierRouteRepository.FindByRoute(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SupplierRouteRepository.FindByRoute failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *supplierRouteRepositoryTrace) UpdateSoftDeleteByProvider(ctx context.Context, arg2 enum.FlightProvider) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "supplierRouteRepository.UpdateSoftDeleteByProvider")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.SupplierRouteRepository.UpdateSoftDeleteByProvider(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SupplierRouteRepository.UpdateSoftDeleteByProvider failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *supplierRouteRepositoryTrace) UpsertMany(ctx context.Context, arg2 []*domain.SupplierRoute) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "supplierRouteRepository.UpsertMany")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.SupplierRouteRepository.UpsertMany(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SupplierRouteRepository.UpsertMany failed")
			span.RecordError(err)
		}
	}
	return res1

}
