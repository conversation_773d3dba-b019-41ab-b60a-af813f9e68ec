// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.opentelemetry.io/otel/codes"
)

type amaSSRTemplateRepositoryTrace struct {
	repositories.AmaSSRTemplateRepository
}

func NewAmaSSRTemplateRepository(arg1 commonMongoDb.DB,

) repositories.AmaSSRTemplateRepository {
	return &amaSSRTemplateRepositoryTrace{
		AmaSSRTemplateRepository: repositories.NewAmaSSRTemplateRepository(
			arg1,
		),
	}
}

func (s *amaSSRTemplateRepositoryTrace) FindAll(ctx context.Context) ([]*domain.SSRTemplate, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "amaSSRTemplateRepository.FindAll")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1, res2 := s.AmaSSRTemplateRepository.FindAll(ctx)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AmaSSRTemplateRepository.FindAll failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *amaSSRTemplateRepositoryTrace) InsertOne(ctx context.Context, arg2 *domain.SSRTemplate) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "amaSSRTemplateRepository.InsertOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.AmaSSRTemplateRepository.InsertOne(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AmaSSRTemplateRepository.InsertOne failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *amaSSRTemplateRepositoryTrace) Update(ctx context.Context, arg2 string, arg3 *domain.SSRTemplate) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "amaSSRTemplateRepository.Update")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.AmaSSRTemplateRepository.Update(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AmaSSRTemplateRepository.Update failed")
			span.RecordError(err)
		}
	}
	return res1

}
