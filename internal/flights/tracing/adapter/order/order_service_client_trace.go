// DO NOT EDIT: code generated from 'gen-tracing.go'
package order_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/order"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type orderServiceClientTrace struct {
	order.OrderServiceClient
}

func NewOrderServiceClient(arg1 *config.Schema,

) order.OrderServiceClient {
	return &orderServiceClientTrace{
		OrderServiceClient: order.NewOrderServiceClient(
			arg1,
		),
	}
}

func (s *orderServiceClientTrace) CreateFlightOrder(ctx context.Context, arg2 *domain.FlightOrder) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderServiceClient.CreateFlightOrder")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.OrderServiceClient.CreateFlightOrder(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderServiceClient.CreateFlightOrder failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *orderServiceClientTrace) PlaceFlightOrder(ctx context.Context, arg2 string, arg3 *domain.PartnerShopInfo, arg4 string, arg5 *domain.BookingSession) (*domain.TransactionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "orderServiceClient.PlaceFlightOrder")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2 := s.OrderServiceClient.PlaceFlightOrder(ctx, arg2, arg3, arg4, arg5)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.OrderServiceClient.PlaceFlightOrder failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
