package data_warehouse

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

const (
	successfulCode = 200
	timeoutContext = time.Second * 30
	pathAPI        = "/report/flight/hub"
	postMethod     = "POST"
)

type dataWareHouseServiceClient struct {
	BaseURL string
	APIKey  string
}

type DataWareHouseServiceClient interface {
	SendReportBooking(ctx context.Context, reports []*domain.SkyHubReport) error
}

func NewDataWareHouseServiceClient(cfg *config.Schema) DataWareHouseServiceClient {
	return &dataWareHouseServiceClient{
		BaseURL: cfg.DataWareHouseServiceEndpoint,
		APIKey:  cfg.DataWareHouseAPIKey,
	}
}

func (c *dataWareHouseServiceClient) SendReportBooking(ctx context.Context, reports []*domain.SkyHubReport) error {
	err := c.doRequest(ctx, pathAPI, postMethod, reports, c.getHeader())
	if err != nil {
		return errors.Wrap(err, "SendReportBooking failed")
	}

	return nil
}

func (c *dataWareHouseServiceClient) do(
	ctx context.Context,
	relativePath string,
	method string,
	body interface{},
	headers map[string]string,
) error {
	response, err := tracingHttp.JSONRequest(ctx, c.BaseURL+relativePath, method, body, headers)
	if err != nil {
		log.Error("tracingHttp.JSONRequest error ",
			log.Any("error", err),
			log.String("relative path", relativePath))
		return err
	}

	if response.StatusCode != successfulCode {
		log.Error("Status code not successful",
			log.Int("Status Code", response.StatusCode))
		return errors.New(response.Status)
	}

	return nil
}

func (c *dataWareHouseServiceClient) doRequest(
	ctx context.Context,
	relativePath string,
	method string,
	body interface{},
	headers map[string]string,
) error {
	err := c.do(ctx, relativePath, method, body, headers)
	if err != nil {
		log.Error("do failed", log.Any("error", err))

		return err
	}

	return nil
}
func (c *dataWareHouseServiceClient) getHeader() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
		"api-key":      c.APIKey,
	}
}
