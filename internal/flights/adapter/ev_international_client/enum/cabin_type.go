package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
)

type CabinType string

const (
	CabinTypeNone           CabinType = ""
	CabinTypeEconomy        CabinType = "ECONOMY"
	CabinTypePremiumEconomy CabinType = "PREMIUM_ECONOMY"
	CabinTypeFirst          CabinType = "FIRST"
	CabinTypeBusiness       CabinType = "BUSINESS"
)

var CabinTypeName = map[CabinType]string{
	CabinTypeNone:           "",
	CabinTypeEconomy:        "ECONOMY",
	CabinTypePremiumEconomy: "PREMIUM_ECONOMY",
	CabinTypeFirst:          "FIRST",
	CabinTypeBusiness:       "BUSINESS",
}

var CabinTypeValue = func() map[string]CabinType {
	value := map[string]CabinType{}
	for k, v := range CabinTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e CabinType) MarshalJSON() ([]byte, error) {
	v, ok := CabinTypeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *CabinType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := CabinTypeValue[string(data)]
	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (e *CabinType) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := CabinTypeValue[s]
	if !ok {
		*e = CabinType(s)
		log.Warn("CabinType UnmarshalXML waring missing key", log.String("key", s))
		return nil
	}

	*e = v

	return nil
}

func (e *CabinType) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(CabinTypeName[*e], start)
}

func (*CabinType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range CabinTypeName {
		vals = append(vals, name)
	}

	return vals
}
