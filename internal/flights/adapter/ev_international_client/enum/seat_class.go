package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
)

type SeatClass string

const (
	SeatClassNone           SeatClass = ""
	SeatClassEconomy        SeatClass = "ECONOMY"
	SeatClassPremiumEconomy SeatClass = "PREMIUM_ECONOMY"
	SeatClassFirst          SeatClass = "FIRST"
	SeatClassBusiness       SeatClass = "BUSINESS"
)

var SeatClassName = map[SeatClass]string{
	SeatClassNone:           "",
	SeatClassEconomy:        "ECONOMY",
	SeatClassPremiumEconomy: "PREMIUM_ECONOMY",
	SeatClassFirst:          "FIRST",
	SeatClassBusiness:       "BUSINESS",
}

var SeatClassValue = func() map[string]SeatClass {
	value := map[string]SeatClass{}
	for k, v := range SeatClassName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e SeatClass) MarshalJSON() ([]byte, error) {
	v, ok := SeatClassName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *SeatClass) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := SeatClassValue[string(data)]
	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (e *SeatClass) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := SeatClassValue[s]
	if !ok {
		*e = SeatClass(s)
		log.Warn("SeatClass UnmarshalXML waring missing key", log.String("key", s))
		return nil
	}

	*e = v

	return nil
}

func (e *SeatClass) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(SeatClassName[*e], start)
}

func (*SeatClass) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range SeatClassName {
		vals = append(vals, name)
	}

	return vals
}
