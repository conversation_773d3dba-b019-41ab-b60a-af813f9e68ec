package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
)

type FlightOption string

const (
	FlightOptionNone      FlightOption = ""
	FlightOptionOneWay    FlightOption = "ONE_WAY"
	FlightOptionReturn    FlightOption = "RETURN"
	FlightOptionMultiCity FlightOption = "multiCity"
)

var FlightOptionName = map[FlightOption]string{
	FlightOptionNone:      "",
	FlightOptionOneWay:    "ONE_WAY",
	FlightOptionReturn:    "RETURN",
	FlightOptionMultiCity: "multiCity",
}

var FlightOptionValue = func() map[string]FlightOption {
	value := map[string]FlightOption{}
	for k, v := range FlightOptionName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e FlightOption) MarshalJSON() ([]byte, error) {
	v, ok := FlightOptionName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *FlightOption) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := FlightOptionValue[string(data)]
	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (e *FlightOption) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := FlightOptionValue[s]
	if !ok {
		*e = FlightOption(s)
		log.Warn("FlightOption UnmarshalXML waring missing key", log.String("key", s))
		return nil
	}

	*e = v

	return nil
}

func (e *FlightOption) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(FlightOptionName[*e], start)
}

func (*FlightOption) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range FlightOptionName {
		vals = append(vals, name)
	}

	return vals
}
