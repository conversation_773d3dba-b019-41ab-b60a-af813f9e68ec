package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"strings"

	"github.com/pkg/errors"
)

type TravelerType string

const (
	TravelerTypeNone         TravelerType = ""
	TravelerTypeAdult        TravelerType = "ADULT"
	TravelerTypeChild        TravelerType = "CHILD"
	TravelerTypeSeatedInfant TravelerType = "SEATED_INFANT"
)

var TravelerTypeName = map[TravelerType]string{
	TravelerTypeNone:         "",
	TravelerTypeAdult:        "ADULT",
	TravelerTypeChild:        "CHILD",
	TravelerTypeSeatedInfant: "SEATED_INFANT",
}

var TravelerTypeValue = func() map[string]TravelerType {
	value := map[string]TravelerType{}
	for k, v := range TravelerTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e TravelerType) MarshalJSON() ([]byte, error) {
	v, ok := TravelerTypeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *TravelerType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := TravelerTypeValue[string(data)]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", strings.ToLower(string(data)), e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (e *TravelerType) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := TravelerTypeValue[strings.ToLower(s)]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", strings.ToLower(s), e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (e *TravelerType) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(TravelerTypeName[*e], start)
}

func (*TravelerType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range TravelerTypeName {
		vals = append(vals, name)
	}

	return vals
}
