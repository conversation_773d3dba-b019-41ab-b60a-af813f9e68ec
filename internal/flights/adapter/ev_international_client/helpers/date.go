package helpers

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

const FullDateLayout = "2006-01-02T15:04:05"
const DOBFormat = "2006-01-02"

func ParseDuration(str string) (int, error) {
	// Loại bỏ "PT" ở đầu chuỗi
	str = strings.TrimPrefix(str, "PT")

	// Tách giờ và phút từ chuỗi
	parts := strings.Split(str, "H")
	if len(parts) != 2 {
		return 0, fmt.<PERSON>rrorf("Chuỗi không hợp lệ")
	}

	hours, err := strconv.Atoi(parts[0])
	if err != nil {
		return 0, err
	}

	// Tách phút từ phần sau "H"
	minutesIndex := strings.Index(parts[1], "M")
	if minutesIndex == -1 {
		return 0, fmt.Errorf("Chuỗi không hợp lệ")
	}

	minutes, err := strconv.Atoi(parts[1][:minutesIndex])
	if err != nil {
		return 0, err
	}

	// Tính tổng số phút
	totalMinutes := hours*60 + minutes

	return totalMinutes, nil
}

func ParseTime(date string) (int64, error) {
	timer, err := time.Parse(FullDateLayout, date)
	if err != nil {
		return 0, err
	}

	return timer.UnixMilli(), nil
}

func ParseTimeDateOnly(date *int64) string {
	if date == nil {
		return ""
	}

	timeValue := time.UnixMilli(*date)
	return timeValue.Format(time.DateOnly)
}
