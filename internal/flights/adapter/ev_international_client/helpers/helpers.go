package helpers

import (
	"fmt"

	commonEnum "gitlab.deepgate.io/apps/common/enum"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func IsEqualItinerary(itineraryA, itineraryB *domain.FlightItinerary) (bool, error) {
	if len(itineraryA.Segments) == 0 || len(itineraryB.Segments) == 0 {
		return false, fmt.Errorf("IsEqualItinerary req nil")
	}

	if len(itineraryA.Segments) != len(itineraryB.Segments) {
		return false, nil
	}

	for index, v := range itineraryA.Segments {
		intineraryTwoSegment := itineraryB.Segments[index]
		iti1 := fmt.Sprintf("%s-%s-%d-%d-%d-%s-%s-%d-%s", v.<PERSON><PERSON><PERSON><PERSON><PERSON>, v.A<PERSON>lace, v.DepartDate, v.ArrivalDate, v.FlightDuration, v.FlightNumber, v.CabinClassCode, itineraryA.FlightDuration, v.BookingClass)
		iti2 := fmt.Sprintf("%s-%s-%d-%d-%d-%s-%s-%d-%s", intineraryTwoSegment.DepartPlace, intineraryTwoSegment.ArrivalPlace, intineraryTwoSegment.DepartDate, intineraryTwoSegment.ArrivalDate, intineraryTwoSegment.FlightDuration, intineraryTwoSegment.FlightNumber, intineraryTwoSegment.CabinClassCode, itineraryB.FlightDuration, intineraryTwoSegment.BookingClass)

		if iti1 != iti2 {
			return false, nil
		}
	}

	return true, nil
}

func IsEqualItinerarySegment(itineraryA, itineraryB *domain.FlightItinerary) (bool, error) {
	if len(itineraryA.Segments) == 0 || len(itineraryB.Segments) == 0 {
		return false, fmt.Errorf("IsEqualItinerary req nil")
	}

	if len(itineraryA.Segments) != len(itineraryB.Segments) {
		return false, nil
	}

	for index, v := range itineraryA.Segments {
		intineraryTwoSegment := itineraryB.Segments[index]
		iti1 := fmt.Sprintf("%s-%s-%s-%s-%s", v.DepartPlace, v.ArrivalPlace, v.FlightNumber, v.CabinClassCode, v.BookingClass)
		iti2 := fmt.Sprintf("%s-%s-%s-%s-%s", intineraryTwoSegment.DepartPlace, intineraryTwoSegment.ArrivalPlace, intineraryTwoSegment.FlightNumber, intineraryTwoSegment.CabinClassCode, intineraryTwoSegment.BookingClass)

		if iti1 != iti2 {
			return false, nil
		}
	}

	return true, nil
}

func GetKeyMapTitleName(paxType enum.PaxType, gender commonEnum.GenderType) string {
	return string(paxType) + "-" + string(commonEnum.GenderTypeName[gender])
}
