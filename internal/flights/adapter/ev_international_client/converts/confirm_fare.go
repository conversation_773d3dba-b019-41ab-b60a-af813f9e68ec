package converts

import (
	"fmt"

	"github.com/pkg/errors"
	evConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func GetItineraryFromBookingSession(info *responses.CommandListResponse, bookingSession *domain.BookingSession) (*domain.TotalFareInfo, error) {
	if info == nil || info.Data == nil || bookingSession == nil {
		return nil, fmt.Errorf("GetItineraryFromRawResponse info nil")
	}
	foundItinerary := false
	for _, data := range info.Data {
		// itinerariesPaxFare := toDomainItinerariesPaxFare(data.TravelerPricings)

		for index, itinerary := range data.Itineraries {
			tempItinerary, err := toDomainFlightItinerary(itinerary, data.TravelerPricings[0].FareDetailsBySegment, index+1)
			if err != nil {
				return nil, errors.Wrap(err, "toDomainFlightItinerary")
			}
			// tempItinerary.PaxFares = itinerariesPaxFare

			isEqual, err := helpers.IsEqualItinerary(bookingSession.Itineraries[index], tempItinerary)
			if err != nil {
				return nil, errors.Wrap(err, "helpers.IsEqualItinerary")
			}

			if isEqual {
				foundItinerary = true
				continue
			}

			foundItinerary = false
			break
		}

		if foundItinerary {
			totalTaxAmount := data.Price.Total - data.Price.Base + data.Price.Commission
			itinerariesPaxFare := ToDomainItinerariesPaxFare(data.TravelerPricings)

			return &domain.TotalFareInfo{
				TotalPaxFares:       itinerariesPaxFare,
				BaseTotalFareAmount: totalTaxAmount + data.Price.Base,
				TotalFareBasic:      data.Price.Base,
				TotalTaxAmount:      totalTaxAmount,
				Currency:            evConstants.DefaultCurrency,
			}, nil
		}
	}

	return nil, domain.ErrItinerarySoldOut
}
