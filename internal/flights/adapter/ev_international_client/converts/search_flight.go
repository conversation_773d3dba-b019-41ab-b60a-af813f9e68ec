package converts

import (
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	evConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/requests"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/responses"
	evEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/enum"
	evHelpers "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func FromDomainSearchRequest(info *domain.SearchFlightsRequest) (*requests.SearchFlightReq, error) {
	if info == nil {
		return nil, fmt.Errorf("FromDomainSearchRequest info nil")
	}

	res := &requests.SearchFlightReq{
		OriginDestinations: fromDomainItinerariesRequest(info.Itineraries),
		Travelers:          fromDomainPassengers(&info.Passengers),
		SearchCriteria: &requests.SearchCriteria{
			MaxFlightOffers: 250,
			FlightFilters: &requests.FlightFilters{
				CabinRestrictions: []*requests.CabinRestrictions{
					{
						Cabin: evEnum.CabinTypeEconomy,
					},
					{
						Cabin: evEnum.CabinTypeBusiness,
					},
					{
						Cabin: evEnum.CabinTypeFirst,
					},
					{
						Cabin: evEnum.CabinTypePremiumEconomy,
					},
				},
			},
		},
		TicketType: "ALL",
		SearchURL:  "https://demo.booking1a.com/services/proxy/api/flights/search",
	}

	if info.IsRoundTrip() {
		res.FlightOption = evEnum.FlightOptionReturn
	} else if len(info.Itineraries) >= 2 {
		res.FlightOption = evEnum.FlightOptionMultiCity
	} else {
		res.FlightOption = evEnum.FlightOptionOneWay
	}

	return res, nil
}

func fromDomainItinerariesRequest(info []*domain.ItineraryRequest) []*requests.OriginDestinations {
	if len(info) == 0 {
		return nil
	}
	res := make([]*requests.OriginDestinations, len(info))
	for index, itinerary := range info {
		res[index] = fromDomainItineraryRequest(itinerary, index+1)
	}

	return res
}

func fromDomainItineraryRequest(info *domain.ItineraryRequest, index int) *requests.OriginDestinations {
	timeValue := time.UnixMilli(info.DepartDate)

	formattedDate := timeValue.Format("2006-01-02")
	res := &requests.OriginDestinations{
		ID:                      index,
		OriginLocationCode:      info.DepartPlace,
		DestinationLocationCode: info.ArrivalPlace,
		DepartureDateTimeRange: &requests.DepartureDateTimeRange{
			Date: formattedDate,
		},
	}

	return res
}

func fromDomainPassengers(info *domain.PaxRequest) []*requests.Travelers {
	if info == nil || (info.ADT == 0 && info.CHD == 0 && info.INF == 0) {
		return nil
	}

	var res []*requests.Travelers
	countADT, countCHD, countINF := 1, 1, 1

	for i := 0; i < info.ADT+info.CHD+info.INF; i++ {
		var travelerType evEnum.TravelerType
		switch {
		case countADT <= info.ADT:
			travelerType = evEnum.TravelerTypeAdult
			countADT++
		case countCHD <= info.CHD:
			travelerType = evEnum.TravelerTypeChild
			countCHD++
		case countINF <= info.INF:
			travelerType = evEnum.TravelerTypeSeatedInfant
			countINF++
		default:
			return res
		}

		res = append(res, &requests.Travelers{
			ID:           i + 1,
			TravelerType: travelerType,
		})
	}

	return res
}

func ToDomainSearchFlightResponse(info *responses.CommandListResponse) ([]*domain.ResponseFlight, error) {
	if info == nil || info.Data == nil {
		return nil, fmt.Errorf("ToDomainSearchFlightResponse info nil")
	}
	result := make([]*domain.ResponseFlight, 0)
	for _, data := range info.Data {
		responseFlight, err := toDomainResponseFlight(data)
		if err != nil {
			return nil, errors.Wrap(err, "toDomainResponseFlight")
		}

		if responseFlight == nil {
			continue
		}

		result = append(result, responseFlight)
	}

	return result, nil
}

func toDomainResponseFlight(info *responses.Data) (*domain.ResponseFlight, error) {
	if info == nil {
		return nil, fmt.Errorf("toDomainResponseFlight info nil")
	}
	itinerariesPaxFare := ToDomainItinerariesPaxFare(info.TravelerPricings)
	flightID, err := strconv.Atoi(info.ID)
	if err != nil {
		return nil, errors.Wrap(err, "strconv.Atoi(info.ID)")
	}

	flightItineries := make([]*domain.FlightItinerary, 0)

	for index, itinerary := range info.Itineraries {
		tempItinerary, err := toDomainFlightItinerary(itinerary, info.TravelerPricings[0].FareDetailsBySegment, index+1)
		if err != nil {
			return nil, errors.Wrap(err, "toDomainFlightItinerary")
		}

		if tempItinerary == nil {
			return nil, nil
		}

		tempItinerary.PaxFares = itinerariesPaxFare
		tempItinerary.FreeBaggage = toDomainFreeBaggageInfo(info.TravelerPricings, index+1)
		flightItineries = append(flightItineries, tempItinerary)
	}
	totalTaxAmount := info.Price.Total - info.Price.Base + info.Price.Commission
	result := &domain.ResponseFlight{
		FlightID:    helpers.GenerateFlightID(enum.FlightProviderEVInternational),
		Index:       flightID,
		Itineraries: flightItineries,
		SearchTotalFareInfo: domain.SearchTotalFareInfo{
			TotalPaxFares:       itinerariesPaxFare,
			BaseTotalFareAmount: totalTaxAmount + info.Price.Base,
			TotalFareBasic:      info.Price.Base,
			TotalTaxAmount:      totalTaxAmount,
			Currency:            evConstants.DefaultCurrency,
		},
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeyOriginFare,
				Value: info.Price,
			},
			{
				Key:   domain.MetaKeyTotalPaxFare,
				Value: itinerariesPaxFare,
			},
		},
		Provider:   enum.FlightProviderEVInternational,
		Dispose:    false,
		OptionType: enum.FlightOptionTypeRecommend,
	}

	return result, nil
}

func toDomainFlightItinerary(info *responses.Itineraries, fareDetails []*responses.FareDetailsBySegment, index int) (*domain.FlightItinerary, error) {
	if info == nil || len(info.Segments) == 0 {
		return nil, fmt.Errorf("toDomainFlightItinerary info nil")
	}
	mapFareDetails := map[string]map[string]*responses.FareDetailsBySegment{}
	for _, fareDetail := range fareDetails {
		if mapFareDetails[fareDetail.ItineraryID] == nil {
			mapFareDetails[fareDetail.ItineraryID] = make(map[string]*responses.FareDetailsBySegment)
		}
		mapFareDetails[fareDetail.ItineraryID][fareDetail.SegmentID] = fareDetail
	}
	itinerySegment, err := toDomainItinerariesSegment(info.Segments, mapFareDetails)
	if err != nil {
		return nil, errors.Wrap(err, "toDomainItinerariesSegment")
	}

	if itinerySegment == nil {
		return nil, nil
	}

	duration := int(0)
	if info.Duration != "" {
		duration, err = evHelpers.ParseDuration(info.Duration)
		if err != nil {
			return nil, errors.Wrap(err, "helpers.ParseDuration(info.Duration)")
		}
	}

	stopNum := len(itinerySegment) - 1 // REVIEW: can review lai logic stopNum nay
	firstFlight := itinerySegment[0]
	lastFlight := itinerySegment[len(itinerySegment)-1]

	result := &domain.FlightItinerary{
		Index:            index,
		FareBasis:        firstFlight.FareBasis,
		CabinClass:       firstFlight.CabinClassCode,
		BookingClass:     firstFlight.BookingClass,
		Availability:     evConstants.DefaultAvailability, // HACK: res ev international ko co availability
		DepartPlace:      firstFlight.DepartPlace,
		DepartDate:       firstFlight.DepartDate,
		ArrivalPlace:     lastFlight.ArrivalPlace,
		ArrivalDate:      lastFlight.ArrivalDate,
		CarrierMarketing: firstFlight.CarrierMarketing,
		FlightNumber:     firstFlight.FlightNumber,
		FlightDuration:   duration,
		StopNumber:       stopNum,
		Currency:         evConstants.DefaultCurrency,
		Segments:         itinerySegment,
	}
	return result, nil
}

func toDomainItinerariesSegment(info []*responses.Segments, mapFareDetail map[string]map[string]*responses.FareDetailsBySegment) ([]*domain.ItinerarySegment, error) {
	if len(info) == 0 || len(mapFareDetail) == 0 {
		return nil, fmt.Errorf("toDomainItinerariesSegment info or fareDetail nil")
	}
	res := make([]*domain.ItinerarySegment, len(info))
	for index, segment := range info {
		tempSegment, err := toDomainItinerarySegment(segment, mapFareDetail)
		if err != nil {
			return nil, errors.Wrap(err, "toDomainItinerarySegment")
		}
		if tempSegment == nil {
			return nil, nil
		}

		res[index] = tempSegment
	}

	return res, nil
}

func toDomainItinerarySegment(info *responses.Segments, mapFareDetail map[string]map[string]*responses.FareDetailsBySegment) (*domain.ItinerarySegment, error) {
	if info == nil {
		return nil, nil
	}

	aircraft := constants.GetAirCraftName(info.Aircraft.Code)
	if info.Aircraft.AircraftModel == "" && aircraft == "" { // HACK: Skip the itinerary if it is not a flight (for example, train, high speed train, etc.)
		return nil, nil
	}

	index, err := strconv.Atoi(info.ID)
	if err != nil {
		return nil, err
	}

	duration, err := evHelpers.ParseDuration(info.Duration)
	if err != nil {
		return nil, errors.Wrap(err, "helpers.ParseDuration(info.Duration)")
	}

	departDate, err := evHelpers.ParseTime(info.Departure.At)
	if err != nil {
		return nil, errors.Wrap(err, "helpers.ParseTime(info.Departure.At)")
	}

	arrivalDate, err := evHelpers.ParseTime(info.Arrival.At)
	if err != nil {
		return nil, errors.Wrap(err, "helpers.ParseTime(info.Arrival.At)")
	}

	fareDetail := mapFareDetail[info.ItineraryID][info.ID]
	if fareDetail == nil {
		return nil, fmt.Errorf("fareDetail nil")
	}

	return &domain.ItinerarySegment{
		Index:            index,
		DepartPlace:      info.Departure.IataCode,
		DepartDate:       departDate,
		DepartDt:         helpers.ToUTCDateTime(departDate),
		DepartTerminal:   info.Departure.Terminal,
		ArrivalPlace:     info.Arrival.IataCode,
		ArrivalDate:      arrivalDate,
		ArrivalDt:        helpers.ToUTCDateTime(arrivalDate),
		ArrivalTerminal:  info.Arrival.Terminal,
		CarrierMarketing: info.CarrierCode,
		CarrierOperator:  info.Operating.CarrierCode,
		FlightNumber:     info.Number,
		Aircraft:         aircraft,
		BookingClass:     fareDetail.PropertyClass,
		CabinClassCode:   fareDetail.Cabin,
		FareBasis:        fareDetail.FareBasis,
		FlightDuration:   duration,
	}, nil
}

func toDomainItineraryPaxFare(info *responses.TravelerPricings) *domain.ItineraryPaxFare {
	if info == nil || info.Price == nil {
		return nil
	}

	taxAmount := info.Price.Total - info.Price.Base + info.Price.Commission

	return &domain.ItineraryPaxFare{
		PaxType:    evConstants.ToDomainTravelerType[info.TravelerType],
		FareAmount: taxAmount + info.Price.Base,
		FareBasic:  info.Price.Base,
		TaxAmount:  taxAmount,
		Currency:   evConstants.DefaultCurrency,
	}
}

func ToDomainItinerariesPaxFare(info []*responses.TravelerPricings) []*domain.ItineraryPaxFare {
	if len(info) == 0 {
		return nil
	}

	result := make([]*domain.ItineraryPaxFare, 0)
	seen := map[evEnum.TravelerType]bool{}
	for _, item := range info {
		if !seen[item.TravelerType] {
			result = append(result, toDomainItineraryPaxFare(item))
			seen[item.TravelerType] = true
		}
	}

	return result
}

func toDomainFreeBaggageInfo(info []*responses.TravelerPricings, itineraryIndex int) []*domain.BaggageInfo {
	if len(info) == 0 {
		return nil
	}
	strItineraryID := strconv.Itoa(itineraryIndex)
	result := make([]*domain.BaggageInfo, 0)
	seen := map[evEnum.TravelerType]bool{}
	for _, travelPricing := range info {
		if len(travelPricing.FareDetailsBySegment) == 0 || seen[travelPricing.TravelerType] {
			continue
		}

		for _, fareDetailsBySegment := range travelPricing.FareDetailsBySegment {
			if fareDetailsBySegment.ItineraryID == strItineraryID {
				if fareDetailsBySegment.IncludedCheckedBags != nil {
					tempBaggageInfo := &domain.BaggageInfo{
						Price:         0,
						Currency:      evConstants.DefaultCurrency,
						IsHandBaggage: false,
						Quantity:      0,
						PaxType:       evConstants.ToDomainTravelerType[travelPricing.TravelerType],
					}

					if fareDetailsBySegment.IncludedCheckedBags.Weight > 0 {
						weightStr := strconv.Itoa(fareDetailsBySegment.IncludedCheckedBags.Weight)
						name := weightStr + " " + fareDetailsBySegment.IncludedCheckedBags.WeightUnit
						tempBaggageInfo.Name = name
						tempBaggageInfo.Quantity = 1
					}

					if fareDetailsBySegment.IncludedCheckedBags.Quantity > 0 {
						tempBaggageInfo.Quantity = fareDetailsBySegment.IncludedCheckedBags.Quantity
					}

					seen[travelPricing.TravelerType] = true

					if tempBaggageInfo.Quantity > 0 || tempBaggageInfo.Name != "" {
						result = append(result, tempBaggageInfo)
					}
				}

				if fareDetailsBySegment.CarryOnBags != nil {
					tempBaggageInfo := &domain.BaggageInfo{
						Price:         0,
						Currency:      evConstants.DefaultCurrency,
						IsHandBaggage: true,
						Quantity:      0,
						PaxType:       evConstants.ToDomainTravelerType[travelPricing.TravelerType],
					}

					if fareDetailsBySegment.CarryOnBags.Weight > 0 {
						weightStr := strconv.Itoa(fareDetailsBySegment.CarryOnBags.Weight)
						name := weightStr + " " + fareDetailsBySegment.CarryOnBags.WeightUnit
						tempBaggageInfo.Name = name
						tempBaggageInfo.Quantity = 1
					}

					if fareDetailsBySegment.CarryOnBags.Quantity > 0 {
						tempBaggageInfo.Quantity = fareDetailsBySegment.CarryOnBags.Quantity
					}

					seen[travelPricing.TravelerType] = true

					if tempBaggageInfo.Quantity > 0 || tempBaggageInfo.Name != "" {
						result = append(result, tempBaggageInfo)
					}
				}

				break
			}
		}
	}

	return result
}
