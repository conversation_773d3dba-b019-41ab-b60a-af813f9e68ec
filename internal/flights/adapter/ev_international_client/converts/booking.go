package converts

import (
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/constants"
	evConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/requests"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	pkgHelpers "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const (
	DefaultPhoneDeviceType = "MOBILE"
	DefaultDocumentType    = "PASSPORT"
)

func FromDomainBookingDetail(bookingDetail *domain.BookingDetails, mailReceiver string) (*requests.CreateBookingReq, error) {
	if bookingDetail == nil {
		return nil, fmt.Errorf("FromDomainBookingDetail bookingDetail nil")
	}
	travelers, err := FromDomainTravelers(bookingDetail.ListPax)
	if err != nil {
		return nil, errors.Wrap(err, "FromDomainTravelers")
	}

	contact, err := FromDomainContactInfo(bookingDetail.ContactInfo, mailReceiver)
	if err != nil {
		return nil, errors.Wrap(err, "FromDomainContactInfo")
	}

	result := &requests.CreateBookingReq{
		Travelers: travelers,
		Contacts:  []*requests.Contacts{contact},
	}

	return result, nil
}

func FromDomainContactInfo(info *domain.Contact, mailReceiver string) (*requests.Contacts, error) {
	if info == nil {
		return nil, fmt.Errorf("FromDomainContactInfo info nil")
	}

	return &requests.Contacts{
		AddresseeName: &requests.AddresseeName{
			FirstName: info.GivenName,
			LastName:  info.Surname,
		},
		Phones: []*requests.Phones{
			{
				DeviceType:         DefaultPhoneDeviceType,
				CountryCallingCode: info.PhoneCode,
				Number:             info.Phone,
			},
		},
		// Purpose:       "",
		EmailAddress: mailReceiver,
	}, nil
}

func FromDomainTravelers(info []*domain.PaxInfo) ([]*requests.Travelers, error) {
	if len(info) == 0 {
		return nil, nil
	}
	res := make([]*requests.Travelers, 0)
	for index, paxInfo := range info {
		tempTraveler, err := fromDomainPaxInfo(paxInfo, index+1)
		if err != nil {
			return nil, errors.Wrap(err, "fromDomainPaxInfo")
		}
		res = append(res, tempTraveler)
	}

	return res, nil
}

func fromDomainPaxInfo(info *domain.PaxInfo, index int) (*requests.Travelers, error) {
	if info == nil {
		return nil, nil
	}
	res := &requests.Travelers{
		ID:          index,
		DateOfBirth: helpers.ParseTimeDateOnly(info.DOB),
		Name: &requests.Name{
			Title:     string(constants.TitleNameFromDomain[helpers.GetKeyMapTitleName(info.Type, info.Gender)]),
			FirstName: info.GivenName,
			LastName:  info.Surname,
		},
		TravelerType: constants.FromDomainTravelerType[info.Type],
		Contact: &requests.Contact{
			Phones: []*requests.Phones{
				{
					DeviceType:         DefaultPhoneDeviceType,
					CountryCallingCode: info.PhoneCode,
					Number:             info.Phone,
				},
			},
		},
		Gender: constants.FromDomainGender[info.Gender],
	}
	if info.Passport != nil {
		res.Documents = []*requests.Documents{
			{
				DocumentType:    DefaultDocumentType,
				Number:          info.Passport.Number,
				ExpiryDate:      helpers.ParseTimeDateOnly(&info.Passport.ExpiryDate),
				IssuanceCountry: info.Passport.IssuingCountry,
				Nationality:     info.Nationality,
				ValidityCountry: info.Nationality,
				Holder:          true,
			},
		}
	}

	return res, nil
}

func GetItineraryFromRawResponse(info *responses.CommandListResponse, bookingDetail *domain.BookingDetails) (*responses.Data, error) {
	if info == nil || info.Data == nil || bookingDetail == nil {
		return nil, fmt.Errorf("GetItineraryFromRawResponse info nil")
	}
	foundItinerary := false
	for _, data := range info.Data {
		// itinerariesPaxFare := toDomainItinerariesPaxFare(data.TravelerPricings)

		for index, itinerary := range data.Itineraries {
			tempItinerary, err := toDomainFlightItinerary(itinerary, data.TravelerPricings[0].FareDetailsBySegment, index+1)
			if err != nil {
				return nil, errors.Wrap(err, "toDomainFlightItinerary")
			}
			// tempItinerary.PaxFares = itinerariesPaxFare

			isEqual, err := helpers.IsEqualItinerary(bookingDetail.Itineraries[index], tempItinerary)
			if err != nil {
				return nil, errors.Wrap(err, "helpers.IsEqualItinerary")
			}

			if isEqual {
				foundItinerary = true
				continue
			}

			foundItinerary = false
			break
		}

		if foundItinerary {
			totalTaxAmount := data.Price.Original - data.Price.Base + data.Price.SystemFee
			if (totalTaxAmount + data.Price.Base) > bookingDetail.FareDataCf.BaseTotalFareAmount {
				return nil, domain.ErrTicketFareChanged // HACK: do gia thay doi lan 2 luc booking nen tra ve ErrItinerarySoldOut
			}

			return data, nil
		}
	}

	return nil, domain.ErrItinerarySoldOut
}

func syncBooking(evItineraries []*domain.FlightItinerary, booking *domain.BookingSession) (bool, error) {
	if len(evItineraries) == 0 || booking == nil {
		return false, errors.WithMessage(domain.ErrInvalidValue, "evInternational.syncBooking")
	}

	hasChanges := false
	for _, evItinerary := range evItineraries {
		for _, iti := range booking.Itineraries {
			itiChange := false
			if iti.ArrivalPlace == evItinerary.ArrivalPlace && iti.DepartPlace == evItinerary.DepartPlace {
				for _, seg := range iti.Segments {
					for _, pSeg := range evItinerary.Segments {
						if seg.ArrivalPlace == pSeg.ArrivalPlace && seg.DepartPlace == pSeg.DepartPlace {
							if pSeg.DepartDate != seg.DepartDate || pSeg.ArrivalDate != seg.ArrivalDate {
								hasChanges = true
								itiChange = true

								seg.DepartDate = pSeg.DepartDate
								seg.ArrivalDate = pSeg.ArrivalDate
								seg.DepartDt = pkgHelpers.ToUTCDateTime(seg.DepartDate)
								seg.ArrivalDt = pkgHelpers.ToUTCDateTime(seg.ArrivalDate)
							}
						}
					}
				}
				// Post processing
				if itiChange {
					var err error

					firstSeg := iti.Segments[0]
					lastSeg := iti.Segments[len(iti.Segments)-1]

					iti.DepartDate = firstSeg.DepartDate
					iti.ArrivalDate = lastSeg.ArrivalDate

					iti.DepartDt, err = pkgHelpers.ParseFakeUTCToRealTimeWithTz(iti.DepartDate, "", iti.DepartDt.Location())
					if err != nil {
						return false, err
					}

					iti.ArrivalDt, err = pkgHelpers.ParseFakeUTCToRealTimeWithTz(iti.ArrivalDate, "", iti.ArrivalDt.Location())
					if err != nil {
						return false, err
					}

					iti.FlightDuration = int(iti.ArrivalDt.Sub(iti.DepartDt).Minutes())
				}
			}
		}
	}

	return hasChanges, nil
}

func CheckFareChange(info *responses.CommandListResponse, booking *domain.BookingSession) (bool, *domain.TotalFareInfo, error) {
	if info == nil || info.Booking == nil || info.Booking.FlightOffer == nil || booking == nil {
		return false, nil, errors.WithMessage(domain.ErrInvalidValue, "CheckFareChange")
	}

	listIties := []*domain.FlightItinerary{}
	data := info.Booking.FlightOffer
	for index, itinerary := range info.Booking.FlightOffer.Itineraries {
		var err error
		tempItinerary, err := toDomainFlightItinerary(itinerary, data.TravelerPricings[0].FareDetailsBySegment, index+1)
		if err != nil {
			log.Error("toDomainFlightItinerary", log.Any("err", err))
			return false, nil, err
		}

		isEqual, err := helpers.IsEqualItinerarySegment(booking.Itineraries[index], tempItinerary)
		if err != nil {
			log.Error("helpers.IsEqualItinerary", log.Any("err", err))
			return false, nil, err
		}

		if isEqual {
			listIties = append(listIties, tempItinerary)
		}
	}

	if len(listIties) > 0 {
		totalTaxAmount := data.Price.Original - data.Price.Base + data.Price.SystemFee

		itinerariesPaxFare := ToDomainItinerariesPaxFare(data.TravelerPricings)

		fareData := &domain.TotalFareInfo{
			TotalPaxFares:       itinerariesPaxFare,
			BaseTotalFareAmount: totalTaxAmount + data.Price.Base,
			TotalFareBasic:      data.Price.Base,
			TotalTaxAmount:      totalTaxAmount,
			Currency:            evConstants.DefaultCurrency,
		}

		hasChanges, err := syncBooking(listIties, booking)
		if err != nil {
			return false, fareData, err
		}
		if totalTaxAmount+data.Price.Base > booking.FareDataCf.TotalFareAmount {
			return hasChanges, fareData, domain.ErrTicketFareChanged
		}

		return hasChanges, fareData, nil
	}

	return false, nil, domain.ErrItinerarySoldOut
}
