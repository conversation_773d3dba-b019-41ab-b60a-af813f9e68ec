package constants

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	evEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

var ToDomainTravelerType = map[evEnum.TravelerType]enum.PaxType{
	evEnum.TravelerTypeAdult:        enum.PaxTypeAdult,
	evEnum.TravelerTypeChild:        enum.PaxTypeChildren,
	evEnum.TravelerTypeSeatedInfant: enum.PaxTypeInfant,
}

var FromDomainTravelerType = map[enum.PaxType]evEnum.TravelerType{
	enum.PaxTypeAdult:    evEnum.TravelerTypeAdult,
	enum.PaxTypeChildren: evEnum.TravelerTypeChild,
	enum.PaxTypeInfant:   evEnum.TravelerTypeSeatedInfant,
}

var FromDomainGender = map[commonEnum.GenderType]string{
	commonEnum.GenderTypeMale:   "MALE",
	commonEnum.GenderTypeFeMale: "FEMALE",
}

var TitleNameFromDomain = map[string]evEnum.TitleName{
	helpers.GetKeyMapTitleName(enum.PaxTypeAdult, commonEnum.GenderTypeMale):    evEnum.TitleNameMr,
	helpers.GetKeyMapTitleName(enum.PaxTypeChildren, commonEnum.GenderTypeMale): evEnum.TitleNameMstr,
	helpers.GetKeyMapTitleName(enum.PaxTypeInfant, commonEnum.GenderTypeMale):   evEnum.TitleNameMstr,

	helpers.GetKeyMapTitleName(enum.PaxTypeAdult, commonEnum.GenderTypeFeMale):    evEnum.TitleNameMs,
	helpers.GetKeyMapTitleName(enum.PaxTypeChildren, commonEnum.GenderTypeFeMale): evEnum.TitleNameMiss,
	helpers.GetKeyMapTitleName(enum.PaxTypeInfant, commonEnum.GenderTypeFeMale):   evEnum.TitleNameMiss,
}
