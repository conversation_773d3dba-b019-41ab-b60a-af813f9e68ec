package ev_international_client

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

type EVInternationalAdapter interface {
	SearchFlight(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error)
	Booking(ctx context.Context, bookingDetails *domain.BookingDetails, searchRequest *domain.SearchFlightsRequest, tracingID string) (*domain.SvcCreateBookingResponse, error)
	ConfirmFare(ctx context.Context, booking *domain.BookingSession) (*domain.TotalFareInfo, error)
	VerifyBookingFlightData(ctx context.Context, booking *domain.BookingSession) (hasChang<PERSON> bool, fareData *domain.TotalFareInfo, err error)
}

type evInternationalAdapter struct {
	evInternationalClient EVInternationalClient
	Cfg                   *config.Schema
}

func NewEVInternationalAdapter(
	evInternationalClient EVInternationalClient,
	Cfg *config.Schema,
) EVInternationalAdapter {
	return &evInternationalAdapter{evInternationalClient, Cfg}
}

func (ev *evInternationalAdapter) Booking(ctx context.Context, bookingDetails *domain.BookingDetails, searchRequest *domain.SearchFlightsRequest, tracingID string) (*domain.SvcCreateBookingResponse, error) {
	if bookingDetails == nil || searchRequest == nil {
		return nil, fmt.Errorf("booking params nil")
	}

	res, err := ev.search(ctx, searchRequest, tracingID)
	if err != nil {
		return nil, err
	}
	req, err := converts.FromDomainBookingDetail(bookingDetails, ev.Cfg.EvMailReceiver)
	if err != nil {
		return nil, err
	}
	flightOffers, err := converts.GetItineraryFromRawResponse(res, bookingDetails)
	if err != nil {
		return nil, errors.Wrap(err, "converts.GetItineraryFromRawResponse")
	}
	req.FlightOffers = append(req.FlightOffers, flightOffers)

	response, err := ev.evInternationalClient.CreateBooking(ctx, req, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "CreateBooking")
	}

	lastTk, err := ev.getLastTk(response)
	if err != nil {
		return nil, errors.Wrap(err, "getLastTk")
	}

	return &domain.SvcCreateBookingResponse{
		LastTicketingDate: lastTk,
		BookingRef:        response.CreateBookingRes.Booking.PnrNumber,
	}, nil
}

func (ev *evInternationalAdapter) ConfirmFare(ctx context.Context, booking *domain.BookingSession) (*domain.TotalFareInfo, error) {
	if booking == nil {
		return nil, fmt.Errorf("ConfirmFare params nil")
	}

	res, err := ev.search(ctx, booking.SearchRequest, booking.SessionID)
	if err != nil {
		return nil, err
	}

	totalFareCf, err := converts.GetItineraryFromBookingSession(res, booking)
	if err != nil {
		return nil, errors.Wrap(err, "converts.GetItineraryFromRawResponse")
	}

	return totalFareCf, nil
}

func (ev *evInternationalAdapter) SearchFlight(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error) {
	res, err := ev.search(ctx, req, tracingID)
	if err != nil {
		return nil, err
	}

	result, err := converts.ToDomainSearchFlightResponse(res)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainSearchFlightResponse")
	}
	return result, nil
}

func (ev *evInternationalAdapter) search(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) (*responses.CommandListResponse, error) {
	evSearchFlightReq, err := converts.FromDomainSearchRequest(req)
	if err != nil {
		return nil, errors.Wrap(err, "converts.FromDomainSearchRequest(req)")
	}

	res, err := ev.evInternationalClient.SearchFlight(ctx, evSearchFlightReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "ev.evInternationalClient.SearchFlight")
	}

	return res, nil
}

func (ev *evInternationalAdapter) getLastTk(response *responses.CommandListResponse) (int64, error) {
	if response == nil || response.CreateBookingRes == nil || response.CreateBookingRes.Booking == nil {
		return 0, fmt.Errorf("CreateBookingRes nil")
	}

	var lastTk int64
	if response.CreateBookingRes.Booking.AirlineLastTkt != (time.Time{}) {
		lastTk = response.CreateBookingRes.Booking.AirlineLastTkt.UnixMilli()
	}
	if response.CreateBookingRes.Booking.BookerLastTkt != (time.Time{}) {
		temp := response.CreateBookingRes.Booking.AirlineLastTkt.UnixMilli()
		if temp > 0 && temp < lastTk {
			lastTk = temp
		}
	}

	return lastTk, nil
}

func (ev *evInternationalAdapter) VerifyBookingFlightData(ctx context.Context, booking *domain.BookingSession) (hasChanges bool, fareData *domain.TotalFareInfo, err error) {
	if booking == nil {
		return false, nil, domain.ErrInvalidValue
	}

	response, err := ev.evInternationalClient.GetBooking(ctx, booking.BookingRef, booking.BookingRef)
	if err != nil {
		return false, nil, errors.Wrap(err, "evInternationalClient.GetBooking error")
	}

	hasChanges, fareData, err = converts.CheckFareChange(response, booking)
	if err != nil {
		return false, fareData, errors.Wrap(err, "converts.CheckFareChange")
	}

	return hasChanges, fareData, nil
}
