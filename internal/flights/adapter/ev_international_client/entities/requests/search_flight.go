package requests

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/enum"

type SearchFlightReq struct {
	OriginDestinations []*OriginDestinations `json:"originDestinations,omitempty"`
	Travelers          []*Travelers          `json:"travelers,omitempty"`
	SearchCriteria     *SearchCriteria       `json:"searchCriteria,omitempty"`
	SeatClass          enum.SeatClass        `json:"seatClass,omitempty"`
	FlightOption       enum.FlightOption     `json:"flightOption,omitempty"`
	TicketType         string                `json:"ticketType,omitempty"`
	CorporateCode      string                `json:"corporateCode,omitempty"`
	SearchURL          string                `json:"searchUrl,omitempty"`
}

type DepartureDateTimeRange struct {
	Date string `json:"date,omitempty"`
}

type OriginDestinations struct {
	ID                      int                     `json:"id,omitempty"`
	OriginLocationCode      string                  `json:"originLocationCode,omitempty"`
	DestinationLocationCode string                  `json:"destinationLocationCode,omitempty"`
	DepartureDateTimeRange  *DepartureDateTimeRange `json:"departureDateTimeRange,omitempty"`
}

type CabinRestrictions struct {
	Cabin enum.CabinType `json:"cabin,omitempty"`
}

type CarrierRestrictions struct {
	IncludedCarrierCodes []string `json:"includedCarrierCodes,omitempty"`
}

type FlightFilters struct {
	CabinRestrictions   []*CabinRestrictions `json:"cabinRestrictions,omitempty"`
	CarrierRestrictions *CarrierRestrictions `json:"carrierRestrictions,omitempty"`
}

type SearchCriteria struct {
	MaxFlightOffers int            `json:"maxFlightOffers,omitempty"`
	FlightFilters   *FlightFilters `json:"flightFilters,omitempty"`
}
