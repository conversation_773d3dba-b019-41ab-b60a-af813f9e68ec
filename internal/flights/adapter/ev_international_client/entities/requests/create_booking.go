package requests

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/enum"
)

type CreateBookingReq struct {
	Travelers    []*Travelers      `json:"travelers,omitempty"`
	FlightOffers []*responses.Data `json:"flightOffers,omitempty"`
	Contacts     []*Contacts       `json:"contacts,omitempty"`
}
type Name struct {
	Title     string `json:"title,omitempty"`
	FirstName string `json:"firstName,omitempty"`
	LastName  string `json:"lastName,omitempty"`
}
type Phones struct {
	DeviceType         string `json:"deviceType,omitempty"`
	CountryCallingCode string `json:"countryCallingCode,omitempty"`
	Number             string `json:"number,omitempty"`
}
type Contact struct {
	Phones []*Phones `json:"phones,omitempty"`
}

type Documents struct {
	DocumentType    string `json:"documentType,omitempty"`
	Number          string `json:"number,omitempty"`
	ExpiryDate      string `json:"expiryDate,omitempty"`
	IssuanceCountry string `json:"issuanceCountry,omitempty"`
	Nationality     string `json:"nationality,omitempty"`
	ValidityCountry string `json:"validityCountry,omitempty"`
	Holder          bool   `json:"holder,omitempty"`
}

type Travelers struct {
	ID            int               `json:"id,omitempty"`
	DateOfBirth   string            `json:"dateOfBirth,omitempty"`
	Name          *Name             `json:"name,omitempty"`
	TravelerType  enum.TravelerType `json:"travelerType,omitempty"`
	TravelerCode  string            `json:"travelerCode,omitempty"`
	Contact       *Contact          `json:"contact,omitempty"`
	Documents     []*Documents      `json:"documents,omitempty"`
	Gender        string            `json:"gender,omitempty"`
	CorporateCode string            `json:"corporateCode,omitempty"`
}
type Departure struct {
	IataCode string `json:"iataCode,omitempty"`
	Terminal string `json:"terminal,omitempty"`
	At       string `json:"at,omitempty"`
}
type Arrival struct {
	IataCode string `json:"iataCode,omitempty"`
	Terminal string `json:"terminal,omitempty"`
	At       string `json:"at,omitempty"`
}
type Aircraft struct {
	Code          string `json:"code,omitempty"`
	AircraftModel string `json:"aircraftModel,omitempty"`
}
type Operating struct {
	CarrierCode string `json:"carrierCode,omitempty"`
}
type Segments struct {
	Departure   *Departure `json:"departure,omitempty"`
	Arrival     *Arrival   `json:"arrival,omitempty"`
	CarrierCode string     `json:"carrierCode,omitempty"`
	Number      string     `json:"number,omitempty"`
	Aircraft    *Aircraft  `json:"aircraft,omitempty"`
	Operating   *Operating `json:"operating,omitempty"`
	Duration    string     `json:"duration,omitempty"`
	ID          string     `json:"id,omitempty"`
	ItineraryID string     `json:"itineraryId,omitempty"`
}
type Itineraries struct {
	Duration string      `json:"duration,omitempty"`
	Segments []*Segments `json:"segments,omitempty"`
}

type Price struct {
	Total         float64 `json:"total,omitempty"`
	Base          int     `json:"base,omitempty"`
	Commission    float64 `json:"commission,omitempty"`
	SystemFee     float64 `json:"systemFee,omitempty"`
	Original      int     `json:"original,omitempty"`
	CampaignLabel string  `json:"campaignLabel,omitempty"`
}

type PricingOptions struct {
	IncludedCheckedBagsOnly bool `json:"includedCheckedBagsOnly,omitempty"`
	RefundableFare          bool `json:"refundableFare,omitempty"`
}

type IncludedCheckedBags struct {
	Quantity int `json:"quantity,omitempty"`
}
type CarryOnBags struct {
	Quantity int `json:"quantity,omitempty"`
}
type FareDetailsBySegment struct {
	ItineraryID               string               `json:"itineraryId,omitempty"`
	SegmentID                 string               `json:"segmentId,omitempty"`
	Cabin                     string               `json:"cabin,omitempty"`
	FareBasis                 string               `json:"fareBasis,omitempty"`
	PropertyClass             string               `json:"propertyClass,omitempty"`
	IncludedCheckedBags       *IncludedCheckedBags `json:"includedCheckedBags,omitempty"`
	CarryOnBags               *CarryOnBags         `json:"carryOnBags,omitempty"`
	BreakPoint                string               `json:"breakPoint,omitempty"`
	PrivateFare               bool                 `json:"privateFare,omitempty"`
	IsPromoteForSpecialPTC    bool                 `json:"isPromoteForSpecialPTC,omitempty"`
	IsNegoFareWithCorporateID bool                 `json:"isNegoFareWithCorporateId,omitempty"`
}
type TravelerPricings struct {
	TravelerID           string                  `json:"travelerId,omitempty"`
	TravelerType         string                  `json:"travelerType,omitempty"`
	Price                *Price                  `json:"price,omitempty"`
	FareDetailsBySegment []*FareDetailsBySegment `json:"fareDetailsBySegment,omitempty"`
}

//	type FlightOffers struct {
//		CarrierEntries                *CarrierEntries     `json:"carrierEntries,omitempty"`
//		ID                            string              `json:"id,omitempty"`
//		Itineraries                   []*Itineraries      `json:"itineraries,omitempty"`
//		Price                         *Price              `json:"price,omitempty"`
//		PricingOptions                *PricingOptions     `json:"pricingOptions,omitempty"`
//		ValidatingAirlineCodes        []string            `json:"validatingAirlineCodes,omitempty"`
//		SelectedValidatingAirlineCode string              `json:"selectedValidatingAirlineCode,omitempty"`
//		TravelerPricings              []*TravelerPricings `json:"travelerPricings,omitempty"`
//		FlightOption                  string              `json:"flightOption,omitempty"`
//		SeatClass                     string              `json:"seatClass,omitempty"`
//		IsAppliedCodeBySystem         bool                `json:"isAppliedCodeBySystem,omitempty"`
//		IsPrivateFare                 bool                `json:"isPrivateFare,omitempty"`
//		IsNDC                         bool                `json:"isNDC,omitempty"`
//		IsPromoteForSpecialPTC        bool                `json:"isPromoteForSpecialPTC,omitempty"`
//		IsNegoFareWithCorporateID     bool                `json:"isNegoFareWithCorporateId,omitempty"`
//	}
type AddresseeName struct {
	FirstName string `json:"firstName,omitempty"`
	LastName  string `json:"lastName,omitempty"`
}
type Contacts struct {
	AddresseeName *AddresseeName `json:"addresseeName,omitempty"`
	Phones        []*Phones      `json:"phones,omitempty"`
	Purpose       string         `json:"purpose,omitempty"`
	EmailAddress  string         `json:"emailAddress,omitempty"`
}
