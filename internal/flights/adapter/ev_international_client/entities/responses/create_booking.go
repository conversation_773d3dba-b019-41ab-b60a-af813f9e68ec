package responses

import (
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/enum"
)

type CreateBookingRes struct {
	Booking *Booking `json:"booking"`
}

type FlightOffer struct {
	ID                            string              `json:"id"`
	Itineraries                   []*Itineraries      `json:"itineraries"`
	Price                         *Price              `json:"price"`
	PricingOptions                *PricingOptions     `json:"pricingOptions"`
	ValidatingAirlineCodes        []string            `json:"validatingAirlineCodes"`
	SelectedValidatingAirlineCode string              `json:"selectedValidatingAirlineCode"`
	TravelerPricings              []*TravelerPricings `json:"travelerPricings"`
	FlightOption                  enum.FlightOption   `json:"flightOption"`
	SeatClass                     enum.SeatClass      `json:"seatClass"`
	IsAppliedCodeBySystem         bool                `json:"isAppliedCodeBySystem"`
	IsPrivateFare                 bool                `json:"isPrivateFare"`
	IsNDC                         bool                `json:"isNDC"`
	IsPromoteForSpecialPTC        bool                `json:"isPromoteForSpecialPTC"`
	IsNegoFareWithCorporateID     bool                `json:"isNegoFareWithCorporateId"`
}
type Name struct {
	Title     string `json:"title"`
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
}
type Documents struct {
	Number          string `json:"number"`
	ExpiryDate      string `json:"expiryDate"`
	IssuanceCountry string `json:"issuanceCountry"`
	Nationality     string `json:"nationality"`
	DocumentType    string `json:"documentType"`
	ValidityCountry string `json:"validityCountry"`
	Holder          bool   `json:"holder"`
}
type Phones struct {
	CountryCallingCode string `json:"countryCallingCode"`
	Number             string `json:"number"`
}
type Contact struct {
	Phones []*Phones `json:"phones"`
}
type Travelers struct {
	ID           string            `json:"id"`
	DateOfBirth  string            `json:"dateOfBirth"`
	TravelerType enum.TravelerType `json:"travelerType"`
	Gender       string            `json:"gender"`
	Name         *Name             `json:"name"`
	Documents    []*Documents      `json:"documents"`
	Contact      *Contact          `json:"contact"`
}
type Booking struct {
	ID                       int          `json:"id"`
	PnrNumber                string       `json:"pnrNumber"`
	BookingDate              time.Time    `json:"bookingDate"`
	TicketingDate            time.Time    `json:"ticketingDate"`
	BookerLastTkt            time.Time    `json:"bookerLastTkt"`
	Status                   string       `json:"status"`
	BookerName               string       `json:"bookerName"`
	Airline                  string       `json:"airline"`
	TotalAmount              float64      `json:"totalAmount"`
	Commission               float64      `json:"commission"`
	FlightOffer              *FlightOffer `json:"flightOffer"`
	Travelers                []*Travelers `json:"travelers"`
	Notes                    string       `json:"notes"`
	SpecialPassengerTypeCode string       `json:"specialPassengerTypeCode"`
	FolderID                 string       `json:"folderId"`
	AirlineLastTkt           time.Time    `json:"airlineLastTkt"`
	FareLastTkt              time.Time    `json:"fareLastTkt"`
	LastDepartureDate        time.Time    `json:"lastDepartureDate"`
	OrderID                  string       `json:"orderId"`
	ReshopInformation        string       `json:"reshopInformation"`
	TicketerName             string       `json:"ticketerName"`
}
