package responses

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/enum"

type CommandListResponse struct {
	*SearchFlightRes
	*CreateBookingRes
	*LoginRes
	*Booking
}

type Departure struct {
	IataCode string `json:"iataCode"`
	Terminal string `json:"terminal"`
	At       string `json:"at"`
}
type Arrival struct {
	IataCode string `json:"iataCode"`
	Terminal string `json:"terminal"`
	At       string `json:"at"`
}
type Aircraft struct {
	Code          string `json:"code"`
	AircraftModel string `json:"aircraftModel"`
}
type Operating struct {
	CarrierCode string `json:"carrierCode"`
}
type Segments struct {
	ID                    string     `json:"id"`
	ItineraryID           string     `json:"itineraryId"`
	Departure             *Departure `json:"departure"`
	Arrival               *Arrival   `json:"arrival"`
	CarrierCode           string     `json:"carrierCode"`
	Number                string     `json:"number"`
	Aircraft              *Aircraft  `json:"aircraft"`
	Operating             *Operating `json:"operating"`
	Duration              string     `json:"duration"`
	AirlineBookingNumber  string     `json:"airlineBookingNumber,omitempty"`
	AirlineBookingCompany string     `json:"airlineBookingCompany,omitempty"`
}
type Itineraries struct {
	Duration string      `json:"duration"`
	Segments []*Segments `json:"segments"`
}
type Price struct {
	Total         float64 `json:"total"`
	Base          float64 `json:"base"`
	Commission    float64 `json:"commission"`
	SystemFee     float64 `json:"systemFee"`
	Original      float64 `json:"original"`
	CampaignFlag  bool    `json:"campaignFlag,omitempty"`
	CampaignLabel string  `json:"campaignLabel,omitempty"`
}
type PricingOptions struct {
	IncludedCheckedBagsOnly bool `json:"includedCheckedBagsOnly"`
	RefundableFare          bool `json:"refundableFare"`
}

type IncludedCheckedBags struct {
	Weight     int    `json:"weight"`
	WeightUnit string `json:"weightUnit"`
	Quantity   int64  `json:"quantity"`
}
type CarryOnBags struct {
	Weight     int    `json:"weight"`
	WeightUnit string `json:"weightUnit"`
	Quantity   int64  `json:"quantity"`
}
type FareDetailsBySegment struct {
	ItineraryID               string               `json:"itineraryId"`
	SegmentID                 string               `json:"segmentId"`
	Cabin                     string               `json:"cabin"`
	FareBasis                 string               `json:"fareBasis"`
	PropertyClass             string               `json:"propertyClass"`
	IncludedCheckedBags       *IncludedCheckedBags `json:"includedCheckedBags"`
	CarryOnBags               *CarryOnBags         `json:"carryOnBags"`
	BreakPoint                string               `json:"breakPoint"`
	PrivateFare               bool                 `json:"privateFare"`
	IsPromoteForSpecialPTC    bool                 `json:"isPromoteForSpecialPTC"`
	IsNegoFareWithCorporateID bool                 `json:"isNegoFareWithCorporateId"`
}
type TravelerPricings struct {
	TravelerID           string                  `json:"travelerId"`
	TravelerType         enum.TravelerType       `json:"travelerType"`
	Price                *Price                  `json:"price"`
	FareDetailsBySegment []*FareDetailsBySegment `json:"fareDetailsBySegment"`
}
