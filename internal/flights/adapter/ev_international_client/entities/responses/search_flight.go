package responses

type SearchFlightRes struct {
	Data         []*Data       `json:"data"`
	Dictionaries *Dictionaries `json:"dictionaries"`
}

type Data struct {
	CarrierEntries                map[string]string   `json:"carrierEntries"`
	ID                            string              `json:"id"`
	Itineraries                   []*Itineraries      `json:"itineraries"`
	Price                         *Price              `json:"price"`
	PricingOptions                *PricingOptions     `json:"pricingOptions"`
	ValidatingAirlineCodes        []string            `json:"validatingAirlineCodes"`
	SelectedValidatingAirlineCode string              `json:"selectedValidatingAirlineCode"`
	TravelerPricings              []*TravelerPricings `json:"travelerPricings"`
	Provider                      string              `json:"provider"`
	FlightOption                  string              `json:"flightOption"`
	SeatClass                     string              `json:"seatClass"`
	IsAppliedCodeBySystem         bool                `json:"isAppliedCodeBySystem"`
	IsPrivateFare                 bool                `json:"isPrivateFare"`
	IsNDC                         bool                `json:"isNDC"`
	IsPromoteForSpecialPTC        bool                `json:"isPromoteForSpecialPTC"`
	IsNegoFareWithCorporateID     bool                `json:"isNegoFareWithCorporateId"`
}

type Location struct {
	CityCode    string `json:"cityCode"`
	CountryCode string `json:"countryCode"`
}

type Dictionaries struct {
	Locations map[string]*Location `json:"locations"`
	Carriers  map[string]string    `json:"carriers"`
}
