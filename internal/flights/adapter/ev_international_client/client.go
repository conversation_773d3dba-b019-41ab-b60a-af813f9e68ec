package ev_international_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"

	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/requests"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_international_client/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	redisRepository "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	hubEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	contextbinding "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/context_binding"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const (
	successfulCode        = 200
	apiPath               = "api-v2"
	methodPost            = "POST"
	methodGet             = "GET"
	defaultRequestTimeout = 60

	radius              = 1000
	searchFlightPath    = "/search"
	createBookingPath   = "/order"
	getBookingPath      = "/booking-info"
	getBookingParames   = "?pnrNumber="
	actionLogin         = "login"
	actionSearchFlight  = "search_flight"
	actionCreateBooking = "create_booking"
	actionGetBooking    = "get_booking"
)

type EVInternationalClient interface {
	SearchFlight(ctx context.Context, req *requests.SearchFlightReq, tracingID string) (*responses.CommandListResponse, error)
	CreateBooking(ctx context.Context, req *requests.CreateBookingReq, tracingID string) (*responses.CommandListResponse, error)
	GetBooking(ctx context.Context, pnrRef, tracingID string) (*responses.CommandListResponse, error)
}

type evInternationalClient struct {
	cfg         *config.Schema
	requestRepo repositories.RequestRepository
	redisRepo   redisRepository.TokenRepository
}

func NewEVInternationalClient(cfg *config.Schema, requestRepo repositories.RequestRepository, redisRepo redisRepository.TokenRepository) EVInternationalClient {
	return &evInternationalClient{
		cfg:         cfg,
		requestRepo: requestRepo,
		redisRepo:   redisRepo,
	}
}

func (c *evInternationalClient) doRequest(
	ctx context.Context,
	method,
	path string,
	action string,
	body interface{},
	header map[string]string,
	tracingID string,
) (*responses.CommandListResponse, error) {
	var err error

	response, statusCode, duration, err := c.do(ctx, path, method, action, body, header)

	headerClone := map[string]string{}
	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     method,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     action,
			IsJson:     true,
			Provider:   hubEnum.FlightProviderEVInternational,
			TracingID:  tracingID,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("EVInternationalClient requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", action),
				log.Any("req", body),
			)
		}
	}(headerClone)
	if err != nil {
		return nil, errors.Wrap(err, "c.do")
	}

	var result *responses.CommandListResponse
	if err := json.Unmarshal(response, &result); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	return result, nil
}

func (c *evInternationalClient) do(
	ctx context.Context,
	fullPath string,
	method string,
	action string,
	body interface{},
	header map[string]string,
) ([]byte, int, int64, error) {
	var duration int64
	beginAt := time.Now()
	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("EVInternationalClient Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))
		return nil, 0, duration, err
	}

	var response *http.Response

	if action == actionLogin {
		loginReq := &requests.LoginReq{}
		err := json.Unmarshal(jsonData, loginReq)
		if err != nil {
			log.Error("ev UnMarshal error",
				log.Any("error", err),
				log.String("relativePath", fullPath),
				log.String("action", action),
				log.Any("req", body))
			return nil, 0, duration, err
		}

		formData := url.Values{}
		formData.Set("username", loginReq.Username)
		formData.Set("password", loginReq.Password)
		formData.Set("grant_type", loginReq.GrantType)
		formData.Set("client_id", loginReq.ClientID)
		formData.Set("client_secret", loginReq.ClientSecret)

		response, err = tracingHttp.RawRequest(ctx, fullPath, method, strings.NewReader(formData.Encode()), header)
		if err != nil {
			return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
		}
	} else {
		payload := bytes.NewBuffer(jsonData)

		response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
		if err != nil {
			return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
		}
	}
	duration = time.Since(beginAt).Milliseconds()

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != successfulCode {
		err := c.getError(resBody)
		if err == nil {

			err = errors.New(response.Status)
		}

		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *evInternationalClient) getError(dataRes []byte) error {
	var result *responses.ErrorRes
	if err := json.Unmarshal(dataRes, &result); err != nil {
		return errors.Wrap(err, "Unmarshal getError")
	}

	if result.ErrorKey == "cannotbook" {
		if result.Title == "Cannot book this flight because price have been changed" {
			return domain.ErrItinerarySoldOut
		}

		if result.Title == "Cannot book this flight" {
			return domain.ErrItinerarySoldOut
		}
	}

	return nil
}

func (c *evInternationalClient) getCtxDCP(ctx context.Context) *domain.DCPInternationalEV {
	dcps, isOK := ctx.Value(contextbinding.ContextDCPsKey{}).(*domain.PartnerDCPs)
	if isOK {
		for _, dcp := range dcps.DCPsInternationalEV {
			if helpers.CanUseDCP(dcp.Level, false) {
				return dcp
			}
		}
	}

	return &domain.DCPInternationalEV{
		BaseURL:      c.cfg.EVInternationalBaseUrl,
		LoginURL:     c.cfg.EVInternationalSecureBaseUrl,
		Username:     c.cfg.EVInternationalUsername,
		Password:     c.cfg.EVInternationalPassword,
		ClientSecret: c.cfg.EVInternationalClientSecret,
		ClientID:     c.cfg.EVInternationalClientID,
		GrantType:    c.cfg.EVInternationalGrantType,
		Level:        enum.DCPLevelBookingAndTicketing,
	}
}

func (c *evInternationalClient) Login(ctx context.Context, tracingID string, dcp *domain.DCPInternationalEV) (*responses.CommandListResponse, error) {

	path := dcp.LoginURL
	req := requests.LoginReq{
		GrantType:    dcp.GrantType,
		ClientID:     dcp.ClientID,
		Username:     dcp.Username,
		Password:     dcp.Password,
		ClientSecret: dcp.ClientSecret,
	}

	headerLogin := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	res, err := c.doRequest(ctx, methodPost, path, actionLogin, req, headerLogin, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	return res, nil
}

func (c *evInternationalClient) GetToken(ctx context.Context, tracingID string) (string, error) {
	dcp := c.getCtxDCP(ctx)
	var token string
	token, err := c.redisRepo.GetToken(ctx, hubEnum.FlightProviderEVInternational, dcp.Username)
	if token != "" && err == nil {
		return token, nil
	}

	if err == nil && token == "" {
		locked, err := c.redisRepo.LockGetToken(ctx, hubEnum.FlightProviderEVInternational, dcp.Username)
		if err != nil {
			return "", errors.Wrap(err, "redisRepo.LockGetTokenWithRetry")
		}

		defer func(locked bool) {
			if locked {
				c.redisRepo.ReleaseGetTokeLock(ctx, hubEnum.FlightProviderEVInternational, dcp.Username)
			}
		}(locked)

		if !locked {
			err = c.redisRepo.WaitForLockToRelease(hubEnum.FlightProviderEVInternational, dcp.Username, 250*time.Millisecond, 20)
			if err != nil {
				return "", errors.Wrap(err, "WaitForLockToRelease")
			}

			token, err := c.redisRepo.GetToken(ctx, hubEnum.FlightProviderEVInternational, dcp.Username)
			if err != nil {
				return "", fmt.Errorf("getToken after WaitForLockToRelease error")
			}

			return token, nil
		}
		res, err := c.Login(ctx, tracingID, dcp)
		if err != nil {
			return "", errors.Wrap(err, "c.Login")
		}

		ttlDuration := time.Second * time.Duration(math.Round(0.9*float64(res.ExpiresIn)))
		if err := c.redisRepo.SetToken(ctx, hubEnum.FlightProviderEVInternational, dcp.Username, res.AccessToken, ttlDuration); err != nil {
			return "", errors.Wrap(err, "redis.CMD().Set.Err")
		}

		return res.AccessToken, nil
	} else if err != nil {
		return "", errors.Wrap(err, "redis.CMD().Get)")
	}

	return token, nil
}

func (c *evInternationalClient) SearchFlight(ctx context.Context, req *requests.SearchFlightReq, tracingID string) (*responses.CommandListResponse, error) {
	token, err := c.GetToken(ctx, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "c.GetToken")
	}
	path := c.cfg.EVInternationalBaseUrl + searchFlightPath
	req.SearchURL = path
	response, err := c.doRequest(ctx, methodPost, path, actionSearchFlight, req, c.getHeader(token), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *evInternationalClient) CreateBooking(ctx context.Context, req *requests.CreateBookingReq, tracingID string) (*responses.CommandListResponse, error) {
	token, err := c.GetToken(ctx, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "getToken")
	}
	path := c.cfg.EVInternationalBaseUrl + createBookingPath

	response, err := c.doRequest(ctx, methodPost, path, actionCreateBooking, req, c.getHeader(token), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *evInternationalClient) GetBooking(ctx context.Context, pnrRef, tracingID string) (*responses.CommandListResponse, error) {
	token, err := c.GetToken(ctx, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "getToken")
	}
	path := c.cfg.EVInternationalBaseUrl + getBookingPath + getBookingParames + pnrRef

	response, err := c.doRequest(ctx, methodGet, path, actionGetBooking, nil, c.getHeader(token), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *evInternationalClient) getHeader(token string) map[string]string {

	return map[string]string{
		"Accept-Language": "vi",
		"Content-Type":    "application/json",
		"Accept":          "application/json, text/plain, */*",
		"Authorization":   "Bearer " + token,
	}
}
