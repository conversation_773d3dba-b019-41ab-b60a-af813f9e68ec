package amadeus_client

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"

	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/air_retrieve_seat_map"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/air_sell_from_recommendation"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/create_form_of_payment"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/create_tsm_from_pricing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/docIssuance_issue_miscellaneous_documents"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/fare_check_rules"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/fare_informative_best_pricing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/fare_price_pnr_with_booking_class"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/issue_ticket"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/pnr_repply"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/queue_place_pnr"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/search_flight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/security_sign_out"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/service_integrated_pricing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/service_standalone_catalogue"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/ticket_create_tst_from_pricing"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/request"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	contextbinding "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/context_binding"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

// const (
// 	INVALID_INPUT   = "INVALID_INPUT"
// 	SOMETHING_ERROR = "SOMETHING_ERROR"
// )

type AmadeusClient interface {
	SearchFlight(ctx context.Context, req *request.SearchFlight, tracingID string) (*search_flight.FareMasterPricerTravelBoardSearchReply, error)
	FareInformationBestPricing(ctx context.Context, req *request.FareInformationBestPricing, tracingID string) (*fare_informative_best_pricing.FareInformativeBestPricingWithoutPNRReply, *models.Session, error)
	AirSellFromRecommendation(ctx context.Context, req *request.AirSellFromRecommendation, tracingID string) (*air_sell_from_recommendation.AirSellFromRecommendationReply, *models.Session, error)
	PNRAddMultiElements(ctx context.Context, req *request.PnrAddMultiElement, tracingID string) (*pnr_repply.PNRReply, *models.Session, error)
	FareCheckRules(ctx context.Context, req *request.FareCheckRules, tracingID string) (*fare_check_rules.FareCheckRulesReply, *models.Session, error)
	FarePricePnrWithBookingClass(ctx context.Context, req *request.FarePricePnrWithBookingClass, tracingID string) (*fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply, *models.Session, error)
	FarePricePnrWithBookingClassFBA(ctx context.Context, req *request.FarePricePnrWithBookingClassFBA, tracingID string) (*fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply, *models.Session, error)
	TicketCreateTSTFromPricing(ctx context.Context, req *request.TicketCreateTSTFromPricing, tracingID string) (*ticket_create_tst_from_pricing.TicketCreateTSTFromPricingReply, *models.Session, error)
	RetrievePNR(ctx context.Context, req *request.RetrivePnr, tracingID string) (*pnr_repply.PNRReply, *models.Session, error)
	PNRCancel(ctx context.Context, req *request.PNRCancel, tracingID string) (*pnr_repply.PNRReply, *models.Session, error)
	IssueTicket(ctx context.Context, req *request.IssueTicket, tracingID string) (*issue_ticket.DocIssuanceIssueTicketReply, *models.Session, error)
	QueuePlacePNR(ctx context.Context, req *request.QueuePlacePNR, tracingID string) (*queue_place_pnr.QueuePlacePNRReply, error)
	SecuritySignOut(ctx context.Context, req *request.SignOutRQ, tracingID string) (*security_sign_out.SecuritySignOutReply, error)
	AirRetrieveSeatMap(ctx context.Context, req *request.AirRetrieveSeatMapRQ, tracingID string) (*air_retrieve_seat_map.AirRetrieveSeatMapReply, error)
	CreateTSMFromPricing(ctx context.Context, req *request.CreateTSMFromPricingRQ, tracingID string) (*create_tsm_from_pricing.TicketCreateTSMFromPricingReply, error)
	ServiceIntegratedPricing(ctx context.Context, req *request.ServiceIntegratedPricingRQ, tracingID string) (*service_integrated_pricing.ServiceIntegratedPricingReply, error)
	DocIssuanceIssueMiscellaneousDocuments(ctx context.Context, req *request.DocIssuanceIssueMiscellaneousDocumentsRQ, tracingID string) (*docIssuance_issue_miscellaneous_documents.DocIssuanceIssueMiscellaneousDocumentsReply, error)
	CreateFOP(ctx context.Context, req *request.CreateFormOfPaymentRQ, tracingID string) (*create_form_of_payment.FOPCreateFormOfPaymentReply, *models.Session, error)
	ServiceStandaloneCatalogue(ctx context.Context, req *request.ServiceStandaloneCatalogue, tracingID string) (*service_standalone_catalogue.ServiceStandaloneCatalogueReply, error)
	GetAmadeusISOCountry() (map[string]*domain.AmadeusISOCountry, error)
}
type amadeusClient struct {
	AmadeusBaseURL           string
	AmadeusExclusionAirlines []string
	RequestRepo              repositories.RequestRepository
	AmadeusISOCountryRepo    repositories.AmadeusISOCountryRepository
	RedisRepo                commonRedis.IRedis
	internalSecretToken      string
}

func NewAmadeusClient(cfg *config.Schema, requestRepo repositories.RequestRepository, amadeusISOCountryRepo repositories.AmadeusISOCountryRepository, redisRepo commonRedis.IRedis) AmadeusClient {
	return &amadeusClient{
		AmadeusBaseURL:           cfg.AmadeusBaseURL,
		AmadeusExclusionAirlines: cfg.AmadeusExclusionAirlines,
		RequestRepo:              requestRepo,
		AmadeusISOCountryRepo:    amadeusISOCountryRepo,
		RedisRepo:                redisRepo,
		internalSecretToken:      cfg.InternalSecretToken,
	}
}

func (a *amadeusClient) callRequest(ctx context.Context,
	action string,
	_ string,
	body interface{},
	rHeaders map[string]string,
	tracingID string,
) ([]byte, error) {
	startTime := time.Now()
	var resBody []byte
	fullPath := a.AmadeusBaseURL + action

	rHeaders["token"] = a.internalSecretToken

	response, responseErr := tracingHttp.JSONRequest(ctx, fullPath, "POST", body, rHeaders)

	duration := time.Since(startTime)

	if responseErr == nil && response != nil && response.Body != nil {
		var err error

		defer response.Body.Close()
		resBody, err = io.ReadAll(response.Body)
		if err != nil {
			return nil, errors.Wrap(err, "io.ReadAll")
		}
	}

	headerClone := map[string]string{}

	for key, val := range rHeaders {
		headerClone[key] = val
	}

	go func(headerClone map[string]string, res *http.Response) {
		stCode := 0
		path := a.AmadeusBaseURL + action
		bCtx, cancel := context.WithTimeout(context.Background(), timeoutContext)

		defer cancel()

		requestID := uuid.NewString()

		if a.RequestRepo == nil {
			return
		}

		if res != nil {
			stCode = res.StatusCode
		}

		doErr := responseErr

		if err := a.RequestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Provider:   enum.FlightProviderAmadeus,
			Path:       path,
			Method:     Method_POST,
			Body:       body,
			Headers:    headerClone,
			Response:   resBody,
			StatusCode: stCode,
			Duration:   duration.Milliseconds(),
			IsJson:     true,
			Action:     action,
			TracingID:  tracingID,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("amadeusClient requestRepo error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", action),
				log.Any("req", body),
			)
		}
	}(headerClone, response)

	if responseErr != nil {
		return nil, errors.Wrap(responseErr, "tracingHttp.JSONRequest")
	}

	return resBody, nil
}

func (a *amadeusClient) fromDomainDCP(dcp *domain.DCPAmadeus) *request.DCP {
	if dcp == nil {
		return nil
	}

	return &request.DCP{
		OfficeID: dcp.PseudoCityCode,
		UserID:   dcp.UserName,
		Password: base64.StdEncoding.EncodeToString([]byte(dcp.Password)),
		BaseURL:  dcp.BaseURL,
	}
}

func (a *amadeusClient) getCtxDCP(ctx context.Context, issue bool) *request.DCP {
	dcps, isOK := ctx.Value(contextbinding.ContextDCPsKey{}).(*domain.PartnerDCPs)
	if isOK {
		for _, dcp := range dcps.DCPsAmadeus {
			if helpers.CanUseDCP(dcp.Level, issue) {
				return a.fromDomainDCP(dcp)
			}
		}
	}

	return &request.DCP{}
}

func (a *amadeusClient) getHeader() map[string]string {
	return map[string]string{
		"Content-Type": "text/xml; charset=utf-8",
	}
}

func (a *amadeusClient) AirRetrieveSeatMap(ctx context.Context, req *request.AirRetrieveSeatMapRQ, tracingID string) (*air_retrieve_seat_map.AirRetrieveSeatMapReply, error) {
	res, err := a.callRequest(ctx, RelativePath_AirRetrieveSeatMap, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply air_retrieve_seat_map.AirRetrieveSeatMapReply

	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) ServiceIntegratedPricing(ctx context.Context, req *request.ServiceIntegratedPricingRQ, tracingID string) (*service_integrated_pricing.ServiceIntegratedPricingReply, error) {
	res, err := a.callRequest(ctx, RelativePath_ServiceIntegratedPricing, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply service_integrated_pricing.ServiceIntegratedPricingReply

	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) DocIssuanceIssueMiscellaneousDocuments(ctx context.Context, req *request.DocIssuanceIssueMiscellaneousDocumentsRQ, tracingID string) (*docIssuance_issue_miscellaneous_documents.DocIssuanceIssueMiscellaneousDocumentsReply, error) {
	res, err := a.callRequest(ctx, RelativePath_DocIssuanceIssueMiscellaneousDocuments, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply docIssuance_issue_miscellaneous_documents.DocIssuanceIssueMiscellaneousDocumentsReply

	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) CreateTSMFromPricing(ctx context.Context, req *request.CreateTSMFromPricingRQ, tracingID string) (*create_tsm_from_pricing.TicketCreateTSMFromPricingReply, error) {
	res, err := a.callRequest(ctx, RelativePath_CreateTSMFromPricing, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply create_tsm_from_pricing.TicketCreateTSMFromPricingReply

	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) SearchFlight(ctx context.Context, req *request.SearchFlight, tracingID string) (*search_flight.FareMasterPricerTravelBoardSearchReply, error) {
	exclusionAirlines := append(req.ExclusionAirlines, a.AmadeusExclusionAirlines...)
	if !validateExclusionAirlines(exclusionAirlines) {
		return nil, fmt.Errorf("SearchFlight: false validateExclusionAirline")
	}

	req.DCP = a.getCtxDCP(ctx, false)
	req.ExclusionAirlines = exclusionAirlines
	res, err := a.callRequest(ctx, RelativePath_SearchFlight, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply search_flight.FareMasterPricerTravelBoardSearchReply

	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) FareInformationBestPricing(ctx context.Context, req *request.FareInformationBestPricing, tracingID string) (*fare_informative_best_pricing.FareInformativeBestPricingWithoutPNRReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_FareInfomationBestPricing, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply fare_informative_best_pricing.FareInformativeBestPricingWithoutPNRReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) AirSellFromRecommendation(ctx context.Context, req *request.AirSellFromRecommendation, tracingID string) (*air_sell_from_recommendation.AirSellFromRecommendationReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_AirSellFromRecommendation, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply air_sell_from_recommendation.AirSellFromRecommendationReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) PNRAddMultiElements(ctx context.Context, req *request.PnrAddMultiElement, tracingID string) (*pnr_repply.PNRReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_PNRAddMultiElements, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}
	var reply pnr_repply.PNRReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) FareCheckRules(ctx context.Context, req *request.FareCheckRules, tracingID string) (*fare_check_rules.FareCheckRulesReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_FareCheckRules, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply fare_check_rules.FareCheckRulesReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) FarePricePnrWithBookingClass(ctx context.Context, req *request.FarePricePnrWithBookingClass, tracingID string) (*fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_FarePricePnrWithBookingClass, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) FarePricePnrWithBookingClassFBA(ctx context.Context, req *request.FarePricePnrWithBookingClassFBA, tracingID string) (*fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_FarePricePnrWithBookingClassFBA, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) TicketCreateTSTFromPricing(ctx context.Context, req *request.TicketCreateTSTFromPricing, tracingID string) (*ticket_create_tst_from_pricing.TicketCreateTSTFromPricingReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_TicketCreateTSTFromPricing, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, err
	}

	var reply ticket_create_tst_from_pricing.TicketCreateTSTFromPricingReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) RetrievePNR(ctx context.Context, req *request.RetrivePnr, tracingID string) (*pnr_repply.PNRReply, *models.Session, error) {
	if req.RecordLocator != nil {
		req.Session = nil
	}

	res, err := a.callRequest(ctx, RelativePath_RetrievePNR, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply pnr_repply.PNRReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) PNRCancel(ctx context.Context, req *request.PNRCancel, tracingID string) (*pnr_repply.PNRReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_PNRCancel, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply pnr_repply.PNRReply

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) IssueTicket(ctx context.Context, req *request.IssueTicket, tracingID string) (*issue_ticket.DocIssuanceIssueTicketReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_IssueTicket, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply issue_ticket.DocIssuanceIssueTicketReply
	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) QueuePlacePNR(ctx context.Context, req *request.QueuePlacePNR, tracingID string) (*queue_place_pnr.QueuePlacePNRReply, error) {
	res, err := a.callRequest(ctx, RelativePath_QueuePlacePNR, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply queue_place_pnr.QueuePlacePNRReply
	_, err = a.unmarshalResponseToModel(res, &reply)

	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) SecuritySignOut(ctx context.Context, req *request.SignOutRQ, tracingID string) (*security_sign_out.SecuritySignOutReply, error) {
	res, err := a.callRequest(ctx, RelativePath_SecuritySignOut, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply security_sign_out.SecuritySignOutReply
	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) ServiceStandaloneCatalogue(ctx context.Context, req *request.ServiceStandaloneCatalogue, tracingID string) (*service_standalone_catalogue.ServiceStandaloneCatalogueReply, error) {
	res, err := a.callRequest(ctx, RelativePath_ServiceStandaloneCatalogue, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "callRequest")
	}

	var reply service_standalone_catalogue.ServiceStandaloneCatalogueReply
	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, nil
}

func (a *amadeusClient) CreateFOP(ctx context.Context, req *request.CreateFormOfPaymentRQ, tracingID string) (*create_form_of_payment.FOPCreateFormOfPaymentReply, *models.Session, error) {
	res, err := a.callRequest(ctx, RelativePath_CreateFOP, Method_POST, req, a.getHeader(), tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "callRequest")
	}

	var reply create_form_of_payment.FOPCreateFormOfPaymentReply
	_, err = a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	session, err := a.unmarshalResponseToModel(res, &reply)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshalResponseToModel")
	}

	return &reply, session, nil
}

func (a *amadeusClient) GetAmadeusISOCountry() (map[string]*domain.AmadeusISOCountry, error) {
	key := "get_amadeus_iso_country"
	amadeusISOCountryCodeMap := map[string]*domain.AmadeusISOCountry{}
	ctx := context.Background()
	byteValue, err := a.RedisRepo.CMD().Get(ctx, key).Bytes()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			res, err := a.AmadeusISOCountryRepo.Find(ctx)
			if err != nil {
				return nil, errors.Wrap(err, "a.AmadeusISOCountryRepo.Find")
			}

			for _, isoCountry := range res {
				amadeusISOCountryCodeMap[isoCountry.Code] = isoCountry
			}

			byteData, err := json.Marshal(amadeusISOCountryCodeMap)
			if err != nil {
				log.Error("json.Marshal(res)", log.Any("error", err))
			}

			expired := time.Hour * 24
			if err := a.RedisRepo.CMD().Set(ctx, key, byteData, expired).Err(); err != nil {
				log.Error("a.RedisRepo.CMD().Set GetAmadeusISOCountry", log.Any("error", err))
			}

			return amadeusISOCountryCodeMap, nil
		}
		return nil, errors.Wrap(err, " a.RedisRepo.CMD().Get")
	}

	err = json.Unmarshal(byteValue, &amadeusISOCountryCodeMap)
	if err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal(byteValue, amadeusISOCountryCodeMap)")
	}

	if len(amadeusISOCountryCodeMap) == 0 {
		res, err := a.AmadeusISOCountryRepo.Find(ctx)
		if err != nil {
			return nil, errors.Wrap(err, "a.AmadeusISOCountryRepo.Find")
		}

		for _, isoCountry := range res {
			amadeusISOCountryCodeMap[isoCountry.Code] = isoCountry
		}

		byteData, err := json.Marshal(amadeusISOCountryCodeMap)
		if err != nil {
			log.Error("json.Marshal(res)", log.Any("error", err))
		}

		expired := time.Hour * 24
		if err := a.RedisRepo.CMD().Set(ctx, key, byteData, expired).Err(); err != nil {
			log.Error("a.RedisRepo.CMD().Set GetAmadeusISOCountry", log.Any("error", err))
		}

		return amadeusISOCountryCodeMap, nil
	}

	return amadeusISOCountryCodeMap, nil
}

func (a *amadeusClient) unmarshalResponseToModel(data []byte, result interface{}) (*models.Session, error) {
	var response models.AmadeusResponse

	err := json.Unmarshal([]byte(data), &response)
	if err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	if response.ErrorCode != nil && response.Message != nil {
		if *response.Message == ErrMessNoFareFound ||
			*response.Message == ErrTooManyRequestedSegments ||
			strings.Contains(*response.Message, ErrNoItinerary) {
			return nil, nil
		}

		if *response.Message == ErrReTicketTST {
			return nil, domain.ErrIssueTicketFailed
		}

		if *response.Message == ErrMessFlightNotConfirmed || *response.Message == ErrMessFareNotAvailable || *response.Message == ErrMsgNoValidFare {
			return nil, domain.ErrItinerarySoldOut
		}

		// SEAT MAP NOT AVAILABLE
		if *response.Message == ErrMsgSeatMapNotAvailable || *response.Message == ErrMsgCommunicationsNotAvailable ||
			*response.Message == ErrMsgSeatMapNotAvailableForThisCarrier ||
			*response.Message == ErrMsgSeatMapNotAvailableZone {
			return nil, domain.ErrSeatMapNotAvailable
		}

		if strings.Contains(*response.Message, ErrMsgNameTooLong) || strings.Contains(*response.Message, ErrMsgNameTooLong2) {
			return nil, domain.ErrFullNameTooLong
		}

		err := errors.New(*response.Message)

		return nil, errors.Wrap(err, "response contains error")
	}

	if response.Data != nil {
		err = xml.Unmarshal([]byte(response.Data.Data), &result)
		if err != nil {
			return nil, errors.Wrap(err, "xml.Unmarshal")
		}

		return response.Data.Session, nil
	}

	return nil, domain.ErrInvalidValue
}

func validateExclusionAirlines(slice []string) bool {
	for _, str := range slice {
		if str == "" || len(str) != 2 {
			return false
		}
	}

	return true
}
