package converts

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/search_flight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/request"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const ERR_INVALID_FORMAT = "ERR_INVALID_FORMAT"

var errInvalidFormat = errors.New(ERR_INVALID_FORMAT)

func ToDomainResponseFlights(in *search_flight.FareMasterPricerTravelBoardSearchReply, req *domain.SearchFlightsRequest) ([]*domain.ResponseFlight, error) {
	out := []*domain.ResponseFlight{}
	if in == nil {
		return nil, errInvalidFormat
	}

	recommendations := in.Recommendation
	flightIndexes := in.FlightIndex
	serviceFeeInfoGrp := in.ServiceFeesGrp
	miniRulesInfoGrp := in.MnrGrp
	index := 0

	for _, rec := range recommendations {
		segmentRef := rec.SegmentFlightRef

		if len(segmentRef) == 0 {
			return nil, errors.Wrap(errInvalidFormat, "len(segmentRef) == 0")
		}

		for _, segRef := range segmentRef {
			temp, err := toDomainResponseFlight(flightIndexes, rec, segRef, serviceFeeInfoGrp[0], miniRulesInfoGrp, req)
			if err != nil {
				return nil, err
			}

			if temp != nil {
				temp.Index = index + 1
				index++
				out = append(out, temp)
			}
		}
	}

	return out, nil
}

func getPaxType(s string) enum.PaxType {
	switch s {
	case "ADT":
		return enum.PaxTypeAdult
	case "CH":
		return enum.PaxTypeChildren
	case "INF":
		return enum.PaxTypeInfant
	case "CHD":
		return enum.PaxTypeChildren
	default:
		return enum.PaxTypeNone
	}
}

func toDomainResponseFlight(
	flightIndexes []*search_flight.FlightIndex,
	rec *search_flight.Recommendation,
	ref *search_flight.ReferenceInfoType,
	serviceFeesGrp *search_flight.ServiceFeesGrp,
	miniRulesGrp *search_flight.MnrGrp,
	req *domain.SearchFlightsRequest,
) (*domain.ResponseFlight, error) {
	outIties := []*domain.FlightItinerary{}
	var serviceRef int64
	// var miniRuleRef int64
	hasIti := false
	for i := 0; i < len(ref.ReferencingDetail); i++ {
		qual := ref.ReferencingDetail[i].RefQualifier
		rawGroupIndex := ref.ReferencingDetail[i].RefNumber
		flightIndex := i

		if qual == nil || rawGroupIndex == nil {
			return nil, errors.Wrap(errInvalidFormat, "qual == nil || rawIndex == nil || *qual != 'S'")
		}
		// ||
		groupIndex, err := strconv.ParseInt(*rawGroupIndex, 10, 0)
		if err != nil {
			return nil, errors.Wrap(errInvalidFormat, "ParseInt")
		}

		groupIndex--

		var outFlightIti *domain.FlightItinerary

		if *qual == "S" {
			hasIti = true
			if flightIndex > len(flightIndexes)-1 || int(groupIndex) > len(flightIndexes[flightIndex].GroupOfFlights)-1 {
				return nil, errors.Wrap(errInvalidFormat, "flightIndex > len(flightIndexes)-1 || groupIndex > len(flightIndexes[flightIndex].GroupOfFlights)-1")
			}

			flight := flightIndexes[flightIndex].GroupOfFlights[groupIndex]

			outFlightIti, err = toDomainFlightItinerary(flightIndex, flight, rec)
			if err != nil {
				return nil, err
			}

			outIties = append(outIties, outFlightIti)
		}

		if *qual == "B" {
			serviceRef = groupIndex
		}

		// mini rules ref
		// if *qual == "M" {
		// 	miniRuleRef = groupIndex
		// }
	}

	if !hasIti {
		return nil, nil
	}

	mapBaggageItems, err := toDomainBaggage(serviceFeesGrp, serviceRef)
	if err != nil {
		return nil, err
	}

	// mapMiniRule, err := toDomainMiniRule(miniRulesGrp, miniRuleRef)
	// if err != nil {
	// 	return nil, err
	// }

	for k, v := range outIties {
		itiBag := mapBaggageItems[k+1]

		if itiBag != nil {
			v.FreeBaggage = append(v.FreeBaggage, mapBaggageItems[k+1])

			if itiBag.PaxType == enum.PaxTypeAdult && req.Passengers.CHD > 0 {
				v.FreeBaggage = append(v.FreeBaggage, &domain.BaggageInfo{
					Name:          itiBag.Name,
					IsHandBaggage: itiBag.IsHandBaggage,
					Quantity:      itiBag.Quantity,
					PaxType:       enum.PaxTypeChildren,
				})
			}
		}
	}

	totalFareAmount, err := strconv.ParseFloat(rec.RecPriceInfo.MonetaryDetail[0].Amount, 64)
	if err != nil {
		return nil, errors.Wrap(err, "totalFareAmount.ParseFloat")
	}

	totalTax, err := strconv.ParseFloat(rec.RecPriceInfo.MonetaryDetail[1].Amount, 64)
	if err != nil {
		return nil, errors.Wrap(err, "totalTax.ParseFloat")
	}

	totalFareBasic := totalFareAmount - totalTax

	totalPaxFares := []*domain.ItineraryPaxFare{}

	for _, pax := range rec.PaxFareProduct {
		if pax == nil || len(pax.PaxReference) == 0 || len(pax.PaxReference[0].Ptc) == 0 {
			return nil, errors.Wrap(errInvalidFormat, "pax == nil || len(pax.PaxReference) == 0 || len(pax.PaxReference[0].Ptc) == 0")
		}

		paxFareAmount, err := strconv.ParseFloat(pax.PaxFareDetail.TotalFareAmount, 64)
		if err != nil {
			return nil, errors.Wrap(err, "paxFareAmount.ParseFloat")
		}

		paxTaxAmount, err := strconv.ParseFloat(pax.PaxFareDetail.TotalTaxAmount, 64)
		if err != nil {
			return nil, errors.Wrap(err, "paxTaxAmount.ParseFloat")
		}

		paxType := getPaxType(*pax.PaxReference[0].Ptc[0])

		paxFares := &domain.ItineraryPaxFare{
			PaxType:    paxType,
			FareAmount: paxFareAmount,
			FareBasic:  paxFareAmount - paxTaxAmount,
			TaxAmount:  paxTaxAmount,
			Currency:   "VND",
		}

		totalPaxFares = append(totalPaxFares, paxFares)
	}

	out := &domain.ResponseFlight{
		FlightID:    helpers.GenerateFlightID(enum.FlightProviderAmadeus),
		Provider:    enum.FlightProviderAmadeus,
		Itineraries: outIties,
		SearchTotalFareInfo: domain.SearchTotalFareInfo{
			BaseTotalFareAmount: totalFareAmount,
			TotalFareBasic:      totalFareBasic,
			TotalTaxAmount:      totalTax,
			Currency:            "VND",
			TotalPaxFares:       totalPaxFares,
		},
		OptionType: enum.FlightOptionTypeRecommend,
	}

	// if mapMiniRule != nil {
	// 	out.MiniRules = mapMiniRule
	// }

	return out, nil
}

// Returns: duration int | carrier marketing string
func getFlightInfo(in *search_flight.GroupOfFlights) (int, string, error) {
	duration := 0
	caMarketing := ""

	if in.PropFlightGrDetail != nil && len(in.PropFlightGrDetail.FlightProposal) != 0 {
		for _, item := range in.PropFlightGrDetail.FlightProposal {
			if item.UnitQualifier == nil || item.Ref == nil || *item.Ref == "" {
				continue
			}

			if *item.UnitQualifier == "EFT" {
				var (
					err        error
					durHtoMins int64
					durMins    int64
				)
				const (
					minsLen   = 2
					hourToMin = 60
				)

				rawDur := *item.Ref
				strHours := string(rawDur[:len(rawDur)-minsLen])
				strMins := rawDur[len(strHours):]

				if strHours != "" {
					durHours, err := strconv.ParseInt(strHours, 10, 0)
					if err != nil {
						return 0, "", errors.Wrap(err, "durHtoMins.ParseInt")
					}

					durHtoMins = durHours * hourToMin
				}

				durMins, err = strconv.ParseInt(strMins, 10, 0)
				if err != nil {
					return 0, "", errors.Wrap(err, "durMins.ParseInt")
				}

				duration = int(durHtoMins + durMins)
			}

			if *item.UnitQualifier == "MCX" {
				caMarketing = *item.Ref
			}
		}
	}

	return duration, caMarketing, nil
}

func toDomainItinerarySegment(idx int, in *search_flight.FlightDetails) (*domain.ItinerarySegment, error) {
	if in == nil {
		return nil, errInvalidFormat
	}

	const rawDateTimeFormat = "020106 1504"

	rawDepartDate := *in.FlightInformation.ProductDateTime.DateOfDeparture
	rawDepartTime := *in.FlightInformation.ProductDateTime.TimeOfDeparture

	departDate, err := time.Parse(rawDateTimeFormat, rawDepartDate+" "+rawDepartTime)
	if err != nil {
		return nil, errors.Wrap(errInvalidFormat, "departDate.time.Parse")
	}

	rawArrivalDate := *in.FlightInformation.ProductDateTime.DateOfArrival
	rawArrivalTime := *in.FlightInformation.ProductDateTime.TimeOfArrival

	arrivalDate, err := time.Parse(rawDateTimeFormat, rawArrivalDate+" "+rawArrivalTime)
	if err != nil {
		return nil, errors.Wrap(errInvalidFormat, "arrivalDate.time.Parse")
	}

	departPlace := *in.FlightInformation.Location[0].LocationId
	arrivalPlace := *in.FlightInformation.Location[1].LocationId
	departTerminal := getTerminal(in.FlightInformation.Location[0])
	arrivalTerminal := getTerminal(in.FlightInformation.Location[1])
	airCraft := getAirCraft(in.FlightInformation.ProductDetail)
	if in.FlightInformation.CompanyId.OperatingCarrier == nil {
		in.FlightInformation.CompanyId.OperatingCarrier = new(string)
	}

	return &domain.ItinerarySegment{
		Index:            idx + 1,
		DepartPlace:      departPlace,
		DepartDate:       departDate.UnixMilli(),
		ArrivalPlace:     arrivalPlace,
		ArrivalDate:      arrivalDate.UnixMilli(),
		DepartTerminal:   departTerminal,
		ArrivalTerminal:  arrivalTerminal,
		Aircraft:         airCraft,
		ArrivalDt:        helpers.ToUTCDateTime(arrivalDate.UnixMilli()),
		DepartDt:         helpers.ToUTCDateTime(departDate.UnixMilli()),
		CarrierMarketing: *in.FlightInformation.CompanyId.MarketingCarrier,
		CarrierOperator:  *in.FlightInformation.CompanyId.OperatingCarrier,
		FlightNumber:     *in.FlightInformation.FlightOrtrainNumber,
	}, nil
}

func getADTFareProduct(ins []*search_flight.PaxFareProduct) *search_flight.PaxFareProduct {
	for _, item := range ins {
		for _, paxRef := range item.PaxReference {
			for _, ptcItem := range paxRef.Ptc {
				if ptcItem != nil && *ptcItem == string(enum.PaxTypeAdult) {
					return item
				}
			}
		}
	}

	return nil
}

func getItiFareDetails(idx int, ins []*search_flight.FareDetails) *search_flight.FareDetails {
	segRef := idx + 1
	for _, item := range ins {
		if item.SegmentRef != nil && item.SegmentRef.SegRef != nil && *item.SegmentRef.SegRef == strconv.Itoa(segRef) {
			return item
		}
	}

	return nil
}

func toDomainFlightItinerary(idx int, in *search_flight.GroupOfFlights, rec *search_flight.Recommendation) (*domain.FlightItinerary, error) {
	if in == nil {
		return nil, errInvalidFormat
	}

	segments := []*domain.ItinerarySegment{}

	stopNum := len(in.FlightDetails) - 1

	fareProduct := getADTFareProduct(rec.PaxFareProduct)

	if fareProduct == nil {
		return nil, errors.New("fare product nil")
	}

	fareDetail := getItiFareDetails(idx, fareProduct.FareDetails)

	if fareDetail == nil {
		return nil, errors.New("fare detail nil")
	}

	flightDetails := in.FlightDetails
	fareBasises := []string{}
	minAvai := 1000000000

	for i, flight := range flightDetails {
		segment, err := toDomainItinerarySegment(i, flight)
		if err != nil {
			return nil, err
		}

		if segment != nil {
			if i < len(fareDetail.GroupOfFares) {
				segmentFare := fareDetail.GroupOfFares[i]
				if segmentFare != nil && segmentFare.ProductInformation != nil {
					segment.BookingClass = *segmentFare.ProductInformation.CabinProduct[0].Rbd
					segment.CabinClassCode = *segmentFare.ProductInformation.CabinProduct[0].Cabin
					segAvai := getAvailability(segmentFare.ProductInformation.CabinProduct[0])
					segment.FareBasis = *segmentFare.ProductInformation.FareProductDetail.FareBasis
					fareBasises = append(fareBasises, segment.FareBasis)

					if segAvai < minAvai {
						minAvai = segAvai
					}
				}
			}

			segments = append(segments, segment)
		}
	}

	if len(segments) == 0 || len(segments) != len(in.FlightDetails) {
		return nil, errors.New("empty segments or length mismath " + fmt.Sprintln(len(segments)))
	}

	fareBasises = lo.Uniq[string](fareBasises)
	fareType := enum.FareTypeThrough
	if len(fareBasises) != 1 {
		fareType = enum.FareTypeBreak
	}

	firstFlight := segments[0]
	lastFlight := segments[len(segments)-1]

	duration, caMarketing, err := getFlightInfo(in)
	if err != nil {
		return nil, errors.Wrap(err, "getDuration")
	}

	res := &domain.FlightItinerary{
		Index:            idx + 1,
		StopNumber:       stopNum,
		CabinClass:       constants.GetCabinName(firstFlight.CabinClassCode),
		CabinClassCode:   firstFlight.CabinClassCode,
		BookingClass:     firstFlight.BookingClass,
		FareBasis:        firstFlight.FareBasis,
		Availability:     minAvai,
		DepartPlace:      firstFlight.DepartPlace,
		DepartDate:       firstFlight.DepartDate,
		ArrivalPlace:     lastFlight.ArrivalPlace,
		ArrivalDate:      lastFlight.ArrivalDate,
		CarrierMarketing: caMarketing,
		FlightNumber:     firstFlight.FlightNumber,
		FlightDuration:   duration,
		Segments:         segments,
		FareType:         fareType,
	}

	return res, nil
}

func ToRequestFlights(ins []*domain.ItineraryRequest) []*request.Flight {
	out := make([]*request.Flight, 0, len(ins))

	for _, item := range ins {
		out = append(out, toRequestFlight(item))
	}

	return out
}

func toRequestFlight(in *domain.ItineraryRequest) *request.Flight {
	if in == nil {
		return nil
	}

	return &request.Flight{
		StartPoint: in.DepartPlace,
		EndPoint:   in.ArrivalPlace,
		DepartDate: in.DepartDate,
	}
}

func toDomainBaggage(serviceFees *search_flight.ServiceFeesGrp, serviceRef int64) (map[int]*domain.BaggageInfo, error) {
	mapBaggage := make(map[int]*domain.BaggageInfo)
	serviceCoverageInfoGrp := serviceFees.ServiceCoverageInfoGrp[serviceRef]
	for _, service := range serviceCoverageInfoGrp.ServiceCovInfoGrp {
		for _, v := range service.CoveragePerFlightsInfo {
			indexSegment, err := strconv.Atoi(*v.NumberOfItemsDetails.RefNum)
			if err != nil {
				return nil, errors.Wrap(errInvalidFormat, "ParseInt")
			}

			if *service.RefInfo.ReferencingDetail[0].RefQualifier != "F" {
				return nil, errors.Wrap(errInvalidFormat, "ervice.RefInfo.ReferencingDetail[0].RefQualifier != F")
			}

			baggageRef, err := strconv.Atoi(*service.RefInfo.ReferencingDetail[0].RefNumber)
			if err != nil {
				return nil, errors.Wrap(errInvalidFormat, "ParseInt")
			}
			baggageRef--

			if baggageName := getBaggageName(serviceFees.FreeBagAllowanceGrp[baggageRef].FreeBagAllownceInfo.BaggageDetails); baggageName != "" {
				mapBaggage[indexSegment] = &domain.BaggageInfo{
					PaxType:       enum.PaxTypeAdult,
					Name:          baggageName,
					IsHandBaggage: false,
					Quantity:      1,
				}
			}
		}
	}

	return mapBaggage, nil
}

// func toDomainMiniRule(miniRulesGrp *search_flight.MnrGrp, miniRuleRef int64) ([]*domain.MiniRule, error) {
// 	if miniRulesGrp == nil || len(miniRulesGrp.MiniRules) == 0 {
// 		return nil, nil
// 	}

// 	mapMiniRules := &domain.MnrDetail{
// 		MiniRules: []*domain.MiniRule{},
// 	}
// 	miniRulesCoverageInfoGrp := miniRulesGrp.MiniRules[miniRuleRef]

// 	for _, catDetail := range miniRulesCoverageInfoGrp.CatGrp {
// 		if catDetail != nil {
// 			monInfo := &domain.MonetaryInformationType{
// 				MonetaryDetails:      []*domain.MonetaryInformationDetailsType{},
// 				OtherMonetaryDetails: nil,
// 			}
// 			if catDetail.MonInfo != nil && catDetail.MonInfo.OtherMonetaryDetails != nil {
// 				monInfo.OtherMonetaryDetails = &domain.MonetaryInformationDetailsType{
// 					TypeQualifier: catDetail.MonInfo.OtherMonetaryDetails.TypeQualifier,
// 					Amount:        catDetail.MonInfo.OtherMonetaryDetails.Amount,
// 				}
// 			}
// 			if catDetail.MonInfo != nil && len(catDetail.MonInfo.MonetaryDetails) > 0 {
// 				for _, monDetail := range catDetail.MonInfo.MonetaryDetails {
// 					if monDetail != nil {
// 						monInfo.MonetaryDetails = append(monInfo.MonetaryDetails, &domain.MonetaryInformationDetailsType{
// 							TypeQualifier: monDetail.TypeQualifier,
// 							Amount:        monDetail.Amount,
// 						})
// 					}
// 				}
// 			}
// 			statusInfo := &domain.StatusType{
// 				StatusInformation: []*domain.StatusDetailsType{},
// 			}
// 			if catDetail.StatusInfo != nil && len(catDetail.StatusInfo.StatusInformation) > 0 {
// 				for _, statusDetail := range catDetail.StatusInfo.StatusInformation {
// 					if statusDetail != nil {
// 						statusInfo.StatusInformation = append(statusInfo.StatusInformation, &domain.StatusDetailsType{
// 							Indicator: statusDetail.Indicator,
// 							Action:    statusDetail.Action,
// 						})
// 					}
// 				}
// 			}
// 			mapMiniRules.MiniRules = append(mapMiniRules.MiniRules, &domain.MiniRule{
// 				CatInfo: &domain.CategDescrType{
// 					DescriptionInfo: &domain.CategoryDescriptionType{
// 						Number: catDetail.CatInfo.DescriptionInfo.Number,
// 					},
// 				},
// 				MonInfo:    monInfo,
// 				StatusInfo: statusInfo,
// 			})
// 		}
// 	}

// 	return mapMiniRules.MiniRules, nil
// }

func emptyAllowance(in *string) bool {
	if in == nil {
		return true
	}

	if strings.TrimSpace(*in) == "0" || strings.TrimSpace(*in) == "" {
		return true
	}

	return false
}

func getBaggageName(baggageDetailsType *search_flight.BaggageDetailsType) string {
	if baggageDetailsType.QuantityCode == nil || baggageDetailsType.FreeAllowance == nil ||
		*baggageDetailsType.QuantityCode == "W" && baggageDetailsType.UnitQualifier == nil ||
		emptyAllowance(baggageDetailsType.FreeAllowance) {
		return ""
	}

	switch *baggageDetailsType.QuantityCode {
	case "W":

		return fmt.Sprintf("%s %s", *baggageDetailsType.FreeAllowance, getBaggageUnit(*baggageDetailsType.UnitQualifier))
	case "N":
		return fmt.Sprintf("%s Pieces", *baggageDetailsType.FreeAllowance)
	default:
		return ""
	}
}

func getBaggageUnit(key string) string {
	switch key {
	case "K":
		return "Kg"
	case "L":
		return "Lbs"
	default:
		return ""
	}
}

func getTerminal(location *search_flight.LocationIdentificationDetailsType) string {
	if location == nil || location.Terminal == nil {
		return ""
	}

	return *location.Terminal
}

func getAirCraft(productDetail *search_flight.AdditionalProductDetailsType) string {
	if productDetail == nil || productDetail.EquipmentType == nil {
		return ""
	}

	return constants.GetAirCraftName(*productDetail.EquipmentType)
}

func getAvailability(cabinProduct *search_flight.CabinProductDetailsType306830C) int {
	defaultAvailability := 9
	if cabinProduct == nil || cabinProduct.AvlStatus == nil {
		return defaultAvailability
	}
	availability, err := strconv.Atoi(*cabinProduct.AvlStatus)
	if err != nil {
		return defaultAvailability
	}

	return availability
}
