package converts

import (
	"fmt"
	"time"

	"gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/air_retrieve_seat_map"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

const (
	Location_SeatWindow      = "W"
	Location_SeatAisle       = "A"
	Location_SeatWindowAisle = "WA"
	Location_Emergency       = "E"
	Type_Buffer_Seat_Zone    = "Z"
)

var (
	Location_NoSeatHere   = []string{"701", "8", "AR", "BA", "CL", "D", "EX", "GN", "KN", "LA", "LN", "SO", "ST", "TA"}
	Facility_SeatBassinet = "B"
	UnAvailableSeat       = []string{"B", "V", "IE", "H", "B", "1", "1C", "BK", "C", "D", "DE", "EC", "LG", "V", "X", "Z"}
)

func GetSeatInfo(characteristics []string) (*domain.SeatProperty, bool) {
	property := &domain.SeatProperty{}
	noSeat := false
	for _, characteristic := range characteristics {
		switch characteristic {
		case Location_SeatAisle:
			property.Aisle = true
		case Location_SeatWindow:
			property.Window = true
		case Location_SeatWindowAisle:
			property.Aisle = true
			property.Window = true
		case Facility_SeatBassinet:
			property.Bassinet = true
		case Location_Emergency:
			property.EmergencyExit = true
		}

		if isContain := helpers.Contains[string](Location_NoSeatHere, characteristic); isContain {
			noSeat = true
		}
	}

	return property, noSeat
}

func IsAisle(characteristics []string) bool {
	for _, characteristic := range characteristics {
		if characteristic == Location_SeatAisle || characteristic == Location_SeatWindowAisle {
			return true
		}
	}
	return false
}

func ToDomainSegmentSeat(res *air_retrieve_seat_map.AirRetrieveSeatMapReply, segment *domain.ItinerarySegment) (*domain.SeatSegment, error) {
	if res == nil || len(res.SeatmapInformation) == 0 {
		return nil, fmt.Errorf("ToDomainSegmentSeat: res == nil || len(res.SeatmapInformation) == 0")
	}

	seatSegment := &domain.SeatSegment{
		Key:          segment.GenerateSegmentID(),
		ItineraryID:  segment.ItineraryID,
		SegmentIndex: segment.Index,
		SeatOptions:  []*domain.SeatOption{},
		ExpiredAt:    time.Now().Add(constants.SeatMapExpireTime).UnixMilli(),
	}
	aisleLocation := []int{}

	seatInfo := res.SeatmapInformation[0]
	freeSeat := false
	if len(seatInfo.CustomerCentricData) == 0 {
		freeSeat = true
	}
	seatPriceMap := map[string]*domain.SeatCharge{}

	if !freeSeat {
		seatPrice := seatInfo.CustomerCentricData[0].SeatPrice
		for _, priceInfo := range seatPrice {
			for _, rowDetail := range priceInfo.RowDetails {
				for _, column := range rowDetail.SeatOccupationDetails {
					seatName := fmt.Sprintf("%d%s", rowDetail.SeatRowNumber, column.SeatColumn)
					seatCharge := &domain.SeatCharge{
						BaseAmount:  0,
						TaxAmount:   0,
						TotalAmount: 0,
						Currency:    "",
					}

					for _, priceDetail := range priceInfo.SeatPrice.MonetaryDetails {
						switch priceDetail.TypeQualifier {
						case "T":
							seatCharge.TotalAmount = priceDetail.Amount
							seatCharge.Currency = priceDetail.Currency
						case "TX":
							seatCharge.TaxAmount = priceDetail.Amount
						case "B":
							seatCharge.BaseAmount = priceDetail.Amount
						}
					}

					seatPriceMap[seatName] = seatCharge
				}
			}
		}
	}

	for _, cabin := range seatInfo.Cabin {
		columns := []*domain.ColumnHeader{}
		if cabin.CompartmentDetails == nil {
			continue
		}

		cabinColumns := cabin.CompartmentDetails.ColumnDetails
		for idx, col := range cabinColumns {
			columns = append(columns, &domain.ColumnHeader{Title: col.SeatColumn})
			if IsAisle(col.Description) && len(cabinColumns) > idx+1 {
				if IsAisle(cabinColumns[idx+1].Description) {
					seatCode := fmt.Sprintf("Aisle_%d", len(aisleLocation))
					columns = append(columns, &domain.ColumnHeader{Title: seatCode, IsAisle: true})
				}
			}
		}

		seatOption := &domain.SeatOption{
			CabinClass: segment.CabinClassCode,
			Rows:       []*domain.SeatRow{},
			Columns:    columns,
		}

		rowRange := cabin.CompartmentDetails.SeatRowRange.Number
		for _, row := range seatInfo.Row {
			rowDetail := row.RowDetails
			if rowDetail.RowCharacteristicDetails != nil && rowDetail.RowCharacteristicDetails.RowCharacteristic == Type_Buffer_Seat_Zone {
				continue
			}

			if len(rowRange) > 1 {
				if int(rowDetail.SeatRowNumber) < rowRange[0] || rowRange[1] < int(rowDetail.SeatRowNumber) {
					continue
				}
			} else {
				return nil, fmt.Errorf("ToDomainSegmentSeat: invalid row range")
			}

			facilities := []*domain.SeatFacility{}

			for _, seat := range rowDetail.SeatOccupationDetails {
				availability := enum.SeatStatusAvailable
				if seat.SeatOccupation != "" && seat.SeatOccupation != "F" {
					availability = enum.SeatStatusOccupied
				}

				property, noSeat := GetSeatInfo(seat.SeatCharacteristic)
				seatType := enum.SeatTypeSeat
				if noSeat {
					seatType = enum.SeatTypeAisle
				}

				charge := &domain.SeatCharge{
					BaseAmount:  0,
					TaxAmount:   0,
					TotalAmount: 0,
					Currency:    constants.VNCurrency,
				}
				if !freeSeat {
					charge = seatPriceMap[fmt.Sprintf("%v%s", rowDetail.SeatRowNumber, seat.SeatColumn)]
					for _, characteristic := range seat.SeatCharacteristic {
						if isContain := helpers.Contains[string](UnAvailableSeat, characteristic); isContain {
							availability = enum.SeatStatusBlocked
						}
					}
				} else {
					for _, characteristic := range seat.SeatCharacteristic {
						if isContain := helpers.Contains[string](UnAvailableSeat, characteristic); isContain {
							availability = enum.SeatStatusBlocked
						}

						if characteristic == "CH" {
							availability = enum.SeatStatusBlocked
						}
					}
				}

				facilities = append(facilities, &domain.SeatFacility{
					Type:         seatType,
					Availability: availability,
					SeatCode:     seat.SeatColumn,
					SeatCharge:   charge,
					Property:     property,
				})
			}

			seatOption.Rows = append(seatOption.Rows, &domain.SeatRow{
				RowNumber:  fmt.Sprintf("%v", rowDetail.SeatRowNumber),
				Facilities: facilities,
			})
		}
		seatSegment.SeatOptions = append(seatSegment.SeatOptions, seatOption)
	}

	return seatSegment, nil
}
