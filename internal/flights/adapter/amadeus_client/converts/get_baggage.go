package converts

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/service_standalone_catalogue"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

const (
	SSRStatusMandatory = "M"
	SSRStatusOptional  = "O"

	RangeQualifierSegment  = "S"
	RangeQualifierCustomer = "P"

	AttributeTypeMIF = "MIF"
	AttributeTypeCNM = "CNM"

	ServiceTypeBaggage              = "BG"
	ServiceTypeSellWithPrice        = "SR"
	ServiceTypePrepaidBaggage       = "PB"
	ServiceTypeExcessCheckedBaggage = "ECB"
)

func ToDomainBaggageOptions(res *service_standalone_catalogue.ServiceStandaloneCatalogueReply, itineraryIndex int) ([]*domain.BaggageOption, error) {
	if res == nil {
		return nil, fmt.Errorf("ToDomainBaggageOptions params nil")
	}

	baggageOptions := []*domain.BaggageOption{}
	mapSSRCodeIdentificationMandatory := map[string][]string{}
	mapSSRCodeCanBook := map[string]bool{}

	if len(res.SsrInformation) == 0 {
		return baggageOptions, nil
	}
	// chi ban nhung baggage co ssrCode trong ssrInfomation, neu ssrFormattedFreetext.Status == SSRStatusMandatory thi bat buoc phai them parameter khi book
	for _, ssrInfo := range res.SsrInformation {
		var ssrCode string
		if ssrInfo.ServiceRequest != nil && ssrInfo.ServiceRequest.SpecialRequirementsInfo != nil {
			ssrCode = ssrInfo.ServiceRequest.SpecialRequirementsInfo.SsrCode
			mapSSRCodeCanBook[ssrCode] = true
		}

		for _, ssrInfoDetail := range ssrInfo.SsrInformationDetails {
			for _, ssrFormattedFreetext := range ssrInfoDetail.SsrFormattedFreetext {
				if ssrFormattedFreetext.ComponentId != nil && ssrFormattedFreetext.Status == SSRStatusMandatory {
					mapSSRCodeIdentificationMandatory[ssrCode] = append(mapSSRCodeIdentificationMandatory[ssrCode], ssrFormattedFreetext.ComponentId.Identifier)
				}
			}
		}
	}

	for _, serviceGroup := range res.ServiceGroup {
		isBaggageService := false
		if serviceGroup.ServiceId != nil {
			for _, itemDetail := range serviceGroup.ServiceId.ItemNumberDetails {
				if itemDetail.Type == ServiceTypeSellWithPrice ||
					itemDetail.Type == ServiceTypePrepaidBaggage ||
					itemDetail.Type == ServiceTypeExcessCheckedBaggage {
					isBaggageService = true
				}
			}
		}

		if !isBaggageService {
			continue
		}

		segmentRefIDs, customerRefIDs, err := getSegmentRefIDsAndCustomerRefIDs(serviceGroup.PassengerAndFlightAssociation)
		if err != nil {
			return nil, errors.Wrap(err, "getSegmentRefIDsAndCustomerRefIDs")
		}

		RFICode, RFISCode, err := getServiceCodes(serviceGroup.ServiceCodes)
		if err != nil {
			return nil, errors.Wrap(err, "getServiceCodes")
		}

		canCustome := false
		isUnitPiece := false
		for _, serviceAttributes := range serviceGroup.ServiceAttributes {
			for _, criteriaDetail := range serviceAttributes.CriteriaDetails {
				if criteriaDetail.AttributeType == AttributeTypeMIF {
					canCustome = criteriaDetail.AttributeDescription == "Y" || criteriaDetail.AttributeDescription == "D"
				}

				if criteriaDetail.AttributeType == AttributeTypeCNM && strings.Contains(criteriaDetail.AttributeDescription, "ADDITIONAL") {
					isUnitPiece = true
				}
			}
		}

		var baggageCharge *domain.BaggageCharge
		for _, pricingGroup := range serviceGroup.PricingGroup {
			if pricingGroup.ComputedTaxSubDetails != nil && pricingGroup.ComputedTaxSubDetails.MonetaryDetails != nil {
				baggageCharge = &domain.BaggageCharge{
					BaseAmount:  pricingGroup.ComputedTaxSubDetails.MonetaryDetails.Amount,
					TaxAmount:   0,
					TotalAmount: pricingGroup.ComputedTaxSubDetails.MonetaryDetails.Amount,
					Currency:    pricingGroup.ComputedTaxSubDetails.MonetaryDetails.Currency,
				}
			}
		}

		var weight, maxWeight int
		quantity := 1
		if serviceGroup.BaggageDescriptionGroup != nil {
			for _, baggageDescriptionRange := range serviceGroup.BaggageDescriptionGroup.Range {
				if baggageDescriptionRange.RangeQualifier == "W" {
					for _, rangeDetail := range baggageDescriptionRange.RangeDetails {
						if rangeDetail.DataType == "K" {
							if rangeDetail.Max > 0 {
								weight = int(rangeDetail.Max)
								maxWeight = int(rangeDetail.Max)
							} else {
								weight = int(rangeDetail.Min)
								maxWeight = int(rangeDetail.Min)
							}
						}
					}
				}
			}

			if serviceGroup.BaggageDescriptionGroup.BaggageData != nil &&
				serviceGroup.BaggageDescriptionGroup.BaggageData.BaggageDetails != nil &&
				serviceGroup.BaggageDescriptionGroup.BaggageData.BaggageDetails.QuantityCode == "PC" &&
				serviceGroup.BaggageDescriptionGroup.BaggageData.BaggageDetails.UnitQualifier == "P" {
				isUnitPiece = true
				quantity = int(serviceGroup.BaggageDescriptionGroup.BaggageData.BaggageDetails.Measurement)
			}
		}

		if len(serviceGroup.ServiceDetailsGroup) == 1 &&
			serviceGroup.ServiceDetailsGroup[0].ServiceDetails != nil &&
			serviceGroup.ServiceDetailsGroup[0].ServiceDetails.SpecialRequirementsInfo != nil &&
			mapSSRCodeCanBook[serviceGroup.ServiceDetailsGroup[0].ServiceDetails.SpecialRequirementsInfo.SsrCode] &&
			serviceGroup.ServiceDetailsGroup[0].ServiceDetails.SpecialRequirementsInfo.ServiceType == ServiceTypeBaggage {

			firstServiceDetail := serviceGroup.ServiceDetailsGroup[0]
			if firstServiceDetail.ServiceDetails.SpecialRequirementsInfo.OtherServiceType != "" { // skip baggage have otherType
				continue
			}

			for _, fsfkwDataGroup := range firstServiceDetail.FsfkwDataGroup {
				if fsfkwDataGroup.FsfkwValues != nil &&
					fsfkwDataGroup.FsfkwValues.CriteriaDetails != nil &&
					fsfkwDataGroup.FsfkwValues.CriteriaDetails.AttributeType == "WVAL" &&
					fsfkwDataGroup.FsfkwRanges != nil &&
					fsfkwDataGroup.FsfkwRanges.RangeDetails != nil {
					if fsfkwDataGroup.FsfkwRanges.RangeDetails.Max > 0 {
						weight = int(fsfkwDataGroup.FsfkwRanges.RangeDetails.Max)
						maxWeight = int(fsfkwDataGroup.FsfkwRanges.RangeDetails.Max)

					} else {
						weight = int(fsfkwDataGroup.FsfkwRanges.RangeDetails.Min)
						maxWeight = int(fsfkwDataGroup.FsfkwRanges.RangeDetails.Min)
					}
				}
			}

			// Skip the case if baggage weight is not detected.
			if weight == 0 && !isUnitPiece {
				continue
			}

			amadeusBaggageParameter := []*domain.AmadeusBaggageParameter{}
			if canCustome {
				for _, mapSSRCodeIdentificationMandatory := range mapSSRCodeIdentificationMandatory[firstServiceDetail.ServiceDetails.SpecialRequirementsInfo.SsrCode] {
					switch mapSSRCodeIdentificationMandatory {
					case "WVAL":
						amadeusBaggageParameter = append(amadeusBaggageParameter, &domain.AmadeusBaggageParameter{
							Name:  "WVAL",
							Value: strconv.Itoa(maxWeight),
						})
					case "FMT":
						amadeusBaggageParameter = append(amadeusBaggageParameter, &domain.AmadeusBaggageParameter{
							Name:  "FMT",
							Value: "TTL",
						})
					case "PVAL":
						amadeusBaggageParameter = append(amadeusBaggageParameter, &domain.AmadeusBaggageParameter{
							Name:  "PVAL",
							Value: "1",
						})
					case "WUNI":
						amadeusBaggageParameter = append(amadeusBaggageParameter, &domain.AmadeusBaggageParameter{
							Name:  "WUNI",
							Value: "KG",
						})
					case "PUNI":
						amadeusBaggageParameter = append(amadeusBaggageParameter, &domain.AmadeusBaggageParameter{
							Name:  "PUNI",
							Value: "PC",
						})
					}
				}
			}

			baggageOptionDetail := &domain.BaggageOptionDetail{
				Type:      enum.BaggageTypeWeight,
				SubCode:   RFISCode,
				Quantity:  int(firstServiceDetail.ServiceDetails.SpecialRequirementsInfo.NumberInParty),
				Weight:    weight,
				MaxWeight: maxWeight,
				Unit:      constants.UnitKilogram,
			}

			tempBaggageOption := &domain.BaggageOption{}

			if isUnitPiece {
				baggageOptionDetail = &domain.BaggageOptionDetail{
					Type:     enum.BaggageTypePiece,
					SubCode:  RFISCode,
					Quantity: quantity,
				}

				tempBaggageOption = &domain.BaggageOption{
					OptionID: uuid.NewString(),
					OfferData: &domain.OfferData{
						AMAOfferData: &domain.AmadeusOfferData{
							RFICode:        RFICode,
							RFISCode:       RFISCode,
							ProviderCode:   firstServiceDetail.ServiceDetails.SpecialRequirementsInfo.AirlineCode,
							CustomerRefIDs: customerRefIDs,
							Parameters:     amadeusBaggageParameter,
						},
					},
					ItineraryIndex:     itineraryIndex,
					SegmentIndex:       segmentRefIDs,
					BaggageInfo:        []*domain.BaggageOptionDetail{baggageOptionDetail},
					TotalBaggageCharge: baggageCharge,
				}
			} else {
				baggageOptionDetail := &domain.BaggageOptionDetail{
					Type:      enum.BaggageTypeWeight,
					SubCode:   RFISCode,
					Quantity:  int(firstServiceDetail.ServiceDetails.SpecialRequirementsInfo.NumberInParty),
					Weight:    weight,
					MaxWeight: maxWeight,
					Unit:      constants.UnitKilogram,
				}

				tempBaggageOption = &domain.BaggageOption{
					OptionID: uuid.NewString(),
					OfferData: &domain.OfferData{
						AMAOfferData: &domain.AmadeusOfferData{
							RFICode:        RFICode,
							RFISCode:       RFISCode,
							ProviderCode:   firstServiceDetail.ServiceDetails.SpecialRequirementsInfo.AirlineCode,
							CustomerRefIDs: customerRefIDs,
							Parameters:     amadeusBaggageParameter,
						},
					},
					ItineraryIndex:     itineraryIndex,
					SegmentIndex:       segmentRefIDs,
					BaggageInfo:        []*domain.BaggageOptionDetail{baggageOptionDetail},
					TotalWeight:        maxWeight,
					Unit:               constants.UnitKilogram,
					TotalBaggageCharge: baggageCharge,
				}
			}

			baggageOptions = append(baggageOptions, tempBaggageOption)
		}
	}

	return baggageOptions, nil
}

func getSegmentRefIDsAndCustomerRefIDs(passengerAndFlightAssociations []*service_standalone_catalogue.RangeDetailsType) ([]int, []int, error) {
	var (
		segmentRefIDs, customerRefIDs []int
	)
	if len(passengerAndFlightAssociations) == 0 {
		return segmentRefIDs, customerRefIDs, fmt.Errorf("params nil")
	}

	for _, association := range passengerAndFlightAssociations {
		if association.RangeQualifier == RangeQualifierSegment {
			for _, rangeDetail := range association.RangeDetails {
				if rangeDetail.Max != 0 {
					for i := rangeDetail.Min; i <= rangeDetail.Max; i++ {
						segmentRefIDs = append(segmentRefIDs, int(i))
					}
				} else {
					segmentRefIDs = append(segmentRefIDs, int(rangeDetail.Min))

				}
			}
		} else if association.RangeQualifier == RangeQualifierCustomer {
			for _, rangeDetail := range association.RangeDetails {
				if rangeDetail.Max != 0 {
					for i := rangeDetail.Min; i <= rangeDetail.Max; i++ {
						customerRefIDs = append(customerRefIDs, int(i))
					}
				} else {
					customerRefIDs = append(customerRefIDs, int(rangeDetail.Min))

				}
			}
		}
	}

	return segmentRefIDs, customerRefIDs, nil
}

// return (RFICode, RFISCode, error )
func getServiceCodes(serviceCodes *service_standalone_catalogue.PricingOrTicketingSubsequentType) (string, string, error) {
	if serviceCodes == nil {
		return "", "", fmt.Errorf("params nil")
	}

	return serviceCodes.SpecialCondition, serviceCodes.OtherSpecialCondition, nil
}
