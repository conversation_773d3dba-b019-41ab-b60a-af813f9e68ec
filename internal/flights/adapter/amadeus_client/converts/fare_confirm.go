package converts

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/pkg/errors"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/fare_price_pnr_with_booking_class"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/pnr_repply"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/request"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

type SSRResponse struct {
	DateStr string
	Format  string
	Tz      string
}

const (
	ACTION_NO_PROCESSING = 0
	ACTION_END_TRANSACT  = 10
	ACTION_COMMIT        = 11

	TYPE_CASH           = "CA"
	TICKETMODE_CANCEL   = "XL"
	TotalFareAmountCode = "712"

	TSTNumber              = "TST"
	AmadeusPaxTypeInfant   = "IN"
	AmadeusPaxTypeChilrent = "CH"
	RefQualifierAdtAndChd  = "PA"
	RefQualifierInf        = "PI"
)

var MapGender = map[commonEnum.GenderType]string{
	commonEnum.GenderTypeNone:   "",
	commonEnum.GenderTypeMale:   "M",
	commonEnum.GenderTypeFeMale: "F",
}

type FromFarePricePNRWithBookingClassToTotalFareInfoRes struct {
	TSTNumbers []int
	TotalFare  *domain.TotalFareInfo
	CommRate   *float64
}

func FromDomainItineraries(itineraries []*domain.FlightItinerary) []*request.Itinerary {
	if len(itineraries) == 0 {
		return nil
	}

	itins := make([]*request.Itinerary, 0, len(itineraries))
	for _, itie := range itineraries {
		itins = append(itins, FromDomainItinerary(itie))
	}

	return itins
}

func FromDomainItinerary(itinerary *domain.FlightItinerary) *request.Itinerary {
	if itinerary == nil {
		return nil
	}

	return &request.Itinerary{
		StartPoint: itinerary.DepartPlace,
		EndPoint:   itinerary.ArrivalPlace,
		Segments:   fromDomainItineraryToSegmentAirsell(itinerary),
	}
}

func FromDomainItinerariesToSegments(itineraries []*domain.FlightItinerary) []*request.Segment {
	if len(itineraries) == 0 {
		return nil
	}
	segments := make([]*request.Segment, 0)
	numberSegments := 0
	for _, itie := range itineraries {
		segments = append(segments, fromDomainItineraryToSegment(itie, &numberSegments)...)
	}

	return segments
}

func FromPNRToLastTicketDate(pnr *pnr_repply.PNRReply, pricingFare *fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply) (int64, [][]string, int, error) {
	opcData := [][]string{}

	if pnr == nil || pnr.DataElementsMaster == nil || len(pnr.DataElementsMaster.DataElementsIndiv) == 0 {
		return 0, opcData, 0, fmt.Errorf("pnrRetrieve nil")
	}

	utcPlus7 := time.FixedZone("UTC+7", 7*60*60) // Tạo múi giờ UTC+7
	carrier := []string{}

	for _, elementIndiv := range pnr.DataElementsMaster.DataElementsIndiv {
		if elementIndiv.ElementManagementData != nil &&
			elementIndiv.ElementManagementData.SegmentName != nil &&
			elementIndiv.OptionElement != nil &&
			elementIndiv.OptionElement.OptionElementInfo != nil &&
			*elementIndiv.ElementManagementData.SegmentName == "OPC" &&
			elementIndiv.OptionElement.OptionElementInfo.Date != nil &&
			elementIndiv.OptionElement.OptionElementInfo.Time != nil {
			// if !strings.Contains(strings.ToUpper(*elementIndiv.OptionElement.OptionElementInfo.Freetext), "SGN") {
			// 	break
			// }

			freeTextData := strings.Split(
				strings.TrimSpace(
					strings.ReplaceAll(
						strings.ReplaceAll(
							strings.ToUpper(*elementIndiv.OptionElement.OptionElementInfo.Freetext),
							" CANCELLATION DUE TO NO TICKET", ""),
						"TIME ZONE", "")),
				" ")

			if len(freeTextData) != 2 {
				break
			}

			dateTimeStr := *elementIndiv.OptionElement.OptionElementInfo.Date + *elementIndiv.OptionElement.OptionElementInfo.Time
			airport := freeTextData[1]
			if isContain := helpers.Contains(carrier, freeTextData[0]); !isContain {
				carrier = append(carrier, freeTextData[0])
			}

			opcData = append(opcData, []string{dateTimeStr, airport})
		}
	}

	minFareLastTkt := int64(0)

	for _, fare := range pricingFare.FareList {
		fareLastTkt, err := unixMlDateFromStructuredDateTime(fare.LastTktDate, utcPlus7)
		if err != nil {
			return 0, opcData, 0, errors.Wrap(err, "unixMlDateFromStructuredDateTime")
		}

		if fareLastTkt != 0 && fareLastTkt < minFareLastTkt || minFareLastTkt == 0 {
			minFareLastTkt = fareLastTkt
		}
	}

	return minFareLastTkt, opcData, len(carrier), nil
}

func FromPNRToSSRLastTicketDate(pnr *pnr_repply.PNRReply) ([]string, error) {
	ssrFreeText := []string{}

	if pnr == nil || pnr.DataElementsMaster == nil || len(pnr.DataElementsMaster.DataElementsIndiv) == 0 {
		return ssrFreeText, fmt.Errorf("pnrRetrieve nil")
	}

	for _, elementIndiv := range pnr.DataElementsMaster.DataElementsIndiv {
		if elementIndiv.ElementManagementData != nil &&
			elementIndiv.ElementManagementData.SegmentName != nil &&
			elementIndiv.ServiceRequest != nil &&
			elementIndiv.ServiceRequest.Ssr != nil &&
			*elementIndiv.ElementManagementData.SegmentName == "SSR" &&
			elementIndiv.ServiceRequest.Ssr.Type != nil &&
			*elementIndiv.ServiceRequest.Ssr.Type != "DOCS" &&
			*elementIndiv.ServiceRequest.Ssr.Type != "CTCE" &&
			*elementIndiv.ServiceRequest.Ssr.Type != "CTCM" {

			freeTextFields := elementIndiv.ServiceRequest.Ssr.FreeText
			for _, freeText := range freeTextFields {
				if strings.Contains(strings.ToUpper(*freeText), "BY") {
					ssrFreeText = append(ssrFreeText, strings.ToUpper(*freeText))
				}
			}
		}
	}

	return ssrFreeText, nil
}

func ParseSSRLastTicketDate(freeText string, template []*domain.SSRTemplate) (*SSRResponse, error) {
	for _, item := range template {
		if item == nil {
			continue
		}

		re := regexp.MustCompile(item.Regex)
		if re == nil {
			continue
		}

		result := re.FindString(freeText)
		if result == "" {
			continue
		}

		timeStr := result
		tz := ""
		if item.TimezoneCheck {
			re := regexp.MustCompile(`[A-Z]{3}`)
			matches := re.FindAllString(timeStr, -1)

			if len(matches) > 0 {
				for _, airport := range matches {
					tempTimeStr := strings.TrimSpace(strings.ReplaceAll(
						timeStr,
						airport, ""),
					)

					re := regexp.MustCompile(`\s{2,}`)
					tempTimeStr = re.ReplaceAllString(tempTimeStr, " ")
					_, err := time.Parse(item.DateFormat, tempTimeStr)

					if err != nil {
						continue
					}

					tz = airport
					timeStr = tempTimeStr
				}
			} else {
				log.Error("[ParseSSRLastTicketDate] tz not found]")
				return nil, commonErrs.ErrInvalidTimezone
			}
		}

		return &SSRResponse{
			DateStr: timeStr,
			Format:  item.DateFormat,
			Tz:      tz,
		}, nil
	}
	return nil, domain.ErrSSRTemplateInvalid
}

func unixMlDateFromStructuredDateTime(in *fare_price_pnr_with_booking_class.StructuredDateTimeInformationType199533S, tZ *time.Location) (int64, error) {
	if in == nil || in.DateTime == nil || in.DateTime.Year == nil || in.DateTime.Month == nil || in.DateTime.Day == nil {
		return 0, nil
	}
	dt := in.DateTime

	min, hour, day, mon, ye := dt.Minutes, dt.Hour, dt.Day, dt.Month, dt.Year

	if *ye == 0 || *mon == "" || *day == "" {
		return 0, nil
	}

	if hour != nil && len(*hour) == 1 {
		*hour = "0" + *hour
	}

	if min != nil && len(*min) == 1 {
		*min = "0" + *min
	}

	if hour == nil || *hour == "" {
		endDayHour := "23"
		endDayMin := "59"
		hour, min = &endDayHour, &endDayMin
	}

	value := fmt.Sprintf("%s/%s/%d %s:%s", *day, *mon, *ye, *hour, *min)

	t, err := time.ParseInLocation("2/1/2006 15:04", value, tZ)
	if err != nil {
		return 0, errors.Wrap(err, "time.parse value "+value)
	}

	return t.UnixMilli(), nil
}

func fromDomainItineraryToSegment(itinerary *domain.FlightItinerary, numberSegments *int) []*request.Segment {
	if itinerary == nil || len(itinerary.Segments) == 0 {
		return nil
	}
	segments := make([]*request.Segment, 0, len(itinerary.Segments))
	for _, segment := range itinerary.Segments {
		if segment != nil {
			*numberSegments++
			segmentTemp := &request.Segment{
				StartPoint:       segment.DepartPlace,
				EndPoint:         segment.ArrivalPlace,
				DepartDate:       segment.DepartDate,
				MarketingCompany: segment.CarrierMarketing,
				OperatingCompany: segment.CarrierOperator,
				FlightNumber:     segment.FlightNumber,
				BookingClass:     segment.BookingClass,
				SegmentTattoo:    *numberSegments,
				GroupNumber:      *numberSegments,
				CompanyCode:      itinerary.CarrierMarketing,
				FareBasicCode:    segment.FareBasis,
			}

			if segment.CarrierOperator == "" {
				segmentTemp.OperatingCompany = segment.CarrierMarketing
			}

			segments = append(segments, segmentTemp)
		}
	}

	return segments
}

func fromDomainItineraryToSegmentAirsell(itinerary *domain.FlightItinerary) []*request.Segment {
	if itinerary == nil || len(itinerary.Segments) == 0 {
		return nil
	}
	segments := make([]*request.Segment, 0, len(itinerary.Segments))
	for _, segment := range itinerary.Segments {
		if segment != nil {
			segments = append(segments, &request.Segment{
				MarketingCompany: segment.CarrierMarketing,
				OperatingCompany: segment.CarrierOperator,
				StartPoint:       segment.DepartPlace,
				EndPoint:         segment.ArrivalPlace,
				DepartDate:       segment.DepartDate,
				FlightNumber:     segment.FlightNumber,
				BookingClass:     segment.BookingClass,
				CompanyCode:      segment.CarrierMarketing,
			})
		}
	}

	return segments
}

func FromFarePricePNRWithBookingClassToTotalFareInfo(
	farePrice *fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply,
	booking *domain.BookingSession,
	paxAdtIndexToIDMap, paxParentIndexToInfIDMap map[int]int,
	paxIndexToPaxTypeMap map[int]enum.PaxType,
	amadeusISOCountryCodeMap map[string]*domain.AmadeusISOCountry,
) (*FromFarePricePNRWithBookingClassToTotalFareInfoRes, error) {
	if farePrice == nil || len(farePrice.FareList) == 0 {
		return nil, fmt.Errorf("FromFarePricePNRWithBookingClassToTotalFareInfo: farePrice == nil || len(farePrice.FareList) == 0 ")
	}

	totalFare := &domain.TotalFareInfo{}
	tstNumber := []int{}
	itineraryPaxFare := []*domain.ItineraryPaxFare{}
	var commRate *float64
	route := getRouteStringFromItineraries(booking)
	paxFareInfos := []*domain.PaxFareInfo{}
	itineraryIDs := []string{}

	for _, itinerary := range booking.Itineraries {
		itineraryIDs = append(itineraryIDs, itinerary.ID)
	}

	for _, fare := range farePrice.FareList {
		var (
			err error
		)
		paxType := enum.PaxTypeNone

		if *fare.FareReference.ReferenceType == TSTNumber {
			tstNumber = append(tstNumber, *fare.FareReference.UniqueReference)
		}
		numPaxSeg := float64(len(fare.PaxSegReference.RefDetails))

		taxAmount, err := calculatorTaxAmount(fare.TaxInformation)
		if err != nil {
			return nil, errors.Wrap(err, "calculatorTaxAmount")
		}

		fareAmount, currency, err := calculatorFareAmount(fare.FareDataInformation)
		if err != nil {
			return nil, errors.Wrap(err, "calculatorFareAmount")
		}

		if *fare.SegmentInformation[0].FareQualifier.FareBasisDetails.DiscTktDesignator == AmadeusPaxTypeInfant {
			paxType = enum.PaxTypeInfant
		} else {
			refNum := fare.PaxSegReference.RefDetails[0].RefNumber
			paxType = paxIndexToPaxTypeMap[refNum]
		}

		paxFare := &domain.ItineraryPaxFare{
			PaxType:    paxType,
			FareAmount: fareAmount,
			FareBasic:  fareAmount - taxAmount,
			TaxAmount:  taxAmount,
			Currency:   currency,
		}

		itineraryPaxFare = append(itineraryPaxFare, paxFare)

		totalFare.Currency = currency
		totalFare.BaseTotalFareAmount += fareAmount * numPaxSeg
		totalFare.TotalFareBasic += (fareAmount - taxAmount) * numPaxSeg
		totalFare.TotalTaxAmount += taxAmount * numPaxSeg

		for _, info := range fare.OtherPricingInfo {
			for _, attr := range info.AttributeDetails {
				if attr.AttributeType != nil && attr.AttributeDescription != nil && *attr.AttributeType == "COM" {
					rawComm := *attr.AttributeDescription
					commV, err := getCommRateFromRawString(rawComm)
					if err != nil {
						return nil, errors.Wrap(err, "getCommRateFromRawString")
					}

					commRate = &commV
				}
			}
		}

		detailFares := []*domain.DetailFareReport{}
		for _, taxInformation := range fare.TaxInformation {
			if taxInformation == nil {
				continue
			}
			var (
				taxDetailName string
				isoCountry    string
				taxAmount     float64
			)

			if taxInformation.AmountDetails != nil && taxInformation.AmountDetails.FareDataMainInformation != nil {
				taxAmount, err = strconv.ParseFloat(taxInformation.AmountDetails.FareDataMainInformation.FareAmount, 64)
				if err != nil {
					return nil, errors.Wrap(err, "strconv.ParseFloat(*taxInfomation.AmountDetails.FareDataMainInformation.FareAmount")
				}
			}

			if taxInformation.TaxDetails != nil {
				taxDetailName = taxInformation.TaxDetails.TaxNature

				if taxInformation.TaxDetails.TaxType != nil {
					isoCountry = taxInformation.TaxDetails.TaxType.IsoCountry
				}
			}

			typeDescription := ""
			if amadeusISOCountryCodeMap[isoCountry] != nil {
				typeDescription = amadeusISOCountryCodeMap[isoCountry].Description
			}

			detailFares = append(detailFares, &domain.DetailFareReport{
				Type:            isoCountry,
				TypeDescription: typeDescription,
				TaxAmount:       taxAmount,
				TaxDetails: []*domain.TaxDetail{
					{
						Name:   taxDetailName,
						Amount: taxAmount,
					},
				},
				TotalAmount: taxAmount,
				Currency:    *taxInformation.AmountDetails.FareDataMainInformation.FareCurrency,
			})
		}

		for _, refDetail := range fare.PaxSegReference.RefDetails {
			if refDetail == nil {
				continue
			}

			if refDetail.RefQualifier == RefQualifierAdtAndChd {
				paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
					PaxID:   paxAdtIndexToIDMap[refDetail.RefNumber],
					PaxType: paxIndexToPaxTypeMap[refDetail.RefNumber],
					PaxFares: []*domain.PaxFareReport{
						{
							ItineraryIDs: itineraryIDs,
							Route:        route,
							FareAmount:   fareAmount,
							FareBasic:    (fareAmount - taxAmount),
							TaxAmount:    taxAmount,
							Currency:     currency,
							DetailFares:  detailFares,
						},
					},
					TotalPrice: fareAmount,
					Currency:   currency,
				})
				continue
			}

			if refDetail.RefQualifier == RefQualifierInf {
				paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
					PaxID:   paxParentIndexToInfIDMap[refDetail.RefNumber],
					PaxType: enum.PaxTypeInfant,
					PaxFares: []*domain.PaxFareReport{
						{
							ItineraryIDs: itineraryIDs,
							Route:        route,
							FareAmount:   fareAmount,
							FareBasic:    (fareAmount - taxAmount),
							TaxAmount:    taxAmount,
							Currency:     currency,
							DetailFares:  detailFares,
						},
					},
					TotalPrice: fareAmount,
					Currency:   currency,
				})
				continue
			}
		}
	}

	totalFare.TotalPaxFares = itineraryPaxFare
	totalFare.PaxFareInfos = paxFareInfos
	return &FromFarePricePNRWithBookingClassToTotalFareInfoRes{
		TSTNumbers: tstNumber,
		TotalFare:  totalFare,
		CommRate:   commRate,
	}, nil
}

func GetPaxMapFromPNR(amadeusPNR *pnr_repply.PNRReply, pnr *domain.PNR) (map[int]int, map[int]int, map[int]enum.PaxType) {
	if amadeusPNR == nil || len(amadeusPNR.TravellerInfo) == 0 {
		return nil, nil, nil
	}
	paxAdtIndexToIDMap := map[int]int{}
	paxIndexToPaxTypeMap := map[int]enum.PaxType{}
	paxParentIndexToInfIDMap := map[int]int{}

	paxDomainPaxIDMap := map[string]int{}

	for _, pax := range pnr.ListPax {
		paxDomainPaxIDMap[fmt.Sprintf("%s-%s", pax.Surname, pax.GivenName)] = pax.ID
	}

	for _, traveller := range amadeusPNR.TravellerInfo {
		paxIndex := *traveller.ElementManagementPassenger.Reference.Number
		paxType := getPaxType(*traveller.PassengerData[0].TravellerInformation.Passenger[0].Type)
		paxIndexToPaxTypeMap[paxIndex] = paxType

		surname := *traveller.PassengerData[0].TravellerInformation.Traveller.Surname
		givenName := *traveller.PassengerData[0].TravellerInformation.Passenger[0].FirstName

		paxAdtIndexToIDMap[paxIndex] = paxDomainPaxIDMap[fmt.Sprintf("%s-%s", surname, givenName)]

		if len(traveller.EnhancedPassengerData) > 0 {
			for _, enhancedPassengerData := range traveller.EnhancedPassengerData {
				if enhancedPassengerData == nil || enhancedPassengerData.EnhancedTravellerInformation == nil ||
					enhancedPassengerData.EnhancedTravellerInformation.TravellerNameInfo == nil ||
					len(enhancedPassengerData.EnhancedTravellerInformation.OtherPaxNamesDetails) == 0 {
					continue
				}

				if enhancedPassengerData.EnhancedTravellerInformation.TravellerNameInfo.Type == "INF" {
					surnameINF := enhancedPassengerData.EnhancedTravellerInformation.OtherPaxNamesDetails[0].Surname
					givenNameINF := enhancedPassengerData.EnhancedTravellerInformation.OtherPaxNamesDetails[0].GivenName
					paxParentIndexToInfIDMap[paxIndex] = paxDomainPaxIDMap[fmt.Sprintf("%s-%s", surnameINF, givenNameINF)]
				}
			}
		}
	}

	return paxAdtIndexToIDMap, paxParentIndexToInfIDMap, paxIndexToPaxTypeMap
}

// Known fmt: *F*3.00
func getCommRateFromRawString(rawStr string) (float64, error) {
	var left, right string
	var afterDot bool

	full := ""

	for _, char := range rawStr {
		if char == '.' {
			afterDot = true
			continue
		}

		if unicode.IsDigit(char) {
			if afterDot {
				right += string(char)
			} else {
				left += string(char)
			}
		} else {
			full = left + "." + right
			left = ""
			right = ""
		}
	}

	if left != "" || right != "" {
		full = left + "." + right
	}

	return strconv.ParseFloat(full, 64)
}

func calculatorTaxAmount(taxInfo []*fare_price_pnr_with_booking_class.TaxInformation) (float64, error) {
	var taxAmount float64

	if len(taxInfo) == 0 {
		return taxAmount, nil
	}

	for _, tax := range taxInfo {
		taxTemp, err := strconv.ParseFloat(tax.AmountDetails.FareDataMainInformation.FareAmount, 64)
		if err != nil {
			return 0, errors.Wrap(err, "taxTemp.ParseFloat")
		}

		taxAmount += taxTemp
	}

	return taxAmount, nil
}

func calculatorFareAmount(fareAmountInfo *fare_price_pnr_with_booking_class.MonetaryInformationType198917S) (float64, string, error) {
	if fareAmountInfo == nil || len(fareAmountInfo.FareDataSupInformation) == 0 {
		return 0, "", fmt.Errorf("calculatorFareAmount: fareAmountInfo == nil || len(fareAmountInfo.FareDataSupInformation) == 0")
	}

	for _, fareSup := range fareAmountInfo.FareDataSupInformation {
		if *fareSup.FareDataQualifier == TotalFareAmountCode {
			fareAmount, err := strconv.ParseFloat(*fareSup.FareAmount, 64)
			if err != nil {
				return 0, "", errors.Wrap(err, "fareAmount.ParseFloat")
			}

			return fareAmount, *fareSup.FareCurrency, nil
		}
	}

	return 0, "", nil
}

func FromModelSessionToRequest(session *models.Session) *request.Session {
	if session == nil {
		return nil
	}

	return &request.Session{
		ID:       session.SessionID,
		Sequence: session.SequenceNumber,
		Token:    session.SecurityToken,
	}
}

func FromDomainTravelers(items []*domain.PaxInfo, companyCode string) []*request.Traveler {
	if len(items) == 0 {
		return nil
	}

	travelers := make([]*request.Traveler, len(items))
	arrIndexAdt := []int{}
	arrIndexInf := []int{}
	for k, traveler := range items {
		travelers[k] = fromDomainTraveler(traveler, companyCode)
		if travelers[k].Type == string(enum.PaxTypeAdult) {
			arrIndexAdt = append(arrIndexAdt, k)
		}

		if travelers[k].Type == string(enum.PaxTypeInfant) {
			arrIndexInf = append(arrIndexInf, k)
		}
	}

	if len(arrIndexInf) > 0 {
		for k, v := range arrIndexInf {
			travelers[v].ParentID = travelers[arrIndexAdt[k]].ID
			travelers[arrIndexAdt[k]].DOB = travelers[v].DOBForPassport
		}
	}

	return travelers
}

func fromDomainTraveler(info *domain.PaxInfo, companyCode string) *request.Traveler {
	if info == nil || info.DOB == nil {
		return nil
	}

	traveler := &request.Traveler{
		ID:             info.ID,
		Type:           string(info.Type),
		LastName:       info.Surname,
		FirstName:      info.GivenName,
		DOBForPassport: *info.DOB,
		Gender:         MapGender[info.Gender],
		Nationality:    info.Nationality,
		CompanyCode:    companyCode,
	}

	if info.Passport != nil {
		traveler.PassportNumber = info.Passport.Number
		traveler.ExpirationDate = info.Passport.ExpiryDate
		traveler.IDIssuingCountry = info.Passport.IssuingCountry
	}

	if info.Type == enum.PaxTypeChildren || info.Type == enum.PaxTypeInfant {
		traveler.DOB = *info.DOB
	}

	return traveler
}

func FromDomainContact(info *domain.Contact, email string) *request.Contact {
	if info == nil {
		return nil
	}

	return &request.Contact{
		Phone: info.Phone,
		Email: email,
	}
}

func FromDomainSeatSelections(itineraries []*domain.FlightItinerary) []*request.SeatSelection {
	result := []*request.SeatSelection{}
	segmentIdxBuffer := 0
	for _, iti := range itineraries {
		for _, pax := range iti.PaxInfo {
			for _, seat := range pax.Seats {
				result = append(result, &request.SeatSelection{
					PassengerTattoo: pax.PaxID + 1,
					SegmentTattoo:   segmentIdxBuffer + seat.SegmentIndex,
					SeatCode:        seat.RowNumber + seat.SeatFacility.SeatCode,
				})
			}
		}
		segmentIdxBuffer += len(iti.Segments)
	}

	return result
}

func getRouteStringFromItineraries(booking *domain.BookingSession) string {
	route := ""
	if booking == nil {
		return route
	}

	if len(booking.Itineraries) > 2 || len(booking.Itineraries) == 2 && booking.Itineraries[0].DepartPlace != booking.Itineraries[1].ArrivalPlace {
		for _, itinerary := range booking.Itineraries {
			prefix := ";"
			if route == "" {
				prefix = ""
			}
			route += fmt.Sprintf("%s%s-%s", prefix, itinerary.DepartPlace, itinerary.ArrivalPlace)
		}

		return route
	} else {
		if len(booking.Itineraries) == 1 {
			return fmt.Sprintf("%s-%s", booking.Itineraries[0].DepartPlace, booking.Itineraries[0].ArrivalPlace)
		}

		if len(booking.Itineraries) == 2 {
			return fmt.Sprintf("%s-%s-%s", booking.Itineraries[0].DepartPlace, booking.Itineraries[0].ArrivalPlace, booking.Itineraries[1].ArrivalPlace)
		}
	}

	return route
}
