package converts

import (
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/pnr_repply"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const rawDateTimeFormat = "020106 1504"

var FailedSegmentCodes = []string{"HX", "UX", "UNS", "X"}

func SyncBooking(pBk *pnr_repply.PNRReply, dBk *domain.BookingSession) (bool, bool, error) {
	hasChanges := false
	needReconfirm := false

	if pBk.OriginDestinationDetails == nil {
		return false, false, errors.New("empty OriginDestinationDetails")
	}

	pSegIDMap := map[int]*pnr_repply.ItineraryInfo{}

	for index, itiInfo := range pBk.OriginDestinationDetails.ItineraryInfo {
		if itiInfo.RelatedProduct != nil {
			if itiInfo.RelatedProduct.Status == "TK" {
				needReconfirm = true
			}

			if lo.Contains(FailedSegmentCodes, itiInfo.RelatedProduct.Status) {
				return false, false, errors.Wrap(domain.ErrIssueTicketFailed, "FailedSegmentCodes")
			}
		}

		segID := index + 1
		pSegIDMap[segID] = itiInfo // BA confirm index cua list itinerary info se tra theo thu tu segment bay
	}

	step := 0
	for _, iti := range dBk.Itineraries {
		itiChange := false

		for _, seg := range iti.Segments {
			segID := step + seg.Index

			pSeg := pSegIDMap[segID]

			if pSeg == nil {
				return false, false, errors.Wrap(domain.ErrIssueTicketFailed, "pSeg nil")
			}

			segFlightTime := pSeg.TravelProduct.Product

			if segFlightTime.ArrDate == nil || segFlightTime.ArrTime == nil || segFlightTime.DepDate == nil || segFlightTime.DepTime == nil {
				return false, false, errors.New("empty product flight time " + dBk.BookingRef)
			}

			rawDepartDate := *segFlightTime.DepDate
			rawDepartTime := *segFlightTime.DepTime

			pSegDepartDate, err := time.Parse(rawDateTimeFormat, rawDepartDate+" "+rawDepartTime)
			if err != nil {
				return false, false, errors.Wrap(errInvalidFormat, "departDate.time.Parse")
			}

			rawArrivalDate := *segFlightTime.ArrDate
			rawArrivalTime := *segFlightTime.ArrTime

			pSegArrivalDate, err := time.Parse(rawDateTimeFormat, rawArrivalDate+" "+rawArrivalTime)
			if err != nil {
				return false, false, errors.Wrap(errInvalidFormat, "arrivalDate.time.Parse")
			}

			if pSegDepartDate.UnixMilli() != seg.DepartDate || pSegArrivalDate.UnixMilli() != seg.ArrivalDate {
				hasChanges = true
				itiChange = true

				seg.DepartDate = pSegDepartDate.UnixMilli()
				seg.ArrivalDate = pSegArrivalDate.UnixMilli()
				seg.DepartDt = helpers.ToUTCDateTime(seg.DepartDate)
				seg.ArrivalDt = helpers.ToUTCDateTime(seg.ArrivalDate)
			}

		}
		// Post processing
		if itiChange {
			var err error

			firstSeg := iti.Segments[0]
			lastSeg := iti.Segments[len(iti.Segments)-1]

			iti.DepartDate = firstSeg.DepartDate
			iti.ArrivalDate = lastSeg.ArrivalDate

			iti.DepartDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.DepartDate, "", iti.DepartDt.Location())
			if err != nil {
				return false, false, err
			}

			iti.ArrivalDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.ArrivalDate, "", iti.ArrivalDt.Location())
			if err != nil {
				return false, false, err
			}

			iti.FlightDuration = int(iti.ArrivalDt.Sub(iti.DepartDt).Minutes())
		}
		step += len(iti.Segments)
	}

	return hasChanges, needReconfirm, nil
}
