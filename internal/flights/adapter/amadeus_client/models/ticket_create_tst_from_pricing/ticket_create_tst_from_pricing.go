package ticket_create_tst_from_pricing

import "encoding/xml"

// ApplicationError ...
type ApplicationError struct {
	XMLName              xml.Name                         `xml:"applicationError"`
	ApplicationErrorInfo *ApplicationErrorInformationType `xml:"applicationErrorInfo"`
	ErrorText            *InteractiveFreeTextTypeI        `xml:"errorText"`
}

// TstList ...
type TstList struct {
	XMLName        xml.Name                       `xml:"tstList"`
	TstReference   *ItemReferencesAndVersionsType `xml:"tstReference"`
	PaxInformation *ReferenceInformationTypeI     `xml:"paxInformation"`
}

// TicketCreateTSTFromPricingReply ...
type TicketCreateTSTFromPricingReply struct {
	XMLName          xml.Name                            `xml:"Ticket_CreateTSTFromPricingReply"`
	ApplicationError *ApplicationError                   `xml:"applicationError"`
	PnrLocatorData   *ReservationControlInformationTypeI `xml:"pnrLocatorData"`
	TstList          []*TstList                          `xml:"tstList"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ApplicationErrorCode      string `xml:"applicationErrorCode"`
	CodeListQualifier         string `xml:"codeListQualifier"`
	CodeListResponsibleAgency string `xml:"codeListResponsibleAgency"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ApplicationErrorDetail *ApplicationErrorDetailType `xml:"applicationErrorDetail"`
}

// InteractiveFreeTextTypeI is Free flow text describing the error
type InteractiveFreeTextTypeI struct {
	ErrorFreeText string `xml:"errorFreeText"`
}

// ItemReferencesAndVersionsType is Gives the TST ID number
type ItemReferencesAndVersionsType struct {
	ReferenceType   string                   `xml:"referenceType"`
	UniqueReference int                      `xml:"uniqueReference"`
	IDDescription   *UniqueIdDescriptionType `xml:"iDDescription"`
}

// ReferenceInformationTypeI is Passenger/segment/TST reference details
type ReferenceInformationTypeI struct {
	RefDetails []*ReferencingDetailsTypeI `xml:"refDetails"`
}

// ReferencingDetailsTypeI is Passenger/segment/TST reference number
type ReferencingDetailsTypeI struct {
	RefQualifier string `xml:"refQualifier"`
	RefNumber    int    `xml:"refNumber"`
}

// ReservationControlInformationDetailsTypeI is Record locator.
type ReservationControlInformationDetailsTypeI struct {
	ControlNumber string `xml:"controlNumber"`
}

// ReservationControlInformationTypeI is Reservation control information
type ReservationControlInformationTypeI struct {
	ReservationInformation *ReservationControlInformationDetailsTypeI `xml:"reservationInformation"`
}

// UniqueIdDescriptionType is The TST Id Number : The Id number allows to determine a TST in the single manner.
type UniqueIdDescriptionType struct {
	IDSequenceNumber int `xml:"iDSequenceNumber"`
}

// AlphaNumericStringLength1To70 is Format limitations: an..70
type AlphaNumericStringLength1To70 string

// AlphaNumericStringLength1To3 is Format limitations: an..3
type AlphaNumericStringLength1To3 string

// NumericIntegerLength1To5 is Format limitations: n..5
type NumericIntegerLength1To5 int

// AlphaNumericStringLength1To5 is Format limitations: an..5
type AlphaNumericStringLength1To5 string

// AlphaNumericStringLength1To20 is Format limitations: an..20
type AlphaNumericStringLength1To20 string

// NumericIntegerLength1To11 is Format limitations: n..11
type NumericIntegerLength1To11 int
