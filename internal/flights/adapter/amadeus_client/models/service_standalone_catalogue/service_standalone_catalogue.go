package service_standalone_catalogue

// FrequentTravellerGroup ...
type FrequentTravellerGroup struct {
	// XMLName               xml.Name                                 `xml:"frequentTravellerGroup"`
	FrequentTravellerInfo *FrequentTravellerIdentificationCodeType `xml:"frequentTravellerInfo"`
	Balance               *MonetaryInformationType                 `xml:"balance"`
}

// PassengerGroup ...
type PassengerGroup struct {
	// XMLName                xml.Name                  `xml:"passengerGroup"`
	PaxReference           *ReferenceInformationType `xml:"paxReference"`
	DiscountCodes          *DiscountDataType         `xml:"discountCodes"`
	FrequentTravellerGroup []*FrequentTravellerGroup `xml:"frequentTravellerGroup"`
}

// FlightInfo ...
type FlightInfo struct {
	// XMLName              xml.Name                         `xml:"flightInfo"`
	FlightDetails        *TravelProductInformationType    `xml:"flightDetails"`
	TravelItineraryInfo  *TravelItineraryInformationTypeI `xml:"travelItineraryInfo"`
	AdditionalFlightInfo *AdditionalProductDetailsTypeI   `xml:"additionalFlightInfo"`
	CodeshareInfo        *CommercialAgreementsType        `xml:"codeshareInfo"`
}

// Portions ...
type Portions struct {
	// XMLName           xml.Name           `xml:"portions"`
	TravelPortions    *ReferenceInfoType `xml:"travelPortions"`
	FlightAssociation *RangeDetailsType  `xml:"flightAssociation"`
}

// SsrInformationDetails ...
type SsrInformationDetails struct {
	// XMLName              xml.Name                            `xml:"ssrInformationDetails"`
	SettingsDetails      *StatusType                         `xml:"settingsDetails"`
	SsrFormattedFreetext []*StructureComponentDefinitionType `xml:"ssrFormattedFreetext"`
}

// SsrInformation ...
type SsrInformation struct {
	// XMLName               xml.Name                               `xml:"ssrInformation"`
	ServiceRequest        *SpecialRequirementsDetailsType174527S `xml:"serviceRequest"`
	ErrorFunctional       *ErrorGroupType                        `xml:"errorFunctional"`
	SsrInformationDetails []*SsrInformationDetails               `xml:"ssrInformationDetails"`
}

// FsfkwDataGroup ...
type FsfkwDataGroup struct {
	// XMLName     xml.Name                 `xml:"fsfkwDataGroup"`
	FsfkwValues *AttributeType208309S    `xml:"fsfkwValues"`
	FsfkwRanges *RangeDetailsType208311S `xml:"fsfkwRanges"`
}

// ServiceDetailsGroup ...
type ServiceDetailsGroup struct {
	// XMLName           xml.Name                        `xml:"serviceDetailsGroup"`
	ServiceDetails    *SpecialRequirementsDetailsType `xml:"serviceDetails"`
	FlightAssociation *RangeDetailsType               `xml:"flightAssociation"`
	FsfkwDataGroup    []*FsfkwDataGroup               `xml:"fsfkwDataGroup"`
}

// SsrIndicatorList ...
type SsrIndicatorList struct {
	// XMLName           xml.Name            `xml:"ssrIndicatorList"`
	ServiceAttributes *CodedAttributeType `xml:"serviceAttributes"`
	SegmentReference  *ReferenceInfoType  `xml:"segmentReference"`
}

// DateGroup ...
type DateGroup struct {
	// XMLName          xml.Name                           `xml:"dateGroup"`
	DateInfo         *StructuredDateTimeInformationType `xml:"dateInfo"`
	SegmentReference *ReferenceInfoType                 `xml:"segmentReference"`
}

// MediaContentGroup ...
type MediaContentGroup struct {
	// XMLName          xml.Name                              `xml:"mediaContentGroup"`
	MediaReference   *ItemReferencesAndVersionsType192375S `xml:"mediaReference"`
	MediaId          *CommunicationContactType             `xml:"mediaId"`
	SegmentReference *ReferenceInfoType                    `xml:"segmentReference"`
}

// QuotaGroup ...
type QuotaGroup struct {
	// XMLName             xml.Name                      `xml:"quotaGroup"`
	ServiceQuota        *SpecialRequirementsQuotaType `xml:"serviceQuota"`
	BookingClassUpgrade []*ProductInformationType     `xml:"bookingClassUpgrade"`
	CabinClassFact      *CabinClassServicesType       `xml:"cabinClassFact"`
	SegmentReference    *ReferenceInfoType            `xml:"segmentReference"`
}

// BaggageDescriptionGroup ...
type BaggageDescriptionGroup struct {
	// XMLName           xml.Name                   `xml:"baggageDescriptionGroup"`
	BaggageData       *ExcessBaggageType         `xml:"baggageData"`
	Range             []*RangeDetailsType191709S `xml:"range"`
	BaggageAttributes []*AttributeType           `xml:"baggageAttributes"`
}

// TaxInfo ...
type TaxInfo struct {
	// XMLName                        xml.Name                           `xml:"taxInfo"`
	ComputedTaxListInfo            *TaxType                           `xml:"computedTaxListInfo"`
	ComputedDiscountAndPenaltyInfo *DiscountAndPenaltyInformationType `xml:"computedDiscountAndPenaltyInfo"`
	ComputedTaxSubDetails          *MonetaryInformationType           `xml:"computedTaxSubDetails"`
}

// CouponInfoGroup ...
type CouponInfoGroup struct {
	// XMLName                xml.Name                       `xml:"couponInfoGroup"`
	Coupon                 *ItemReferencesAndVersionsType `xml:"coupon"`
	MonetaryInfo           *MonetaryInformationType       `xml:"monetaryInfo"`
	FareAttribute          []*AttributeType               `xml:"fareAttribute"`
	SegmentCouponReference *ReferenceInfoType             `xml:"segmentCouponReference"`
	TaxInfo                []*TaxInfo                     `xml:"taxInfo"`
}

// PricingGroup ...
type PricingGroup struct {
	// XMLName                     xml.Name                     `xml:"pricingGroup"`
	CodeshareCarrierInfo        *CommercialAgreementsType    `xml:"codeshareCarrierInfo"`
	ComputedTaxSubDetails       *MonetaryInformationType     `xml:"computedTaxSubDetails"`
	PassengerReference          *ReferenceInfoType           `xml:"passengerReference"`
	AdditionalConversionDetails *ConversionRateType          `xml:"additionalConversionDetails"`
	FeeApplication              *SpecificDataInformationType `xml:"feeApplication"`
	CouponInfoGroup             []*CouponInfoGroup           `xml:"couponInfoGroup"`
}

// ServiceGroup ...
type ServiceGroup struct {
	// XMLName                       xml.Name                                `xml:"serviceGroup"`
	ServiceId                     *ItemNumberType                         `xml:"serviceId"`
	PassengerAndFlightAssociation []*RangeDetailsType                     `xml:"passengerAndFlightAssociation"`
	UniquePassengerId             []*ReferenceInfoType                    `xml:"uniquePassengerId"`
	ServiceAssociation            []*ItemReferencesAndVersionsType192370S `xml:"serviceAssociation"`
	ServiceCodes                  *PricingOrTicketingSubsequentType       `xml:"serviceCodes"`
	ServiceAttributes             []*AttributeType                        `xml:"serviceAttributes"`
	ServiceDetailsGroup           []*ServiceDetailsGroup                  `xml:"serviceDetailsGroup"`
	SsrIndicatorList              []*SsrIndicatorList                     `xml:"ssrIndicatorList"`
	DateGroup                     []*DateGroup                            `xml:"dateGroup"`
	MediaContentGroup             []*MediaContentGroup                    `xml:"mediaContentGroup"`
	ServiceDecriptionInfo         []*InteractiveFreeTextType              `xml:"serviceDecriptionInfo"`
	AgeAggregation                *NumberOfUnitsType                      `xml:"ageAggregation"`
	QuotaGroup                    []*QuotaGroup                           `xml:"quotaGroup"`
	SvcLocation                   []*PlaceLocationIdentificationType      `xml:"svcLocation"`
	BaggageDescriptionGroup       *BaggageDescriptionGroup                `xml:"baggageDescriptionGroup"`
	PricingGroup                  []*PricingGroup                         `xml:"pricingGroup"`
	ErrorGroup                    []*ErrorGroupType                       `xml:"errorGroup"`
}

// ServiceStandaloneCatalogueReply ...
type ServiceStandaloneCatalogueReply struct {
	// XMLName          xml.Name                     `xml:"Service_StandaloneCatalogueReply"`
	ErrorGroup       []*ErrorGroupType            `xml:"errorGroup"`
	PricingIndicator *PricingTicketingDetailsType `xml:"pricingIndicator"`
	PricingDate      *DateAndTimeInformationType  `xml:"pricingDate"`
	PassengerGroup   []*PassengerGroup            `xml:"passengerGroup"`
	FlightInfo       []*FlightInfo                `xml:"flightInfo"`
	Portions         []*Portions                  `xml:"portions"`
	SsrInformation   []*SsrInformation            `xml:"ssrInformation"`
	ServiceGroup     []*ServiceGroup              `xml:"serviceGroup"`
}

// AdditionalProductDetailsTypeI is Leg details
type AdditionalProductDetailsTypeI struct {
	LegDetails *AdditionalProductTypeI `xml:"legDetails"`
}

// AdditionalProductTypeI is Equipment
type AdditionalProductTypeI struct {
	Equipment string `xml:"equipment"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// AttributeInformationTypeU is To identify the type of attribute and the attribute
type AttributeInformationTypeU struct {
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// AttributeInformationType is Attribute description
type AttributeInformationType struct {
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// AttributeType is Criteria details
type AttributeType struct {
	CriteriaSetType string                      `xml:"criteriaSetType"`
	CriteriaDetails []*AttributeInformationType `xml:"criteriaDetails"`
}

// AttributeType208309S is Details for the message criteria (name, value).
type AttributeType208309S struct {
	// XMLName         xml.Name                   `xml:"AttributeType_208309S"`
	CriteriaDetails *AttributeInformationTypeU `xml:"criteriaDetails"`
}

// BaggageDetailsType is Process indicator
type BaggageDetailsType struct {
	FreeAllowance    int32  `xml:"freeAllowance"`
	Measurement      int32  `xml:"measurement"`
	QuantityCode     string `xml:"quantityCode"`
	UnitQualifier    string `xml:"unitQualifier"`
	ProcessIndicator string `xml:"processIndicator"`
}

// CabinClassFeaturesType is Cabin/Class code designator
type CabinClassFeaturesType struct {
	Level       string   `xml:"level"`
	Service     string   `xml:"service"`
	Description string   `xml:"description"`
	Designator  []string `xml:"designator"`
}

// CabinClassServicesType is List of services/features associated to cabin/class
type CabinClassServicesType struct {
	ServiceType    string                    `xml:"serviceType"`
	CabinClassInfo []*CabinClassFeaturesType `xml:"cabinClassInfo"`
}

// CharacteristicDescriptionType is The description of the keyword.
type CharacteristicDescriptionType struct {
	Identification string `xml:"identification"`
	MainDesc       string `xml:"mainDesc"`
	OtherDesc      string `xml:"otherDesc"`
}

// CodedAttributeInformationType is Convey coded key and corresponding value
type CodedAttributeInformationType struct {
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
	Language             string `xml:"language"`
	Encoding             string `xml:"encoding"`
	Source               string `xml:"source"`
}

// CodedAttributeType is Used to have tag value without code list for tag
type CodedAttributeType struct {
	AttributeFunction string                           `xml:"attributeFunction"`
	AttributeDetails  []*CodedAttributeInformationType `xml:"attributeDetails"`
}

// CommercialAgreementsType is To specify commercial agreements between two  or more companies related to joint, shared, lease operations etc.
type CommercialAgreementsType struct {
	CodeshareDetails      *CompanyRoleIdentificationType   `xml:"codeshareDetails"`
	OtherCodeshareDetails []*CompanyRoleIdentificationType `xml:"otherCodeshareDetails"`
}

// CommunicationContactDetailsType is this type is used to identify the url : BO - Boleto FOP
type CommunicationContactDetailsType struct {
	UrlAddress string `xml:"urlAddress"`
	UrlType    string `xml:"urlType"`
}

// CommunicationContactType is Communication channel
type CommunicationContactType struct {
	Communication *CommunicationContactDetailsType `xml:"communication"`
}

// CompanyIdentificationType is Other company
type CompanyIdentificationType struct {
	MarketingCompany string `xml:"marketingCompany"`
	OperatingCompany string `xml:"operatingCompany"`
	OtherCompany     string `xml:"otherCompany"`
}

// CompanyRoleIdentificationType is suffix
type CompanyRoleIdentificationType struct {
	TransportStageQualifier string `xml:"transportStageQualifier"`
	AirlineDesignator       string `xml:"airlineDesignator"`
	FlightNumber            string `xml:"flightNumber"`
	OperationalSuffix       string `xml:"operationalSuffix"`
}

// ConversionRateDetailsType is To specify the conversion rate and the monetary amount.
type ConversionRateDetailsType struct {
	ConversionType          string  `xml:"conversionType"`
	Currency                string  `xml:"currency"`
	RateType                string  `xml:"rateType"`
	PricingAmount           float64 `xml:"pricingAmount"`
	ConvertedValueAmount    float64 `xml:"convertedValueAmount"`
	DutyTaxFeeType          string  `xml:"dutyTaxFeeType"`
	MeasurementValue        float64 `xml:"measurementValue"`
	MeasurementSignificance string  `xml:"measurementSignificance"`
}

// ConversionRateType is To specify details of a conversion rate related to an amount.
type ConversionRateType struct {
	ConversionRateDetails *ConversionRateDetailsType   `xml:"conversionRateDetails"`
	OtherConvRateDetails  []*ConversionRateDetailsType `xml:"otherConvRateDetails"`
}

// DataTypeInformationType is Status (automated, manually added, exempted). Default is automated
type DataTypeInformationType struct {
	SubType string `xml:"subType"`
	Status  string `xml:"status"`
}

// DateAndTimeDetailsType is Location
type DateAndTimeDetailsType struct {
	Qualifier      string `xml:"qualifier"`
	Date           string `xml:"date"`
	Time           string `xml:"time"`
	OtherQualifier string `xml:"otherQualifier"`
	OtherTime      string `xml:"otherTime"`
	MovementType   string `xml:"movementType"`
	Location       string `xml:"location"`
}

// DateAndTimeInformationType is Date and time details
type DateAndTimeInformationType struct {
	DateAndTimeDetails []*DateAndTimeDetailsType `xml:"dateAndTimeDetails"`
}

// DiscountAndPenaltyInformationType is Discount penalty details
type DiscountAndPenaltyInformationType struct {
	DiscountPenaltyQualifier string                                    `xml:"discountPenaltyQualifier"`
	DiscountPenaltyDetails   []*DiscountPenaltyMonetaryInformationType `xml:"discountPenaltyDetails"`
}

// DiscountDataType is Promotion name (free text)
type DiscountDataType struct {
	DiscountGroup []*DiscountGroupType `xml:"discountGroup"`
	DiscountName  []string             `xml:"discountName"`
}

// DiscountGroupType ...
type DiscountGroupType struct {
	StartDate string `xml:"startDate"`
	EndDate   string `xml:"endDate"`
}

// DiscountPenaltyMonetaryInformationType is Currency
type DiscountPenaltyMonetaryInformationType struct {
	Function   string  `xml:"function"`
	AmountType string  `xml:"amountType"`
	Amount     float64 `xml:"amount"`
	Rate       string  `xml:"rate"`
	Currency   string  `xml:"currency"`
}

// ErrorGroupType is The desciption of warning or error.
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType         `xml:"errorWarningDescription"`
}

// ExcessBaggageType is To specify information concerning excess baggage charges and the associated baggage details.
type ExcessBaggageType struct {
	BaggageDetails      *BaggageDetailsType `xml:"baggageDetails"`
	OtherBaggageDetails *BaggageDetailsType `xml:"otherBaggageDetails"`
	ExtraBaggageDetails *BaggageDetailsType `xml:"extraBaggageDetails"`
}

// FreeTextDetailsType is encoding
type FreeTextDetailsType struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
	Status               string `xml:"status"`
	CompanyId            string `xml:"companyId"`
	Language             string `xml:"language"`
	Source               string `xml:"source"`
	Encoding             string `xml:"encoding"`
}

// FreeTextInformationType is Free text and message sequence numbers of the remarks.
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []string             `xml:"freeText"`
}

// FreeTextQualificationType is Add code sets in the list
type FreeTextQualificationType struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
}

// FrequentTravellerIdentificationCodeType is Frequent traveller identification
type FrequentTravellerIdentificationCodeType struct {
	FrequentTravellerDetails *FrequentTravellerIdentificationType `xml:"frequentTravellerDetails"`
}

// FrequentTravellerIdentificationType is type
type FrequentTravellerIdentificationType struct {
	Carrier       string `xml:"carrier"`
	Number        string `xml:"number"`
	TierLevel     string `xml:"tierLevel"`
	PriorityCode  string `xml:"priorityCode"`
	CustomerValue int32  `xml:"customerValue"`
	Type          string `xml:"type"`
}

// InteractiveFreeTextType is Free text
type InteractiveFreeTextType struct {
	FreeTextQualification *FreeTextQualificationType `xml:"freeTextQualification"`
	FreeText              []string                   `xml:"freeText"`
}

// ItemNumberIdentificationType is Type
type ItemNumberIdentificationType struct {
	Number string `xml:"number"`
	Type   string `xml:"type"`
}

// ItemNumberType is Item number details
type ItemNumberType struct {
	ItemNumberDetails []*ItemNumberIdentificationType `xml:"itemNumberDetails"`
}

// ItemReferencesAndVersionsType is Pricing record (TST or PQR) Tattoo
type ItemReferencesAndVersionsType struct {
	ReferenceType   string `xml:"referenceType"`
	UniqueReference int32  `xml:"uniqueReference"`
}

// ItemReferencesAndVersionsType192370S is Exchange and link unique identifiers
type ItemReferencesAndVersionsType192370S struct {
	// XMLName         xml.Name                   `xml:"ItemReferencesAndVersionsType_192370S"`
	UniqueReference int32                      `xml:"uniqueReference"`
	ActionCategory  string                     `xml:"actionCategory"`
	IdSection       []*UniqueIdDescriptionType `xml:"idSection"`
}

// ItemReferencesAndVersionsType192375S is Exchange and link unique identifiers
type ItemReferencesAndVersionsType192375S struct {
	// XMLName         xml.Name                          `xml:"ItemReferencesAndVersionsType_192375S"`
	ReferenceType   string                            `xml:"referenceType"`
	UniqueReference int32                             `xml:"uniqueReference"`
	ActionCategory  string                            `xml:"actionCategory"`
	IdSection       []*UniqueIdDescriptionType269144C `xml:"idSection"`
}

// LocationDetailsTypeI is Country
type LocationDetailsTypeI struct {
	City    string `xml:"city"`
	Country string `xml:"country"`
}

// LocationIdentificationBatchType is Identification of a location by code or name
type LocationIdentificationBatchType struct {
	Code      string `xml:"code"`
	Qualifier string `xml:"qualifier"`
	Agency    string `xml:"agency"`
	Name      string `xml:"name"`
}

// LocationType is True location id
type LocationType struct {
	TrueLocationId string `xml:"trueLocationId"`
}

// MonetaryInformationDetailsType is location
type MonetaryInformationDetailsType struct {
	TypeQualifier string  `xml:"typeQualifier"`
	Amount        float64 `xml:"amount"`
	Currency      string  `xml:"currency"`
	Location      string  `xml:"location"`
}

// MonetaryInformationType is To convey monetary amounts, rates and percentages.
type MonetaryInformationType struct {
	MonetaryDetails      *MonetaryInformationDetailsType   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType `xml:"otherMonetaryDetails"`
}

// NumberOfUnitDetailsType is Unit qualifier
type NumberOfUnitDetailsType struct {
	NumberOfUnit  int32  `xml:"numberOfUnit"`
	UnitQualifier string `xml:"unitQualifier"`
}

// NumberOfUnitsType is Quantity details
type NumberOfUnitsType struct {
	QuantityDetails []*NumberOfUnitDetailsType `xml:"quantityDetails"`
}

// PlaceLocationIdentificationType is To identify a place or a location and/or related locations.
type PlaceLocationIdentificationType struct {
	LocationType          string                                `xml:"locationType"`
	LocationDescription   *LocationIdentificationBatchType      `xml:"locationDescription"`
	FirstLocationDetails  *RelatedLocationOneIdentificationType `xml:"firstLocationDetails"`
	SecondLocationDetails *RelatedLocationTwoIdentificationType `xml:"secondLocationDetails"`
	RelationType          string                                `xml:"relationType"`
}

// PositionIdentificationBatchTypeU is Root level is 0. For each next level, this value is increased by one.
type PositionIdentificationBatchTypeU struct {
	Level string `xml:"level"`
}

// PricingOrTicketingSubsequentType is To convey additional information related to a ticket.
type PricingOrTicketingSubsequentType struct {
	SpecialCondition           string `xml:"specialCondition"`
	OtherSpecialCondition      string `xml:"otherSpecialCondition"`
	AdditionalSpecialCondition string `xml:"additionalSpecialCondition"`
}

// PricingTicketingDetailsType is Selling location details
type PricingTicketingDetailsType struct {
	PriceTicketDetails     *PricingTicketingInformationType `xml:"priceTicketDetails"`
	SellingLocationDetails *LocationDetailsTypeI            `xml:"sellingLocationDetails"`
}

// PricingTicketingInformationType is Indicators like type of fare, sold/ticketed inside/outside, electronic ticketing restrictions, possible restrictions warning, additional information concerning Last Ticketing date
type PricingTicketingInformationType struct {
	Indicators []string `xml:"indicators"`
}

// ProductDateTimeType is Arrival time
type ProductDateTimeType struct {
	DepartureDate string `xml:"departureDate"`
	DepartureTime string `xml:"departureTime"`
	ArrivalDate   string `xml:"arrivalDate"`
	ArrivalTime   string `xml:"arrivalTime"`
}

// ProductDetailsType is Contains the RBD modifier as Night Class
type ProductDetailsType struct {
	Designator         string   `xml:"designator"`
	AvailabilityStatus string   `xml:"availabilityStatus"`
	SpecialService     string   `xml:"specialService"`
	Option             []string `xml:"option"`
}

// ProductIdentificationDetailsType is Booking class
type ProductIdentificationDetailsType struct {
	FlightNumber string `xml:"flightNumber"`
	BookingClass string `xml:"bookingClass"`
}

// ProductInformationType is Booking Code Details
type ProductInformationType struct {
	ProductDetailsQualifier string                `xml:"productDetailsQualifier"`
	BookingClassDetails     []*ProductDetailsType `xml:"bookingClassDetails"`
}

// ProductTypeDetailsType is Booking access identifier
type ProductTypeDetailsType struct {
	FlightIndicator []string `xml:"flightIndicator"`
}

// ProductTypeDetailsType254225C is Flight indicator
type ProductTypeDetailsType254225C struct {
	// XMLName         xml.Name `xml:"ProductTypeDetailsType_254225C"`
	FlightIndicator []string `xml:"flightIndicator"`
}

// QuotaRelatedInformationType is Status which should be replied when quota are reached, for example UN.
type QuotaRelatedInformationType struct {
	Availability            int32  `xml:"availability"`
	QuotaReachedReplyStatus string `xml:"quotaReachedReplyStatus"`
}

// RangeDetailsType is Range of elements
type RangeDetailsType struct {
	RangeQualifier string       `xml:"rangeQualifier"`
	RangeDetails   []*RangeType `xml:"rangeDetails"`
}

// RangeDetailsType191709S is Range of elements
type RangeDetailsType191709S struct {
	// XMLName        xml.Name     `xml:"RangeDetailsType_191709S"`
	RangeQualifier string       `xml:"rangeQualifier"`
	RangeDetails   []*RangeType `xml:"rangeDetails"`
}

// RangeDetailsType208311S is To identify a range.
type RangeDetailsType208311S struct {
	// XMLName      xml.Name          `xml:"RangeDetailsType_208311S"`
	RangeDetails *RangeType288260C `xml:"rangeDetails"`
}

// RangeType is Max
type RangeType struct {
	DataType string  `xml:"dataType"`
	Min      float64 `xml:"min"`
	Max      float64 `xml:"max"`
}

// RangeType288260C is Range minimum and maximum limits.
type RangeType288260C struct {
	// XMLName xml.Name `xml:"RangeType_288260C"`
	Min int32 `xml:"min"`
	Max int32 `xml:"max"`
}

// ReferenceInfoType is Reference details
type ReferenceInfoType struct {
	ReferenceDetails []*ReferencingDetailsType `xml:"referenceDetails"`
	// DummyNET         *DummyNET                 `xml:"Dummy.NET"`
}

// ReferenceInformationType ...
type ReferenceInformationType struct {
	PassengerReference *ReferencingDetailsTypeI `xml:"passengerReference"`
}

// ReferencingDetailsTypeI ...
type ReferencingDetailsTypeI struct {
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsType ...
type ReferencingDetailsType struct {
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// RelatedLocationOneIdentificationType ...
type RelatedLocationOneIdentificationType struct {
	Code      string `xml:"code"`
	Qualifier string `xml:"qualifier"`
	Agency    string `xml:"agency"`
	Name      string `xml:"name"`
}

// RelatedLocationTwoIdentificationType ...
type RelatedLocationTwoIdentificationType struct {
	Code      string `xml:"code"`
	Qualifier string `xml:"qualifier"`
	Agency    string `xml:"agency"`
	Name      string `xml:"name"`
}

// SpecialRequirementsDataDetailsType ...
type SpecialRequirementsDataDetailsType struct {
	SeatNumber           string   `xml:"seatNumber"`
	MeasureUnitQualifier string   `xml:"measureUnitQualifier"`
	CrossRef             string   `xml:"crossRef"`
	SeatCharacteristics  []string `xml:"seatCharacteristics"`
}

// SpecialRequirementsDetailsType ...
type SpecialRequirementsDetailsType struct {
	SpecialRequirementsInfo *SpecialRequirementsTypeDetailsType245333C `xml:"specialRequirementsInfo"`
	SeatDetails             []*SpecialRequirementsDataDetailsType      `xml:"seatDetails"`
}

// SpecialRequirementsDetailsType174527S ...
type SpecialRequirementsDetailsType174527S struct {
	// XMLName                 xml.Name                              `xml:"SpecialRequirementsDetailsType_174527S"`
	SpecialRequirementsInfo *SpecialRequirementsTypeDetailsType   `xml:"specialRequirementsInfo"`
	SeatDetails             []*SpecialRequirementsDataDetailsType `xml:"seatDetails"`
}

// SpecialRequirementsQuotaType ...
type SpecialRequirementsQuotaType struct {
	QuotaInfo *QuotaRelatedInformationType `xml:"quotaInfo"`
}

// SpecialRequirementsTypeDetailsType ...
type SpecialRequirementsTypeDetailsType struct {
	SsrCode          string   `xml:"ssrCode"`
	ActionCode       string   `xml:"actionCode"`
	NumberInParty    int32    `xml:"numberInParty"`
	AirlineCode      string   `xml:"airlineCode"`
	ServiceType      string   `xml:"serviceType"`
	OtherServiceType string   `xml:"otherServiceType"`
	BoardPoint       string   `xml:"boardPoint"`
	OffPoint         string   `xml:"offPoint"`
	ServiceFreeText  []string `xml:"serviceFreeText"`
}

// SpecialRequirementsTypeDetailsType245333C ...
type SpecialRequirementsTypeDetailsType245333C struct {
	// XMLName          xml.Name `xml:"SpecialRequirementsTypeDetailsType_245333C"`
	SsrCode          string   `xml:"ssrCode"`
	ActionCode       string   `xml:"actionCode"`
	NumberInParty    int32    `xml:"numberInParty"`
	AirlineCode      string   `xml:"airlineCode"`
	ServiceType      string   `xml:"serviceType"`
	OtherServiceType string   `xml:"otherServiceType"`
	ServiceFreeText  []string `xml:"serviceFreeText"`
}

// SpecificDataInformationType ...
type SpecificDataInformationType struct {
	DataTypeInformation *DataTypeInformationType `xml:"dataTypeInformation"`
}

// StatusDetailsType ...
type StatusDetailsType struct {
	Indicator   string `xml:"indicator"`
	Action      string `xml:"action"`
	Type        string `xml:"type"`
	Description string `xml:"description"`
}

// StatusType ...
type StatusType struct {
	StatusInformation []*StatusDetailsType `xml:"statusInformation"`
}

// StructureComponentDefinitionType ...
type StructureComponentDefinitionType struct {
	Function    string                                `xml:"function"`
	ComponentId *StructureComponentIdentificationType `xml:"componentId"`
	Status      string                                `xml:"status"`
	Position    *PositionIdentificationBatchTypeU     `xml:"position"`
	Description *CharacteristicDescriptionType        `xml:"description"`
}

// StructureComponentIdentificationType ...
type StructureComponentIdentificationType struct {
	Identifier string `xml:"identifier"`
}

// StructuredDateTimeInformationType ...
type StructuredDateTimeInformationType struct {
	BusinessSemantic string                    `xml:"businessSemantic"`
	TimeMode         string                    `xml:"timeMode"`
	DateTime         *StructuredDateTimeType   `xml:"dateTime"`
	TimeZoneInfo     *TimeZoneIinformationType `xml:"timeZoneInfo"`
}

// StructuredDateTimeType ...
type StructuredDateTimeType struct {
	Year         string `xml:"year"`
	Month        string `xml:"month"`
	Day          string `xml:"day"`
	Hour         string `xml:"hour"`
	Minutes      string `xml:"minutes"`
	Seconds      int32  `xml:"seconds"`
	Milliseconds int32  `xml:"milliseconds"`
}

// TaxDetailsType ...
type TaxDetailsType struct {
	Rate         string   `xml:"rate"`
	CountryCode  string   `xml:"countryCode"`
	CurrencyCode string   `xml:"currencyCode"`
	Type         []string `xml:"type"`
}

// TaxType ...
type TaxType struct {
	TaxCategory string            `xml:"taxCategory"`
	TaxDetails  []*TaxDetailsType `xml:"taxDetails"`
}

// TimeZoneIinformationType ...
type TimeZoneIinformationType struct {
	CountryCode string `xml:"countryCode"`
	Code        int32  `xml:"code"`
	Suffix      string `xml:"suffix"`
}

// TravelItineraryInformationTypeI ...
type TravelItineraryInformationTypeI struct {
	CabinDesignator    string                         `xml:"cabinDesignator"`
	ProductTypeDetails *ProductTypeDetailsType254225C `xml:"productTypeDetails"`
}

// TravelProductInformationType ...
type TravelProductInformationType struct {
	FlightDate           *ProductDateTimeType              `xml:"flightDate"`
	BoardPointDetails    *LocationType                     `xml:"boardPointDetails"`
	OffpointDetails      *LocationType                     `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationType        `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsType `xml:"flightIdentification"`
	FlightTypeDetails    *ProductTypeDetailsType           `xml:"flightTypeDetails"`
	ItemNumber           int32                             `xml:"itemNumber"`
}

// UniqueIdDescriptionType ...
type UniqueIdDescriptionType struct {
	ReferenceQualifier string `xml:"referenceQualifier"`
	PrimeId            string `xml:"primeId"`
}

// UniqueIdDescriptionType269144C ...
type UniqueIdDescriptionType269144C struct {
	// XMLName            xml.Name `xml:"UniqueIdDescriptionType_269144C"`
	SystemQualifier    string `xml:"systemQualifier"`
	VersionNumber      string `xml:"versionNumber"`
	ReferenceQualifier string `xml:"referenceQualifier"`
	PrimeId            string `xml:"primeId"`
	SecondaryId        string `xml:"secondaryId"`
	Description        string `xml:"description"`
}

// NumericDecimalLength1To35 ...
type NumericDecimalLength1To35 float64

// NumericStringLength1To35 ...
type NumericStringLength1To35 string

// NumericDecimalLength1To18 ...
type NumericDecimalLength1To18 float64
