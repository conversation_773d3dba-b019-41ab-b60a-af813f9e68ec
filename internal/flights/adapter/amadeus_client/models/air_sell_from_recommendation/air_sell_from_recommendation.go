package air_sell_from_recommendation

import "encoding/xml"

// ErrorAtMessageLevel ...
type ErrorAtMessageLevel struct {
	XMLName         xml.Name                         `xml:"errorAtMessageLevel"`
	ErrorSegment    *ApplicationErrorInformationType `xml:"errorSegment"`
	InformationText *InteractiveFreeTextTypeI        `xml:"informationText"`
}

// ErrorItinerarylevel ...
type ErrorItinerarylevel struct {
	XMLName         xml.Name                         `xml:"errorItinerarylevel"`
	ErrorSegment    *ApplicationErrorInformationType `xml:"errorSegment"`
	InformationText *InteractiveFreeTextTypeI        `xml:"informationText"`
}

// ErrorAtSegmentLevel ...
type ErrorAtSegmentLevel struct {
	XMLName         xml.Name                         `xml:"errorAtSegmentLevel"`
	ErrorSegment    *ApplicationErrorInformationType `xml:"errorSegment"`
	InformationText *InteractiveFreeTextTypeI        `xml:"informationText"`
}

// SegmentInformation ...
type SegmentInformation struct {
	XMLName             xml.Name                        `xml:"segmentInformation"`
	FlightDetails       *TravelProductInformationTypeI  `xml:"flightDetails"`
	ApdSegment          *AdditionalProductDetailsTypeI  `xml:"apdSegment"`
	ActionDetails       *RelatedProductInformationTypeI `xml:"actionDetails"`
	InformationText     *InteractiveFreeTextTypeI       `xml:"informationText"`
	ErrorAtSegmentLevel []*ErrorAtSegmentLevel          `xml:"errorAtSegmentLevel"`
}

// ItineraryDetails ...
type ItineraryDetails struct {
	XMLName             xml.Name                          `xml:"itineraryDetails"`
	OriginDestination   *OriginAndDestinationDetailsTypeI `xml:"originDestination"`
	ErrorItinerarylevel []*ErrorItinerarylevel            `xml:"errorItinerarylevel"`
	SegmentInformation  []*SegmentInformation             `xml:"segmentInformation"`
}

// AirSellFromRecommendationReply ...
type AirSellFromRecommendationReply struct {
	XMLName             xml.Name                   `xml:"Air_SellFromRecommendationReply"`
	Message             *MessageActionDetailsTypeI `xml:"message"`
	ErrorAtMessageLevel []*ErrorAtMessageLevel     `xml:"errorAtMessageLevel"`
	ItineraryDetails    []*ItineraryDetails        `xml:"itineraryDetails"`
}

// AdditionalProductDetailsTypeI is Facilities on board.
type AdditionalProductDetailsTypeI struct {
	LegDetails            *AdditionalProductTypeI   `xml:"legDetails"`
	DepartureStationInfo  *StationInformationTypeI  `xml:"departureStationInfo"`
	ArrivalStationInfo    *StationInformationTypeI  `xml:"arrivalStationInfo"`
	FacilitiesInformation []*ProductFacilitiesTypeI `xml:"facilitiesInformation"`
}

// AdditionalProductTypeI is Day of operation. (1 to 7)
type AdditionalProductTypeI struct {
	Equipment                 string   `xml:"equipment"`
	NumberOfStops             int      `xml:"numberOfStops"`
	Duration                  int      `xml:"duration"`
	Percentage                int      `xml:"percentage"`
	DaysOfOperation           string   `xml:"daysOfOperation"`
	DateTimePeriod            string   `xml:"dateTimePeriod"`
	ComplexingFlightIndicator string   `xml:"complexingFlightIndicator"`
	Locations                 []string `xml:"locations"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// CompanyIdentificationTypeI is This will be the carrier code. ex: AF
type CompanyIdentificationTypeI struct {
	MarketingCompany string `xml:"marketingCompany"`
	OperatingCompany string `xml:"operatingCompany"`
	OtherCompany     string `xml:"otherCompany"`
}

// FreeTextQualificationTypeI is Language, coded.
type FreeTextQualificationTypeI struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
	Status               string `xml:"status"`
	CompanyId            string `xml:"companyId"`
	Language             string `xml:"language"`
}

// InteractiveFreeTextTypeI is Free text itself.
type InteractiveFreeTextTypeI struct {
	FreeTextQualification *FreeTextQualificationTypeI `xml:"freeTextQualification"`
	FreeText              []string                    `xml:"freeText"`
}

// LocationTypeI is This is the Board/Off point. ex: NCE
type LocationTypeI struct {
	TrueLocationId string `xml:"trueLocationId"`
	TrueLocation   string `xml:"trueLocation"`
}

// MarriageControlDetailsTypeI is Sequence number within the marriage.
type MarriageControlDetailsTypeI struct {
	Relation           string `xml:"relation"`
	MarriageIdentifier int    `xml:"marriageIdentifier"`
	LineNumber         int    `xml:"lineNumber"`
	OtherRelation      string `xml:"otherRelation"`
	CarrierCode        string `xml:"carrierCode"`
}

// MessageActionDetailsTypeI is This is the response type.
type MessageActionDetailsTypeI struct {
	MessageFunctionDetails *MessageFunctionBusinessDetailsTypeI `xml:"messageFunctionDetails"`
	ResponseType           string                               `xml:"responseType"`
}

// MessageFunctionBusinessDetailsTypeI is This will be needed in case if in the future, master pricer will need to peform a more accurate algorithm.
type MessageFunctionBusinessDetailsTypeI struct {
	BusinessFunction          string   `xml:"businessFunction"`
	MessageFunction           string   `xml:"messageFunction"`
	ResponsibleAgency         string   `xml:"responsibleAgency"`
	AdditionalMessageFunction []string `xml:"additionalMessageFunction"`
}

// OriginAndDestinationDetailsTypeI is Place/Location Identification.
type OriginAndDestinationDetailsTypeI struct {
	Origin      string `xml:"origin"`
	Destination string `xml:"destination"`
}

// ProductDateTimeTypeI is This is the date discrepancy between departure and arrival date.
type ProductDateTimeTypeI struct {
	DepartureDate string `xml:"departureDate"`
	DepartureTime int    `xml:"departureTime"`
	ArrivalDate   string `xml:"arrivalDate"`
	ArrivalTime   int    `xml:"arrivalTime"`
	DateVariation int    `xml:"dateVariation"`
}

// ProductFacilitiesTypeI is Extendion code for facility.
type ProductFacilitiesTypeI struct {
	Code          string   `xml:"code"`
	Description   string   `xml:"description"`
	Qualifier     string   `xml:"qualifier"`
	ExtensionCode []string `xml:"extensionCode"`
}

// ProductIdentificationDetailsTypeI is Dominance indicator. Used for marriage.
type ProductIdentificationDetailsTypeI struct {
	FlightNumber      string   `xml:"flightNumber"`
	BookingClass      string   `xml:"bookingClass"`
	OperationalSuffix string   `xml:"operationalSuffix"`
	Modifier          []string `xml:"modifier"`
}

// ProductTypeDetailsTypeI is It gives some functional information on the flight. ETK candidate etc...
type ProductTypeDetailsTypeI struct {
	FlightIndicator []string `xml:"flightIndicator"`
}

// RelatedProductInformationTypeI is Status, Coded
type RelatedProductInformationTypeI struct {
	Quantity   int      `xml:"quantity"`
	StatusCode []string `xml:"statusCode"`
}

// StationInformationTypeI is Terminal information.
type StationInformationTypeI struct {
	GateDescription string `xml:"gateDescription"`
	Terminal        string `xml:"terminal"`
	Concourse       string `xml:"concourse"`
}

// TravelProductInformationTypeI is Marriage Control Details.
type TravelProductInformationTypeI struct {
	FlightDate           *ProductDateTimeTypeI              `xml:"flightDate"`
	BoardPointDetails    *LocationTypeI                     `xml:"boardPointDetails"`
	OffpointDetails      *LocationTypeI                     `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationTypeI        `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsTypeI `xml:"flightIdentification"`
	FlightTypeDetails    *ProductTypeDetailsTypeI           `xml:"flightTypeDetails"`
	SpecialSegment       string                             `xml:"specialSegment"`
	MarriageDetails      []*MarriageControlDetailsTypeI     `xml:"marriageDetails"`
}

// AlphaNumericStringLength1To3 is Format limitations: an..3
type AlphaNumericStringLength1To3 string

// AlphaNumericStringLength1To70 is Format limitations: an..70
type AlphaNumericStringLength1To70 string

// NumericIntegerLength1To15 is Format limitations: n..15
type NumericIntegerLength1To15 int

// AlphaNumericStringLength1To5 is Format limitations: an..5
type AlphaNumericStringLength1To5 string

// AlphaNumericStringLength1To4 is Format limitations: an..4
type AlphaNumericStringLength1To4 string

// AlphaNumericStringLength1To35 is Format limitations: an..35
type AlphaNumericStringLength1To35 string

// NumericIntegerLength1To4 is Format limitations: n..4
type NumericIntegerLength1To4 int

// NumericIntegerLength1To1 is Format limitations: n1
type NumericIntegerLength1To1 int

// AlphaNumericStringLength1To25 is Format limitations: an..25
type AlphaNumericStringLength1To25 string

// AlphaNumericStringLength1To17 is Format limitations: an..17
type AlphaNumericStringLength1To17 string

// AlphaNumericStringLength1To2 is Format limitations: an..2
type AlphaNumericStringLength1To2 string

// AlphaNumericStringLength1To7 is Format limitations: an..7
type AlphaNumericStringLength1To7 string

// AlphaNumericStringLength1To6 is Format limitations: an..6
type AlphaNumericStringLength1To6 string

// NumericIntegerLength1To10 is Format limitations: n..10
type NumericIntegerLength1To10 int

// NumericIntegerLength1To6 is Format limitations: n..6
type NumericIntegerLength1To6 int

// AlphaNumericStringLength1To8 is Format limitations: an..8
type AlphaNumericStringLength1To8 string

// NumericIntegerLength1To3 is Format limitations: n..3
type NumericIntegerLength1To3 int

// NumericIntegerLength1To8 is Format limitations: n..8
type NumericIntegerLength1To8 int

// AlphaNumericStringLength1To1 is Format limitations: an1
type AlphaNumericStringLength1To1 string
