package create_form_of_payment

// MopDetails ...
type MopDetails struct {
	//XMLName              xml.Name                           `xml:"mopDetails"`
	FopPNRDetails        *TicketingFormOfPaymentType223002S `xml:"fopPNRDetails"`
	OldFopFreeflow       *FreeTextInformationType202263S    `xml:"oldFopFreeflow"`
	PnrSupplementaryData []*PNRSupplementaryDataType        `xml:"pnrSupplementaryData"`
}

// MopDescription ...
type MopDescription struct {
	//XMLName                   xml.Name                  `xml:"mopDescription"`
	FopSequenceNumber         *SequenceDetailsType      `xml:"fopSequenceNumber"`
	FopMasterElementReference *ReferenceInfoType        `xml:"fopMasterElementReference"`
	StakeholderPayerReference *ReferenceInformationType `xml:"stakeholderPayerReference"`
	MopDetails                *MopDetails               `xml:"mopDetails"`
	MopElementError           *ErrorGroupType           `xml:"mopElementError"`
	PaymentModule             *PaymentGroupType         `xml:"paymentModule"`
}

// FopDescription ...
type FopDescription struct {
	//XMLName                xml.Name                        `xml:"fopDescription"`
	FopReference           *ElementManagementSegmentType   `xml:"fopReference"`
	PassengerAssociation   []*ReferenceInformationType     `xml:"passengerAssociation"`
	PnrElementAssociation  []*ReferenceInfoType190198S     `xml:"pnrElementAssociation"`
	AdditionalMonetaryData *CodedAttributeType             `xml:"additionalMonetaryData"`
	FreeFlowFop            *FreeTextInformationType202263S `xml:"freeFlowFop"`
	FpElementError         *ErrorGroupType96609G           `xml:"fpElementError"`
	MopDescription         []*MopDescription               `xml:"mopDescription"`
}

// FOPCreateFormOfPaymentReply ...
type FOPCreateFormOfPaymentReply struct {
	//XMLName           xml.Name              `xml:"FOP_CreateFormOfPaymentReply"`
	TransmissionError *ErrorGroupType96608G `xml:"transmissionError"`
	FopDescription    []*FopDescription     `xml:"fopDescription"`
}

// AccountHolderInformationTypeI is To specify frequent traveller cardholder information.
type AccountHolderInformationTypeI struct {
	Surname     string `xml:"surname"`
	GivenName   string `xml:"givenName"`
	Institution string `xml:"institution"`
}

// AdditionalFareQualifierDetailsType is To specify the fare basis and ticket designator codes.
type AdditionalFareQualifierDetailsType struct {
	RateClass         string   `xml:"rateClass"`
	CommodityCategory string   `xml:"commodityCategory"`
	PricingGroup      string   `xml:"pricingGroup"`
	SecondRateClass   []string `xml:"secondRateClass"`
}

// AddressDetailsTypeU is Address Text. Any of the following address lines may start with a tag: Door number- Street- ExternalNumber- InternalNumber- County- Neighbourhood- State-
type AddressDetailsTypeU struct {
	Format string `xml:"format"`
	Line1  string `xml:"line1"`
	Line2  string `xml:"line2"`
	Line3  string `xml:"line3"`
	Line4  string `xml:"line4"`
	Line5  string `xml:"line5"`
	Line6  string `xml:"line6"`
}

// AddressType is To convey a sub-entity within a country : region, states..
type AddressType struct {
	AddressDetails *AddressDetailsTypeU                 `xml:"addressDetails"`
	City           string                              `xml:"city"`
	ZipCode        string                              `xml:"zipCode"`
	CountryCode    string                              `xml:"countryCode"`
	RegionDetails  *CountrySubEntityDetailsTypeU305993C `xml:"regionDetails"`
}

// AddressType68622S is Country code. ISO 3166 code for the country
type AddressType68622S struct {
	//XMLName        xml.Name                       `xml:"AddressType_68622S"`
	AddressDetails *AddressDetailsTypeU `xml:"addressDetails"`
	City           string              `xml:"city"`
	ZipCode        string              `xml:"zipCode"`
	CountryCode    string              `xml:"countryCode"`
}

// AddressType68644S is details of the shipping adress
type AddressType68644S struct {
	//XMLName             xml.Name                       `xml:"AddressType_68644S"`
	AddressUsageDetails *AddressUsageTypeU            `xml:"addressUsageDetails"`
	AddressDetails      *AddressDetailsTypeU          `xml:"addressDetails"`
	City                string                       `xml:"city"`
	ZipCode             string                       `xml:"zipCode"`
	RegionDetails       *CountrySubEntityDetailsTypeU `xml:"regionDetails"`
	LocationDetails     *LocationIdentificationTypeU  `xml:"locationDetails"`
}

// AddressType68661S is City name.
type AddressType68661S struct {
	//XMLName xml.Name                       `xml:"AddressType_68661S"`
	City string `xml:"city"`
}

// AddressUsageTypeU is Status of the street adress on file with the payment provider(Paypal for eg)
type AddressUsageTypeU struct {
	Status string `xml:"status"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorDetailType118902C is Identification of a code list.
type ApplicationErrorDetailType118902C struct {
	//XMLName       xml.Name                      `xml:"ApplicationErrorDetailType_118902C"`
	ErrorCode     string `xml:"errorCode"`
	ErrorCategory string `xml:"errorCategory"`
}

// ApplicationErrorDetailType122445C is Code identifying the agency responsible for a code list. 1A Amadeus PP Paypal
type ApplicationErrorDetailType122445C struct {
	//XMLName        xml.Name                      `xml:"ApplicationErrorDetailType_122445C"`
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorDetailType220899C is Identification of a code list.
type ApplicationErrorDetailType220899C struct {
	//XMLName       xml.Name                      `xml:"ApplicationErrorDetailType_220899C"`
	ErrorCode     string `xml:"errorCode"`
	ErrorCategory string `xml:"errorCategory"`
}

// ApplicationErrorDetailType297562C is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType297562C struct {
	//XMLName        xml.Name                      `xml:"ApplicationErrorDetailType_297562C"`
	ErrorCode      string `xml:"errorCode"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// ApplicationErrorInformationType202262S is Application error details.
type ApplicationErrorInformationType202262S struct {
	//XMLName      xml.Name                           `xml:"ApplicationErrorInformationType_202262S"`
	ErrorDetails *ApplicationErrorDetailType220899C `xml:"errorDetails"`
}

// ApplicationErrorInformationType215551S is Application error details.
type ApplicationErrorInformationType215551S struct {
	//XMLName      xml.Name                           `xml:"ApplicationErrorInformationType_215551S"`
	ErrorDetails *ApplicationErrorDetailType297562C `xml:"errorDetails"`
}

// ApplicationErrorInformationType76949S is Application error details.
type ApplicationErrorInformationType76949S struct {
	//XMLName      xml.Name                           `xml:"ApplicationErrorInformationType_76949S"`
	ErrorDetails *ApplicationErrorDetailType118902C `xml:"errorDetails"`
}

// ApplicationErrorInformationType79911S is Application error details.
type ApplicationErrorInformationType79911S struct {
	//XMLName      xml.Name                           `xml:"ApplicationErrorInformationType_79911S"`
	ErrorDetails *ApplicationErrorDetailType122445C `xml:"errorDetails"`
}

// AsynchPaymentGroupType is Stores account number and expiry date
type AsynchPaymentGroupType struct {
	AsunchronousPaymentDetails *FormOfPaymentTypeI69625S `xml:"asunchronousPaymentDetails"`
}

// AsynchPaymentStatusGroupType is URL provided by bank/ PSP link in case of asynchronous payment.
type AsynchPaymentStatusGroupType struct {
	ApprovalReferenceNumber *GenericAuthorisationResultType  `xml:"approvalReferenceNumber"`
	AsyncPaymentUrl         *CommunicationContactType202097S `xml:"asyncPaymentUrl"`
}

// AttributeInformationTypeU is This is the data value.  This may contain:  - a potential attribute of the switch  - the value of the structured data of the FOP
type AttributeInformationTypeU struct {
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// AttributeInformationTypeU107107C is will conatin the formatted error message
type AttributeInformationTypeU107107C struct {
	//XMLName              xml.Name                        `xml:"AttributeInformationTypeU_107107C"`
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// AttributeInformationTypeU297559C is will contain the value of the data  Sale Indicator:  "I" for online and Internet sales. "A" for Call center and IVR sales. "P" for Kiosk sales. "S" for Swipe. "E" Offline Travel agency sales. "S" for Swipe. "T" for Telephone.  ExtendedPayment: Y YES N NO  PaymentType: CREDIT  DEBIT
type AttributeInformationTypeU297559C struct {
	//XMLName              xml.Name                        `xml:"AttributeInformationTypeU_297559C"`
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// AttributeType is Details for the message criteria (name, value).
type AttributeType struct {
	CriteriaSetType string                      `xml:"criteriaSetType"`
	CriteriaDetails []*AttributeInformationTypeU `xml:"criteriaDetails"`
}

// AttributeType202070S is Details for the message criteria (name, value).
type AttributeType202070S struct {
	//XMLName         xml.Name                      `xml:"AttributeType_202070S"`
	CriteriaSetType string                      `xml:"criteriaSetType"`
	CriteriaDetails []*AttributeInformationTypeU `xml:"criteriaDetails"`
}

// AttributeType202252S is List of attributes and status linked to credit card process. Most of them are link dependant.
type AttributeType202252S struct {
	//XMLName         xml.Name                     `xml:"AttributeType_202252S"`
	CriteriaSetType string                    `xml:"criteriaSetType"`
	CriteriaDetails *AttributeInformationTypeU `xml:"criteriaDetails"`
}

// AttributeType215546S is Details for the message criteria (name, value).
type AttributeType215546S struct {
	//XMLName         xml.Name                            `xml:"AttributeType_215546S"`
	CriteriaSetType string                             `xml:"criteriaSetType"`
	CriteriaDetails []*AttributeInformationTypeU297559C `xml:"criteriaDetails"`
}

// AttributeType215552S is Details for the message criteria (name, value).
type AttributeType215552S struct {
	//XMLName         xml.Name                            `xml:"AttributeType_215552S"`
	CriteriaDetails []*AttributeInformationTypeU107107C `xml:"criteriaDetails"`
}

// AuthenticationDataType is Transaction status (authentication). Values : Y : authentication successful N : authentication failed U : unable to authenticate A : attempt processing performed C : challenge requested D : decoupled challenge requested R : authentication rejected, do not authorize I : information only
type AuthenticationDataType struct {
	Veres                   string `xml:"veres"`
	Pares                   string `xml:"pares"`
	CreditCardCompany       string `xml:"creditCardCompany"`
	AuthenticationIndicator string `xml:"authenticationIndicator"`
	CaavAlgorithm           string `xml:"caavAlgorithm"`
	TransStatus             string `xml:"transStatus"`
}

// AuthorizationApprovalDataType is Source of approval for the payment authorisation. A Automatically obtained by the system. M Manually entered by an agent.
type AuthorizationApprovalDataType struct {
	ApprovalCode     string `xml:"approvalCode"`
	SourceOfApproval string `xml:"sourceOfApproval"`
}

// BinaryDataType is used to store binary data
type BinaryDataType struct {
	DataLength string `xml:"dataLength"`
	DataType   string `xml:"dataType"`
	BinaryData string `xml:"binaryData"`
}

// BinaryDataType222902S is Contains the compression type used. Either ZLIB or NONE. If not present, should be considered as NONE
type BinaryDataType222902S struct {
	//XMLName      xml.Name                          `xml:"BinaryDataType_222902S"`
	DataLength   string `xml:"dataLength"`
	DataType     string `xml:"dataType"`
	BinaryData   string `xml:"binaryData"`
	CompressType string `xml:"compressType"`
}

// BrowserInformationType is Indicates the type of cardholder device.
type BrowserInformationType struct {
	DeviceCategory string `xml:"deviceCategory"`
}

// BrowserInformationType222662S is indicates whether the browser has Java activated or not Y - yes N - no U - unknown
type BrowserInformationType222662S struct {
	//XMLName             xml.Name                         `xml:"BrowserInformationType_222662S"`
	DeviceCategory      string                 `xml:"deviceCategory"`
	UserAgentHeader     string                 `xml:"userAgentHeader"`
	AcceptHeader        string                 `xml:"acceptHeader"`
	IetfLanguageTag     string                 `xml:"ietfLanguageTag"`
	ScreenDimensions    *DimensionsDetailsTypeI `xml:"screenDimensions"`
	ColorDepth          *ValueRangeTypeI        `xml:"colorDepth"`
	LocalDateTime       *StructuredDateTimeType `xml:"localDateTime"`
	IsCookieEnabled     string                 `xml:"isCookieEnabled"`
	IsJavaScriptEnabled string                 `xml:"isJavaScriptEnabled"`
	IsJavaEnabled       string                 `xml:"isJavaEnabled"`
}

// BusinessSectorsType is Other travel sector
type BusinessSectorsType struct {
	PrimaryTravelSector string   `xml:"primaryTravelSector"`
	OtherTravelSector   []string `xml:"otherTravelSector"`
}

// CodedAttributeInformationType is provides the attribute Type
type CodedAttributeInformationType struct {
	AttributeType string `xml:"attributeType"`
}

// CodedAttributeInformationType220870C is onoData   Order Number(Qantas specific)  gwtData   Government Warrant number  ccHolderName  Conveys the CN   onoRequired  YES - NO  gwtRequired   YES - NO  cind    MANU - AUTO  bestFareCandidate   YES - NO
type CodedAttributeInformationType220870C struct {
	//XMLName              xml.Name                        `xml:"CodedAttributeInformationType_220870C"`
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// CodedAttributeInformationType266039C is if the option from data element Indicator is configurable, this data element contains the parameter.
type CodedAttributeInformationType266039C struct {
	//XMLName              xml.Name                        `xml:"CodedAttributeInformationType_266039C"`
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// CodedAttributeInformationType274155C is Type of the attribute
type CodedAttributeInformationType274155C struct {
	//XMLName       xml.Name                      `xml:"CodedAttributeInformationType_274155C"`
	AttributeType string `xml:"attributeType"`
}

// CodedAttributeType is provides details for the Attribute
type CodedAttributeType struct {
	AttributeDetails *CodedAttributeInformationType `xml:"attributeDetails"`
}

// CodedAttributeType196207S is Used to have tag value without code list for tag
type CodedAttributeType196207S struct {
	//XMLName          xml.Name                                `xml:"CodedAttributeType_196207S"`
	AttributeDetails []*CodedAttributeInformationType274155C `xml:"attributeDetails"`
}

// CodedAttributeType197625S is Used to store the qualifier of the exchange rate information.
type CodedAttributeType197625S struct {
	//XMLName          xml.Name                                `xml:"CodedAttributeType_197625S"`
	AttributeDetails []*CodedAttributeInformationType266039C `xml:"attributeDetails"`
}

// CodedAttributeType202071S is provides details for the Attribute
type CodedAttributeType202071S struct {
	//XMLName           xml.Name                                `xml:"CodedAttributeType_202071S"`
	AttributeFunction string                                 `xml:"attributeFunction"`
	AttributeDetails  []*CodedAttributeInformationType220870C `xml:"attributeDetails"`
}

// CommunicationContactDetailsTypeU is will be AH for World Wide Web
type CommunicationContactDetailsTypeU struct {
	InternetAddress string `xml:"internetAddress"`
	AdressQualifier string `xml:"adressQualifier"`
}

// CommunicationContactDetailsTypeU107103C is URL Address: identifier possible value is: - R: return url
type CommunicationContactDetailsTypeU107103C struct {
	//XMLName    xml.Name                        `xml:"CommunicationContactDetailsTypeU_107103C"`
	UrlAddress string `xml:"urlAddress"`
	UrlType    string `xml:"urlType"`
}

// CommunicationContactDetailsTypeU107110C is will be AH for World Wide Web
type CommunicationContactDetailsTypeU107110C struct {
	//XMLName       xml.Name                        `xml:"CommunicationContactDetailsTypeU_107110C"`
	Url           string `xml:"url"`
	CodeQualifier string `xml:"codeQualifier"`
}

// CommunicationContactDetailsTypeU129344C is URL Address: identifier  possible values are: - R: return url - C: cancel url - U: customize url
type CommunicationContactDetailsTypeU129344C struct {
	//XMLName    xml.Name                         `xml:"CommunicationContactDetailsTypeU_129344C"`
	UrlAddress string `xml:"urlAddress"`
	UrlType    string `xml:"urlType"`
}

// CommunicationContactDetailsTypeU274202C is Communication number of a department or employee in a specified channel.
type CommunicationContactDetailsTypeU274202C struct {
	//XMLName    xml.Name                         `xml:"CommunicationContactDetailsTypeU_274202C"`
	UrlAddress string `xml:"urlAddress"`
	UrlType    string `xml:"urlType"`
}

// CommunicationContactDetailsType is this type is used to identify the url : BO - Boleto FOP
type CommunicationContactDetailsType struct {
	UrlAddress string `xml:"urlAddress"`
	UrlType    string `xml:"urlType"`
}

// CommunicationContactDetailsType306099C is Communication identifier such as URL address.
type CommunicationContactDetailsType306099C struct {
	//XMLName         xml.Name                         `xml:"CommunicationContactDetailsType_306099C"`
	InternetAddress string `xml:"internetAddress"`
	AdressQualifier string `xml:"adressQualifier"`
}

// CommunicationContactType is Describes communication channel
type CommunicationContactType struct {
	UrlDetails *CommunicationContactDetailsTypeU274202C `xml:"urlDetails"`
}

// CommunicationContactType202087S is Communication channel
type CommunicationContactType202087S struct {
	//XMLName       xml.Name                                 `xml:"CommunicationContactType_202087S"`
	Communication *CommunicationContactDetailsTypeU107103C `xml:"communication"`
}

// CommunicationContactType202097S is Communication channel
type CommunicationContactType202097S struct {
	//XMLName       xml.Name                         `xml:"CommunicationContactType_202097S"`
	Communication *CommunicationContactDetailsType `xml:"communication"`
}

// CommunicationContactType202237S is Communication channel
type CommunicationContactType202237S struct {
	//XMLName       xml.Name                          `xml:"CommunicationContactType_202237S"`
	Communication *CommunicationContactDetailsTypeU `xml:"communication"`
}

// CommunicationContactType202254S is Communication channel
type CommunicationContactType202254S struct {
	//XMLName       xml.Name                                 `xml:"CommunicationContactType_202254S"`
	Communication *CommunicationContactDetailsTypeU129344C `xml:"communication"`
}

// CommunicationContactType215555S is Communication channel
type CommunicationContactType215555S struct {
	//XMLName       xml.Name                                 `xml:"CommunicationContactType_215555S"`
	Communication *CommunicationContactDetailsTypeU107110C `xml:"communication"`
}

// CommunicationContactType222766S is Communication channel
type CommunicationContactType222766S struct {
	//XMLName       xml.Name                                `xml:"CommunicationContactType_222766S"`
	Communication *CommunicationContactDetailsType306099C `xml:"communication"`
}

// CompanyIdentificationTypeI is Code or name to identify a company and any associated companies.
type CompanyIdentificationTypeI struct {
	MarketingCompany string `xml:"marketingCompany"`
	OperatingCompany string `xml:"operatingCompany"`
	OtherCompany     string `xml:"otherCompany"`
}

// CompanyInformationType is This data element is used to convey the numeric merchant ID.
type CompanyInformationType struct {
	CompanyCode        string `xml:"companyCode"`
	CompanyNumericCode string `xml:"companyNumericCode"`
}

// CompanyInformationType202069S is This data element is used to convey the company name of a company IDEAL PAYPAL ...
type CompanyInformationType202069S struct {
	//XMLName     xml.Name                       `xml:"CompanyInformationType_202069S"`
	CompanyName string `xml:"companyName"`
}

// CompanyInformationType40602S is This data element is used to convey the company code of merchant of the authorization transaction. (Airline code for an airline ...)  Ex: AF for Air France
type CompanyInformationType40602S struct {
	//XMLName     xml.Name                       `xml:"CompanyInformationType_40602S"`
	CompanyCode string `xml:"companyCode"`
}

// CountryInformationTypeU is Country code.  Locale of pages displayed by Paypal during Express Checkout. Character length and limitations: Amy two char country code.  The following two-chars are supported by PayPal: AT, AU, Be, CA, CH, CN, DE, ES, FR, GB, IT, NL, PL and US. Default is US.
type CountryInformationTypeU struct {
	CountryNameCode string `xml:"countryNameCode"`
}

// CountrySubEntityDetailsTypeU is name of the region
type CountrySubEntityDetailsTypeU struct {
	Code string `xml:"code"`
	Name string `xml:"name"`
}

// CountrySubEntityDetailsTypeU305993C is Name of the state, the province, or any other applicable type of main country subdivisions.
type CountrySubEntityDetailsTypeU305993C struct {
	//XMLName   xml.Name                       `xml:"CountrySubEntityDetailsTypeU_305993C"`
	Code      string `xml:"code"`
	Qualifier string `xml:"qualifier"`
	Agency    string `xml:"agency"`
	Name      string `xml:"name"`
}

// CouponInformationDetailsTypeI is segment tattoo associated to this coupon
type CouponInformationDetailsTypeI struct {
	CpnNumber         int    `xml:"cpnNumber"`
	CpnStatus         string `xml:"cpnStatus"`
	CpnSequenceNumber int    `xml:"cpnSequenceNumber"`
}

// CouponInformationDetailsType is To specify the coupon number, status, value, and other related information.
type CouponInformationDetailsType struct {
	CpnNumber                    string `xml:"cpnNumber"`
	CpnStatus                    string `xml:"cpnStatus"`
	CpnAmount                    string `xml:"cpnAmount"`
	CpnExchangeMedia             string `xml:"cpnExchangeMedia"`
	SettlementAuthorization      string `xml:"settlementAuthorization"`
	VoluntaryIndic               string `xml:"voluntaryIndic"`
	CpnPreviousStatus            string `xml:"cpnPreviousStatus"`
	CpnSequenceNumber            string `xml:"cpnSequenceNumber"`
	CpnReferenceNumber           string `xml:"cpnReferenceNumber"`
	CpnInConnectionWithQualifier string `xml:"cpnInConnectionWithQualifier"`
}

// CouponInformationTypeI is Conveys coupon details
type CouponInformationTypeI struct {
	CouponDetails      *CouponInformationDetailsTypeI   `xml:"couponDetails"`
	OtherCouponDetails []*CouponInformationDetailsTypeI `xml:"otherCouponDetails"`
}

// CouponInformationType is To identify data specific to a value (flight) coupon.
type CouponInformationType struct {
	CouponDetails      *CouponInformationDetailsType   `xml:"couponDetails"`
	OtherCouponDetails []*CouponInformationDetailsType `xml:"otherCouponDetails"`
}

// VirtualCreditCardData is Used to specify the optional(s) validity date(s) of the Virtual Credit Card
type VirtualCreditCardData struct {
	//XMLName                     xml.Name                         `xml:"virtualCreditCardData"`
	VirtualCreditCardParameters *VirtualCardParametersType       `xml:"virtualCreditCardParameters"`
	ValidityDate                *StructuredPeriodInformationType `xml:"validityDate"`
}

// CreditCardDataGroupType is Contains card holder's address information.
type CreditCardDataGroupType struct {
	CreditCardDetails     *CreditCardDataType         `xml:"creditCardDetails"`
	FortknoxIds           []*ReferenceInfoType190166S `xml:"fortknoxIds"`
	CardHolderAddress     *AddressType68622S          `xml:"cardHolderAddress"`
	VirtualCreditCardData *VirtualCreditCardData      `xml:"virtualCreditCardData"`
}

// CreditCardDataType is specify credit card data for credit card authorization - USED IN CCVRQT
type CreditCardDataType struct {
	CcInfo *CreditCardInformationType `xml:"ccInfo"`
}

// CreditCardInformationType is The tier level (gold, platinium, etc...) can be used for fraud or authorization processes.
type CreditCardInformationType struct {
	VendorCode            string `xml:"vendorCode"`
	VendorCodeSubType     string `xml:"vendorCodeSubType"`
	CardNumber            string `xml:"cardNumber"`
	SecurityId            string `xml:"securityId"`
	ExpiryDate            string `xml:"expiryDate"`
	StartDate             string `xml:"startDate"`
	EndDate               string `xml:"endDate"`
	CcHolderName          string `xml:"ccHolderName"`
	IssuingBankName       string `xml:"issuingBankName"`
	CardCountryOfIssuance string `xml:"cardCountryOfIssuance"`
	IssueNumber           string `xml:"issueNumber"`
	IssuingBankLongName   string `xml:"issuingBankLongName"`
	Track1                string `xml:"track1"`
	Track2                string `xml:"track2"`
	Track3                string `xml:"track3"`
	PinCode               string `xml:"pinCode"`
	RawTrackData          string `xml:"rawTrackData"`
	TierLevel             string `xml:"tierLevel"`
}

// CreditCardSecurityType is Conveys all data of authentication process. Only used today for "Verified by Visa" process
type CreditCardSecurityType struct {
	AuthenticationDataDetails *AuthenticationDataType `xml:"authenticationDataDetails"`
	TdsVersion                string                 `xml:"tdsVersion"`
}

// BrowserData is Contains in freeflow format data about the customer's browser. - userAgent - acceptHeaders This entities are independantly optional.
type BrowserData struct {
	//XMLName             xml.Name                          `xml:"browserData"`
	BrowserProperties   *BrowserInformationType           `xml:"browserProperties"`
	FreeFlowBrowserData []*FreeTextInformationType215562S `xml:"freeFlowBrowserData"`
}

// SchemeAuthenticationData ...
type SchemeAuthenticationData struct {
	//XMLName           xml.Name                      `xml:"schemeAuthenticationData"`
	SchemeCompany     *CompanyInformationType40602S `xml:"schemeCompany"`
	SchemeDataElement []*AttributeType215546S       `xml:"schemeDataElement"`
}

// CreditCardStatusGroupType is Transaction Information: - type of authorization message submit for the given FOP - bulk, superbulk, no bulk process - STAN number (identifying a pair of Credit Card authorization request/response).
type CreditCardStatusGroupType struct {
	AuthorisationSupplementaryData *SpecificVisaLinkCreditCardInformationType  `xml:"authorisationSupplementaryData"`
	ApprovalDetails                *GenericAuthorisationResultType             `xml:"approvalDetails"`
	LocalDateTime                  []*StructuredDateTimeInformationType202270S `xml:"localDateTime"`
	AuthorisationInformation       *TransactionInformationForTicketingType     `xml:"authorisationInformation"`
	BrowserData                    *BrowserData                                `xml:"browserData"`
	TdsInformation                 *ThreeDomainSecureGroupType                 `xml:"tdsInformation"`
	CardSupplementaryData          []*AttributeType202252S                     `xml:"cardSupplementaryData"`
	TransactionStatus              []*ErrorGroupType253648G                    `xml:"transactionStatus"`
	SchemeAuthenticationData       *SchemeAuthenticationData                   `xml:"schemeAuthenticationData"`
}

// CurrenciesType is To specify currencies used in the transaction and relevant details for the rate of exchange.
type CurrenciesType struct {
	FirstCurrencyDetails  *CurrencyDetailsTypeU `xml:"firstCurrencyDetails"`
	SecondCurrencyDetails *CurrencyDetailsTypeU `xml:"secondCurrencyDetails"`
	ExchangeRate          float64               `xml:"exchangeRate"`
}

// CurrencyDetailsTypeU is The usage to which a currency relates
type CurrencyDetailsTypeU struct {
	CurrencyQualifier string `xml:"currencyQualifier"`
	CurrencyIsoCode   string `xml:"currencyIsoCode"`
	CurrencyType      string `xml:"currencyType"`
	CurrencyRate      float64 `xml:"currencyRate"`
}

// VirtualCreditCardStatusGroup is Contains the adress associated to the Virtual Credit Card for AVS verification
type VirtualCreditCardStatusGroup struct {
	//XMLName                     xml.Name                          `xml:"virtualCreditCardStatusGroup"`
	VirtualCreditCardParameters *VirtualCardParametersType175504S `xml:"virtualCreditCardParameters"`
	VirtualCreditCardData       *CreditCardDataType               `xml:"virtualCreditCardData"`
	FortknoxIds                 []*ReferenceInfoType190166S       `xml:"fortknoxIds"`
	VCCAssociatedAdress         *AddressType68622S                `xml:"vCCAssociatedAdress"`
}

// GroupAmopProcess ...
type GroupAmopProcess struct {
	//XMLName         xml.Name                   `xml:"groupAmopProcess"`
	GroupUsage      *CodedAttributeType196207S `xml:"groupUsage"`
	AmopProcessData []*AttributeType215546S    `xml:"amopProcessData"`
}

// AmopPayload ...
type AmopPayload struct {
	//XMLName           xml.Name                     `xml:"amopPayload"`
	BinaryDescription *MessageStructureVersionType `xml:"binaryDescription"`
	BinaryData        *BinaryDataType222902S       `xml:"binaryData"`
}

// AmopGroupUrl ...
type AmopGroupUrl struct {
	//XMLName             xml.Name                           `xml:"amopGroupUrl"`
	GroupUsage          *CodedAttributeType196207S         `xml:"groupUsage"`
	Url                 *CommunicationContactType          `xml:"url"`
	TransactionDateTime *StructuredDateTimeInformationType `xml:"transactionDateTime"`
	AmopUrlData         []*AttributeType215546S            `xml:"amopUrlData"`
	AmopPayload         []*AmopPayload                     `xml:"amopPayload"`
}

// GroupAmopContext ...
type GroupAmopContext struct {
	//XMLName         xml.Name                       `xml:"groupAmopContext"`
	ClientTokenId   *ItemReferencesAndVersionsType `xml:"clientTokenId"`
	AmopContextData []*AttributeType215546S        `xml:"amopContextData"`
	Communication   []*CommunicationContactType    `xml:"communication"`
}

// GroupAmopParameters ...
type GroupAmopParameters struct {
	//XMLName           xml.Name                   `xml:"groupAmopParameters"`
	GroupUsage        *CodedAttributeType196207S `xml:"groupUsage"`
	AmopParameterData []*AttributeType215546S    `xml:"amopParameterData"`
	AmopGroupUrl      []*AmopGroupUrl            `xml:"amopGroupUrl"`
	GroupAmopContext  *GroupAmopContext          `xml:"groupAmopContext"`
}

// AmopDetailedData ...
type AmopDetailedData struct {
	//XMLName             xml.Name                            `xml:"amopDetailedData"`
	StepDefinition      *CodedAttributeType196207S          `xml:"stepDefinition"`
	MessageVersion      *MessageStructureVersionType        `xml:"messageVersion"`
	PaymentDataMap      []*AttributeType215546S             `xml:"paymentDataMap"`
	GroupAmopProcess    *GroupAmopProcess                   `xml:"groupAmopProcess"`
	Dummy               *DummySegmentTypeI                  `xml:"dummy"`
	GroupAmopParameters *GroupAmopParameters                `xml:"groupAmopParameters"`
	TransactionResult   *ResponseAnalysisDetailsType202257S `xml:"transactionResult"`
	ErrorGroup          []*ErrorGroupType                   `xml:"errorGroup"`
}

// DetailedPaymentDataType is This group will convey the detailed data of the payment done via a web payment provider using accounts. First exple Paypal
type DetailedPaymentDataType struct {
	FopInformation               *FormOfPaymentTypeI191756S    `xml:"fopInformation"`
	FundTransferDetailledData    *DetailedWebFundTransferType  `xml:"fundTransferDetailledData"`
	AsyncDetailledDataGroup      *AsynchPaymentStatusGroupType `xml:"asyncDetailledDataGroup"`
	Dummy                        *DummySegmentTypeI            `xml:"dummy"`
	InvoiceDetailedDataGroup     *InvoiceValidationGroupType   `xml:"invoiceDetailedDataGroup"`
	CreditCardDetailedData       *CreditCardStatusGroupType    `xml:"creditCardDetailedData"`
	WebAccountDetailledData      *WebAccountSuppDataGroupType  `xml:"webAccountDetailledData"`
	VirtualCreditCardStatusGroup *VirtualCreditCardStatusGroup `xml:"virtualCreditCardStatusGroup"`
	AmopDetailedData             *AmopDetailedData             `xml:"amopDetailedData"`
}

// RedirectionData is URL to which the customer will be redirected to
type RedirectionData struct {
	//XMLName       xml.Name                         `xml:"redirectionData"`
	AcquirerId    *ReferenceInfoType215556S        `xml:"acquirerId"`
	IssuerURLData *CommunicationContactType215555S `xml:"issuerURLData"`
}

// ConsumerData ...
type ConsumerData struct {
	//XMLName         xml.Name                         `xml:"consumerData"`
	ConsumerName    *TravellerInformationType215558S `xml:"consumerName"`
	ConsumerAccount *ReferenceInfoType215557S        `xml:"consumerAccount"`
	ConsumerAddress *AddressType68661S               `xml:"consumerAddress"`
}

// DetailedWebFundTransferType is will convey the merchant's website URL, in order for the PSP to know where to come back once payment authorisation has been done.
type DetailedWebFundTransferType struct {
	MerchantSiteLanguage *LanguageBatchTypeU                 `xml:"merchantSiteLanguage"`
	ReturnURL            *CommunicationContactType202087S    `xml:"returnURL"`
	RedirectionData      *RedirectionData                    `xml:"redirectionData"`
	ConsumerData         *ConsumerData                       `xml:"consumerData"`
	TransferStatus       *ResponseAnalysisDetailsType202257S `xml:"transferStatus"`
	ErrorGroup           []*PaymentErrorGroupType            `xml:"errorGroup"`
}

// DeviceControlDetailsType is Stores the identification of the device.
type DeviceControlDetailsType struct {
	DeviceIdentification *IdentificationNumberTypeI `xml:"deviceIdentification"`
}

// DimensionsDetailsTypeI is Height of the screen
type DimensionsDetailsTypeI struct {
	Unit   string `xml:"unit"`
	Width  string `xml:"width"`
	Height string `xml:"height"`
}

// DiscountPenaltyInformationType ...
type DiscountPenaltyInformationType struct {
	FareQualifier string `xml:"fareQualifier"`
	RateCategory  string `xml:"rateCategory"`
	Amount        float64 `xml:"amount"`
	Percentage    int    `xml:"percentage"`
}

// DistributionChannelType is Access type field.
type DistributionChannelType struct {
	DistributionChannelField string `xml:"distributionChannelField"`
	SubGroup                 string `xml:"subGroup"`
	AccessType               string `xml:"accessType"`
}

// DocumentDetailsTypeI is Document number : correspond to invoice or credit note.
type DocumentDetailsTypeI struct {
	Number string `xml:"number"`
}

// DocumentInformationDetailsType is Document information
type DocumentInformationDetailsType struct {
	DocumentDetails *DocumentDetailsTypeI `xml:"documentDetails"`
}

// DummySegmentTypeI is To serve the purpose of a mandatory segment at the beginning of a group and to avoid segment collision.
type DummySegmentTypeI struct {
}

// ElementManagementSegmentType is To specify the PNR segments/elements references and action to apply
type ElementManagementSegmentType struct {
	Reference *ReferencingDetailsType261040C `xml:"reference"`
}

// EncoderCapacityType ...
type EncoderCapacityType struct {
	PrimaryOutputEncoder string `xml:"primaryOutputEncoder"`
	OtherOutputEncoder   string `xml:"otherOutputEncoder"`
}

// ErrorGroupType is The description of warning or error.
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType202240S  `xml:"errorWarningDescription"`
}

// ErrorGroupType253647G is The desciption of warning or error.
type ErrorGroupType253647G struct {
	//XMLName                   xml.Name                         `xml:"ErrorGroupType_253647G"`
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType         `xml:"errorWarningDescription"`
}

// ErrorGroupType253648G is The desciption of warning or error.
type ErrorGroupType253648G struct {
	//XMLName                   xml.Name                                `xml:"ErrorGroupType_253648G"`
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType202262S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType202263S         `xml:"errorWarningDescription"`
}

// ErrorGroupType302085G is The desciption of warning or error.
type ErrorGroupType302085G struct {
	//XMLName                   xml.Name                               `xml:"ErrorGroupType_302085G"`
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType79911S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType215562S        `xml:"errorWarningDescription"`
}

// ErrorGroupType96608G is The desciption of warning or error.
type ErrorGroupType96608G struct {
	//XMLName                   xml.Name                               `xml:"ErrorGroupType_96608G"`
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType76949S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType202263S        `xml:"errorWarningDescription"`
}

// ErrorGroupType96609G is The desciption of warning or error.
type ErrorGroupType96609G struct {
	//XMLName                   xml.Name                               `xml:"ErrorGroupType_96609G"`
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType76949S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType202240S        `xml:"errorWarningDescription"`
}

// FareCategoryCodesType is To designate non-system specific combinations of fare types.
type FareCategoryCodesType struct {
	FareType      string   `xml:"fareType"`
	OtherFareType []string `xml:"otherFareType"`
}

// FareDetailsType ...
type FareDetailsType struct {
	Qualifier    string `xml:"qualifier"`
	Rate         int    `xml:"rate"`
	Country      string `xml:"country"`
	FareCategory string `xml:"fareCategory"`
}

// FareQualifierDetailsType ...
type FareQualifierDetailsType struct {
	MovementType          string                             `xml:"movementType"`
	FareCategories        *FareCategoryCodesType              `xml:"fareCategories"`
	FareDetails           *FareDetailsType                    `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareQualifierDetailsType `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountPenaltyInformationType   `xml:"discountDetails"`
}

// FormOfPaymentDetailsTypeI is Details pin code type (in case of encrypted pin code)
type FormOfPaymentDetailsTypeI struct {
	Type             string `xml:"type"`
	Indicator        string `xml:"indicator"`
	MerchantCode     string `xml:"merchantCode"`
	ExpiryDate       string `xml:"expiryDate"`
	CustomerAccount  string `xml:"customerAccount"`
	MembershipStatus string `xml:"membershipStatus"`
	TransactionInfo  string `xml:"transactionInfo"`
	PinCode          string `xml:"pinCode"`
	PinCodeType      string `xml:"pinCodeType"`
}

// FormOfPaymentDetailsTypeI268332C is Generic type of the Mean of Payment used : CC credit Card CA cash CH cheque WW web payment... INV invoice
type FormOfPaymentDetailsTypeI268332C struct {
	//XMLName xml.Name                       `xml:"FormOfPaymentDetailsTypeI_268332C"`
	Type string `xml:"type"`
}

// FormOfPaymentDetailsTypeI66014C is Stores the payer account number
type FormOfPaymentDetailsTypeI66014C struct {
	//XMLName         xml.Name                       `xml:"FormOfPaymentDetailsTypeI_66014C"`
	Type            string `xml:"type"`
	ProviderCode    string `xml:"providerCode"`
	ExpiryDate      string `xml:"expiryDate"`
	CustomerAccount string `xml:"customerAccount"`
}

// FormOfPaymentDetailsType is Details pin code type (in case of encrypted pin code)
type FormOfPaymentDetailsType struct {
	Type                string `xml:"type"`
	Indicator           string `xml:"indicator"`
	VendorCode          string `xml:"vendorCode"`
	CreditCardNumber    string `xml:"creditCardNumber"`
	ExpiryDate          string `xml:"expiryDate"`
	ApprovalCode        string `xml:"approvalCode"`
	SourceOfApproval    string `xml:"sourceOfApproval"`
	AddressVerification string `xml:"addressVerification"`
	CustomerAccount     string `xml:"customerAccount"`
	ExtendedPayment     string `xml:"extendedPayment"`
	FopFreeText         string `xml:"fopFreeText"`
	MembershipStatus    string `xml:"membershipStatus"`
	TransactionInfo     string `xml:"transactionInfo"`
	PinCode             string `xml:"pinCode"`
	PinCodeType         string `xml:"pinCodeType"`
}

// FormOfPaymentDetailsType266040C is To specify the form of payment type, amounts, approval codes, and other related information.
type FormOfPaymentDetailsType266040C struct {
	//XMLName xml.Name                       `xml:"FormOfPaymentDetailsType_266040C"`
	Type string `xml:"type"`
}

// FormOfPaymentInformationType is Fop is a old / new fop.
type FormOfPaymentInformationType struct {
	FopCode   string `xml:"fopCode"`
	FopStatus string `xml:"fopStatus"`
}

// FormOfPaymentInformationType282478C is This is the FOP electronic ticketing code. This is used to classify any FOP from the FOP table and also to determine how the FOP should be transmitted to the airline concerned. Based on this, the absence of the switch would make the FOP disallowed for ETKT, for National System Ticketing Server Travel Agency locations and all Central Ticketing offices  (@ET value)
type FormOfPaymentInformationType282478C struct {
	//XMLName                 xml.Name                       `xml:"FormOfPaymentInformationType_282478C"`
	FopCode                 string `xml:"fopCode"`
	FopMapTable             string `xml:"fopMapTable"`
	FopBillingCode          string `xml:"fopBillingCode"`
	AccountHolderNumber     string `xml:"accountHolderNumber"`
	SaleIndicator           string `xml:"saleIndicator"`
	DealNumber              string `xml:"dealNumber"`
	CollectingAgencyIataNum int    `xml:"collectingAgencyIataNum"`
	ServiceChargeIndicator  string `xml:"serviceChargeIndicator"`
	FopStatus               string `xml:"fopStatus"`
	FopEdiCode              string `xml:"fopEdiCode"`
	FopReportingCode        string `xml:"fopReportingCode"`
	FopPrintedCode          string `xml:"fopPrintedCode"`
	FopElecTicketingCode    string `xml:"fopElecTicketingCode"`
}

// FormOfPaymentInformationType306313C is This is the FOP electronic ticketing code. This is used to classify any FOP from the FOP table and also to determine how the FOP should be transmitted to the airline concerned. Based on this, the absence of the switch would make the FOP disallowed for ETKT, for National System Ticketing Server Travel Agency locations and all Central Ticketing offices  (@ET value)
type FormOfPaymentInformationType306313C struct {
	//XMLName              xml.Name                       `xml:"FormOfPaymentInformationType_306313C"`
	FopCode              string `xml:"fopCode"`
	FopMapTable          string `xml:"fopMapTable"`
	FopBillingCode       string `xml:"fopBillingCode"`
	FopStatus            string `xml:"fopStatus"`
	FopEdiCode           string `xml:"fopEdiCode"`
	FopReportingCode     string `xml:"fopReportingCode"`
	FopPrintedCode       string `xml:"fopPrintedCode"`
	FopElecTicketingCode string `xml:"fopElecTicketingCode"`
}

// FormOfPaymentTypeI is Contains the Account data, the transaction data and financial data
type FormOfPaymentTypeI struct {
	FormOfPayment      *FormOfPaymentDetailsTypeI   `xml:"formOfPayment"`
	OtherFormOfPayment []*FormOfPaymentDetailsTypeI `xml:"otherFormOfPayment"`
}

// FormOfPaymentTypeI191756S is Generic status(new/old) and type(cash, cheque, card...) of the MOP
type FormOfPaymentTypeI191756S struct {
	//XMLName       xml.Name                          `xml:"FormOfPaymentTypeI_191756S"`
	FormOfPayment *FormOfPaymentDetailsTypeI268332C `xml:"formOfPayment"`
}

// FormOfPaymentTypeI69625S is Stores account number and expiry date
type FormOfPaymentTypeI69625S struct {
	//XMLName       xml.Name                         `xml:"FormOfPaymentTypeI_69625S"`
	FormOfPayment *FormOfPaymentDetailsTypeI66014C `xml:"formOfPayment"`
}

// FormOfPaymentType is Details on the form of payment
type FormOfPaymentType struct {
	FormOfPayment *FormOfPaymentDetailsType266040C `xml:"formOfPayment"`
}

// FormOfPaymentType202076S is Details on the form of payment
type FormOfPaymentType202076S struct {
	//XMLName            xml.Name                    `xml:"FormOfPaymentType_202076S"`
	FormOfPayment      *FormOfPaymentDetailsType   `xml:"formOfPayment"`
	OtherFormOfPayment []*FormOfPaymentDetailsType `xml:"otherFormOfPayment"`
}

// ShopperDetails is Defines if the shopper has been able to log on the airline's account or not
type ShopperDetails struct {
	//XMLName       xml.Name                `xml:"shopperDetails"`
	ShopperID     *UserIdentificationType `xml:"shopperID"`
	ShopperLogged *StatusType             `xml:"shopperLogged"`
}

// SecurityCode ...
type SecurityCode struct {
	//XMLName      xml.Name               `xml:"securityCode"`
	SecurityType *SecurityScreeningType `xml:"securityType"`
	SecurityKey  *BinaryDataType        `xml:"securityKey"`
}

// FraudScreeningGroupType is Defines if the shopper is also a traveller or not.
type FraudScreeningGroupType struct {
	FraudScreening     *StatusType68675S                         `xml:"fraudScreening"`
	PointOfService     []*PointOfSaleInformationType             `xml:"pointOfService"`
	PosOperations      *PointOfServicesOperationsType            `xml:"posOperations"`
	PaymentTerminal    *PaymentDeviceTerminal                    `xml:"paymentTerminal"`
	IpAdress           *DeviceControlDetailsType                 `xml:"ipAdress"`
	MerchantURL        *CommunicationContactType202237S          `xml:"merchantURL"`
	PayerPhoneOrEmail  []*PhoneAndEmailAddressType222736S        `xml:"payerPhoneOrEmail"`
	BrowserInformation *BrowserInformationType222662S            `xml:"browserInformation"`
	ShopperSession     *SystemDetailsInfoType                    `xml:"shopperSession"`
	PayerName          *TravellerInformationType202251S          `xml:"payerName"`
	PayerDateOfBirth   *StructuredDateTimeInformationType202247S `xml:"payerDateOfBirth"`
	BillingAddress     *AddressType                              `xml:"billingAddress"`
	FormOfIdDetails    []*ReferenceInfoType202246S               `xml:"formOfIdDetails"`
	TravelShopper      *StatusType                               `xml:"travelShopper"`
	ShopperDetails     *ShopperDetails                           `xml:"shopperDetails"`
	SecurityCode       []*SecurityCode                           `xml:"securityCode"`
}

// FreeTextDetailsType is ZZZ mutually agreed
type FreeTextDetailsType struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	Source               string `xml:"source"`
	Encoding             string `xml:"encoding"`
}

// FreeTextDetailsType220880C ...
type FreeTextDetailsType220880C struct {
	//XMLName              xml.Name                       `xml:"FreeTextDetailsType_220880C"`
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
	Status               string `xml:"status"`
	CompanyId            string `xml:"companyId"`
	Language             string `xml:"language"`
	Source               string `xml:"source"`
	Encoding             string `xml:"encoding"`
}

// FreeTextDetailsType297567C is encoding
type FreeTextDetailsType297567C struct {
	//XMLName              xml.Name                      `xml:"FreeTextDetailsType_297567C"`
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
	Source               string `xml:"source"`
	Encoding             string `xml:"encoding"`
}

// FreeTextInformationType is Free text and message sequence numbers of the remarks.
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType220880C `xml:"freeTextDetails"`
	FreeText        []string                   `xml:"freeText"`
}

// FreeTextInformationType202240S is Purchase free text description
type FreeTextInformationType202240S struct {
	//XMLName         xml.Name                          `xml:"FreeTextInformationType_202240S"`
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []string            `xml:"freeText"`
}

// FreeTextInformationType202263S is Free text and message sequence numbers of the remarks.
type FreeTextInformationType202263S struct {
	//XMLName         xml.Name                        `xml:"FreeTextInformationType_202263S"`
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        string              `xml:"freeText"`
}

// FreeTextInformationType215562S is Free text and message sequence numbers of the remarks.
type FreeTextInformationType215562S struct {
	//XMLName         xml.Name                        `xml:"FreeTextInformationType_215562S"`
	FreeTextDetails *FreeTextDetailsType297567C `xml:"freeTextDetails"`
	FreeText        string                     `xml:"freeText"`
}

// FrequencyDetailsTypeU is indicates extended payment start date format
type FrequencyDetailsTypeU struct {
	InstalmentsNumber         int    `xml:"instalmentsNumber"`
	InstalmentsFrequency      string `xml:"instalmentsFrequency"`
	InstalmentsStartDate      string `xml:"instalmentsStartDate"`
	InstalmentsDatrDateFormat string `xml:"instalmentsDatrDateFormat"`
}

// FrequencyTypeU is extended payment characteristics
type FrequencyTypeU struct {
	ExtendedPaymentDetails *FrequencyDetailsTypeU `xml:"extendedPaymentDetails"`
}

// FrequencyType is Used to represent days of the week or days of the month. For week : 1 is monday and 7 is sunday. For month : 1 is the first day of the month.
type FrequencyType struct {
	Qualifier string   `xml:"qualifier"`
	Value     []string `xml:"value"`
}

// FrequentTravellerVerificationType is To provide frequent traveller information for mileage accrual and account updating.
type FrequentTravellerVerificationType struct {
	ActionRequest        string                        `xml:"actionRequest"`
	Tier                 string                        `xml:"tier"`
	CompanyDetails       *CompanyIdentificationTypeI    `xml:"companyDetails"`
	AccountDetails       *ProductAccountDetailsType     `xml:"accountDetails"`
	OtherProductsDetails *ProductAccountDetailsType     `xml:"otherProductsDetails"`
	DateDetails          *ValidDateInformationTypeI     `xml:"dateDetails"`
	AccountHolderDetails *AccountHolderInformationTypeI `xml:"accountHolderDetails"`
}

// GenericAuthorisationResultType is transaction authorization approval data
type GenericAuthorisationResultType struct {
	ApprovalCodeData *AuthorizationApprovalDataType `xml:"approvalCodeData"`
}

// GenericAuthorisationResultType202258S is transaction authorization approval data
type GenericAuthorisationResultType202258S struct {
	//XMLName          xml.Name                       `xml:"GenericAuthorisationResultType_202258S"`
	ApprovalCodeData *AuthorizationApprovalDataType `xml:"approvalCodeData"`
}

// IdentificationNumberTypeI is will contain IP for IP address
type IdentificationNumberTypeI struct {
	Address   string `xml:"address"`
	Qualifier string `xml:"qualifier"`
}

// IdentificationNumberTypeI306019C is Goods item identification number.
type IdentificationNumberTypeI306019C struct {
	//XMLName          xml.Name                       `xml:"IdentificationNumberTypeI_306019C"`
	DeviceIdentifier string `xml:"deviceIdentifier"`
	Qualifier        string `xml:"qualifier"`
}

// InformationTypeDataTypeU is Element used to convey rules data type.
type InformationTypeDataTypeU struct {
	Identification string `xml:"identification"`
}

// FopInformationGroup is Amount paid by other FOP
type FopInformationGroup struct {
	//XMLName              xml.Name                           `xml:"fopInformationGroup"`
	FopInformation       *TicketingFormOfPaymentType203357S `xml:"fopInformation"`
	FopInformationAmount *MonetaryInformationType203358S    `xml:"fopInformationAmount"`
}

// ConjunctiveTicketGroup ...
type ConjunctiveTicketGroup struct {
	//XMLName                   xml.Name                  `xml:"conjunctiveTicketGroup"`
	ConjunctiveFlag           *StatusType141910S        `xml:"conjunctiveFlag"`
	ConjunctiveDocumentNumber *TicketNumberType         `xml:"conjunctiveDocumentNumber"`
	CouponStatus              []*CouponInformationTypeI `xml:"couponStatus"`
}

// FareComponentDetails ...
type FareComponentDetails struct {
	//XMLName             xml.Name                   `xml:"fareComponentDetails"`
	FareComponentId     *ReferenceInfoType202078S  `xml:"fareComponentId"`
	CouponStatus        []*CouponInformationType   `xml:"couponStatus"`
	MonetaryInformation []*MonetaryInformationType `xml:"monetaryInformation"`
}

// UnusedTicketCoupons ...
type UnusedTicketCoupons struct {
	//XMLName           xml.Name                      `xml:"unusedTicketCoupons"`
	FareBasis         *FareQualifierDetailsType     `xml:"fareBasis"`
	FlightInformation *TravelProductInformationType `xml:"flightInformation"`
	CouponStatus      *CouponInformationType        `xml:"couponStatus"`
}

// FopParentTicketGroup ...
type FopParentTicketGroup struct {
	//XMLName              xml.Name                          `xml:"fopParentTicketGroup"`
	FopDetails           *FormOfPaymentType202076S         `xml:"fopDetails"`
	FopInformation       *TicketingFormOfPaymentType       `xml:"fopInformation"`
	MonetaryInformation  []*MonetaryInformationType202074S `xml:"monetaryInformation"`
	OldFopFreeflow       *FreeTextInformationType          `xml:"oldFopFreeflow"`
	ApprovalDetails      *GenericAuthorisationResultType   `xml:"approvalDetails"`
	Status               *StatusType156547S                `xml:"status"`
	PnrSupplementaryData []*PNRSupplementaryDataType       `xml:"pnrSupplementaryData"`
}

// ParentTicketGroup ...
type ParentTicketGroup struct {
	//XMLName                xml.Name                                  `xml:"parentTicketGroup"`
	DocumentNumber         *TicketNumberType                         `xml:"documentNumber"`
	MonetaryInformation    []*MonetaryInformationType202074S         `xml:"monetaryInformation"`
	TaxInformation         []*TaxType                                `xml:"taxInformation"`
	ConjunctiveTicketGroup []*ConjunctiveTicketGroup                 `xml:"conjunctiveTicketGroup"`
	OriginatorInfo         *OriginatorOfRequestDetailsTypeI          `xml:"originatorInfo"`
	EDocrecordLocator      *ReservationControlInformationType167717S `xml:"eDocrecordLocator"`
	RecipientNameSurname   *TravellerInformationType                 `xml:"recipientNameSurname"`
	FrequentFlyerNumber    *FrequentTravellerVerificationType        `xml:"frequentFlyerNumber"`
	Dates                  []*StructuredDateTimeInformationType      `xml:"dates"`
	AdditionalInformation  []*FreeTextInformationType                `xml:"additionalInformation"`
	FareComponentDetails   []*FareComponentDetails                   `xml:"fareComponentDetails"`
	UnusedTicketCoupons    []*UnusedTicketCoupons                    `xml:"unusedTicketCoupons"`
	FopParentTicketGroup   []*FopParentTicketGroup                   `xml:"fopParentTicketGroup"`
}

// DateDetails ...
type DateDetails struct {
	//XMLName xml.Name                                `xml:"dateDetails"`
	Date *StructuredPeriodInformationType202063S `xml:"date"`
}

// PassengerDetails ...
type PassengerDetails struct {
	//XMLName              xml.Name                  `xml:"passengerDetails"`
	RecipientNameSurname *TravellerInformationType `xml:"recipientNameSurname"`
}

// FlightDetails ...
type FlightDetails struct {
	//XMLName           xml.Name                      `xml:"flightDetails"`
	FlightInformation *TravelProductInformationType `xml:"flightInformation"`
}

// RuleList ...
type RuleList struct {
	//XMLName          xml.Name                  `xml:"ruleList"`
	RuleType         *InformationTypeDataTypeU `xml:"ruleType"`
	DateDetails      []*DateDetails            `xml:"dateDetails"`
	PassengerDetails []*PassengerDetails       `xml:"passengerDetails"`
	FlightDetails    []*FlightDetails          `xml:"flightDetails"`
	OtherDetails     *StatusType141910S        `xml:"otherDetails"`
}

// InvoiceFopGroupType is Stores award code and I/U qualifier
type InvoiceFopGroupType struct {
	InvoiceInformation       *FormOfPaymentTypeI                `xml:"invoiceInformation"`
	Routing                  *RoutingInformationTypeI           `xml:"routing"`
	IruQualifier             []*StatusType68646S                `xml:"iruQualifier"`
	FopInformationGroup      []*FopInformationGroup             `xml:"fopInformationGroup"`
	AccountSupplementaryData []*AttributeType                   `xml:"accountSupplementaryData"`
	BookingReference         *ReservationControlInformationType `xml:"bookingReference"`
	ParentTicketGroup        []*ParentTicketGroup               `xml:"parentTicketGroup"`
	RuleList                 []*RuleList                        `xml:"ruleList"`
}

// InvoiceValidationGroupType is Element used to convey transaction status.
type InvoiceValidationGroupType struct {
	ApprovalCodeDetails *GenericAuthorisationResultType202258S `xml:"approvalCodeDetails"`
	DocumentInformation *DocumentInformationDetailsType        `xml:"documentInformation"`
	TransactionStatus   *ErrorGroupType253647G                 `xml:"transactionStatus"`
}

// ItemReferencesAndVersionsType is The value of the payment record/correlator Id
type ItemReferencesAndVersionsType struct {
	ReferenceType   string `xml:"referenceType"`
	UniqueReference string `xml:"uniqueReference"`
}

// KeypadCapacityType ...
type KeypadCapacityType struct {
	PrimaryInputKeypad string   `xml:"primaryInputKeypad"`
	OtherInputKeypad   []string `xml:"otherInputKeypad"`
}

// LanguageBatchTypeU is ISO639-1 value of the language Dutch = nl
type LanguageBatchTypeU struct {
	LanguageQualifier string                    `xml:"languageQualifier"`
	LanguageDetails   *LanguageDetailsBatchTypeU `xml:"languageDetails"`
}

// LanguageDetailsBatchTypeU is ISO639-1 value  nl for Dutch
type LanguageDetailsBatchTypeU struct {
	LanguageCode string `xml:"languageCode"`
}

// LocationIdentificationTypeU is name of the location
type LocationIdentificationTypeU struct {
	Code string `xml:"code"`
	Name string `xml:"name"`
}

// LocationTypeI is To identify a location by code or name.
type LocationTypeI struct {
	TrueLocationId string `xml:"trueLocationId"`
	TrueLocation   string `xml:"trueLocation"`
}

// LocationType is Defines the code of the port in the list
type LocationType struct {
	Code      string `xml:"code"`
	Name      string `xml:"name"`
	Country   string `xml:"country"`
	Qualifier string `xml:"qualifier"`
}

// MarriageControlDetailsTypeI is To identify details concerning the marriage status of travel product.
type MarriageControlDetailsTypeI struct {
	Relation           string `xml:"relation"`
	MarriageIdentifier string `xml:"marriageIdentifier"`
	LineNumber         int    `xml:"lineNumber"`
	OtherRelation      string `xml:"otherRelation"`
	CarrierCode        string `xml:"carrierCode"`
}

// AmopData is Will be used to convey information dedicated to the Payment.
type AmopData struct {
	//XMLName             xml.Name                     `xml:"amopData"`
	PaymentStep         *CodedAttributeType196207S   `xml:"paymentStep"`
	MessageVersion      *MessageStructureVersionType `xml:"messageVersion"`
	PaymentDataMap      []*AttributeType215546S      `xml:"paymentDataMap"`
	GroupAmopProcess    *GroupAmopProcess            `xml:"groupAmopProcess"`
	Dummy               *DummySegmentTypeI           `xml:"dummy"`
	GroupAmopParameters *GroupAmopParameters         `xml:"groupAmopParameters"`
}

// MeanOfPaymentDataType is will convey all data needed for a  payment done on the web using an account
type MeanOfPaymentDataType struct {
	FopInformation   *FormOfPaymentType        `xml:"fopInformation"`
	FundTransferData *WebFundTransferGroupType `xml:"fundTransferData"`
	AsyncDataGroup   *AsynchPaymentGroupType   `xml:"asyncDataGroup"`
	Dummy            *DummySegmentTypeI        `xml:"dummy"`
	InvoiceDataGroup *InvoiceFopGroupType      `xml:"invoiceDataGroup"`
	CreditCardData   *CreditCardDataGroupType  `xml:"creditCardData"`
	WebAccountData   *WebAccountGroupType      `xml:"webAccountData"`
	AmopData         *AmopData                 `xml:"amopData"`
}

// MeasurementDetailsTypeI is Fraud screening result description. This data is set in accordance with the fraud score set by the airline.  OK : Fraud screening result approved KO : Fraud screening result declined WRN : Fraud screening result Warning
type MeasurementDetailsTypeI struct {
	Significance string `xml:"significance"`
}

// MeasurementsType is will convey the result of the fraud screening process on the PSP/bank side : will look like 300 Points
type MeasurementsType struct {
	MeasurementQualifier  string                  `xml:"measurementQualifier"`
	MeasurementDetails    *MeasurementDetailsTypeI `xml:"measurementDetails"`
	ValueRange            *ValueRangeTypeI         `xml:"valueRange"`
	SurfaceLayerIndicator string                  `xml:"surfaceLayerIndicator"`
}

// MessageIdentifierType is Name space
type MessageIdentifierType struct {
	MessageTypeIdentifier    string `xml:"messageTypeIdentifier"`
	MessageTypeVersionNumber string `xml:"messageTypeVersionNumber"`
	MessageTypeReleaseNumber string `xml:"messageTypeReleaseNumber"`
	ControllingAgency        string `xml:"controllingAgency"`
	Domain                   string `xml:"domain"`
}

// MessageReferenceType is Additional POS Information - Terminal Type  Field 60.1 - Position 1  CAT (Cardholder-Activated Terminal indicator) or UAT (Unattended Acceptance Terminal)
type MessageReferenceType struct {
	RetrievalReferenceNumber      string `xml:"retrievalReferenceNumber"`
	AuthorCharacteristicIndicator string `xml:"authorCharacteristicIndicator"`
	AuthorResponseCode            string `xml:"authorResponseCode"`
	CardLevelResult               string `xml:"cardLevelResult"`
	TerminalType                  string `xml:"terminalType"`
}

// MessageStructureVersionType is Indicates the format and the validation of the message to which it applies
type MessageStructureVersionType struct {
	MessageIdentifier *MessageIdentifierType `xml:"messageIdentifier"`
	StructureType     string                `xml:"structureType"`
}

// MonetaryInformationDetailsTypeI is currency
type MonetaryInformationDetailsTypeI struct {
	TypeQualifier string `xml:"typeQualifier"`
	Amount        string `xml:"amount"`
	Currency      string `xml:"currency"`
}

// MonetaryInformationDetailsType is location
type MonetaryInformationDetailsType struct {
	TypeQualifier string `xml:"typeQualifier"`
	Amount        float64 `xml:"amount"`
	Currency      string `xml:"currency"`
	Location      string `xml:"location"`
}

// MonetaryInformationDetailsType266075C is IATA alphabetic currency code.  Eg: USD,GBP,EUR...
type MonetaryInformationDetailsType266075C struct {
	//XMLName       xml.Name                      `xml:"MonetaryInformationDetailsType_266075C"`
	TypeQualifier string `xml:"typeQualifier"`
	Amount        float64 `xml:"amount"`
	Currency      string `xml:"currency"`
}

// MonetaryInformationType is To convey monetary amounts, rates and percentages.
type MonetaryInformationType struct {
	MonetaryDetails *MonetaryInformationDetailsType `xml:"monetaryDetails"`
}

// MonetaryInformationType202047S is Contains the currencies and the various amounts
type MonetaryInformationType202047S struct {
	//XMLName              xml.Name                                 `xml:"MonetaryInformationType_202047S"`
	MonetaryDetails      *MonetaryInformationDetailsType266075C   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType266075C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType202074S is To convey monetary amounts, rates and percentages.
type MonetaryInformationType202074S struct {
	//XMLName              xml.Name                          `xml:"MonetaryInformationType_202074S"`
	MonetaryDetails      *MonetaryInformationDetailsType   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType203358S is monertary details
type MonetaryInformationType203358S struct {
	//XMLName              xml.Name                           `xml:"MonetaryInformationType_203358S"`
	MonetaryDetails      *MonetaryInformationDetailsTypeI   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsTypeI `xml:"otherMonetaryDetails"`
}

// NumberOfUnitDetailsType is Identification of number of units and its purpose.
type NumberOfUnitDetailsType struct {
	NumberOfUnit  string `xml:"numberOfUnit"`
	UnitQualifier string `xml:"unitQualifier"`
}

// OperatingEnvinronmentType is Card Present or not, allows a better risk assessment of hte transaction
type OperatingEnvinronmentType struct {
	Environment     string `xml:"environment"`
	OfflineCapacity string `xml:"offlineCapacity"`
	OnlineCapacity  string `xml:"onlineCapacity"`
	SupervisedBy    string `xml:"supervisedBy"`
	Vicinity        string `xml:"vicinity"`
	Interaction     string `xml:"interaction"`
	CardPresence    string `xml:"cardPresence"`
}

// OriginatorDetailsTypeI is ISO code of language.
type OriginatorDetailsTypeI struct {
	CodedCountry  string `xml:"codedCountry"`
	CodedCurrency string `xml:"codedCurrency"`
	CodedLanguage string `xml:"codedLanguage"`
}

// OriginatorIdentificationDetailsTypeI is Third in-house identifier
type OriginatorIdentificationDetailsTypeI struct {
	OriginatorId           string  `xml:"originatorId"`
	InHouseIdentification1 string `xml:"inHouseIdentification1"`
	InHouseIdentification2 string `xml:"inHouseIdentification2"`
	InHouseIdentification3 string `xml:"inHouseIdentification3"`
}

// OriginatorOfRequestDetailsTypeI is Group identification
type OriginatorOfRequestDetailsTypeI struct {
	DeliveringSystem     *SystemDetailsTypeI2502C              `xml:"deliveringSystem"`
	OriginIdentification *OriginatorIdentificationDetailsTypeI `xml:"originIdentification"`
	LocationDetails      *LocationTypeI                        `xml:"locationDetails"`
	CascadingSystem      *SystemDetailsTypeI2502C              `xml:"cascadingSystem"`
	OriginatorTypeCode   string                               `xml:"originatorTypeCode"`
	OriginDetails        *OriginatorDetailsTypeI               `xml:"originDetails"`
	Originator           string                               `xml:"originator"`
	CommunicationNumber  string                               `xml:"communicationNumber"`
	PartyIdentification  string                               `xml:"partyIdentification"`
}

// OutputDisplayCapacityType ...
type OutputDisplayCapacityType struct {
	PrimaryChannelSupervision string `xml:"primaryChannelSupervision"`
	OtherChannelSupervision   string `xml:"otherChannelSupervision"`
}

// PNRSupplementaryDataType is will convey the values of the FOP data and switch maps
type PNRSupplementaryDataType struct {
	DataAndSwitchMap *AttributeType202070S `xml:"dataAndSwitchMap"`
}

// PartyIdentifierType is To specify a party identification. To specify a party identification. To specify a party identification. To specify a party identification
type PartyIdentifierType struct {
	PartyIdentifier    string `xml:"partyIdentifier"`
	PartyCodeQualifier string `xml:"partyCodeQualifier"`
}

// CurrenciesRatesGroup is used to defined if the currency rate is informative, proposed, applied etc...
type CurrenciesRatesGroup struct {
	//XMLName                xml.Name                   `xml:"currenciesRatesGroup"`
	CurrenciesExchangeRate *CurrenciesType            `xml:"currenciesExchangeRate"`
	RateType               *CodedAttributeType197625S `xml:"rateType"`
}

// PaymentDataGroupType is will convey all the monetary informations related to the payment : amount, currency, sub-amounts
type PaymentDataGroupType struct {
	MerchantInformation            *CompanyInformationType                   `xml:"merchantInformation"`
	MonetaryInformation            []*MonetaryInformationType202047S         `xml:"monetaryInformation"`
	CurrenciesRatesGroup           []*CurrenciesRatesGroup                   `xml:"currenciesRatesGroup"`
	SliderConversion               *SliderConversionType                     `xml:"sliderConversion"`
	PaymentId                      []*ItemReferencesAndVersionsType          `xml:"paymentId"`
	ExtendedPaymentInfo            *FrequencyTypeU                           `xml:"extendedPaymentInfo"`
	TransactionDateTime            *StructuredDateTimeInformationType202239S `xml:"transactionDateTime"`
	ExpirationPeriod               *QuantityType                             `xml:"expirationPeriod"`
	DistributionChannelInformation *TerminalIdentificationDescriptionType    `xml:"distributionChannelInformation"`
	PurchaseDescription            *FreeTextInformationType202240S           `xml:"purchaseDescription"`
	Association                    []*ReferenceInfoType202078S               `xml:"association"`
	FraudScreeningData             *FraudScreeningGroupType                  `xml:"fraudScreeningData"`
	PaymentDataMap                 []*AttributeType215546S                   `xml:"paymentDataMap"`
}

// PaymentDeviceTerminal is Maximum length of capture of the PIN (12 max requirement for ISO)
type PaymentDeviceTerminal struct {
	DeviceReference           *IdentificationNumberTypeI306019C `xml:"deviceReference"`
	Category                  string                           `xml:"category"`
	Mobility                  string                           `xml:"mobility"`
	KeypadCapacity            *KeypadCapacityType               `xml:"keypadCapacity"`
	Readers                   *ReaderCapacityType               `xml:"readers"`
	Encoders                  *EncoderCapacityType              `xml:"encoders"`
	Screen                    *OutputDisplayCapacityType        `xml:"screen"`
	Printer                   *OutputDisplayCapacityType        `xml:"printer"`
	OnlineCapability          string                           `xml:"onlineCapability"`
	OfflineCapability         string                           `xml:"offlineCapability"`
	PinCaptureMaxLength       string                           `xml:"pinCaptureMaxLength"`
	VerificationMethods       *VerificationMethodsType          `xml:"verificationMethods"`
	CardCapture               string                           `xml:"cardCapture"`
	DataAuthenticationMode    string                           `xml:"dataAuthenticationMode"`
	IdentityVerificationMeans []string                         `xml:"identityVerificationMeans"`
	AvailableService          []string                         `xml:"availableService"`
}

// ErrorSupplementaryData is will convey the suggested expiration period : it indicates the maximum date/time of validity at the acquirer, so that the merchant can offer a new transaction. It is the remaining time until the start of unavailability less 4 min.
type ErrorSupplementaryData struct {
	//XMLName                    xml.Name                                  `xml:"errorSupplementaryData"`
	ErrorSupplementaryMessages *AttributeType215552S                     `xml:"errorSupplementaryMessages"`
	ExpirationDate             *StructuredDateTimeInformationType215553S `xml:"expirationDate"`
}

// PaymentErrorGroupType is The description of warning or error.
type PaymentErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType215551S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType202263S         `xml:"errorWarningDescription"`
	ErrorSupplementaryData    *ErrorSupplementaryData                 `xml:"errorSupplementaryData"`
}

// PaymentGroupType is will convey the result of the payment and related to the detailed Mean Of Payment
type PaymentGroupType struct {
	GroupUsage               *CodedAttributeType197625S   `xml:"groupUsage"`
	PaymentData              *PaymentDataGroupType        `xml:"paymentData"`
	PaymentStatus            *PaymentStatusGroupType      `xml:"paymentStatus"`
	PaymentSupplementaryData []*CodedAttributeType202071S `xml:"paymentSupplementaryData"`
	MopInformation           *MeanOfPaymentDataType       `xml:"mopInformation"`
	Dummy                    *DummySegmentTypeI           `xml:"dummy"`
	MopDetailedData          *DetailedPaymentDataType     `xml:"mopDetailedData"`
}

// PaymentStatusGroupType is will convey the value of the fraud screening checks done by the PSP/bank
type PaymentStatusGroupType struct {
	PaymentStatusInformation *ResponseAnalysisDetailsType   `xml:"paymentStatusInformation"`
	PaymentStatusHistory     []*ResponseAnalysisDetailsType `xml:"paymentStatusHistory"`
	PaymentStatusError       *ErrorGroupType                `xml:"paymentStatusError"`
	FraudScreeningResult     *MeasurementsType              `xml:"fraudScreeningResult"`
}

// PhoneAndEmailAddressType is Email of the buyer as entered during checkout. PayPal uses this value to pre-fill the Paypal membership sign-up portion of the Paypal login page.
type PhoneAndEmailAddressType struct {
	PhoneOrEmailType       string                        `xml:"phoneOrEmailType"`
	TelephoneNumberDetails *StructuredTelephoneNumberType `xml:"telephoneNumberDetails"`
	EmailAddress           string                        `xml:"emailAddress"`
}

// PhoneAndEmailAddressType222736S is Email address
type PhoneAndEmailAddressType222736S struct {
	//XMLName                xml.Name                              `xml:"PhoneAndEmailAddressType_222736S"`
	PhoneOrEmailType       string                               `xml:"phoneOrEmailType"`
	TelephoneNumberDetails *StructuredTelephoneNumberType306061C `xml:"telephoneNumberDetails"`
	EmailAddress           string                               `xml:"emailAddress"`
}

// PointOfSaleInformationType is To specify point of sale information by party identification or location identification.
type PointOfSaleInformationType struct {
	PointOfSale     *PartyIdentifierType `xml:"pointOfSale"`
	LocationDetails *LocationType        `xml:"locationDetails"`
}

// PointOfServicesOperationsType is To specify point of services detailed information of operations.
type PointOfServicesOperationsType struct {
	OperatingEnvironment *OperatingEnvinronmentType `xml:"operatingEnvironment"`
	BusinessSectors      *BusinessSectorsType       `xml:"businessSectors"`
	DeliveryTypeGoods    []string                  `xml:"deliveryTypeGoods"`
}

// ProductAccountDetailsType is To specify frequent traveller card information.
type ProductAccountDetailsType struct {
	ReferenceType       string `xml:"referenceType"`
	FrequentTravellerId string `xml:"frequentTravellerId"`
	Category            string `xml:"category"`
	SequenceNumber      string `xml:"sequenceNumber"`
	VersionNumber       string `xml:"versionNumber"`
	RateClass           string `xml:"rateClass"`
	ApprovalCode        string `xml:"approvalCode"`
}

// ProductDateTimeTypeI is To specify the dates and times associated with a product.
type ProductDateTimeTypeI struct {
	DepartureDate string `xml:"departureDate"`
	DepartureTime string `xml:"departureTime"`
	ArrivalDate   string `xml:"arrivalDate"`
	ArrivalTime   string `xml:"arrivalTime"`
	DateVariation string `xml:"dateVariation"`
}

// ProductIdentificationDetailsType is Code, number or name to identify a specific product or service.
type ProductIdentificationDetailsType struct {
	FlightNumber      string   `xml:"flightNumber"`
	BookingClass      string   `xml:"bookingClass"`
	OperationalSuffix string   `xml:"operationalSuffix"`
	Modifier          []string `xml:"modifier"`
}

// ProductLocationDetailsTypeI is product EBANK
type ProductLocationDetailsTypeI struct {
	OtherStation string `xml:"otherStation"`
}

// ProductTypeDetailsType is indicates whether the flight is domestic or international
type ProductTypeDetailsType struct {
	FlightIndicator []string `xml:"flightIndicator"`
}

// QuantityDetailsTypeI is SEC for duration in seconds
type QuantityDetailsTypeI struct {
	Qualifier string `xml:"qualifier"`
	Value     string `xml:"value"`
	Unit      string `xml:"unit"`
}

// QuantityType is To specify an appropriate quantity.
type QuantityType struct {
	QuantityDetails []*QuantityDetailsTypeI `xml:"quantityDetails"`
}

// ReaderCapacityType ...
type ReaderCapacityType struct {
	PrimaryInputReader string   `xml:"primaryInputReader"`
	OtherInputReader   []string `xml:"otherInputReader"`
}

// ReferenceInfoType is Element type and tatoo number of MEP and PAI element (for SFP).
type ReferenceInfoType struct {
	ReferenceDetails []*ReferencingDetailsType260461C `xml:"referenceDetails"`
}

// ReferenceInfoType190166S is To provide specific Hotel reference identification.
type ReferenceInfoType190166S struct {
	//XMLName          xml.Name                       `xml:"ReferenceInfoType_190166S"`
	ReferenceDetails *ReferencingDetailsType266037C `xml:"referenceDetails"`
}

// ReferenceInfoType190198S is To provide specific Hotel reference identification.
type ReferenceInfoType190198S struct {
	//XMLName          xml.Name                       `xml:"ReferenceInfoType_190198S"`
	ReferenceDetails *ReferencingDetailsType266070C `xml:"referenceDetails"`
}

// ReferenceInfoType202078S is Reference details
type ReferenceInfoType202078S struct {
	//XMLName          xml.Name                  `xml:"ReferenceInfoType_202078S"`
	ReferenceDetails []*ReferencingDetailsType `xml:"referenceDetails"`
	string           string                   `xml:"Dummy.NET"`
}

// ReferenceInfoType202086S ...
type ReferenceInfoType202086S struct {
	//XMLName          xml.Name                 `xml:"ReferenceInfoType_202086S"`
	ReferenceDetails *ReferencingDetailsTypeI `xml:"referenceDetails"`
}

// ReferenceInfoType202099S ...
type ReferenceInfoType202099S struct {
	//XMLName        xml.Name                          `xml:"ReferenceInfoType_202099S"`
	PayerAccountId []*ReferencingDetailsTypeI107111C `xml:"payerAccountId"`
}

// ReferenceInfoType202246S ...
type ReferenceInfoType202246S struct {
	//XMLName          xml.Name                        `xml:"ReferenceInfoType_202246S"`
	ReferenceDetails *ReferencingDetailsTypeI107111C `xml:"referenceDetails"`
}

// ReferenceInfoType215556S ...
type ReferenceInfoType215556S struct {
	//XMLName          xml.Name                        `xml:"ReferenceInfoType_215556S"`
	ReferenceDetails *ReferencingDetailsTypeI107111C `xml:"referenceDetails"`
}

// ReferenceInfoType215557S ...
type ReferenceInfoType215557S struct {
	//XMLName          xml.Name                        `xml:"ReferenceInfoType_215557S"`
	ReferenceDetails *ReferencingDetailsTypeI107116C `xml:"referenceDetails"`
}

// ReferenceInfoType222768S ...
type ReferenceInfoType222768S struct {
	//XMLName          xml.Name                       `xml:"ReferenceInfoType_222768S"`
	ReferenceDetails *ReferencingDetailsType306100C `xml:"referenceDetails"`
}

// ReferenceInformationType ...
type ReferenceInformationType struct {
	PassengerReference *ReferencingDetailsType260700C `xml:"passengerReference"`
}

// ReferencingDetailsTypeI ...
type ReferencingDetailsTypeI struct {
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsTypeI107111C ...
type ReferencingDetailsTypeI107111C struct {
	//XMLName xml.Name                       `xml:"ReferencingDetailsTypeI_107111C"`
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsTypeI107116C ...
type ReferencingDetailsTypeI107116C struct {
	//XMLName xml.Name                       `xml:"ReferencingDetailsTypeI_107116C"`
	Value string `xml:"value"`
}

// ReferencingDetailsType ...
type ReferencingDetailsType struct {
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsType260461C ...
type ReferencingDetailsType260461C struct {
	//XMLName xml.Name                       `xml:"ReferencingDetailsType_260461C"`
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsType260700C ...
type ReferencingDetailsType260700C struct {
	//XMLName xml.Name                      `xml:"ReferencingDetailsType_260700C"`
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsType261040C ...
type ReferencingDetailsType261040C struct {
	//XMLName   xml.Name                       `xml:"ReferencingDetailsType_261040C"`
	Qualifier string `xml:"qualifier"`
	Number    string `xml:"number"`
}

// ReferencingDetailsType266037C ...
type ReferencingDetailsType266037C struct {
	//XMLName xml.Name                       `xml:"ReferencingDetailsType_266037C"`
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsType266070C ...
type ReferencingDetailsType266070C struct {
	//XMLName xml.Name                      `xml:"ReferencingDetailsType_266070C"`
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ReferencingDetailsType306100C ...
type ReferencingDetailsType306100C struct {
	//XMLName xml.Name                       `xml:"ReferencingDetailsType_306100C"`
	Value string `xml:"value"`
}

// ReservationControlInformationDetailsType ...
type ReservationControlInformationDetailsType struct {
	CompanyId     string `xml:"companyId"`
	ControlNumber string `xml:"controlNumber"`
	ControlType   string `xml:"controlType"`
	BfeType       string `xml:"bfeType"`
}

// ReservationControlInformationDetailsType220877C ...
type ReservationControlInformationDetailsType220877C struct {
	//XMLName       xml.Name                       `xml:"ReservationControlInformationDetailsType_220877C"`
	CompanyId     string `xml:"companyId"`
	ControlNumber string `xml:"controlNumber"`
	ControlType   string `xml:"controlType"`
	Date          string `xml:"date"`
	Time          int    `xml:"time"`
	BfeType       string `xml:"bfeType"`
}

// ReservationControlInformationType ...
type ReservationControlInformationType struct {
	Reservation *ReservationControlInformationDetailsType `xml:"reservation"`
}

// ReservationControlInformationType167717S ...
type ReservationControlInformationType167717S struct {
	//XMLName     xml.Name                                         `xml:"ReservationControlInformationType_167717S"`
	Reservation *ReservationControlInformationDetailsType220877C `xml:"reservation"`
}

// ResponseAnalysisDetailsType ...
type ResponseAnalysisDetailsType struct {
	ResponseType string `xml:"responseType"`
	StatusCode   string `xml:"statusCode"`
}

// ResponseAnalysisDetailsType202257S ...
type ResponseAnalysisDetailsType202257S struct {
	//XMLName    xml.Name               `xml:"ResponseAnalysisDetailsType_202257S"`
	StatusCode string `xml:"statusCode"`
}

// ResponseAnalysisDetailsType215561S ...
type ResponseAnalysisDetailsType215561S struct {
	//XMLName      xml.Name               `xml:"ResponseAnalysisDetailsType_215561S"`
	ResponseType string `xml:"responseType"`
	StatusCode   string `xml:"statusCode"`
}

// ResponseIdentificationType ...
type ResponseIdentificationType struct {
	TransacIdentifier string `xml:"transacIdentifier"`
	ValidationCode    string `xml:"validationCode"`
	BanknetRefNumber  string `xml:"banknetRefNumber"`
	BanknetDate       string `xml:"banknetDate"`
}

// RoutingInformationTypeI ...
type RoutingInformationTypeI struct {
	RoutingDetails *ProductLocationDetailsTypeI `xml:"routingDetails"`
}

// SecurityScreeningType ...
type SecurityScreeningType struct {
	ScreeningInstructions string `xml:"screeningInstructions"`
}

// SequenceDetailsType ...
type SequenceDetailsType struct {
	SequenceDetails *SequenceInformationTypeU `xml:"sequenceDetails"`
}

// SequenceInformationTypeU ...
type SequenceInformationTypeU struct {
	Number             string `xml:"number"`
	IdentificationCode string `xml:"identificationCode"`
}

// SliderConversionType ...
type SliderConversionType struct {
	SliderMode     *StatusDetailsType275596C  `xml:"sliderMode"`
	SliderPosition []*NumberOfUnitDetailsType `xml:"sliderPosition"`
}

// SpecificVisaLinkCreditCardInformationType ...
type SpecificVisaLinkCreditCardInformationType struct {
	MsgRef             *MessageReferenceType       `xml:"msgRef"`
	RespIdentification *ResponseIdentificationType `xml:"respIdentification"`
}

// StatusDetailsTypeI ...
type StatusDetailsTypeI struct {
	Indicator   string `xml:"indicator"`
	Action      string `xml:"action"`
	Description string `xml:"description"`
}

// StatusDetailsTypeI107129C ...
type StatusDetailsTypeI107129C struct {
	//XMLName   xml.Name                      `xml:"StatusDetailsTypeI_107129C"`
	Indicator string `xml:"indicator"`
	Action    string `xml:"action"`
}

// StatusDetailsTypeI129365C ...
type StatusDetailsTypeI129365C struct {
	//XMLName   xml.Name                     `xml:"StatusDetailsTypeI_129365C"`
	Indicator string `xml:"indicator"`
	Action    string `xml:"action"`
}

// StatusDetailsType ...
type StatusDetailsType struct {
	Action      string `xml:"action"`
	Description string `xml:"description"`
}

// StatusDetailsType205625C ...
type StatusDetailsType205625C struct {
	//XMLName     xml.Name                       `xml:"StatusDetailsType_205625C"`
	Indicator   string `xml:"indicator"`
	Action      string `xml:"action"`
	Type        string `xml:"type"`
	Description string `xml:"description"`
}

// StatusDetailsType223059C ...
type StatusDetailsType223059C struct {
	//XMLName   xml.Name                      `xml:"StatusDetailsType_223059C"`
	Indicator string `xml:"indicator"`
	Type      string `xml:"type"`
}

// StatusDetailsType275596C ...
type StatusDetailsType275596C struct {
	//XMLName   xml.Name                      `xml:"StatusDetailsType_275596C"`
	Indicator string `xml:"indicator"`
	Type      string `xml:"type"`
}

// StatusType ...
type StatusType struct {
	StatusInformation []*StatusDetailsType `xml:"statusInformation"`
}

// StatusType141910S ...
type StatusType141910S struct {
	//XMLName           xml.Name                    `xml:"StatusType_141910S"`
	StatusInformation []*StatusDetailsType205625C `xml:"statusInformation"`
}

// StatusType156547S ...
type StatusType156547S struct {
	//XMLName           xml.Name                    `xml:"StatusType_156547S"`
	StatusInformation []*StatusDetailsType223059C `xml:"statusInformation"`
}

// StatusType68646S ...
type StatusType68646S struct {
	//XMLName           xml.Name              `xml:"StatusType_68646S"`
	StatusInformation []*StatusDetailsTypeI `xml:"statusInformation"`
	string            string               `xml:"Dummy.NET"`
}

// StatusType68675S ...
type StatusType68675S struct {
	//XMLName           xml.Name                   `xml:"StatusType_68675S"`
	StatusInformation *StatusDetailsTypeI107129C `xml:"statusInformation"`
}

// StatusType84988S ...
type StatusType84988S struct {
	//XMLName           xml.Name                   `xml:"StatusType_84988S"`
	StatusInformation *StatusDetailsTypeI129365C `xml:"statusInformation"`
}

// StructuredDateTimeInformationType ...
type StructuredDateTimeInformationType struct {
	BusinessSemantic string                        `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType220873C `xml:"dateTime"`
}

// StructuredDateTimeInformationType202239S ...
type StructuredDateTimeInformationType202239S struct {
	//XMLName          xml.Name                       `xml:"StructuredDateTimeInformationType_202239S"`
	BusinessSemantic string                        `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType220917C `xml:"dateTime"`
}

// StructuredDateTimeInformationType202247S ...
type StructuredDateTimeInformationType202247S struct {
	//XMLName  xml.Name                       `xml:"StructuredDateTimeInformationType_202247S"`
	DateTime *StructuredDateTimeType220922C `xml:"dateTime"`
}

// StructuredDateTimeInformationType202270S ...
type StructuredDateTimeInformationType202270S struct {
	//XMLName          xml.Name                       `xml:"StructuredDateTimeInformationType_202270S"`
	BusinessSemantic string                        `xml:"businessSemantic"`
	TimeMode         string                        `xml:"timeMode"`
	DateTime         *StructuredDateTimeType220893C `xml:"dateTime"`
	TimeZoneInfo     *TimeZoneIinformationType      `xml:"timeZoneInfo"`
}

// StructuredDateTimeInformationType215553S ...
type StructuredDateTimeInformationType215553S struct {
	//XMLName  xml.Name                       `xml:"StructuredDateTimeInformationType_215553S"`
	DateTime *StructuredDateTimeType297563C `xml:"dateTime"`
}

// StructuredDateTimeType ...
type StructuredDateTimeType struct {
	Year         int    `xml:"year"`
	Month        int    `xml:"month"`
	Day          int    `xml:"day"`
	Hour         int    `xml:"hour"`
	Minutes      int    `xml:"minutes"`
	Seconds      int    `xml:"seconds"`
	Milliseconds string `xml:"milliseconds"`
}

// StructuredDateTimeType220873C ...
type StructuredDateTimeType220873C struct {
	//XMLName      xml.Name                  `xml:"StructuredDateTimeType_220873C"`
	Year         string `xml:"year"`
	Month        string `xml:"month"`
	Day          string `xml:"day"`
	Hour         string `xml:"hour"`
	Minutes      string `xml:"minutes"`
	Seconds      int    `xml:"seconds"`
	Milliseconds string `xml:"milliseconds"`
}

// StructuredDateTimeType220893C ...
type StructuredDateTimeType220893C struct {
	//XMLName      xml.Name                  `xml:"StructuredDateTimeType_220893C"`
	Year         int    `xml:"year"`
	Month        int    `xml:"month"`
	Day          int    `xml:"day"`
	Hour         int    `xml:"hour"`
	Minutes      int    `xml:"minutes"`
	Seconds      int    `xml:"seconds"`
	Milliseconds string `xml:"milliseconds"`
}

// StructuredDateTimeType220917C ...
type StructuredDateTimeType220917C struct {
	//XMLName      xml.Name                  `xml:"StructuredDateTimeType_220917C"`
	Year         int    `xml:"year"`
	Month        int    `xml:"month"`
	Day          int    `xml:"day"`
	Hour         int    `xml:"hour"`
	Minutes      int    `xml:"minutes"`
	Seconds      int    `xml:"seconds"`
	Milliseconds string `xml:"milliseconds"`
}

// StructuredDateTimeType220922C ...
type StructuredDateTimeType220922C struct {
	//XMLName xml.Name                  `xml:"StructuredDateTimeType_220922C"`
	Year  int `xml:"year"`
	Month int `xml:"month"`
	Day   int `xml:"day"`
}

// StructuredDateTimeType247346C ...
type StructuredDateTimeType247346C struct {
	//XMLName      xml.Name  `xml:"StructuredDateTimeType_247346C"`
	Year         string `xml:"year"`
	Month        string `xml:"month"`
	Day          string `xml:"day"`
	Hour         string `xml:"hour"`
	Minutes      string `xml:"minutes"`
	Seconds      string  `xml:"seconds"`
	Milliseconds string  `xml:"milliseconds"`
}

// StructuredDateTimeType297563C ...
type StructuredDateTimeType297563C struct {
	//XMLName xml.Name                  `xml:"StructuredDateTimeType_297563C"`
	Year    int `xml:"year"`
	Month   int `xml:"month"`
	Day     int `xml:"day"`
	Hour    int `xml:"hour"`
	Minutes int `xml:"minutes"`
	Seconds int `xml:"seconds"`
}

// StructuredPeriodInformationType ...
type StructuredPeriodInformationType struct {
	BeginDateTime *StructuredDateTimeType247346C `xml:"beginDateTime"`
	EndDateTime   *StructuredDateTimeType247346C `xml:"endDateTime"`
}

// StructuredPeriodInformationType202063S ...
type StructuredPeriodInformationType202063S struct {
	//XMLName          xml.Name                       `xml:"StructuredPeriodInformationType_202063S"`
	BusinessSemantic string                        `xml:"businessSemantic"`
	TimeMode         string                        `xml:"timeMode"`
	BeginDateTime    *StructuredDateTimeType220873C `xml:"beginDateTime"`
	EndDateTime      *StructuredDateTimeType220873C `xml:"endDateTime"`
	Frequency        *FrequencyType                 `xml:"frequency"`
	TimeZoneInfo     *TimeZoneIinformationType      `xml:"timeZoneInfo"`
}

// StructuredTelephoneNumberType ...
type StructuredTelephoneNumberType struct {
	InternationalDialCode string `xml:"internationalDialCode"`
	LocalPrefixCode       string `xml:"localPrefixCode"`
	AreaCode              string `xml:"areaCode"`
	TelephoneNumber       string `xml:"telephoneNumber"`
}

// StructuredTelephoneNumberType306061C ...
type StructuredTelephoneNumberType306061C struct {
	//XMLName         xml.Name                       `xml:"StructuredTelephoneNumberType_306061C"`
	TelephoneNumber string `xml:"telephoneNumber"`
}

// SystemDetailsInfoType ...
type SystemDetailsInfoType struct {
	WorkstationId    string             `xml:"workstationId"`
	DeliveringSystem *SystemDetailsTypeI `xml:"deliveringSystem"`
}

// SystemDetailsTypeI ...
type SystemDetailsTypeI struct {
	CompanyId string `xml:"companyId"`
}

// SystemDetailsTypeI2502C ...
type SystemDetailsTypeI2502C struct {
	//XMLName    xml.Name                       `xml:"SystemDetailsTypeI_2502C"`
	CompanyId  string `xml:"companyId"`
	LocationId string `xml:"locationId"`
	Location   string `xml:"location"`
}

// TaxDetailsType ...
type TaxDetailsType struct {
	Rate         string `xml:"rate"`
	CountryCode  string `xml:"countryCode"`
	CurrencyCode string `xml:"currencyCode"`
	Type         string `xml:"type"`
}

// TaxType ...
type TaxType struct {
	TaxCategory string         `xml:"taxCategory"`
	TaxDetails  *TaxDetailsType `xml:"taxDetails"`
}

// TerminalIdentificationDescriptionType ...
type TerminalIdentificationDescriptionType struct {
	TerminalID          string                  `xml:"terminalID"`
	DistributionChannel *DistributionChannelType `xml:"distributionChannel"`
	OriginatorType      string                  `xml:"originatorType"`
	TransactionContext  *TransactionContextType  `xml:"transactionContext"`
}

// TdsBlobData ...
type TdsBlobData struct {
	//XMLName          xml.Name                  `xml:"tdsBlobData"`
	TdsBlbIdentifier *ReferenceInfoType222768S `xml:"tdsBlbIdentifier"`
	TdsBlbData       *BinaryDataType           `xml:"tdsBlbData"`
}

// ThreeDomainSecureGroupType ...
type ThreeDomainSecureGroupType struct {
	AuthenticationData *CreditCardSecurityType          `xml:"authenticationData"`
	AcsURL             *CommunicationContactType222766S `xml:"acsURL"`
	TdsBlobData        []*TdsBlobData                   `xml:"tdsBlobData"`
}

// TicketNumberDetailsType ...
type TicketNumberDetailsType struct {
	Number              string `xml:"number"`
	Type                string `xml:"type"`
	NumberOfBooklets    string `xml:"numberOfBooklets"`
	DataIndicator       string `xml:"dataIndicator"`
	RequestNotification string `xml:"requestNotification"`
	InConnectionWith    string `xml:"inConnectionWith"`
}

// TicketNumberType ...
type TicketNumberType struct {
	DocumentDetails *TicketNumberDetailsType `xml:"documentDetails"`
	Status          string                  `xml:"status"`
}

// TicketingFormOfPaymentType ...
type TicketingFormOfPaymentType struct {
	FopDetails *FormOfPaymentInformationType `xml:"fopDetails"`
}

// TicketingFormOfPaymentType203357S ...
type TicketingFormOfPaymentType203357S struct {
	//XMLName    xml.Name                             `xml:"TicketingFormOfPaymentType_203357S"`
	FopDetails *FormOfPaymentInformationType282478C `xml:"fopDetails"`
}

// TicketingFormOfPaymentType223002S ...
type TicketingFormOfPaymentType223002S struct {
	//XMLName    xml.Name                               `xml:"TicketingFormOfPaymentType_223002S"`
	FopDetails []*FormOfPaymentInformationType306313C `xml:"fopDetails"`
}

// TimeZoneIinformationType ...
type TimeZoneIinformationType struct {
	CountryCode string `xml:"countryCode"`
	Code        string `xml:"code"`
	Suffix      string `xml:"suffix"`
}

// TransactionContextType ...
type TransactionContextType struct {
	TransactionCondition     string `xml:"transactionCondition"`
	IdentityVerifiedBy       string `xml:"identityVerifiedBy"`
	RemoteCommerceIndicators string `xml:"remoteCommerceIndicators"`
	AuthorCharacteristicInd  string `xml:"authorCharacteristicInd"`
	AuthorLifecycleLimit     string `xml:"authorLifecycleLimit"`
	CardSequenceNumber       int    `xml:"cardSequenceNumber"`
}

// TransactionInformationForTicketingType ...
type TransactionInformationForTicketingType struct {
	TransactionDetails *TransactionInformationsType `xml:"transactionDetails"`
}

// TransactionInformationsType ...
type TransactionInformationsType struct {
	Code                      string `xml:"code"`
	Type                      string `xml:"type"`
	IssueIndicator            string `xml:"issueIndicator"`
	TransmissionControlNumber string `xml:"transmissionControlNumber"`
}

// TravelProductInformationType ...
type TravelProductInformationType struct {
	FlightDate           *ProductDateTimeTypeI             `xml:"flightDate"`
	BoardPointDetails    *LocationTypeI                    `xml:"boardPointDetails"`
	OffpointDetails      *LocationTypeI                    `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationTypeI       `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsType `xml:"flightIdentification"`
	FlightTypeDetails    *ProductTypeDetailsType           `xml:"flightTypeDetails"`
	ItemNumber           int                              `xml:"itemNumber"`
	SpecialSegment       string                           `xml:"specialSegment"`
	MarriageDetails      []*MarriageControlDetailsTypeI    `xml:"marriageDetails"`
}

// TravellerDetailsTypeI ...
type TravellerDetailsTypeI struct {
	GivenName string `xml:"givenName"`
}

// TravellerDetailsTypeI107098C ...
type TravellerDetailsTypeI107098C struct {
	//XMLName   xml.Name                       `xml:"TravellerDetailsTypeI_107098C"`
	GivenName string `xml:"givenName"`
}

// TravellerDetailsType ...
type TravellerDetailsType struct {
	GivenName                string `xml:"givenName"`
	Type                     string `xml:"type"`
	UniqueCustomerIdentifier string `xml:"uniqueCustomerIdentifier"`
	InfantIndicator          string `xml:"infantIndicator"`
	Title                    string `xml:"title"`
	Age                      string `xml:"age"`
}

// TravellerInformationType ...
type TravellerInformationType struct {
	PaxDetails      *TravellerSurnameInformationType `xml:"paxDetails"`
	OtherPaxDetails []*TravellerDetailsType          `xml:"otherPaxDetails"`
}

// TravellerInformationType202101S ...
type TravellerInformationType202101S struct {
	//XMLName         xml.Name                                `xml:"TravellerInformationType_202101S"`
	PaxDetails      *TravellerSurnameInformationType220907C `xml:"paxDetails"`
	OtherPaxDetails *TravellerDetailsTypeI107098C           `xml:"otherPaxDetails"`
}

// TravellerInformationType202251S ...
type TravellerInformationType202251S struct {
	//XMLName             xml.Name                                `xml:"TravellerInformationType_202251S"`
	CcHolderNameDetails *TravellerSurnameInformationType220923C `xml:"ccHolderNameDetails"`
	OtherNameDetails    *TravellerDetailsTypeI                  `xml:"otherNameDetails"`
}

// TravellerInformationType215558S ...
type TravellerInformationType215558S struct {
	//XMLName    xml.Name                                `xml:"TravellerInformationType_215558S"`
	PaxDetails *TravellerSurnameInformationType220923C `xml:"paxDetails"`
}

// TravellerSurnameInformationType ...
type TravellerSurnameInformationType struct {
	Surname  string `xml:"surname"`
	Type     string `xml:"type"`
	Quantity string `xml:"quantity"`
	Gender   string `xml:"gender"`
}

// TravellerSurnameInformationType220907C ...
type TravellerSurnameInformationType220907C struct {
	//XMLName xml.Name                       `xml:"TravellerSurnameInformationType_220907C"`
	Surname string `xml:"surname"`
	Type    string `xml:"type"`
}

// TravellerSurnameInformationType220923C ...
type TravellerSurnameInformationType220923C struct {
	//XMLName xml.Name                       `xml:"TravellerSurnameInformationType_220923C"`
	Surname string `xml:"surname"`
}

// UserIdentificationType ...
type UserIdentificationType struct {
	OriginatorTypeCode string `xml:"originatorTypeCode"`
	Originator         string `xml:"originator"`
}

// ValidDateInformationTypeI ...
type ValidDateInformationTypeI struct {
	FirstDate  string `xml:"firstDate"`
	SecondDate string `xml:"secondDate"`
}

// ValueRangeTypeI ...
type ValueRangeTypeI struct {
	Unit  string `xml:"unit"`
	Value string `xml:"value"`
}

// VerificationMethodsType ...
type VerificationMethodsType struct {
	PrimaryVerificationMethod string   `xml:"primaryVerificationMethod"`
	OtherVerificationMethod   []string `xml:"otherVerificationMethod"`
}

// VirtualCardInformationType ...
type VirtualCardInformationType struct {
	VendorCode            string   `xml:"vendorCode"`
	MaximumAuthorizations float64   `xml:"maximumAuthorizations"`
	Currency              []string `xml:"currency"`
}

// VirtualCardInformationType247217C ...
type VirtualCardInformationType247217C struct {
	//XMLName    xml.Name                      `xml:"VirtualCardInformationType_247217C"`
	VendorCode string `xml:"vendorCode"`
}

// VirtualCardParametersType ...
type VirtualCardParametersType struct {
	VirtualCardInformation *VirtualCardInformationType `xml:"virtualCardInformation"`
}

// VirtualCardParametersType175504S ...
type VirtualCardParametersType175504S struct {
	//XMLName                xml.Name                           `xml:"VirtualCardParametersType_175504S"`
	VirtualCardInformation *VirtualCardInformationType247217C `xml:"virtualCardInformation"`
}

// WebAccountGroupType ...
type WebAccountGroupType struct {
	WebAccountSign  *PhoneAndEmailAddressType      `xml:"webAccountSign"`
	PaymentProvider *CompanyInformationType202069S `xml:"paymentProvider"`
}

// MerchantSiteDetails ...
type MerchantSiteDetails struct {
	//XMLName     xml.Name                           `xml:"merchantSiteDetails"`
	CountryData *CountryInformationTypeU           `xml:"countryData"`
	CppPspColor []*FreeTextInformationType202263S  `xml:"cppPspColor"`
	Urls        []*CommunicationContactType202254S `xml:"urls"`
}

// ErrorDescription ...
type ErrorDescription struct {
	//XMLName          xml.Name                            `xml:"errorDescription"`
	SeverityCodeType *ResponseAnalysisDetailsType215561S `xml:"severityCodeType"`
	PaymentError     []*ErrorGroupType302085G            `xml:"paymentError"`
}

// WebAccountSuppDataGroupType ...
type WebAccountSuppDataGroupType struct {
	PspIndicator        *StatusType68646S                `xml:"pspIndicator"`
	UatpCard            *CreditCardDataType              `xml:"uatpCard"`
	PaypalParameters    []*StatusType84988S              `xml:"paypalParameters"`
	PspUrl              *CommunicationContactType202254S `xml:"pspUrl"`
	ShippingAddress     *AddressType68644S               `xml:"shippingAddress"`
	PayerName           *TravellerInformationType202101S `xml:"payerName"`
	AccountNumber       *ReferenceInfoType202099S        `xml:"accountNumber"`
	MerchantSiteDetails *MerchantSiteDetails             `xml:"merchantSiteDetails"`
	ErrorDescription    []*ErrorDescription              `xml:"errorDescription"`
}

// WebFundTransferGroupType ...
type WebFundTransferGroupType struct {
	IssuerId        *ReferenceInfoType202086S      `xml:"issuerId"`
	PaymentProvider *CompanyInformationType202069S `xml:"paymentProvider"`
}
