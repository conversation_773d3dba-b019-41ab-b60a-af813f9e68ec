package create_tsm_from_pricing

import (
	"encoding/xml"
)

// ApplicationError ...
type ApplicationError struct {
	XMLName              xml.Name                         `xml:"applicationError"`
	ApplicationErrorInfo *ApplicationErrorInformationType `xml:"applicationErrorInfo"`
	ErrorText            *InteractiveFreeTextTypeI        `xml:"errorText"`
}

// TstList ...
type TstList struct {
	XMLName        xml.Name                       `xml:"tstList"`
	TsmReference   *ItemReferencesAndVersionsType `xml:"tsmReference"`
	PaxInformation *ReferenceInformationTypeI     `xml:"paxInformation"`
}

// TicketCreateTSMFromPricingReply ...
type TicketCreateTSMFromPricingReply struct {
	XMLName          xml.Name                            `xml:"Ticket_CreateTSMFromPricingReply"`
	ApplicationError *ApplicationError                   `xml:"applicationError"`
	PnrLocatorData   *ReservationControlInformationTypeI `xml:"pnrLocatorData"`
	TstList          []*TstList                          `xml:"tstList"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ApplicationErrorCode      string `xml:"applicationErrorCode"`
	CodeListQualifier         string `xml:"codeListQualifier"`
	CodeListResponsibleAgency string `xml:"codeListResponsibleAgency"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ApplicationErrorDetail *ApplicationErrorDetailType `xml:"applicationErrorDetail"`
}

// InteractiveFreeTextTypeI is Free flow text describing the error
type InteractiveFreeTextTypeI struct {
	ErrorFreeText string `xml:"errorFreeText"`
}

// ItemReferencesAndVersionsType is Gives the TSM ID number
type ItemReferencesAndVersionsType struct {
	ReferenceType   string                   `xml:"referenceType"`
	UniqueReference int                      `xml:"uniqueReference"`
	IDDescription   *UniqueIdDescriptionType `xml:"iDDescription"`
}

// ReferenceInformationTypeI is Passenger/segment/TSM reference details
type ReferenceInformationTypeI struct {
	RefDetails []*ReferencingDetailsTypeI `xml:"refDetails"`
}

// ReferencingDetailsTypeI is Passenger/segment/TSM reference number
type ReferencingDetailsTypeI struct {
	RefQualifier string `xml:"refQualifier"`
	RefNumber    int    `xml:"refNumber"`
}

// ReservationControlInformationDetailsTypeI is Record locator.
type ReservationControlInformationDetailsTypeI struct {
	ControlNumber string `xml:"controlNumber"`
}

// ReservationControlInformationTypeI is Reservation control information
type ReservationControlInformationTypeI struct {
	ReservationInformation *ReservationControlInformationDetailsTypeI `xml:"reservationInformation"`
}

// UniqueIdDescriptionType is The TSM Id Number : The Id number allows to determine a TSM in the single manner.
type UniqueIdDescriptionType struct {
	IDSequenceNumber int `xml:"iDSequenceNumber"`
}