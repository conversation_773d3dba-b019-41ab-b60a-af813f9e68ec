package queue_place_pnr

// ErrorReturn ...
type ErrorReturn struct {
	// XMLName         xml.Name                          `xml:"errorReturn"`
	ErrorDefinition *ApplicationErrorInformationTypeI `xml:"errorDefinition"`
	ErrorText       *FreeTextInformationType          `xml:"errorText"`
}

// QueuePlacePNRReply ...
type QueuePlacePNRReply struct {
	// XMLName       xml.Name                            `xml:"Queue_PlacePNRReply"`
	ErrorReturn   *ErrorReturn                        `xml:"errorReturn"`
	RecordLocator *ReservationControlInformationTypeI `xml:"recordLocator"`
}

// ApplicationErrorDetailTypeI is error code owner
type ApplicationErrorDetailTypeI struct {
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationTypeI is error details
type ApplicationErrorInformationTypeI struct {
	ErrorDetails *ApplicationErrorDetailTypeI `xml:"errorDetails"`
}

// FreeTextDetailsType is Encoding Informations
type FreeTextDetailsType struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	Source               string `xml:"source"`
	Encoding             string `xml:"encoding"`
}

// FreeTextInformationType is Free text
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []string             `xml:"freeText"`
}

// ReservationControlInformationDetailsTypeI is contains the record locator to be queue placed
type ReservationControlInformationDetailsTypeI struct {
	ControlNumber string `xml:"controlNumber"`
}

// ReservationControlInformationTypeI is contains the record locator
type ReservationControlInformationTypeI struct {
	Reservation *ReservationControlInformationDetailsTypeI `xml:"reservation"`
}

// AlphaNumericStringLength1To199 is Format limitations: an..199
type AlphaNumericStringLength1To199 string

// AlphaNumericStringLength1To3 is Format limitations: an..3
type AlphaNumericStringLength1To3 string

// AlphaNumericStringLength1To8 is Format limitations: an..8
type AlphaNumericStringLength1To8 string
