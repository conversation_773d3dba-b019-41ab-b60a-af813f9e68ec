package fare_informative_best_pricing

import (
	"encoding/xml"
)

// GeneralIndicatorsGroup ...
type GeneralIndicatorsGroup struct {
	// XMLName           xml.Name                      `xml:"generalIndicatorsGroup"`
	GeneralIndicators *PricingTicketingDetailsTypeI `xml:"generalIndicators"`
}

// SurchargesGroup ...
type SurchargesGroup struct {
	// XMLName         xml.Name                            `xml:"surchargesGroup"`
	TaxesAmount     *TaxTypeI                           `xml:"taxesAmount"`
	PenaltiesAmount *DiscountAndPenaltyInformationTypeI `xml:"penaltiesAmount"`
	PfcAmount       []*MonetaryInformationTypeI         `xml:"pfcAmount"`
}

// CorporateGroup ...
type CorporateGroup struct {
	// XMLName       xml.Name                         `xml:"corporateGroup"`
	CorporateData *FareCalculationCodeDetailsTypeI `xml:"corporateData"`
}

// NegoFareGroup ...
type NegoFareGroup struct {
	// XMLName               xml.Name                            `xml:"negoFareGroup"`
	NegoFareIndicators    *PricingTicketingSubsequentTypeI    `xml:"negoFareIndicators"`
	ExtNegoFareIndicators *FareQualifierDetailsTypeI          `xml:"extNegoFareIndicators"`
	NegoFareAmount        *DiscountAndPenaltyInformationTypeI `xml:"negoFareAmount"`
	NegoFareText          *InteractiveFreeTextTypeI           `xml:"negoFareText"`
}

// CabinGroup ...
type CabinGroup struct {
	// XMLName      xml.Name                 `xml:"cabinGroup"`
	CabinSegment *ProductInformationTypeI `xml:"cabinSegment"`
}

// SegmentLevelGroup ...
type SegmentLevelGroup struct {
	// XMLName                      xml.Name                             `xml:"segmentLevelGroup"`
	SegmentInformation           *TravelProductInformationTypeI33489S `xml:"segmentInformation"`
	AdditionalInformation        *PricingTicketingDetailsType         `xml:"additionalInformation"`
	FareBasis                    *FareQualifierDetailsTypeI           `xml:"fareBasis"`
	CabinGroup                   []*CabinGroup                        `xml:"cabinGroup"`
	BaggageAllowance             *ExcessBaggageTypeI                  `xml:"baggageAllowance"`
	PtcSegment                   *NumberOfUnitsTypeI                  `xml:"ptcSegment"`
	CouponInformation            *QuantityTypeI                       `xml:"couponInformation"`
	FlightProductInformationType []*FlightProductInformationType      `xml:"flightProductInformationType"`
}

// Group29 ...
type Group29 struct {
	// XMLName                  xml.Name                             `xml:"group29"`
	StructuredFareCalcG28ADT *ActionDetailsTypeI                  `xml:"structuredFareCalcG28ADT"`
	StructuredFareCalcG28TVL *TravelProductInformationTypeI33489S `xml:"structuredFareCalcG28TVL"`
}

// Group28 ...
type Group28 struct {
	// XMLName                  xml.Name                         `xml:"group28"`
	StructuredFareCalcG28ITM *ItemNumberTypeI                 `xml:"structuredFareCalcG28ITM"`
	Group29                  []*Group29                       `xml:"group29"`
	StructuredFareCalcG28MON *MonetaryInformationTypeI        `xml:"structuredFareCalcG28MON"`
	StructuredFareCalcG28PTS *PricingTicketingSubsequentTypeI `xml:"structuredFareCalcG28PTS"`
	StructuredFareCalcG28FCC *FareCalculationCodeDetailsTypeI `xml:"structuredFareCalcG28FCC"`
	StructuredFareCalcG28PTK *PricingTicketingDetailsTypeI    `xml:"structuredFareCalcG28PTK"`
	StructuredFareCalcG28FRU *FareRulesInformationTypeI       `xml:"structuredFareCalcG28FRU"`
}

// Group27 ...
type Group27 struct {
	// XMLName                  xml.Name                  `xml:"group27"`
	StructuredFareCalcG27EQN *NumberOfUnitsTypeI       `xml:"structuredFareCalcG27EQN"`
	Group28                  []*Group28                `xml:"group28"`
	DummySegmentGroup27      *DummySegmentTypeI        `xml:"dummySegmentGroup27"`
	StructuredFareCalcG27MON *MonetaryInformationTypeI `xml:"structuredFareCalcG27MON"`
	StructuredFareCalcG27TXD []*TaxTypeI               `xml:"structuredFareCalcG27TXD"`
	StructuredFareCalcG27CVR *ConversionRateTypeI      `xml:"structuredFareCalcG27CVR"`
}

// StructuredFareCalcGroup ...
type StructuredFareCalcGroup struct {
	// XMLName               xml.Name                       `xml:"structuredFareCalcGroup"`
	StructureFareCalcRoot *FareComponentInformationTypeI `xml:"structureFareCalcRoot"`
	Group27               []*Group27                     `xml:"group27"`
}

// FeeDetails ...
type FeeDetails struct {
	// XMLName        xml.Name                      `xml:"feeDetails"`
	FeeInfo        *SpecificDataInformationTypeI `xml:"feeInfo"`
	FeeAmounts     *MonetaryInformationTypeI     `xml:"feeAmounts"`
	FeeTaxes       []*TaxTypeI                   `xml:"feeTaxes"`
	FeeDescription *InteractiveFreeTextTypeI     `xml:"feeDescription"`
}

// CarrierFeeGroup ...
type CarrierFeeGroup struct {
	// XMLName    xml.Name               `xml:"carrierFeeGroup"`
	FeeType    *SelectionDetailsTypeI `xml:"feeType"`
	FeeDetails []*FeeDetails          `xml:"feeDetails"`
}

// FareInfoGroup ...
type FareInfoGroup struct {
	// XMLName                   xml.Name                        `xml:"fareInfoGroup"`
	EmptySegment              *FareInformationTypeI           `xml:"emptySegment"`
	PricingIndicators         *PricingTicketingDetailsType    `xml:"pricingIndicators"`
	FareAmount                *MonetaryInformationType199048S `xml:"fareAmount"`
	TextData                  []*InteractiveFreeTextTypeI     `xml:"textData"`
	OfferReferences           *OfferReferencesType            `xml:"offerReferences"`
	SurchargesGroup           *SurchargesGroup                `xml:"surchargesGroup"`
	CorporateGroup            []*CorporateGroup               `xml:"corporateGroup"`
	NegoFareGroup             []*NegoFareGroup                `xml:"negoFareGroup"`
	SegmentLevelGroup         []*SegmentLevelGroup            `xml:"segmentLevelGroup"`
	StructuredFareCalcGroup   *StructuredFareCalcGroup        `xml:"structuredFareCalcGroup"`
	CarrierFeeGroup           []*CarrierFeeGroup              `xml:"carrierFeeGroup"`
	FareComponentDetailsGroup []*FareComponentDetailsType     `xml:"fareComponentDetailsGroup"`
}

// PricingGroupLevelGroup ...
type PricingGroupLevelGroup struct {
	// XMLName       xml.Name                       `xml:"pricingGroupLevelGroup"`
	NumberOfPax   *SegmentRepetitionControlTypeI `xml:"numberOfPax"`
	PassengersID  []*SpecificTravellerTypeI      `xml:"passengersID"`
	FareInfoGroup *FareInfoGroup                 `xml:"fareInfoGroup"`
}

// MainGroup ...
type MainGroup struct {
	// XMLName                xml.Name                      `xml:"mainGroup"`
	DummySegment           *DummySegmentTypeI            `xml:"dummySegment"`
	ConvertionRate         *ConversionRateTypeI          `xml:"convertionRate"`
	GeneralIndicatorsGroup *GeneralIndicatorsGroup       `xml:"generalIndicatorsGroup"`
	PricingGroupLevelGroup []*PricingGroupLevelGroup     `xml:"pricingGroupLevelGroup"`
	Media                  *VersionedBinaryStructureType `xml:"media"`
}

// FareInformativeBestPricingWithoutPNRReply ...
type FareInformativeBestPricingWithoutPNRReply struct {
	// XMLName        xml.Name                   `xml:"Fare_InformativeBestPricingWithoutPNRReply"`
	MessageDetails *MessageActionDetailsTypeI `xml:"messageDetails"`
	ErrorGroup     *ErrorGroupType            `xml:"errorGroup"`
	MainGroup      *MainGroup                 `xml:"mainGroup"`
}

// ActionDetailsTypeI is To specify the action that should be taken on a selected reference number.
type ActionDetailsTypeI struct {
	NumberOfItemsDetails *ProcessingInformationTypeI `xml:"numberOfItemsDetails"`
	LastItemsDetails     []*ReferenceTypeI           `xml:"lastItemsDetails"`
}

// AdditionalFareQualifierDetailsTypeI is To specify the fare basis and ticket designator codes.
type AdditionalFareQualifierDetailsTypeI struct {
	RateClass         *string   `xml:"rateClass"`
	CommodityCategory *string   `xml:"commodityCategory"`
	PricingGroup      *string   `xml:"pricingGroup"`
	SecondRateClass   []*string `xml:"secondRateClass"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      *string `xml:"errorCode"`
	ErrorCategory  *string `xml:"errorCategory"`
	ErrorCodeOwner *string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// BaggageDetailsTypeI is To specify the number and weight of baggage.
type BaggageDetailsTypeI struct {
	FreeAllowance    *int    `xml:"freeAllowance"`
	Measurement      *int    `xml:"measurement"`
	QuantityCode     *string `xml:"quantityCode"`
	UnitQualifier    *string `xml:"unitQualifier"`
	ProcessIndicator *string `xml:"processIndicator"`
}

// BagtagDetailsTypeI is To identify baggage by company identification, serial numbers, and destination.
type BagtagDetailsTypeI struct {
	Company            *string `xml:"company"`
	Identifier         *string `xml:"identifier"`
	Number             *int    `xml:"number"`
	Location           *string `xml:"location"`
	CompanyNumber      *string `xml:"companyNumber"`
	Indicator          *string `xml:"indicator"`
	Characteristic     *string `xml:"characteristic"`
	SpecialRequirement *string `xml:"specialRequirement"`
	Measurement        *int    `xml:"measurement"`
	UnitQualifier      *string `xml:"unitQualifier"`
	Description        *string `xml:"description"`
}

// BinaryDataType is Contains the compression type used. Either ZLIB or NONE. If not present, should be considered as NONE
type BinaryDataType struct {
	DataLength   *int    `xml:"dataLength"`
	DataType     *string `xml:"dataType"`
	BinaryData   *string `xml:"binaryData"`
	CompressType *string `xml:"compressType"`
}

// CabinProductDetailsType is Availibility status : posting level
type CabinProductDetailsType struct {
	Rbd             *string `xml:"rbd"`
	BookingModifier *string `xml:"bookingModifier"`
	Cabin           *string `xml:"cabin"`
	AvlStatus       *string `xml:"avlStatus"`
}

// CompanyIdentificationNumbersTypeI is Number to identify a company and any associated companies.
type CompanyIdentificationNumbersTypeI struct {
	Identifier      *string `xml:"identifier"`
	OtherIdentifier *string `xml:"otherIdentifier"`
}

// CompanyIdentificationTypeI is Carrier owner fo the fare family
type CompanyIdentificationTypeI struct {
	OtherCompany *string `xml:"otherCompany"`
}

// CompanyIdentificationTypeI58119C is Code or name to identify a company and any associated companies.
type CompanyIdentificationTypeI58119C struct {
	// XMLName          xml.Name `xml:"CompanyIdentificationTypeI_58119C"`
	MarketingCompany *string `xml:"marketingCompany"`
	OperatingCompany *string `xml:"operatingCompany"`
	OtherCompany     *string `xml:"otherCompany"`
}

// ConversionRateDetailsTypeI is To specify the conversion rate and the monetary amount.
type ConversionRateDetailsTypeI struct {
	ConversionType          *string  `xml:"conversionType"`
	Currency                *string  `xml:"currency"`
	RateType                *string  `xml:"rateType"`
	PricingAmount           *float64 `xml:"pricingAmount"`
	ConvertedValueAmount    *int     `xml:"convertedValueAmount"`
	DutyTaxFeeType          *string  `xml:"dutyTaxFeeType"`
	MeasurementValue        *int     `xml:"measurementValue"`
	MeasurementSignificance *string  `xml:"measurementSignificance"`
}

// ConversionRateTypeI is To specify details of a conversion rate related to an amount.
type ConversionRateTypeI struct {
	ConversionRateDetails *ConversionRateDetailsTypeI   `xml:"conversionRateDetails"`
	OtherConvRateDetails  []*ConversionRateDetailsTypeI `xml:"otherConvRateDetails"`
}

// CouponTaxDetailsGroup is Location info
type CouponTaxDetailsGroup struct {
	// XMLName        xml.Name                         `xml:"couponTaxDetailsGroup"`
	TaxTriggerInfo *DutyTaxFeeDetailsType           `xml:"taxTriggerInfo"`
	TaxDetails     *TaxType                         `xml:"taxDetails"`
	MonetaryInfo   *MonetaryInformationType         `xml:"monetaryInfo"`
	LocationInfo   *PlaceLocationIdentificationType `xml:"locationInfo"`
}

// CouponDetailsType is Flight Connection Type
type CouponDetailsType struct {
	ProductId             *ReferenceInfoType            `xml:"productId"`
	FlightConnectionType  *TravelProductInformationType `xml:"flightConnectionType"`
	CouponTaxDetailsGroup []*CouponTaxDetailsGroup      `xml:"couponTaxDetailsGroup"`
}

// DataInformationTypeI is To identify specific data and a quantity related to the data.
type DataInformationTypeI struct {
	Indicator *string `xml:"indicator"`
	Value     *int    `xml:"value"`
	Unit      *string `xml:"unit"`
}

// DataTypeInformationTypeI is To identify the type of data to be sent and to qualify the data when required.
type DataTypeInformationTypeI struct {
	Type        *string `xml:"type"`
	StatusEvent *string `xml:"statusEvent"`
}

// DiscountAndPenaltyInformationTypeI is To specify information about discounts and penalties
type DiscountAndPenaltyInformationTypeI struct {
	DiscountPenaltyQualifier *string                                    `xml:"discountPenaltyQualifier"`
	DiscountPenaltyDetails   []*DiscountPenaltyMonetaryInformationTypeI `xml:"discountPenaltyDetails"`
}

// DiscountPenaltyInformationTypeI is To indicate the discounts and penalties by fare type.
type DiscountPenaltyInformationTypeI struct {
	FareQualifier *string  `xml:"fareQualifier"`
	RateCategory  *string  `xml:"rateCategory"`
	Amount        *float64 `xml:"amount"`
	Percentage    *int     `xml:"percentage"`
}

// DiscountPenaltyInformationType ...
type DiscountPenaltyInformationType struct {
	FareQualifier *string `xml:"fareQualifier"`
}

// DiscountPenaltyMonetaryInformationTypeI is To specify the type of discount and penalty information, the monetary amount, and associated information.
type DiscountPenaltyMonetaryInformationTypeI struct {
	Function   *string  `xml:"function"`
	AmountType *string  `xml:"amountType"`
	Amount     *float64 `xml:"amount"`
	Rate       *string  `xml:"rate"`
	Currency   *string  `xml:"currency"`
}

// DummySegmentTypeI is To serve the purpose of a mandatory segment at the beginning of a group and to avoid segment collision.
type DummySegmentTypeI struct {
}

// DutyTaxFeeDetailsType is Tax qualifier
type DutyTaxFeeDetailsType struct {
	TaxQualifier *string `xml:"taxQualifier"`
}

// ErrorGroupType is The desciption of warning or error.
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType         `xml:"errorWarningDescription"`
}

// ExcessBaggageDetailsTypeI is To specify details concerning a traveller's excess baggage.
type ExcessBaggageDetailsTypeI struct {
	Currency         *string  `xml:"currency"`
	Amount           *float64 `xml:"amount"`
	ProcessIndicator *string  `xml:"processIndicator"`
}

// ExcessBaggageTypeI is To specify information concerning excess baggage charges and the associated baggage details.
type ExcessBaggageTypeI struct {
	ExcessBaggageDetails *ExcessBaggageDetailsTypeI `xml:"excessBaggageDetails"`
	BaggageDetails       *BaggageDetailsTypeI       `xml:"baggageDetails"`
	OtherBaggageDetails  *BaggageDetailsTypeI       `xml:"otherBaggageDetails"`
	ExtraBaggageDetails  *BaggageDetailsTypeI       `xml:"extraBaggageDetails"`
	BagTagDetails        []*BagtagDetailsTypeI      `xml:"bagTagDetails"`
}

// FareCalculationCodeDetailsTypeI is To specify fare calculation information.
type FareCalculationCodeDetailsTypeI struct {
	ChargeCategory    *string  `xml:"chargeCategory"`
	Amount            *float64 `xml:"amount"`
	LocationCode      *string  `xml:"locationCode"`
	OtherLocationCode *string  `xml:"otherLocationCode"`
	Rate              *float64 `xml:"rate"`
}

// FareCategoryCodesTypeI is To designate non-system specific combinations of fare types.
type FareCategoryCodesTypeI struct {
	FareType      *string   `xml:"fareType"`
	OtherFareType []*string `xml:"otherFareType"`
}

// FareComponentDetailsTypeI is Input designator
type FareComponentDetailsTypeI struct {
	DataType        *string `xml:"dataType"`
	Count           *int    `xml:"count"`
	PricingDate     *string `xml:"pricingDate"`
	AccountCode     *string `xml:"accountCode"`
	InputDesignator *string `xml:"inputDesignator"`
}

// FareComponentDetailsType is Used to specify coupons included in the fare component or in the bound.
type FareComponentDetailsType struct {
	FareComponentID      *ItemNumberType                   `xml:"fareComponentID"`
	MarketFareComponent  *TravelProductInformationTypeI    `xml:"marketFareComponent"`
	MonetaryInformation  *MonetaryInformationType199049S   `xml:"monetaryInformation"`
	ComponentClassInfo   *PricingOrTicketingSubsequentType `xml:"componentClassInfo"`
	FareQualifiersDetail *FareQualifierDetailsType         `xml:"fareQualifiersDetail"`
	FareFamilyDetails    *FareFamilyType                   `xml:"fareFamilyDetails"`
	FareFamilyOwner      *TransportIdentifierType          `xml:"fareFamilyOwner"`
	CouponDetailsGroup   []*CouponDetailsType              `xml:"couponDetailsGroup"`
}

// FareComponentInformationTypeI is Ticket document number
type FareComponentInformationTypeI struct {
	FareComponentDetails *FareComponentDetailsTypeI `xml:"fareComponentDetails"`
	TicketNumber         *string                    `xml:"ticketNumber"`
}

// FareDetailsTypeI is Fare indicators
type FareDetailsTypeI struct {
	Qualifier    *string  `xml:"qualifier"`
	Rate         *float64 `xml:"rate"`
	Country      *string  `xml:"country"`
	FareCategory *string  `xml:"fareCategory"`
}

// FareFamilyDetailsType is Commercial fare Family Short name
type FareFamilyDetailsType struct {
	CommercialFamily *string `xml:"commercialFamily"`
}

// FareFamilyType is Indicates Commercial Fare Family Short names
type FareFamilyType struct {
	FareFamilyname          *string                  `xml:"fareFamilyname"`
	Hierarchy               *int                     `xml:"hierarchy"`
	CommercialFamilyDetails []*FareFamilyDetailsType `xml:"commercialFamilyDetails"`
}

// FareInformationTypeI is Not used
type FareInformationTypeI struct {
	ValueQualifier   *string                           `xml:"valueQualifier"`
	Value            *int                              `xml:"value"`
	FareDetails      *FareDetailsTypeI                 `xml:"fareDetails"`
	IdentityNumber   *string                           `xml:"identityNumber"`
	FareTypeGrouping *FareTypeGroupingInformationTypeI `xml:"fareTypeGrouping"`
	RateCategory     []*string                         `xml:"rateCategory"`
}

// FareQualifierDetailsTypeI is To specify the details which qualify a fare
type FareQualifierDetailsTypeI struct {
	MovementType          *string                              `xml:"movementType"`
	FareCategories        *FareCategoryCodesTypeI              `xml:"fareCategories"`
	FareDetails           *FareDetailsTypeI                    `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareQualifierDetailsTypeI `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountPenaltyInformationTypeI   `xml:"discountDetails"`
}

// FareQualifierDetailsType ...
type FareQualifierDetailsType struct {
	DiscountDetails []*DiscountPenaltyInformationType `xml:"discountDetails"`
}

// FareRulesInformationTypeI is To specify the tariff, fare supplier, and paragraph number for a fare rule.
type FareRulesInformationTypeI struct {
	TariffClassId  *string                           `xml:"tariffClassId"`
	CompanyDetails *CompanyIdentificationTypeI58119C `xml:"companyDetails"`
	RuleSectionId  []*string                         `xml:"ruleSectionId"`
}

// FareTypeGroupingInformationTypeI is To designate system specific combinations of fare types and fare groupings
type FareTypeGroupingInformationTypeI struct {
	PricingGroup []*string `xml:"pricingGroup"`
}

// FlightProductInformationType is Indicates flight cabin details
type FlightProductInformationType struct {
	CabinProduct []*CabinProductDetailsType `xml:"cabinProduct"`
	// DummyNET     *DummyNET                  `xml:"Dummy.NET"`
}

// FreeTextDetailsType ...
type FreeTextDetailsType struct {
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Status               *string `xml:"status"`
	CompanyId            *string `xml:"companyId"`
	Language             *string `xml:"language"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextInformationType ...
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []*string            `xml:"freeText"`
}

// FreeTextQualificationTypeI ...
type FreeTextQualificationTypeI struct {
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Status               *string `xml:"status"`
	CompanyId            *string `xml:"companyId"`
	Language             *string `xml:"language"`
}

// InteractiveFreeTextTypeI ...
type InteractiveFreeTextTypeI struct {
	FreeTextQualification *FreeTextQualificationTypeI `xml:"freeTextQualification"`
	FreeText              []*string                   `xml:"freeText"`
}

// ItemNumberIdentificationTypeI ...
type ItemNumberIdentificationTypeI struct {
	Number            *string `xml:"number"`
	Type              *string `xml:"type"`
	Qualifier         *string `xml:"qualifier"`
	ResponsibleAgency *string `xml:"responsibleAgency"`
}

// ItemNumberIdentificationType ...
type ItemNumberIdentificationType struct {
	Number *string `xml:"number"`
	Type   *string `xml:"type"`
}

// ItemNumberTypeI ...
type ItemNumberTypeI struct {
	ItemNumberDetails []*ItemNumberIdentificationTypeI `xml:"itemNumberDetails"`
}

// ItemNumberType ...
type ItemNumberType struct {
	ItemNumberDetails []*ItemNumberIdentificationType `xml:"itemNumberDetails"`
}

// LocationDetailsTypeI ...
type LocationDetailsTypeI struct {
	City    *string `xml:"city"`
	Country *string `xml:"country"`
}

// LocationIdentificationBatchType ...
type LocationIdentificationBatchType struct {
	Code *string `xml:"code"`
}

// LocationTypeI ...
type LocationTypeI struct {
	TrueLocationId *string `xml:"trueLocationId"`
}

// LocationTypeI58141C ...
type LocationTypeI58141C struct {
	// XMLName        xml.Name `xml:"LocationTypeI_58141C"`
	TrueLocationId *string `xml:"trueLocationId"`
	TrueLocation   *string `xml:"trueLocation"`
}

// MarriageControlDetailsTypeI ...
type MarriageControlDetailsTypeI struct {
	Relation           *string `xml:"relation"`
	MarriageIdentifier *int    `xml:"marriageIdentifier"`
	LineNumber         *int    `xml:"lineNumber"`
	OtherRelation      *string `xml:"otherRelation"`
	CarrierCode        *string `xml:"carrierCode"`
}

// MessageActionDetailsTypeI ...
type MessageActionDetailsTypeI struct {
	MessageFunctionDetails *MessageFunctionBusinessDetailsTypeI `xml:"messageFunctionDetails"`
	ResponseType           *string                              `xml:"responseType"`
}

// MessageFunctionBusinessDetailsTypeI ...
type MessageFunctionBusinessDetailsTypeI struct {
	BusinessFunction          *string   `xml:"businessFunction"`
	MessageFunction           *string   `xml:"messageFunction"`
	ResponsibleAgency         *string   `xml:"responsibleAgency"`
	AdditionalMessageFunction []*string `xml:"additionalMessageFunction"`
}

// MessageIdentifierType ...
type MessageIdentifierType struct {
	MessageTypeIdentifier    *string `xml:"messageTypeIdentifier"`
	MessageTypeVersionNumber *string `xml:"messageTypeVersionNumber"`
	MessageTypeReleaseNumber *string `xml:"messageTypeReleaseNumber"`
	ControllingAgency        *string `xml:"controllingAgency"`
	Domain                   *string `xml:"domain"`
}

// MessageStructureVersionType ...
type MessageStructureVersionType struct {
	MessageIdentifier *MessageIdentifierType `xml:"messageIdentifier"`
	StructureType     *string                `xml:"structureType"`
}

// MonetaryInformationDetailsTypeI ...
type MonetaryInformationDetailsTypeI struct {
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
	Location      *string `xml:"location"`
}

// MonetaryInformationDetailsType ...
type MonetaryInformationDetailsType struct {
	TypeQualifier *string  `xml:"typeQualifier"`
	Amount        *float64 `xml:"amount"`
	Currency      *string  `xml:"currency"`
	Location      *string  `xml:"location"`
}

// MonetaryInformationDetailsType223822C ...
type MonetaryInformationDetailsType223822C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsType_223822C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsType262581C ...
type MonetaryInformationDetailsType262581C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsType_262581C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
	Location      *string `xml:"location"`
}

// MonetaryInformationTypeI ...
type MonetaryInformationTypeI struct {
	MonetaryDetails      *MonetaryInformationDetailsTypeI   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsTypeI `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType ...
type MonetaryInformationType struct {
	MonetaryDetails      *MonetaryInformationDetailsType   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType199048S ...
type MonetaryInformationType199048S struct {
	// XMLName              xml.Name                                 `xml:"MonetaryInformationType_199048S"`
	MonetaryDetails      *MonetaryInformationDetailsType262581C   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType262581C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType199049S ...
type MonetaryInformationType199049S struct {
	// XMLName              xml.Name                                 `xml:"MonetaryInformationType_199049S"`
	MonetaryDetails      *MonetaryInformationDetailsType223822C   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType223822C `xml:"otherMonetaryDetails"`
}

// NumberOfUnitDetailsTypeI ...
type NumberOfUnitDetailsTypeI struct {
	NumberOfUnit  *int    `xml:"numberOfUnit"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// NumberOfUnitsTypeI ...
type NumberOfUnitsTypeI struct {
	QuantityDetails      *NumberOfUnitDetailsTypeI   `xml:"quantityDetails"`
	OtherQuantityDetails []*NumberOfUnitDetailsTypeI `xml:"otherQuantityDetails"`
}

// OfferReferencesType ...
type OfferReferencesType struct {
	OfferIdentifier *OfferType                  `xml:"offerIdentifier"`
	References      []*ReferenceInfoType218150S `xml:"references"`
}

// OfferType ...
type OfferType struct {
	Reference            *string `xml:"reference"`
	OfferId              *string `xml:"offerId"`
	UniqueOfferReference *string `xml:"uniqueOfferReference"`
}

// PlaceLocationIdentificationType ...
type PlaceLocationIdentificationType struct {
	LocationType        *string                          `xml:"locationType"`
	LocationDescription *LocationIdentificationBatchType `xml:"locationDescription"`
}

// PricingOrTicketingSubsequentType ...
type PricingOrTicketingSubsequentType struct {
	FareBasisDetails *RateTariffClassInformationType `xml:"fareBasisDetails"`
}

// PricingTicketingDetailsTypeI ...
type PricingTicketingDetailsTypeI struct {
	PriceTicketDetails     *PricingTicketingInformationTypeI  `xml:"priceTicketDetails"`
	PriceTariffType        *string                            `xml:"priceTariffType"`
	ProductDateTimeDetails *ProductDateTimeTypeI              `xml:"productDateTimeDetails"`
	CompanyDetails         *CompanyIdentificationTypeI58119C  `xml:"companyDetails"`
	CompanyNumberDetails   *CompanyIdentificationNumbersTypeI `xml:"companyNumberDetails"`
	LocationDetails        *LocationDetailsTypeI              `xml:"locationDetails"`
	OtherLocationDetails   *LocationDetailsTypeI              `xml:"otherLocationDetails"`
	IdNumber               *int                               `xml:"idNumber"`
	MonetaryAmount         *float64                           `xml:"monetaryAmount"`
}

// PricingTicketingDetailsType ...
type PricingTicketingDetailsType struct {
	PriceTicketDetails     *PricingTicketingInformationTypeI  `xml:"priceTicketDetails"`
	PriceTariffType        *string                            `xml:"priceTariffType"`
	ProductDateTimeDetails *ProductDateTimeTypeI              `xml:"productDateTimeDetails"`
	CompanyDetails         *CompanyIdentificationTypeI58119C  `xml:"companyDetails"`
	CompanyNumberDetails   *CompanyIdentificationNumbersTypeI `xml:"companyNumberDetails"`
	LocationDetails        *LocationDetailsTypeI              `xml:"locationDetails"`
	OtherLocationDetails   *LocationDetailsTypeI              `xml:"otherLocationDetails"`
	IdNumber               *string                            `xml:"idNumber"`
	MonetaryAmount         *float64                           `xml:"monetaryAmount"`
}

// PricingTicketingInformationTypeI ...
type PricingTicketingInformationTypeI struct {
	Indicators []*string `xml:"indicators"`
}

// PricingTicketingSubsequentTypeI ...
type PricingTicketingSubsequentTypeI struct {
	ItemNumber                 *string                          `xml:"itemNumber"`
	FareBasisDetails           *RateTariffClassInformationTypeI `xml:"fareBasisDetails"`
	FareValue                  *int                             `xml:"fareValue"`
	PriceType                  *string                          `xml:"priceType"`
	SpecialCondition           *string                          `xml:"specialCondition"`
	OtherSpecialCondition      *string                          `xml:"otherSpecialCondition"`
	AdditionalSpecialCondition *string                          `xml:"additionalSpecialCondition"`
	TaxCategory                []*string                        `xml:"taxCategory"`
}

// ProcessingInformationTypeI ...
type ProcessingInformationTypeI struct {
	ActionQualifier    *string `xml:"actionQualifier"`
	ReferenceQualifier *string `xml:"referenceQualifier"`
	NumberOfItems      *string `xml:"numberOfItems"`
}

// ProductDateTimeTypeI ...
type ProductDateTimeTypeI struct {
	DepartureDate *string `xml:"departureDate"`
	DepartureTime *int    `xml:"departureTime"`
	ArrivalDate   *string `xml:"arrivalDate"`
	ArrivalTime   *int    `xml:"arrivalTime"`
	DateVariation *int    `xml:"dateVariation"`
}

// ProductDetailsTypeI ...
type ProductDetailsTypeI struct {
	Designator         *string   `xml:"designator"`
	AvailabilityStatus *string   `xml:"availabilityStatus"`
	SpecialService     *string   `xml:"specialService"`
	Option             []*string `xml:"option"`
}

// ProductIdentificationDetailsTypeI ...
type ProductIdentificationDetailsTypeI struct {
	FlightNumber      *string   `xml:"flightNumber"`
	BookingClass      *string   `xml:"bookingClass"`
	OperationalSuffix *string   `xml:"operationalSuffix"`
	Modifier          []*string `xml:"modifier"`
}

// ProductInformationTypeI ...
type ProductInformationTypeI struct {
	ProductDetailsQualifier *string                `xml:"productDetailsQualifier"`
	BookingClassDetails     []*ProductDetailsTypeI `xml:"bookingClassDetails"`
}

// ProductTypeDetailsTypeI ...
type ProductTypeDetailsTypeI struct {
	FlightIndicator []*string `xml:"flightIndicator"`
}

// ProductTypeDetailsType ...
type ProductTypeDetailsType struct {
	FlightIndicator *string `xml:"flightIndicator"`
}

// QuantityDetailsTypeI ...
type QuantityDetailsTypeI struct {
	Qualifier *string `xml:"qualifier"`
	Value     *int    `xml:"value"`
	Unit      *string `xml:"unit"`
}

// QuantityTypeI ...
type QuantityTypeI struct {
	QuantityDetails      *QuantityDetailsTypeI   `xml:"quantityDetails"`
	OtherquantityDetails []*QuantityDetailsTypeI `xml:"otherquantityDetails"`
}

// RateTariffClassInformationTypeI ...
type RateTariffClassInformationTypeI struct {
	RateTariffClass          *string `xml:"rateTariffClass"`
	RateTariffIndicator      *string `xml:"rateTariffIndicator"`
	OtherRateTariffClass     *string `xml:"otherRateTariffClass"`
	OtherRateTariffIndicator *string `xml:"otherRateTariffIndicator"`
}

// RateTariffClassInformationType ...
type RateTariffClassInformationType struct {
	RateTariffClass      *string `xml:"rateTariffClass"`
	OtherRateTariffClass *string `xml:"otherRateTariffClass"`
}

// ReferenceInfoType ...
type ReferenceInfoType struct {
	ReferenceDetails *ReferencingDetailsType `xml:"referenceDetails"`
}

// ReferenceInfoType218150S ...
type ReferenceInfoType218150S struct {
	// XMLName          xml.Name                         `xml:"ReferenceInfoType_218150S"`
	ReferenceDetails []*ReferencingDetailsType300632C `xml:"referenceDetails"`
	// DummyNET         *DummyNET                        `xml:"Dummy.NET"`
}

// ReferenceTypeI ...
type ReferenceTypeI struct {
	NumberOfItems      *string   `xml:"numberOfItems"`
	LastItemIdentifier []*string `xml:"lastItemIdentifier"`
}

// ReferencingDetailsType ...
type ReferencingDetailsType struct {
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType300632C ...
type ReferencingDetailsType300632C struct {
	XMLName xml.Name `xml:"ReferencingDetailsType_300632C"`
	Type    *string  `xml:"type"`
	Value   *string  `xml:"value"`
}

// SegmentRepetitionControlDetailsTypeI ...
type SegmentRepetitionControlDetailsTypeI struct {
	Quantity           *int `xml:"quantity"`
	NumberOfUnits      *int `xml:"numberOfUnits"`
	TotalNumberOfItems *int `xml:"totalNumberOfItems"`
}

// SegmentRepetitionControlTypeI ...
type SegmentRepetitionControlTypeI struct {
	SegmentControlDetails []*SegmentRepetitionControlDetailsTypeI `xml:"segmentControlDetails"`
}

// SelectionDetailsInformationTypeI ...
type SelectionDetailsInformationTypeI struct {
	Option            *string `xml:"option"`
	OptionInformation *string `xml:"optionInformation"`
}

// SelectionDetailsTypeI ...
type SelectionDetailsTypeI struct {
	SelectionDetails      *SelectionDetailsInformationTypeI   `xml:"selectionDetails"`
	OtherSelectionDetails []*SelectionDetailsInformationTypeI `xml:"otherSelectionDetails"`
}

// SpecificDataInformationTypeI ...
type SpecificDataInformationTypeI struct {
	DataTypeInformation *DataTypeInformationTypeI `xml:"dataTypeInformation"`
	DataInformation     []*DataInformationTypeI   `xml:"dataInformation"`
}

// SpecificTravellerDetailsTypeI ...
type SpecificTravellerDetailsTypeI struct {
	ReferenceNumber  *string `xml:"referenceNumber"`
	MeasurementValue *int    `xml:"measurementValue"`
	FirstDate        *string `xml:"firstDate"`
	Surname          *string `xml:"surname"`
	FirstName        *string `xml:"firstName"`
}

// SpecificTravellerTypeI ...
type SpecificTravellerTypeI struct {
	TravellerDetails []*SpecificTravellerDetailsTypeI `xml:"travellerDetails"`
	// DummyNET         *DummyNET                        `xml:"Dummy.NET"`
}

// TaxDetailsTypeI ...
type TaxDetailsTypeI struct {
	Rate         *string   `xml:"rate"`
	CountryCode  *string   `xml:"countryCode"`
	CurrencyCode *string   `xml:"currencyCode"`
	Type         []*string `xml:"type"`
}

// TaxDetailsType ...
type TaxDetailsType struct {
	CountryCode *string   `xml:"countryCode"`
	Type        []*string `xml:"type"`
}

// TaxTypeI ...
type TaxTypeI struct {
	TaxCategory *string            `xml:"taxCategory"`
	TaxDetails  []*TaxDetailsTypeI `xml:"taxDetails"`
}

// TaxType ...
type TaxType struct {
	TaxCategory *string         `xml:"taxCategory"`
	TaxDetails  *TaxDetailsType `xml:"taxDetails"`
}

// TransportIdentifierType ...
type TransportIdentifierType struct {
	CompanyIdentification *CompanyIdentificationTypeI `xml:"companyIdentification"`
}

// TravelProductInformationTypeI ...
type TravelProductInformationTypeI struct {
	BoardPointDetails *LocationTypeI `xml:"boardPointDetails"`
	OffpointDetails   *LocationTypeI `xml:"offpointDetails"`
}

// TravelProductInformationTypeI33489S ...
type TravelProductInformationTypeI33489S struct {
	// XMLName              xml.Name                           `xml:"TravelProductInformationTypeI_33489S"`
	FlightDate           *ProductDateTimeTypeI              `xml:"flightDate"`
	BoardPointDetails    *LocationTypeI58141C               `xml:"boardPointDetails"`
	OffpointDetails      *LocationTypeI58141C               `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationTypeI58119C  `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsTypeI `xml:"flightIdentification"`
	FlightTypeDetails    *ProductTypeDetailsTypeI           `xml:"flightTypeDetails"`
	ItemNumber           *int                               `xml:"itemNumber"`
	SpecialSegment       *string                            `xml:"specialSegment"`
	MarriageDetails      []*MarriageControlDetailsTypeI     `xml:"marriageDetails"`
}

// TravelProductInformationType ...
type TravelProductInformationType struct {
	BoardPointDetails *LocationTypeI          `xml:"boardPointDetails"`
	OffpointDetails   *LocationTypeI          `xml:"offpointDetails"`
	FlightTypeDetails *ProductTypeDetailsType `xml:"flightTypeDetails"`
}

// VersionedBinaryStructureType ...
type VersionedBinaryStructureType struct {
	BinaryDescription *MessageStructureVersionType `xml:"binaryDescription"`
	BinaryData        *BinaryDataType              `xml:"binaryData"`
}
