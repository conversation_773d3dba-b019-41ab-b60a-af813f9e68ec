package air_retrieve_seat_map

// Cabin ...
type Cabin struct {
	//  XMLName            xml.Name                `xml:"cabin"`
	CompartmentDetails *CabinDetailsTypeI      `xml:"compartmentDetails"`
	CabinFacilities    []*CabinFacilitiesTypeI `xml:"cabinFacilities"`
}

// Row ...
type Row struct {
	//  XMLName       xml.Name                `xml:"row"`
	RowDetails    *RowDetailsType         `xml:"rowDetails"`
	CabinFacility []*CabinFacilitiesTypeI `xml:"cabinFacility"`
}

// SeatPrice ...
type SeatPrice struct {
	//  XMLName         xml.Name                    `xml:"seatPrice"`
	SeatPrice       *MonetaryInformationTypeI   `xml:"seatPrice"`
	DiscountCodes   *DiscountDataType           `xml:"discountCodes"`
	TaxDetails      *TaxTypeI                   `xml:"taxDetails"`
	PriceRef        *ReferenceInfoType215670S   `xml:"priceRef"`
	AdditionalInfo  *CodedAttributeType         `xml:"additionalInfo"`
	EnrichedContent []*CommunicationContactType `xml:"enrichedContent"`
	RowDetails      []*RowDetailsTypeI          `xml:"rowDetails"`
}

// CustomerCentricData ...
type CustomerCentricData struct {
	//  XMLName          xml.Name                     `xml:"customerCentricData"`
	Dummy            *DummySegmentTypeI           `xml:"dummy"`
	TravellerDetails []*TravellerInformationTypeI `xml:"travellerDetails"`
	SeatPrice        []*SeatPrice                 `xml:"seatPrice"`
}

// CatalogData ...
type CatalogData struct {
	//  XMLName              xml.Name                   `xml:"catalogData"`
	CatalogueRef         *ReferenceInfoType         `xml:"catalogueRef"`
	CatalogueDescription []*FreeTextInformationType `xml:"catalogueDescription"`
}

// SeatmapInformation ...
type SeatmapInformation struct {
	//  XMLName               xml.Name                       `xml:"seatmapInformation"`
	FlightDateInformation *TravelProductInformationTypeI `xml:"flightDateInformation"`
	SeatmapErrorInfo      *ErrorInformationTypeI         `xml:"seatmapErrorInfo"`
	SeatmapWarningInfo    *WarningInformationTypeI       `xml:"seatmapWarningInfo"`
	AdditionalProductInfo *AdditionalProductDetailsTypeI `xml:"additionalProductInfo"`
	EquipmentInformation  *EquipmentInfoTypeI            `xml:"equipmentInformation"`
	Cabin                 []*Cabin                       `xml:"cabin"`
	Row                   []*Row                         `xml:"row"`
	FreeText              []*InteractiveFreeTextTypeI    `xml:"freeText"`
	CommercialDescription *FreeTextInformationType       `xml:"commercialDescription"`
	CustomerCentricData   []*CustomerCentricData         `xml:"customerCentricData"`
	CatalogData           []*CatalogData                 `xml:"catalogData"`
}

// AirRetrieveSeatMapReply ...
type AirRetrieveSeatMapReply struct {
	//  XMLName               xml.Name                      `xml:"Air_RetrieveSeatMapReply"`
	ResponseDetails       *ResponseAnalysisDetailsTypeI `xml:"responseDetails"`
	ErrorInformation      *ErrorInformationTypeI        `xml:"errorInformation"`
	WarningInformation    *WarningInformationType       `xml:"warningInformation"`
	SeatRequestParameters *SeatRequestParametersTypeI   `xml:"seatRequestParameters"`
	SeatmapInformation    []*SeatmapInformation         `xml:"seatmapInformation"`
}

// AdditionalEquipmentInformationTypeI is Airline Organisation FOR Airline Owner
type AdditionalEquipmentInformationTypeI struct {
	FittedConfigurationCode string `xml:"fittedConfigurationCode"`
	AirlineDetails          string `xml:"airlineDetails"`
}

// AdditionalProductDetailsTypeI is Such things as meals or entertrainment on board
type AdditionalProductDetailsTypeI struct {
	LegDetails            *AdditionalProductTypeI    `xml:"legDetails"`
	DepartureStationInfo  *StationInformationTypeI   `xml:"departureStationInfo"`
	ArrivalStationInfo    *StationInformationTypeI   `xml:"arrivalStationInfo"`
	TravellerTimeDetails  *TravellerTimeDetailsTypeI `xml:"travellerTimeDetails"`
	FacilitiesInformation []*ProductFacilitiesTypeI  `xml:"facilitiesInformation"`
}

// AdditionalProductTypeI is Days of the week the flight operates: 1=Monday to 7=Sunday
type AdditionalProductTypeI struct {
	Equipment       string `xml:"equipment"`
	NumberOfStops   int    `xml:"numberOfStops"`
	Duration        int    `xml:"duration"`
	DaysOfOperation int    `xml:"daysOfOperation"`
}

// CabinClassDesignationTypeI is Cabin compartment designator
type CabinClassDesignationTypeI struct {
	ClassDesignator       string `xml:"classDesignator"`
	BookingClass          string `xml:"bookingClass"`
	CabinClass            int    `xml:"cabinClass"`
	CompartmentDesignator string `xml:"compartmentDesignator"`
}

// CabinClassSeatRowRangeDetailsTypeI is Specifies the first and last row number within a particular cabin class compartment.
type CabinClassSeatRowRangeDetailsTypeI struct {
	Number []int `xml:"number"`
}

// CabinDetailsTypeI is List of column designators in this cabin
type CabinDetailsTypeI struct {
	ClassDetails          *CabinClassDesignationTypeI          `xml:"classDetails"`
	SeatRowRange          *CabinClassSeatRowRangeDetailsTypeI  `xml:"seatRowRange"`
	CabinZoneCode         string                               `xml:"cabinZoneCode"`
	SmokingSeatRowRange   *SmokingAreaSeatRowRangeDetailsTypeI `xml:"smokingSeatRowRange"`
	DefaultSeatOccupation string                               `xml:"defaultSeatOccupation"`
	OverwingSeatRowRange  *OverwingSeatRowRangeTypeI           `xml:"overwingSeatRowRange"`
	ColumnDetails         []*CabinWidthAndColumnDetailsTypeI   `xml:"columnDetails"`
}

// CabinFacilitiesDetailsTypeI is To indicate the location of a cabin facility within a numbered row (e.g. right, etc.).
type CabinFacilitiesDetailsTypeI struct {
	Type     string `xml:"type"`
	Location string `xml:"location"`
}

// CabinFacilitiesTypeI is Information describing a specific area in a means of transport.
type CabinFacilitiesTypeI struct {
	RowLocation               string                         `xml:"rowLocation"`
	CabinFacilityDetails      *CabinFacilitiesDetailsTypeI   `xml:"cabinFacilityDetails"`
	OtherCabinFacilityDetails []*CabinFacilitiesDetailsTypeI `xml:"otherCabinFacilityDetails"`
}

// CabinWidthAndColumnDetailsTypeI is Column description - To indicate if the column is along a window or an aisle or between two other columns.
type CabinWidthAndColumnDetailsTypeI struct {
	SeatColumn  string   `xml:"seatColumn"`
	Description []string `xml:"description"`
}

// CodedAttributeInformationType is Attribute description
type CodedAttributeInformationType struct {
	AttributeType        string `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// CodedAttributeType is CR 12421952: chargeable seat ticketability flag in seat map with prices  =) Group 5 M999 - SeatPrice / ATC C1 - additionalInfo : added codeset ITC
type CodedAttributeType struct {
	AttributeDetails []*CodedAttributeInformationType `xml:"attributeDetails"`
}

// CommunicationContactDetailsType is Type of URL
type CommunicationContactDetailsType struct {
	UrlAddress string `xml:"urlAddress"`
	UrlType    string `xml:"urlType"`
}

// CommunicationContactType is Communication channel
type CommunicationContactType struct {
	Communication *CommunicationContactDetailsType `xml:"communication"`
}

// CompanyIdentificationTypeI is A 2-3 character code to specify the operating airline designator code
type CompanyIdentificationTypeI struct {
	MarketingCompany string `xml:"marketingCompany"`
	OperatingCompany string `xml:"operatingCompany"`
}

// ConfigurationDetailsTypeI is Seats - number of seats
type ConfigurationDetailsTypeI struct {
	ClassDesignator string `xml:"classDesignator"`
	NumberOfSeats   int    `xml:"numberOfSeats"`
}

// DiscountDataType is List applicable redemption/upgrade promotions
type DiscountDataType struct {
	DiscountGroup []*DiscountGroupType `xml:"discountGroup"`
}

// DiscountGroupType ...
type DiscountGroupType struct {
	DiscountCode        string `xml:"discountCode"`
	DiscountIdentifier  string `xml:"discountIdentifier"`
	DiscountDescription string `xml:"discountDescription"`
}

// DummySegmentTypeI is To serve the purpose of a mandatory segment at the beginning of a group and to avoid segment collision.
type DummySegmentTypeI struct {
}

// EquipmentInfoTypeI is Free text
type EquipmentInfoTypeI struct {
	CabinClassDetails        []*ConfigurationDetailsTypeI         `xml:"cabinClassDetails"`
	IataAircraftTypeCode     string                               `xml:"iataAircraftTypeCode"`
	ConfigVersionDetails     *AdditionalEquipmentInformationTypeI `xml:"configVersionDetails"`
	ConfigVersionDescription string                               `xml:"configVersionDescription"`
}

// ErrorInformationDetailsTypeI is Free text
type ErrorInformationDetailsTypeI struct {
	ProcessingLevel int    `xml:"processingLevel"`
	Code            int    `xml:"code"`
	Description     string `xml:"description"`
}

// ErrorInformationTypeI is Error information
type ErrorInformationTypeI struct {
	ErrorDetails      *ErrorInformationDetailsTypeI   `xml:"errorDetails"`
	OtherErrorDetails []*ErrorInformationDetailsTypeI `xml:"otherErrorDetails"`
}

// FreeTextDetailsType is Type of encoding
type FreeTextDetailsType struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
	Source               string `xml:"source"`
	Encoding             string `xml:"encoding"`
}

// FreeTextInformationType is This field contains the commercial name
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []string             `xml:"freeText"`
}

// FreeTextQualificationTypeI is language
type FreeTextQualificationTypeI struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
	Status               string `xml:"status"`
	CompanyId            string `xml:"companyId"`
	Language             string `xml:"language"`
}

// GenericDetailsTypeI is Allows seat assignment request/response to provide additional seat detail.
type GenericDetailsTypeI struct {
	CabinClassDesignator  string   `xml:"cabinClassDesignator"`
	NoSmokingIndicator    string   `xml:"noSmokingIndicator"`
	CabinClass            int      `xml:"cabinClass"`
	CompartmentDesignator string   `xml:"compartmentDesignator"`
	SeatCharacteristic    []string `xml:"seatCharacteristic"`
}

// InteractiveFreeTextTypeI is Free text and message sequence numbers of the remarks.
type InteractiveFreeTextTypeI struct {
	FreeTextQualification *FreeTextQualificationTypeI `xml:"freeTextQualification"`
	FreeText              []string                    `xml:"freeText"`
}

// LocationTypeI is A 3 character ATA/IATA airport/city code to specify place of departure
type LocationTypeI struct {
	TrueLocationId string `xml:"trueLocationId"`
}

// MonetaryInformationDetailsTypeI is location
type MonetaryInformationDetailsTypeI struct {
	TypeQualifier string   `xml:"typeQualifier"`
	Amount        float64  `xml:"amount"`
	Currency      string   `xml:"currency"`
	Location      []string `xml:"location"`
}

// MonetaryInformationTypeI is Monetary Details
type MonetaryInformationTypeI struct {
	MonetaryDetails []*MonetaryInformationDetailsTypeI `xml:"monetaryDetails"`
}

// OverwingSeatRowRangeTypeI is To specify the first and last row number in the range.
type OverwingSeatRowRangeTypeI struct {
	Number []int `xml:"number"`
}

// ProductDateTimeTypeI is Date variation
type ProductDateTimeTypeI struct {
	DepartureDate string `xml:"departureDate"`
	DepartureTime string `xml:"departureTime"`
	ArrivalDate   string `xml:"arrivalDate"`
	ArrivalTime   string `xml:"arrivalTime"`
	DateVariation int    `xml:"dateVariation"`
}

// ProductFacilitiesTypeI is Can contain a 2 character Meal Service Code if 9932 is 'M'.
type ProductFacilitiesTypeI struct {
	Code        string `xml:"code"`
	Description string `xml:"description"`
}

// ProductIdentificationDetailsTypeI is Modifier
type ProductIdentificationDetailsTypeI struct {
	FlightNumber      string   `xml:"flightNumber"`
	BookingClass      string   `xml:"bookingClass"`
	OperationalSuffix string   `xml:"operationalSuffix"`
	Modifier          []string `xml:"modifier"`
}

// RangeOfRowsDetailsTypeI is List of existing seat column designators for this particular row.
type RangeOfRowsDetailsTypeI struct {
	SeatRowNumber int      `xml:"seatRowNumber"`
	NumberOfRows  int      `xml:"numberOfRows"`
	SeatColumn    []string `xml:"seatColumn"`
}

// ReferenceInfoType is Reference details
type ReferenceInfoType struct {
	ReferenceDetails *ReferencingDetailsType `xml:"referenceDetails"`
}

// ReferenceInfoType215670S is To provide specific Hotel reference identification.
type ReferenceInfoType215670S struct {
	//  XMLName          xml.Name                  `xml:"ReferenceInfoType_215670S"`
	ReferenceDetails []*ReferencingDetailsType `xml:"referenceDetails"`
}

// ReferencingDetailsType is pricing description reference
type ReferencingDetailsType struct {
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// ResponseAnalysisDetailsTypeI is STATUS, CODED Processing status is e.g. OK - data follows (O) or OK  - no data follow (P) or non recoverable error (X).
type ResponseAnalysisDetailsTypeI struct {
	ResponseType string `xml:"responseType"`
	StatusCode   string `xml:"statusCode"`
}

// RowCharacteristicsDetailsTypeI is Characteristic which relates to all seats within the specified row number.
type RowCharacteristicsDetailsTypeI struct {
	RowCharacteristic      string   `xml:"rowCharacteristic"`
	OtherRowCharacteristic []string `xml:"otherRowCharacteristic"`
}

// RowDetailsTypeI is Seat occupation details
type RowDetailsTypeI struct {
	SeatRowNumber         int                           `xml:"seatRowNumber"`
	SeatOccupationDetails []*SeatOccupationDetailsTypeI `xml:"seatOccupationDetails"`
}

// RowDetailsType is Seat occupation details
type RowDetailsType struct {
	SeatRowNumber            float64                              `xml:"seatRowNumber"`
	RowCharacteristicDetails *RowCharacteristicsDetailsTypeI      `xml:"rowCharacteristicDetails"`
	SeatOccupationDetails    []*SeatOccupationDetailsTypeI262401C `xml:"seatOccupationDetails"`
}

// SeatOccupationDetailsTypeI is To indicate seat availability
type SeatOccupationDetailsTypeI struct {
	SeatColumn     string `xml:"seatColumn"`
	SeatOccupation string `xml:"seatOccupation"`
}

// SeatOccupationDetailsTypeI262401C is Characteristics of the seat: window, aisle, for passenger with infant, facing a bulkhead...
type SeatOccupationDetailsTypeI262401C struct {
	//  XMLName            xml.Name `xml:"SeatOccupationDetailsTypeI_262401C"`
	SeatColumn         string   `xml:"seatColumn"`
	SeatOccupation     string   `xml:"seatOccupation"`
	SeatCharacteristic []string `xml:"seatCharacteristic"`
}

// SeatRequestParametersTypeI is Free text
type SeatRequestParametersTypeI struct {
	GenericDetails      *GenericDetailsTypeI     `xml:"genericDetails"`
	RangeOfRowsDetails  *RangeOfRowsDetailsTypeI `xml:"rangeOfRowsDetails"`
	ProcessingIndicator string                   `xml:"processingIndicator"`
	ReferenceNumber     string                   `xml:"referenceNumber"`
	Description         string                   `xml:"description"`
}

// SmokingAreaSeatRowRangeDetailsTypeI is Specifies the first and last row number within a particular smoking seat row range.
type SmokingAreaSeatRowRangeDetailsTypeI struct {
	Number []int `xml:"number"`
}

// StationInformationTypeI is Concourse of arrival
type StationInformationTypeI struct {
	GateDescription string `xml:"gateDescription"`
	Terminal        string `xml:"terminal"`
	Concourse       string `xml:"concourse"`
}

// TaxDetailsTypeI is Monetary Function
type TaxDetailsTypeI struct {
	Rate           float64  `xml:"rate"`
	CountryCode    string   `xml:"countryCode"`
	CurrencyCode   string   `xml:"currencyCode"`
	Type           string   `xml:"type"`
	FiledAmount    float64  `xml:"filedAmount"`
	FiledCurrency  string   `xml:"filedCurrency"`
	FiledType      string   `xml:"filedType"`
	ConversionRate int      `xml:"conversionRate"`
	TaxQualifier   []string `xml:"taxQualifier"`
}

// TaxTypeI is Tax Details
type TaxTypeI struct {
	TaxCategory string             `xml:"taxCategory"`
	TaxDetails  []*TaxDetailsTypeI `xml:"taxDetails"`
}

// TravelProductInformationTypeI is Product identification details
type TravelProductInformationTypeI struct {
	FlightDate           *ProductDateTimeTypeI              `xml:"flightDate"`
	BoardPointDetails    *LocationTypeI                     `xml:"boardPointDetails"`
	OffpointDetails      *LocationTypeI                     `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationTypeI        `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsTypeI `xml:"flightIdentification"`
}

// TravellerDetailsTypeI is Identification code, 2 cases: ID<1 to 51 char free text) or CR<1 to 40 char free text)
type TravellerDetailsTypeI struct {
	GivenName                string   `xml:"givenName"`
	Type                     string   `xml:"type"`
	UniqueCustomerIdentifier string   `xml:"uniqueCustomerIdentifier"`
	InfantIndicator          string   `xml:"infantIndicator"`
	Title                    []string `xml:"title"`
}

// TravellerInformationTypeI is PTR 12266145 [Medium]: Recovery for Pax tatoo on more than 2 digits - temporary fix  =) Group 4 C99 - customerCentricData / TIF C99 - travellerDetails / C324 - otherPaxDetails / C9944 - uniqueCustomerIdentifier (an..2 to an..10)
type TravellerInformationTypeI struct {
	PaxDetails      *TravellerSurnameInformationTypeI `xml:"paxDetails"`
	OtherPaxDetails []*TravellerDetailsTypeI          `xml:"otherPaxDetails"`
}

// TravellerSurnameInformationTypeI is Status, coded
type TravellerSurnameInformationTypeI struct {
	Surname  string `xml:"surname"`
	Type     string `xml:"type"`
	Quantity int    `xml:"quantity"`
	Status   string `xml:"status"`
}

// TravellerTimeDetailsTypeI is Check-in date and time (ddmmyyhhmm)
type TravellerTimeDetailsTypeI struct {
	CheckInDateTime int `xml:"checkInDateTime"`
}

// WarningDetailsTypeI is Text Information related to a cabin class.
type WarningDetailsTypeI struct {
	ProcessingLevel int    `xml:"processingLevel"`
	Number          int    `xml:"number"`
	Description     string `xml:"description"`
}

// WarningDetailsType is Text Information related to a cabin class.
type WarningDetailsType struct {
	ProcessingLevel int    `xml:"processingLevel"`
	Number          int    `xml:"number"`
	Description     string `xml:"description"`
}

// WarningInformationTypeI is Details of the subsequent warning message
type WarningInformationTypeI struct {
	WarningDetails      *WarningDetailsTypeI   `xml:"warningDetails"`
	OtherWarningDetails []*WarningDetailsTypeI `xml:"otherWarningDetails"`
}

// WarningInformationType is Details of the subsequent warning message
type WarningInformationType struct {
	WarningDetails      *WarningDetailsType   `xml:"warningDetails"`
	OtherWarningDetails []*WarningDetailsType `xml:"otherWarningDetails"`
}

// AlphaStringLength1To1 is Format limitations: a1
type AlphaStringLength1To1 string

// AlphaNumericStringLength1To35 is Format limitations: an..35
type AlphaNumericStringLength1To35 string

// AlphaNumericStringLength1To70 is Format limitations: an..70
type AlphaNumericStringLength1To70 string

// AlphaNumericStringLength1To3 is Format limitations: an..3
type AlphaNumericStringLength1To3 string

// NumericDecimalLength1To3 is Format limitations: n..3
type NumericDecimalLength1To3 float64

// AlphaNumericStringLength1To320 is Format limitations: an..320
type AlphaNumericStringLength1To320 string

// NumericIntegerLength1To3 is Format limitations: n..3
type NumericIntegerLength1To3 int

// NumericIntegerLength1To1 is Format limitations: n1
type NumericIntegerLength1To1 int

// NumericIntegerLength1To4 is Format limitations: n..4
type NumericIntegerLength1To4 int

// AlphaNumericStringLength1To1 is Format limitations: an1
type AlphaNumericStringLength1To1 string

// AlphaStringLength3To3 is Format limitations: a3
type AlphaStringLength3To3 string

// AlphaNumericStringLength1To4 is Format limitations: an..4
type AlphaNumericStringLength1To4 string

// AlphaNumericStringLength1To7 is Format limitations: an..7
type AlphaNumericStringLength1To7 string

// AlphaNumericStringLength3To3 is Format limitations: an3
type AlphaNumericStringLength3To3 string

// NumericIntegerLength1To2 is Format limitations: n..2
type NumericIntegerLength1To2 int

// NumericIntegerLength4To4 is Format limitations: n4
type NumericIntegerLength4To4 int

// AlphaNumericStringLength1To6 is Format limitations: an..6
type AlphaNumericStringLength1To6 string

// AlphaNumericStringLength1To2 is Format limitations: an..2
type AlphaNumericStringLength1To2 string

// NumericIntegerLength10To10 is Format limitations: n10
type NumericIntegerLength10To10 int

// AlphaNumericStringLength1To12 is Format limitations: an..12
type AlphaNumericStringLength1To12 string

// AlphaStringLength1To2 is Format limitations: a..2
type AlphaStringLength1To2 string

// AlphaNumericStringLength1To10 is Format limitations: an..10
type AlphaNumericStringLength1To10 string

// NumericDecimalLength1To35 is Format limitations: n..35
type NumericDecimalLength1To35 float64

// AlphaNumericStringLength1To25 is Format limitations: an..25
type AlphaNumericStringLength1To25 string

// AlphaNumericStringLength1To16 is Format limitations: an..16
type AlphaNumericStringLength1To16 string

// AlphaNumericStringLength1To256 is Format limitations: an..256
type AlphaNumericStringLength1To256 string

// NumericDecimalLength1To17 is Format limitations: n..17
type NumericDecimalLength1To17 float64

// NumericIntegerLength1To18 is Format limitations: n..18
type NumericIntegerLength1To18 int

// AlphaNumericStringLength1To5 is Format limitations: an..5
type AlphaNumericStringLength1To5 string

// AlphaNumericStringLength1To2500 is Format limitations: an..2500
type AlphaNumericStringLength1To2500 string

// AMAEDICodesetTypeLength1to3 is Used for codes in the AMADEUS code tables. Code Length is three alphanumeric characters.
type AMAEDICodesetTypeLength1to3 string

// AMAEDICodesetTypeLength1to2 is Used for codes in the AMADEUS code tables. Code Length is two alphanumeric characters.
type AMAEDICodesetTypeLength1to2 string

// DateDDMMYY is Date format: DDMMYY
type DateDDMMYY string

// Time24HHMM is Time format: 24H. All digits are mandatory . Example: from 0000 to 2359
type Time24HHMM string
