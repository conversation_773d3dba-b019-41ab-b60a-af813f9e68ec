package search_flight

// ErrorMessage ...
type ErrorMessage struct {
	// XMLName          xml.Name                               `xml:"errorMessage"`
	ApplicationError *ApplicationErrorInformationType78543S `xml:"applicationError"`
	ErrorMessageText *InteractiveFreeTextType78544S         `xml:"errorMessageText"`
}

// AmountsPerSgt ...
type AmountsPerSgt struct {
	// XMLName xml.Name                        `xml:"amountsPerSgt"`
	SgtRef  *ReferenceInfoType133176S       `xml:"sgtRef"`
	Amounts *MonetaryInformationType228104S `xml:"amounts"`
}

// AmountInfoForAllPax ...
type AmountInfoForAllPax struct {
	// XMLName          xml.Name                        `xml:"amountInfoForAllPax"`
	ItineraryAmounts *MonetaryInformationType228104S `xml:"itineraryAmounts"`
	AmountsPerSgt    []*AmountsPerSgt                `xml:"amountsPerSgt"`
}

// AmountInfoPerPax ...
type AmountInfoPerPax struct {
	// XMLName          xml.Name                        `xml:"amountInfoPerPax"`
	PaxRef           *SpecificTravellerType          `xml:"paxRef"`
	PaxAttributes    *FareInformationType80868S      `xml:"paxAttributes"`
	ItineraryAmounts *MonetaryInformationType228104S `xml:"itineraryAmounts"`
	AmountsPerSgt    []*AmountsPerSgt                `xml:"amountsPerSgt"`
}

// FeeDetails ...
type FeeDetails struct {
	// XMLName                 xml.Name                             `xml:"feeDetails"`
	FeeReference            *ItemReferencesAndVersionsType78564S `xml:"feeReference"`
	FeeInformation          *DiscountAndPenaltyInformationType   `xml:"feeInformation"`
	FeeParameters           *AttributeType78561S                 `xml:"feeParameters"`
	ConvertedOrOriginalInfo *ConversionRateTypeI78562S           `xml:"convertedOrOriginalInfo"`
}

// Dictionary ...
type Dictionary struct {
	// XMLName  xml.Name                     `xml:"dictionary"`
	Category *ProductGroupInformationType `xml:"category"`
	Element  []*KeywordType               `xml:"element"`
}

// AdditionalInfo ...
type AdditionalInfo struct {
	// XMLName      xml.Name                   `xml:"additionalInfo"`
	IdentDetails *ProductIdentificationType `xml:"identDetails"`
	DateInfo     *DateTimePeriodType        `xml:"dateInfo"`
}

// OfficeIdDetails ...
type OfficeIdDetails struct {
	// XMLName             xml.Name                             `xml:"officeIdDetails"`
	OfficeIdInformation *UserIdentificationType              `xml:"officeIdInformation"`
	OfficeIdReference   *ItemReferencesAndVersionsType78536S `xml:"officeIdReference"`
}

// MilesAccrual ...
type MilesAccrual struct {
	// XMLName             xml.Name                  `xml:"milesAccrual"`
	MilesAccrualId      *ItemDescriptionType      `xml:"milesAccrualId"`
	MilesAccrualDetails []*ProgramCodeType209757S `xml:"milesAccrualDetails"`
}

// InitialEMDInfo ...
type InitialEMDInfo struct {
	// XMLName         xml.Name                           `xml:"initialEMDInfo"`
	EmdReference    *CouponInformationType             `xml:"emdReference"`
	EmdPaxReference *TravellerReferenceInformationType `xml:"emdPaxReference"`
	EmdAmounts      *MonetaryInformationType212668S    `xml:"emdAmounts"`
}

// EmdRecoDetails ...
type EmdRecoDetails struct {
	// XMLName          xml.Name                        `xml:"emdRecoDetails"`
	EmdReference     *CouponInformationType          `xml:"emdReference"`
	SrvCoverageRef   *ReferenceInfoType212767S       `xml:"srvCoverageRef"`
	EmdDetailAmounts *MonetaryInformationType212672S `xml:"emdDetailAmounts"`
	EmdError         *ErrorGroupType                 `xml:"emdError"`
}

// EmdRecoPaxDetails ...
type EmdRecoPaxDetails struct {
	// XMLName         xml.Name                           `xml:"emdRecoPaxDetails"`
	EmdPaxReference *TravellerReferenceInformationType `xml:"emdPaxReference"`
	EmdPaxAmounts   *MonetaryInformationType212672S    `xml:"emdPaxAmounts"`
	EmdRecoDetails  []*EmdRecoDetails                  `xml:"emdRecoDetails"`
}

// EmdRecommendation ...
type EmdRecommendation struct {
	// XMLName           xml.Name                        `xml:"emdRecommendation"`
	EdmRecoId         *ElementManagementSegmentType   `xml:"edmRecoId"`
	EmdRecoAmounts    *MonetaryInformationType212672S `xml:"emdRecoAmounts"`
	EmdRecoPaxDetails []*EmdRecoPaxDetails            `xml:"emdRecoPaxDetails"`
}

// FlightDetails ...
type FlightDetails struct {
	// XMLName               xml.Name                        `xml:"flightDetails"`
	FlightInformation     *TravelProductType              `xml:"flightInformation"`
	AvlInfo               []*FlightProductInformationType `xml:"avlInfo"`
	TechnicalStop         []*DateAndTimeInformationType   `xml:"technicalStop"`
	CommercialAgreement   *CommercialAgreementsType       `xml:"commercialAgreement"`
	AddInfo               *HeaderInformationTypeI         `xml:"addInfo"`
	FlightCharacteristics *FlightCharacteristicsType      `xml:"flightCharacteristics"`
	FlightServices        []*FlightServicesType           `xml:"flightServices"`
	MealServices          *MealServicesType               `xml:"mealServices"`
	FlightRef             *ReferenceInfoType227042S       `xml:"flightRef"`
}

// GroupOfFlights ...
type GroupOfFlights struct {
	// XMLName            xml.Name             `xml:"groupOfFlights"`
	PropFlightGrDetail *ProposedSegmentType `xml:"propFlightGrDetail"`
	FlightDetails      []*FlightDetails     `xml:"flightDetails"`
}

// FlightIndex ...
type FlightIndex struct {
	// XMLName             xml.Name                         `xml:"flightIndex"`
	RequestedSegmentRef *OriginAndDestinationRequestType `xml:"requestedSegmentRef"`
	GroupOfFlights      []*GroupOfFlights                `xml:"groupOfFlights"`
	NbOfUnits           *NumberOfUnitsType               `xml:"nbOfUnits"`
}

// RecommandationSegmentsFareDetails ...
type RecommandationSegmentsFareDetails struct {
	// XMLName                    xml.Name                         `xml:"recommandationSegmentsFareDetails"`
	RecommendationSegRef       *OriginAndDestinationRequestType `xml:"recommendationSegRef"`
	SegmentMonetaryInformation *MonetaryInformationType228104S  `xml:"segmentMonetaryInformation"`
	Reference                  *ReferenceInfoType224557S        `xml:"reference"`
}

// Fare ...
type Fare struct {
	// XMLName             xml.Name                        `xml:"fare"`
	PricingMessage      *InteractiveFreeTextType78559S  `xml:"pricingMessage"`
	MonetaryInformation *MonetaryInformationType199534S `xml:"monetaryInformation"`
}

// GroupOfFares ...
type GroupOfFares struct {
	// XMLName                    xml.Name                             `xml:"groupOfFares"`
	ProductInformation         *FlightProductInformationType227082S `xml:"productInformation"`
	FareCalculationCodeDetails []*FareCalculationCodeDetailsType    `xml:"fareCalculationCodeDetails"`
	TicketInfos                *FareQualifierDetailsType            `xml:"ticketInfos"`
	FareFamiliesRef            *ReferenceInfoType227141S            `xml:"fareFamiliesRef"`
}

// FareDetails ...
type FareDetails struct {
	// XMLName                   xml.Name                         `xml:"fareDetails"`
	SegmentRef                *OriginAndDestinationRequestType `xml:"segmentRef"`
	GroupOfFares              []*GroupOfFares                  `xml:"groupOfFares"`
	PsgSegMonetaryInformation *MonetaryInformationType228104S  `xml:"psgSegMonetaryInformation"`
	Reference                 *ReferenceInformationType        `xml:"reference"`
	MajCabin                  []*ProductInformationType        `xml:"majCabin"`
}

// PaxFareProduct ...
type PaxFareProduct struct {
	// XMLName             xml.Name                               `xml:"paxFareProduct"`
	PaxFareDetail       *PricingTicketingSubsequentType228122S `xml:"paxFareDetail"`
	FeeRef              *ReferenceInfoType214315S              `xml:"feeRef"`
	PaxReference        []*TravellerReferenceInformationType   `xml:"paxReference"`
	PassengerRange      *RangeDetailsType                      `xml:"passengerRange"`
	PassengerTaxDetails *TaxType                               `xml:"passengerTaxDetails"`
	Fare                []*Fare                                `xml:"fare"`
	FareDetails         []*FareDetails                         `xml:"fareDetails"`
}

// CnxContextDetails ...
type CnxContextDetails struct {
	// XMLName     xml.Name                             `xml:"cnxContextDetails"`
	FareCnxInfo *FlightProductInformationType223656S `xml:"fareCnxInfo"`
}

// FareContextDetails ...
type FareContextDetails struct {
	// XMLName              xml.Name                                `xml:"fareContextDetails"`
	RequestedSegmentInfo *OriginAndDestinationRequestType134833S `xml:"requestedSegmentInfo"`
	CnxContextDetails    []*CnxContextDetails                    `xml:"cnxContextDetails"`
}

// SpecificProductDetails ...
type SpecificProductDetails struct {
	// XMLName            xml.Name                        `xml:"specificProductDetails"`
	ProductReferences  *PricingTicketingSubsequentType `xml:"productReferences"`
	FareContextDetails []*FareContextDetails           `xml:"fareContextDetails"`
}

// SpecificRecDetails ...
type SpecificRecDetails struct {
	// XMLName                xml.Name                       `xml:"specificRecDetails"`
	SpecificRecItem        *ItemReferencesAndVersionsType `xml:"specificRecItem"`
	SpecificProductDetails []*SpecificProductDetails      `xml:"specificProductDetails"`
}

// Recommendation ...
type Recommendation struct {
	// XMLName                           xml.Name                             `xml:"recommendation"`
	ItemNumber                        *ItemNumberType224478S               `xml:"itemNumber"`
	WarningMessage                    []*InteractiveFreeTextType78544S     `xml:"warningMessage"`
	FareFamilyRef                     *ReferenceInfoType133176S            `xml:"fareFamilyRef"`
	RecPriceInfo                      *MonetaryInformationType204470S      `xml:"recPriceInfo"`
	MiniRule                          []*MiniRulesType78547S               `xml:"miniRule"`
	SegmentFlightRef                  []*ReferenceInfoType                 `xml:"segmentFlightRef"`
	RecommandationSegmentsFareDetails []*RecommandationSegmentsFareDetails `xml:"recommandationSegmentsFareDetails"`
	PaxFareProduct                    []*PaxFareProduct                    `xml:"paxFareProduct"`
	SpecificRecDetails                []*SpecificRecDetails                `xml:"specificRecDetails"`
	DistributionMethod                *Method                              `xml:"distributionMethod"`
}

// AmtGroup ...
type AmtGroup struct {
	// XMLName xml.Name                  `xml:"amtGroup"`
	Ref    *ReferenceInfoType165972S `xml:"ref"`
	Amount *MonetaryInformationTypeI `xml:"amount"`
}

// PsgInfo ...
type PsgInfo struct {
	// XMLName       xml.Name                                 `xml:"psgInfo"`
	Ref           *SegmentRepetitionControlTypeI           `xml:"ref"`
	Description   *FareInformationTypeI                    `xml:"description"`
	FreqTraveller *FrequentTravellerIdentificationCodeType `xml:"freqTraveller"`
	Amount        *MonetaryInformationTypeI                `xml:"amount"`
	Fare          *FlightProductInformationType161491S     `xml:"fare"`
	Attribute     []*AttributeTypeU                        `xml:"attribute"`
}

// OtherSolutions ...
type OtherSolutions struct {
	// XMLName   xml.Name              `xml:"otherSolutions"`
	Reference *SequenceDetailsTypeU `xml:"reference"`
	AmtGroup  []*AmtGroup           `xml:"amtGroup"`
	PsgInfo   []*PsgInfo            `xml:"psgInfo"`
}

// WarningInfo ...
type WarningInfo struct {
	// XMLName             xml.Name                       `xml:"warningInfo"`
	GlobalMessageMarker *DummySegmentTypeI             `xml:"globalMessageMarker"`
	GlobalMessage       *InteractiveFreeTextType78534S `xml:"globalMessage"`
}

// GlobalInformation ...
type GlobalInformation struct {
	// XMLName    xml.Name                  `xml:"globalInformation"`
	Attributes *CodedAttributeType78535S `xml:"attributes"`
}

// ServiceFeeRefGrp ...
type ServiceFeeRefGrp struct {
	// XMLName xml.Name           `xml:"serviceFeeRefGrp"`
	RefInfo *ReferenceInfoType `xml:"refInfo"`
}

// ServiceCovInfoGrp ...
type ServiceCovInfoGrp struct {
	// XMLName                xml.Name                  `xml:"serviceCovInfoGrp"`
	PaxRefInfo             *SpecificTravellerType    `xml:"paxRefInfo"`
	CoveragePerFlightsInfo []*ActionDetailsType      `xml:"coveragePerFlightsInfo"`
	CarrierInfo            *TransportIdentifierType  `xml:"carrierInfo"`
	RefInfo                *ReferenceInfoType134840S `xml:"refInfo"`
}

// ServiceCoverageInfoGrp ...
type ServiceCoverageInfoGrp struct {
	// XMLName           xml.Name             `xml:"serviceCoverageInfoGrp"`
	ItemNumberInfo    *ItemNumberType      `xml:"itemNumberInfo"`
	ServiceCovInfoGrp []*ServiceCovInfoGrp `xml:"serviceCovInfoGrp"`
}

// ServiceMatchedInfoGroup ...
type ServiceMatchedInfoGroup struct {
	// XMLName       xml.Name                        `xml:"serviceMatchedInfoGroup"`
	PaxRefInfo    *SpecificTravellerType          `xml:"paxRefInfo"`
	PricingInfo   *FareInformationType80868S      `xml:"pricingInfo"`
	AmountInfo    *MonetaryInformationType204470S `xml:"amountInfo"`
	AttributeInfo []*CodedAttributeType           `xml:"attributeInfo"`
}

// ServiceDetailsGrp ...
type ServiceDetailsGrp struct {
	// XMLName                 xml.Name                   `xml:"serviceDetailsGrp"`
	RefInfo                 *ReferenceInfoType134840S  `xml:"refInfo"`
	ServiceMatchedInfoGroup []*ServiceMatchedInfoGroup `xml:"serviceMatchedInfoGroup"`
}

// ServiceFeeInfoGrp ...
type ServiceFeeInfoGrp struct {
	// XMLName           xml.Name             `xml:"serviceFeeInfoGrp"`
	ItemNumberInfo    *ItemNumberType      `xml:"itemNumberInfo"`
	ServiceDetailsGrp []*ServiceDetailsGrp `xml:"serviceDetailsGrp"`
}

// FeeDescriptionGrp ...
type FeeDescriptionGrp struct {
	// XMLName                xml.Name                        `xml:"feeDescriptionGrp"`
	ItemNumberInfo         *ItemNumberType80866S           `xml:"itemNumberInfo"`
	ServiceAttributesInfo  *AttributeType                  `xml:"serviceAttributesInfo"`
	ServiceDescriptionInfo *SpecialRequirementsDetailsType `xml:"serviceDescriptionInfo"`
	CommercialName         *InteractiveFreeTextType        `xml:"commercialName"`
}

// FreeBagAllowanceGrp ...
type FreeBagAllowanceGrp struct {
	// XMLName             xml.Name               `xml:"freeBagAllowanceGrp"`
	FreeBagAllownceInfo *ExcessBaggageType     `xml:"freeBagAllownceInfo"`
	ItemNumberInfo      *ItemNumberType166130S `xml:"itemNumberInfo"`
}

// ServiceFeesGrp ...
type ServiceFeesGrp struct {
	// XMLName                xml.Name                  `xml:"serviceFeesGrp"`
	ServiceTypeInfo        *SelectionDetailsType     `xml:"serviceTypeInfo"`
	ServiceFeeRefGrp       []*ServiceFeeRefGrp       `xml:"serviceFeeRefGrp"`
	ServiceCoverageInfoGrp []*ServiceCoverageInfoGrp `xml:"serviceCoverageInfoGrp"`
	GlobalMessageMarker    *DummySegmentTypeI        `xml:"globalMessageMarker"`
	ServiceFeeInfoGrp      []*ServiceFeeInfoGrp      `xml:"serviceFeeInfoGrp"`
	ServiceDetailsGrp      []*ServiceDetailsGrp      `xml:"serviceDetailsGrp"`
	FreeBagAllowanceGrp    []*FreeBagAllowanceGrp    `xml:"freeBagAllowanceGrp"`
}

// CatGrp ...
type CatGrp struct {
	// XMLName    xml.Name                 `xml:"catGrp"`
	CatInfo    *CategDescrType          `xml:"catInfo"`
	MonInfo    *MonetaryInformationType `xml:"monInfo"`
	StatusInfo *StatusType              `xml:"statusInfo"`
}

// MnrDetails ...
type MnrDetails struct {
	// XMLName  xml.Name                             `xml:"mnrDetails"`
	MnrRef   *ItemNumberType176648S               `xml:"mnrRef"`
	DateInfo []*DateAndTimeInformationType182345S `xml:"dateInfo"`
	CatGrp   []*CatGrp                            `xml:"catGrp"`
}

// MnrGrp ...
type MnrGrp struct {
	// XMLName    xml.Name       `xml:"mnrGrp"`
	Mnr       *MiniRulesType `xml:"mnr"`
	MiniRules []*MnrDetails  `xml:"mnrDetails"`
}

// OfferItems ...
type OfferItems struct {
	// XMLName          xml.Name                             `xml:"offerItems"`
	OfferItemDetails *OfferItemType                       `xml:"offerItemDetails"`
	References       []*ReferenceInfoType165972S          `xml:"references"`
	PaxReference     []*TravellerReferenceInformationType `xml:"paxReference"`
}

// Offers ...
type Offers struct {
	// XMLName      xml.Name                           `xml:"offers"`
	OfferDetails *OfferType                         `xml:"offerDetails"`
	TimeLimits   *DateAndTimeInformationType220498S `xml:"timeLimits"`
	OfferItems   []*OfferItems                      `xml:"offerItems"`
}

// OffersGroup ...
type OffersGroup struct {
	// XMLName        xml.Name                       `xml:"offersGroup"`
	Response       *ApplicationType               `xml:"response"`
	WarningMessage *InteractiveFreeTextType78544S `xml:"warningMessage"`
	Offers         []*Offers                      `xml:"offers"`
}

// VirtualInterlining ...
type VirtualInterlining struct {
	// XMLName        xml.Name          `xml:"virtualInterlining"`
	Itinerary      *TopologyType     `xml:"itinerary"`
	Recommendation []*Recommendation `xml:"recommendation"`
}

// FareMasterPricerTravelBoardSearchReply ...
type FareMasterPricerTravelBoardSearchReply struct {
	// XMLName        xml.Name             `xml:"Fare_MasterPricerTravelBoardSearchReply"`
	ReplyStatus    *StatusType224999S   `xml:"replyStatus"`
	ErrorMessage   *ErrorMessage        `xml:"errorMessage"`
	ConversionRate *ConversionRateTypeI `xml:"conversionRate"`
	// SolutionFamily      []*FareInformationType           `xml:"solutionFamily"`
	// FamilyInformation   []*FareFamilyType                `xml:"familyInformation"`
	// AmountInfoForAllPax *AmountInfoForAllPax             `xml:"amountInfoForAllPax"`
	// AmountInfoPerPax    []*AmountInfoPerPax              `xml:"amountInfoPerPax"`
	// FeeDetails          []*FeeDetails                    `xml:"feeDetails"`
	// BucketInfo          []*BucketInformationType         `xml:"bucketInfo"`
	// ThemeIdText         []*ThemeText                     `xml:"themeIdText"`
	// Dictionary          []*Dictionary                    `xml:"dictionary"`
	// AdditionalInfo      []*AdditionalInfo                `xml:"additionalInfo"`
	// CompanyIdText       []*CompanyIdentificationTextType `xml:"companyIdText"`
	// OfficeIdDetails     []*OfficeIdDetails               `xml:"officeIdDetails"`
	// ProgramDetails      []*ProgramCodeType               `xml:"programDetails"`
	// MilesAccrual        []*MilesAccrual                  `xml:"milesAccrual"`
	// InitialEMDInfo      []*InitialEMDInfo                `xml:"initialEMDInfo"`
	// EmdRecommendation   []*EmdRecommendation             `xml:"emdRecommendation"`
	FlightIndex    []*FlightIndex    `xml:"flightIndex"`
	Recommendation []*Recommendation `xml:"recommendation"`
	// OtherSolutions      []*OtherSolutions                `xml:"otherSolutions"`
	// WarningInfo         []*WarningInfo                   `xml:"warningInfo"`
	// GlobalInformation   []*GlobalInformation             `xml:"globalInformation"`
	ServiceFeesGrp []*ServiceFeesGrp `xml:"serviceFeesGrp"`
	// MultiDimensionRef   []*MultiDimensionalValueType     `xml:"multiDimensionRef"`
	// Value               []*ValueSearchCriteriaType       `xml:"value"`
	MnrGrp *MnrGrp `xml:"mnrGrp"`
	// OffersGroup         []*OffersGroup                   `xml:"offersGroup"`
	// VirtualInterlining  []*VirtualInterlining            `xml:"virtualInterlining"`
	// AmenitiesGroup      *GroupAmenities                  `xml:"amenitiesGroup"`
}

// AccrualType is Name
type AccrualType struct {
	Code *string `xml:"code"`
	Name *string `xml:"name"`
}

// AccrualType290124C is Value
type AccrualType290124C struct {
	// XMLName xml.Name `xml:"AccrualType_290124C"`
	Code  *string `xml:"code"`
	Value string  `xml:"value"`
}

// ActionDetailsType is Range of segments
type ActionDetailsType struct {
	NumberOfItemsDetails *ProcessingInformationType `xml:"numberOfItemsDetails"`
	LastItemsDetails     []*ReferenceType           `xml:"lastItemsDetails"`
}

// AdditionalFareQualifierDetailsTypeI is Second rate class
type AdditionalFareQualifierDetailsTypeI struct {
	RateClass        *string   `xml:"rateClass"`
	TicketDesignator *string   `xml:"ticketDesignator"`
	PricingGroup     *string   `xml:"pricingGroup"`
	SecondRateClass  []*string `xml:"secondRateClass"`
}

// AdditionalProductDetailsType is Location places of the stops
type AdditionalProductDetailsType struct {
	EquipmentType  *string   `xml:"equipmentType"`
	OperatingDay   *string   `xml:"operatingDay"`
	TechStopNumber *string   `xml:"techStopNumber"`
	LocationId     []*string `xml:"locationId"`
}

// Amenities is Provides information on the amenity
type Amenities struct {
	AmentityContentSourceDetails *AmenityProvider      `xml:"amentityContentSourceDetails"`
	AmenityDetails               []*AmenityDescription `xml:"amenityDetails"`
}

// AmenityDescription is Number returned gives the configuration inside a mean of transportation (e.g. two columns of 2 seats each gives the configuration "22", two columns of 2, one column of 3 gives the configuration "232")
type AmenityDescription struct {
	AmenityReference string  `xml:"amenityReference"`
	AmenityType      *string `xml:"amenityType"`
	AmenityAttribute *string `xml:"amenityAttribute"`
	IsChargeable     *string `xml:"isChargeable"`
	SeatSpace        string  `xml:"seatSpace"`
	SeatSpaceUnit    *string `xml:"seatSpaceUnit"`
	SeatFlateness    *string `xml:"seatFlateness"`
	RowLayout        *string `xml:"rowLayout"`
}

// AmenityProvider is Provides the Source of the Amenity Content (e.g ATPCO)
type AmenityProvider struct {
	AmenityProvider *string `xml:"amenityProvider"`
}

// ApplicationErrorDetailType is Code identifying the data validation error condition.
type ApplicationErrorDetailType struct {
	ErrorCode *string `xml:"errorCode"`
}

// ApplicationErrorInformationType is The code assigned by the receiver of a message for identification of a data validation error condition.
type ApplicationErrorInformationType struct {
	Error *string `xml:"error"`
}

// ApplicationErrorInformationType214133S is Application error details.
type ApplicationErrorInformationType214133S struct {
	// XMLName      xml.Name                    `xml:"ApplicationErrorInformationType_214133S"`
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// ApplicationErrorInformationType78543S is Details on application error.
type ApplicationErrorInformationType78543S struct {
	// XMLName                xml.Name                         `xml:"ApplicationErrorInformationType_78543S"`
	ApplicationErrorDetail *ApplicationErrorInformationType `xml:"applicationErrorDetail"`
}

// ApplicationIdentificationType is Unique identifier of the item.
type ApplicationIdentificationType struct {
	InternalId *string `xml:"internalId"`
}

// ApplicationType is Application details
type ApplicationType struct {
	ApplicationDetails *ApplicationIdentificationType `xml:"applicationDetails"`
}

// AttributeInformationTypeU is Attribute description
type AttributeInformationTypeU struct {
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// AttributeInformationType is Reference to company Id.
type AttributeInformationType struct {
	FeeParameterType        *string `xml:"feeParameterType"`
	FeeParameterDescription *string `xml:"feeParameterDescription"`
}

// AttributeInformationType97181C is Attribute description
type AttributeInformationType97181C struct {
	// XMLName              xml.Name `xml:"AttributeInformationType_97181C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// AttributeTypeU is provides details for the Attribute
type AttributeTypeU struct {
	AttributeFunction *string                    `xml:"attributeFunction"`
	AttributeDetails  *AttributeInformationTypeU `xml:"attributeDetails"`
}

// AttributeType is Criteria details
type AttributeType struct {
	AttributeQualifier *string                           `xml:"attributeQualifier"`
	AttributeDetails   []*AttributeInformationType97181C `xml:"attributeDetails"`
}

// AttributeType78561S is Fee/reduction parameters.
type AttributeType78561S struct {
	// XMLName      xml.Name                    `xml:"AttributeType_78561S"`
	FeeParameter []*AttributeInformationType `xml:"feeParameter"`
}

// BaggageDetailsType is Unit qualifier
type BaggageDetailsType struct {
	FreeAllowance *string `xml:"freeAllowance"`
	QuantityCode  *string `xml:"quantityCode"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// BagtagDetailsType is Number
type BagtagDetailsType struct {
	Identifier *string `xml:"identifier"`
	Number     *string `xml:"number"`
}

// BucketInformationType is Mode
type BucketInformationType struct {
	Number *string `xml:"number"`
	Name   *string `xml:"name"`
	Mode   *string `xml:"mode"`
}

// CabinInformationType is Cabin code designator
type CabinInformationType struct {
	Service *string   `xml:"service"`
	Cabin   []*string `xml:"cabin"`
}

// CabinProductDetailsType is Availibility status : posting level
type CabinProductDetailsType struct {
	Rbd             *string `xml:"rbd"`
	BookingModifier *string `xml:"bookingModifier"`
	Cabin           *string `xml:"cabin"`
	AvlStatus       *string `xml:"avlStatus"`
}

// CabinProductDetailsType229142C is Availibility status : posting level
type CabinProductDetailsType229142C struct {
	// XMLName   xml.Name `xml:"CabinProductDetailsType_229142C"`
	Rbd       *string `xml:"rbd"`
	Cabin     *string `xml:"cabin"`
	AvlStatus *string `xml:"avlStatus"`
}

// CabinProductDetailsType306830C is Availibility status : posting level
type CabinProductDetailsType306830C struct {
	// XMLName         xml.Name `xml:"CabinProductDetailsType_306830C"`
	Rbd             *string `xml:"rbd"`
	BookingModifier *string `xml:"bookingModifier"`
	Cabin           *string `xml:"cabin"`
	AvlStatus       *string `xml:"avlStatus"`
}

// CabinProductDetailsType306831C is Availibility status : posting level
type CabinProductDetailsType306831C struct {
	// XMLName         xml.Name `xml:"CabinProductDetailsType_306831C"`
	Rbd             *string `xml:"rbd"`
	BookingModifier *string `xml:"bookingModifier"`
	Cabin           *string `xml:"cabin"`
	AvlStatus       *string `xml:"avlStatus"`
}

// CategDescrType is Category processing indicator
type CategDescrType struct {
	DescriptionInfo  *CategoryDescriptionType `xml:"descriptionInfo"`
	ProcessIndicator *string                  `xml:"processIndicator"`
}

// CategoryDescriptionType is Category Code (ATPCO component code, e.g ADV for Advance purchase, STP for stopover restrictions, ELG for eligibility restrictions...)
type CategoryDescriptionType struct {
	Number *string `xml:"number"`
	Code   *string `xml:"code"`
}

// ClassInformationType is Class designator
type ClassInformationType struct {
	Service *string   `xml:"service"`
	Rbd     []*string `xml:"rbd"`
}

// CodedAttributeInformationType is Fee Id Number
type CodedAttributeInformationType struct {
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeInformationType283620C is Attribute description
type CodedAttributeInformationType283620C struct {
	// XMLName              xml.Name `xml:"CodedAttributeInformationType_283620C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeInformationType299767C is Attribute description
type CodedAttributeInformationType299767C struct {
	// XMLName              xml.Name `xml:"CodedAttributeInformationType_299767C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeType is Attribute details
type CodedAttributeType struct {
	AttributeFunction *string                                 `xml:"attributeFunction"`
	AttributeDetails  []*CodedAttributeInformationType283620C `xml:"attributeDetails"`
}

// CodedAttributeType78535S is Fee/reduction Id
type CodedAttributeType78535S struct {
	// XMLName          xml.Name                         `xml:"CodedAttributeType_78535S"`
	AttributeDetails []*CodedAttributeInformationType `xml:"attributeDetails"`
}

// CommercialAgreementsType is Other codeshare details
type CommercialAgreementsType struct {
	CodeshareDetails      *CompanyRoleIdentificationType   `xml:"codeshareDetails"`
	OtherCodeshareDetails []*CompanyRoleIdentificationType `xml:"otherCodeshareDetails"`
}

// CompanyIdentificationTextType is Company id free text.
type CompanyIdentificationTextType struct {
	TextRefNumber *string `xml:"textRefNumber"`
	CompanyText   *string `xml:"companyText"`
}

// CompanyIdentificationTypeI is Company
type CompanyIdentificationTypeI struct {
	MarketingCompany *string `xml:"marketingCompany"`
	OperatingCompany *string `xml:"operatingCompany"`
	OtherCompany     *string `xml:"otherCompany"`
}

// CompanyIdentificationType is airline alliance code
type CompanyIdentificationType struct {
	MarketingCarrier *string `xml:"marketingCarrier"`
	OperatingCarrier *string `xml:"operatingCarrier"`
	Alliance         *string `xml:"alliance"`
}

// CompanyRoleIdentificationType is flight number
type CompanyRoleIdentificationType struct {
	CodeShareType     *string `xml:"codeShareType"`
	AirlineDesignator *string `xml:"airlineDesignator"`
	FlightNumber      *string `xml:"flightNumber"`
}

// CompanyRoleIdentificationType120783C is company identification
type CompanyRoleIdentificationType120783C struct {
	// XMLName                 xml.Name `xml:"CompanyRoleIdentificationType_120783C"`
	TransportStageQualifier *string `xml:"transportStageQualifier"`
	Company                 *string `xml:"company"`
}

// ConversionRateDetailsTypeI is amount
type ConversionRateDetailsTypeI struct {
	ConversionType *string `xml:"conversionType"`
	Currency       *string `xml:"currency"`
	Amount         *string `xml:"amount"`
}

// ConversionRateDetail is Applicable ISO country code or Tax designator code.
type ConversionRateDetail struct {
	// XMLName             xml.Name `xml:"conversionRateDetail"`
	ConversionType      *string `xml:"conversionType"`
	Currency            *string `xml:"currency"`
	Rate                *string `xml:"rate"`
	ConvertedAmountLink *string `xml:"convertedAmountLink"`
	TaxQualifier        *string `xml:"taxQualifier"`
}

// ConversionRateTypeI is Detail of conversion rate of First Monetary Unit.
type ConversionRateTypeI struct {
	ConversionRateDetail []*ConversionRateDetail `xml:"conversionRateDetail"`
}

// ConversionRateTypeI78562S is Details of conversion
type ConversionRateTypeI78562S struct {
	// XMLName              xml.Name                      `xml:"ConversionRateTypeI_78562S"`
	ConversionRateDetail []*ConversionRateDetailsTypeI `xml:"conversionRateDetail"`
}

// CouponInformationDetailsType is EMD reference
type CouponInformationDetailsType struct {
	CpnNumber          *string `xml:"cpnNumber"`
	CpnReferenceNumber *string `xml:"cpnReferenceNumber"`
}

// CouponInformationType is EMD information
type CouponInformationType struct {
	CouponDetails *CouponInformationDetailsType `xml:"couponDetails"`
}

// CriteriaiDetaislType is Attribute
type CriteriaiDetaislType struct {
	Type      *string `xml:"type"`
	Value     *string `xml:"value"`
	Attribute *string `xml:"attribute"`
}

// DataInformationType is Ancillary services options
type DataInformationType struct {
	Indicator *string `xml:"indicator"`
}

// DataTypeInformationType is Status (automated, manually added, exempted). Default is automated
type DataTypeInformationType struct {
	SubType *string `xml:"subType"`
	Option  *string `xml:"option"`
}

// DateAndTimeDetailsType is Place/location identification.
type DateAndTimeDetailsType struct {
	DateQualifier  *string `xml:"dateQualifier"`
	Date           *string `xml:"date"`
	FirstTime      *string `xml:"firstTime"`
	EquipementType *string `xml:"equipementType"`
	LocationId     *string `xml:"locationId"`
}

// DateAndTimeDetailsType256192C is Location
type DateAndTimeDetailsType256192C struct {
	// XMLName   xml.Name `xml:"DateAndTimeDetailsType_256192C"`
	Qualifier *string `xml:"qualifier"`
	Date      *string `xml:"date"`
	Time      *string `xml:"time"`
	Location  *string `xml:"location"`
}

// DateAndTimeDetailsType302262C is Time
type DateAndTimeDetailsType302262C struct {
	// XMLName   xml.Name `xml:"DateAndTimeDetailsType_302262C"`
	Qualifier *string `xml:"qualifier"`
	Date      *string `xml:"date"`
	Time      *string `xml:"time"`
}

// DateAndTimeInformationType is Details on date and time
type DateAndTimeInformationType struct {
	StopDetails []*DateAndTimeDetailsType `xml:"stopDetails"`
	DummyNET    *interface{}              `xml:"Dummy.NET"`
}

// DateAndTimeInformationType182345S ...
type DateAndTimeInformationType182345S struct {
	// XMLName            xml.Name                         `xml:"DateAndTimeInformationType_182345S"`
	DateAndTimeDetails []*DateAndTimeDetailsType256192C `xml:"dateAndTimeDetails"`
	DummyNET           *interface{}                     `xml:"Dummy.NET"`
}

// DateAndTimeInformationType220498S ...
type DateAndTimeInformationType220498S struct {
	// XMLName            xml.Name                         `xml:"DateAndTimeInformationType_220498S"`
	DateAndTimeDetails []*DateAndTimeDetailsType302262C `xml:"dateAndTimeDetails"`
}

// DateTimePeriodDetailsBatchType ...
type DateTimePeriodDetailsBatchType struct {
	DateTimeQualifier *string `xml:"dateTimeQualifier"`
	DateTimeDetails   *string `xml:"dateTimeDetails"`
}

// DateTimePeriodDetailsTypeI ...
type DateTimePeriodDetailsTypeI struct {
	Qualifier *string `xml:"qualifier"`
	Value     *string `xml:"value"`
}

// DateTimePeriodType ...
type DateTimePeriodType struct {
	DateTimeDescription *DateTimePeriodDetailsBatchType `xml:"dateTimeDescription"`
}

// DimensionDetailType ...
type DimensionDetailType struct {
	BucketRef *string `xml:"bucketRef"`
	ValueRef  *string `xml:"valueRef"`
}

// DiscountAndPenaltyInformationType ...
type DiscountAndPenaltyInformationType struct {
	FeeIdentification *string                                 `xml:"feeIdentification"`
	FeeInformation    *DiscountPenaltyMonetaryInformationType `xml:"feeInformation"`
}

// DiscountPenaltyInformationType ...
type DiscountPenaltyInformationType struct {
	FareQualifier *string `xml:"fareQualifier"`
	RateCategory  *string `xml:"rateCategory"`
	Amount        string  `xml:"amount"`
	Percentage    string  `xml:"percentage"`
}

// DiscountPenaltyMonetaryInformationType ...
type DiscountPenaltyMonetaryInformationType struct {
	FeeType       *string `xml:"feeType"`
	FeeAmountType *string `xml:"feeAmountType"`
	FeeAmount     string  `xml:"feeAmount"`
	FeeCurrency   *string `xml:"feeCurrency"`
}

// DistributionMethodDetails ...
type DistributionMethodDetails struct {
	MethodCode        *string `xml:"methodCode"`
	DistriProductCode *string `xml:"distriProductCode"`
}

// DummySegmentTypeI ...
type DummySegmentTypeI struct {
}

// ElementManagementSegmentType ...
type ElementManagementSegmentType struct {
	LineNumber *string `xml:"lineNumber"`
}

// ErrorGroupType ...
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType214133S `xml:"errorOrWarningCodeDetails"`
}

// ExcessBaggageType ...
type ExcessBaggageType struct {
	BaggageDetails *BaggageDetailsType  `xml:"baggageDetails"`
	BagTagDetails  []*BagtagDetailsType `xml:"bagTagDetails"`
}

// FareCalculationCodeDetailsType ...
type FareCalculationCodeDetailsType struct {
	Qualifier         *string `xml:"qualifier"`
	Amount            string  `xml:"amount"`
	LocationCode      *string `xml:"locationCode"`
	OtherLocationCode *string `xml:"otherLocationCode"`
	Rate              string  `xml:"rate"`
}

// FareCategoryCodesTypeI ...
type FareCategoryCodesTypeI struct {
	FareType      *string   `xml:"fareType"`
	OtherFareType []*string `xml:"otherFareType"`
}

// FareDetailsTypeI ...
type FareDetailsTypeI struct {
	Qualifier    *string `xml:"qualifier"`
	Rate         string  `xml:"rate"`
	Country      *string `xml:"country"`
	FareCategory *string `xml:"fareCategory"`
}

// FareDetailsType ...
type FareDetailsType struct {
	PassengerTypeQualifier *string `xml:"passengerTypeQualifier"`
}

// FareDetailsType193037C ...
type FareDetailsType193037C struct {
	// XMLName      xml.Name `xml:"FareDetailsType_193037C"`
	Qualifier    *string `xml:"qualifier"`
	Rate         *string `xml:"rate"`
	Country      *string `xml:"country"`
	FareCategory *string `xml:"fareCategory"`
}

// FareFamilyDetailsType ...
type FareFamilyDetailsType struct {
	CommercialFamily *string `xml:"commercialFamily"`
}

// FareFamilyType ...
type FareFamilyType struct {
	RefNumber               *string                  `xml:"refNumber"`
	FareFamilyname          *string                  `xml:"fareFamilyname"`
	Hierarchy               *string                  `xml:"hierarchy"`
	Cabin                   *string                  `xml:"cabin"`
	CommercialFamilyDetails []*FareFamilyDetailsType `xml:"commercialFamilyDetails"`
	Description             *string                  `xml:"description"`
	Carrier                 *string                  `xml:"carrier"`
	Services                []*ServicesReferences    `xml:"services"`
	BookingClass            []*string                `xml:"bookingClass"`
}

// FareInformationTypeI ...
type FareInformationTypeI struct {
	ValueQualifier *string `xml:"valueQualifier"`
	Value          *string `xml:"value"`
}

// FareInformationType ...
type FareInformationType struct {
	ValueQualifier   *string                          `xml:"valueQualifier"`
	Value            *string                          `xml:"value"`
	FareDetails      *FareDetailsType193037C          `xml:"fareDetails"`
	IdentityNumber   *string                          `xml:"identityNumber"`
	FareTypeGrouping *FareTypeGroupingInformationType `xml:"fareTypeGrouping"`
	RateCategory     *string                          `xml:"rateCategory"`
}

// FareInformationType80868S ...
type FareInformationType80868S struct {
	// XMLName     xml.Name         `xml:"FareInformationType_80868S"`
	FareDetails *FareDetailsType `xml:"fareDetails"`
}

// FareProductDetailsType ...
type FareProductDetailsType struct {
	FareBasis *string `xml:"fareBasis"`
}

// FareProductDetailsType311123C ...
type FareProductDetailsType311123C struct {
	// XMLName       xml.Name  `xml:"FareProductDetailsType_311123C"`
	FareBasis     *string   `xml:"fareBasis"`
	PassengerType *string   `xml:"passengerType"`
	FareType      []*string `xml:"fareType"`
}

// FareQualifierDetailsType ...
type FareQualifierDetailsType struct {
	MovementType          *string                              `xml:"movementType"`
	FareCategories        *FareCategoryCodesTypeI              `xml:"fareCategories"`
	FareDetails           *FareDetailsTypeI                    `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareQualifierDetailsTypeI `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountPenaltyInformationType    `xml:"discountDetails"`
}

// FareTypeGroupingInformationType ...
type FareTypeGroupingInformationType struct {
	PricingGroup *string `xml:"pricingGroup"`
}

// FlightCharacteristicsType ...
type FlightCharacteristicsType struct {
	OnTimePerformance *OnTimePerformanceType `xml:"onTimePerformance"`
	InFlightSrv       []*string              `xml:"inFlightSrv"`
}

// FlightProductInformationType ...
type FlightProductInformationType struct {
	CabinProduct   []*CabinProductDetailsType     `xml:"cabinProduct"`
	ContextDetails *ProductTypeDetailsType205137C `xml:"contextDetails"`
}

// FlightProductInformationType161491S ...
type FlightProductInformationType161491S struct {
	// XMLName           xml.Name                        `xml:"FlightProductInformationType_161491S"`
	CabinProduct      *CabinProductDetailsType229142C `xml:"cabinProduct"`
	FareProductDetail *FareProductDetailsType         `xml:"fareProductDetail"`
}

// FlightProductInformationType223656S ...
type FlightProductInformationType223656S struct {
	// XMLName        xml.Name                          `xml:"FlightProductInformationType_223656S"`
	CabinProduct   []*CabinProductDetailsType306831C `xml:"cabinProduct"`
	ContextDetails *ProductTypeDetailsType           `xml:"contextDetails"`
}

// FlightProductInformationType227082S ...
type FlightProductInformationType227082S struct {
	// XMLName           xml.Name                          `xml:"FlightProductInformationType_227082S"`
	CabinProduct      []*CabinProductDetailsType306830C `xml:"cabinProduct"`
	FareProductDetail *FareProductDetailsType311123C    `xml:"fareProductDetail"`
	CorporateId       []*string                         `xml:"corporateId"`
	BreakPoint        *string                           `xml:"breakPoint"`
	ContextDetails    *ProductTypeDetailsType           `xml:"contextDetails"`
}

// FlightProposalType ...
type FlightProposalType struct {
	ReqSgt    *string `xml:"reqSgt"`
	PsdSgtRef *string `xml:"psdSgtRef"`
}

// FlightServicesType ...
type FlightServicesType struct {
	ServiceType *string                 `xml:"serviceType"`
	CabinInfo   []*CabinInformationType `xml:"cabinInfo"`
	ClassInfo   []*ClassInformationType `xml:"classInfo"`
}

// FreeTextQualificationTypeI ...
type FreeTextQualificationTypeI struct {
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
}

// FreeTextQualificationType ...
type FreeTextQualificationType struct {
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
}

// FreeTextQualificationType120769C ...
type FreeTextQualificationType120769C struct {
	// XMLName              xml.Name `xml:"FreeTextQualificationType_120769C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Language             *string `xml:"language"`
}

// FrequentTravellerIdentificationCodeType ...
type FrequentTravellerIdentificationCodeType struct {
	FrequentTravellerDetails []*FrequentTravellerIdentificationType `xml:"frequentTravellerDetails"`
}

// FrequentTravellerIdentificationType ...
type FrequentTravellerIdentificationType struct {
	Carrier      *string `xml:"carrier"`
	Number       *string `xml:"number"`
	TierLevel    *string `xml:"tierLevel"`
	PriorityCode *string `xml:"priorityCode"`
}

// GroupAmenities ...
type GroupAmenities struct {
	Amenities *Amenities `xml:"amenities"`
}

// HeaderInformationTypeI ...
type HeaderInformationTypeI struct {
	Status                []*string                   `xml:"status"`
	DateTimePeriodDetails *DateTimePeriodDetailsTypeI `xml:"dateTimePeriodDetails"`
	ReferenceNumber       *string                     `xml:"referenceNumber"`
	ProductIdentification []*string                   `xml:"productIdentification"`
}

// InteractiveFreeTextType ...
type InteractiveFreeTextType struct {
	FreeTextQualification *FreeTextQualificationTypeI `xml:"freeTextQualification"`
	FreeText              *string                     `xml:"freeText"`
}

// InteractiveFreeTextType78534S ...
type InteractiveFreeTextType78534S struct {
	// XMLName               xml.Name                   `xml:"InteractiveFreeTextType_78534S"`
	FreeTextQualification *FreeTextQualificationType `xml:"freeTextQualification"`
	Description           []*string                  `xml:"description"`
}

// InteractiveFreeTextType78544S ...
type InteractiveFreeTextType78544S struct {
	// XMLName               xml.Name                          `xml:"InteractiveFreeTextType_78544S"`
	FreeTextQualification *FreeTextQualificationType120769C `xml:"freeTextQualification"`
	Description           []*string                         `xml:"description"`
}

// InteractiveFreeTextType78559S ...
type InteractiveFreeTextType78559S struct {
	// XMLName               xml.Name                          `xml:"InteractiveFreeTextType_78559S"`
	FreeTextQualification *FreeTextQualificationType120769C `xml:"freeTextQualification"`
	Description           []*string                         `xml:"description"`
}

// ItemDescriptionDetailsTypeI ...
type ItemDescriptionDetailsTypeI struct {
	Identifier *string `xml:"identifier"`
}

// ItemDescriptionType ...
type ItemDescriptionType struct {
	ItemDescription *ItemDescriptionDetailsTypeI `xml:"itemDescription"`
}

// ItemNumberIdentificationType ...
type ItemNumberIdentificationType struct {
	Number            *string `xml:"number"`
	Type              *string `xml:"type"`
	Qualifier         *string `xml:"qualifier"`
	ResponsibleAgency *string `xml:"responsibleAgency"`
}

// ItemNumberIdentificationType192491C ...
type ItemNumberIdentificationType192491C struct {
	// XMLName           xml.Name `xml:"ItemNumberIdentificationType_192491C"`
	Number            *string `xml:"number"`
	Type              *string `xml:"type"`
	Qualifier         *string `xml:"qualifier"`
	ResponsibleAgency *string `xml:"responsibleAgency"`
}

// ItemNumberIdentificationType234878C ...
type ItemNumberIdentificationType234878C struct {
	// XMLName xml.Name `xml:"ItemNumberIdentificationType_234878C"`
	Number *string `xml:"number"`
	Type   *string `xml:"type"`
}

// ItemNumberIdentificationType248537C ...
type ItemNumberIdentificationType248537C struct {
	// XMLName xml.Name `xml:"ItemNumberIdentificationType_248537C"`
	Number *string `xml:"number"`
}

// ItemNumberIdentificationType295743C ...
type ItemNumberIdentificationType295743C struct {
	// XMLName    xml.Name `xml:"ItemNumberIdentificationType_295743C"`
	Number     *string `xml:"number"`
	NumberType *string `xml:"numberType"`
}

// ItemNumberType ...
type ItemNumberType struct {
	ItemNumber *ItemNumberIdentificationType192491C `xml:"itemNumber"`
}

// ItemNumberType166130S ...
type ItemNumberType166130S struct {
	// XMLName           xml.Name                               `xml:"ItemNumberType_166130S"`
	ItemNumberDetails []*ItemNumberIdentificationType234878C `xml:"itemNumberDetails"`
}

// ItemNumberType176648S ...
type ItemNumberType176648S struct {
	// XMLName           xml.Name                               `xml:"ItemNumberType_176648S"`
	ItemNumberDetails []*ItemNumberIdentificationType248537C `xml:"itemNumberDetails"`
}

// ItemNumberType224478S ...
type ItemNumberType224478S struct {
	// XMLName          xml.Name                                `xml:"itemNumber"`
	ItemNumberId     *ItemNumberIdentificationType295743C    `xml:"itemNumberId"`
	CodeShareDetails []*CompanyRoleIdentificationType120783C `xml:"codeShareDetails"`
	PriceTicketing   *PricingTicketingInformationType        `xml:"priceTicketing"`
}

// ItemNumberType80866S ...
type ItemNumberType80866S struct {
	// XMLName           xml.Name                      `xml:"ItemNumberType_80866S"`
	ItemNumberDetails *ItemNumberIdentificationType `xml:"itemNumberDetails"`
}

// ItemReferencesAndVersionsType ...
type ItemReferencesAndVersionsType struct {
	ReferenceType *string `xml:"referenceType"`
	RefNumber     *string `xml:"refNumber"`
}

// ItemReferencesAndVersionsType78536S ...
type ItemReferencesAndVersionsType78536S struct {
	// XMLName       xml.Name `xml:"ItemReferencesAndVersionsType_78536S"`
	ReferenceType *string `xml:"referenceType"`
	RefNumber     *string `xml:"refNumber"`
}

// ItemReferencesAndVersionsType78564S ...
type ItemReferencesAndVersionsType78564S struct {
	// XMLName       xml.Name `xml:"ItemReferencesAndVersionsType_78564S"`
	ReferenceType *string `xml:"referenceType"`
	FeeRefNumber  *string `xml:"feeRefNumber"`
}

// ItineraryDetailsType ...
type ItineraryDetailsType struct {
	AirportCityQualifier *string `xml:"airportCityQualifier"`
	SegmentNumber        *string `xml:"segmentNumber"`
}

// KeywordIdentifierType ...
type KeywordIdentifierType struct {
	Number *string `xml:"number"`
}

// KeywordType ...
type KeywordType struct {
	Identifier  *KeywordIdentifierType `xml:"identifier"`
	Description *string                `xml:"description"`
}

// LabelType ...
type LabelType struct {
	Code *string `xml:"code"`
	Name *string `xml:"name"`
}

// LabelType290117C ...
type LabelType290117C struct {
	// XMLName xml.Name `xml:"LabelType_290117C"`
	Code *string `xml:"code"`
	Name *string `xml:"name"`
}

// LabelType290125C ...
type LabelType290125C struct {
	// XMLName xml.Name `xml:"LabelType_290125C"`
	Code *string `xml:"code"`
}

// LocationIdentificationDetailsType ...
type LocationIdentificationDetailsType struct {
	LocationId           *string `xml:"locationId"`
	AirportCityQualifier *string `xml:"airportCityQualifier"`
	Terminal             *string `xml:"terminal"`
}

// MealServicesType ...
type MealServicesType struct {
	ServiceDetails []*ServiceDetailsType `xml:"serviceDetails"`
}

// Method ...
type Method struct {
	DistributionMethodDetails []*DistributionMethodDetails `xml:"distributionMethodDetails"`
}

// MiniRulesDetailsType ...
type MiniRulesDetailsType struct {
	Interpretation *string   `xml:"interpretation"`
	Value          []*string `xml:"value"`
}

// MiniRulesIndicatorType ...
type MiniRulesIndicatorType struct {
	RuleIndicator []*string `xml:"ruleIndicator"`
}

// MiniRulesType ...
type MiniRulesType struct {
	Category *string `xml:"category"`
}

// MiniRulesType78547S ...
type MiniRulesType78547S struct {
	// XMLName         xml.Name                `xml:"MiniRulesType_78547S"`
	RestrictionType *string                 `xml:"restrictionType"`
	Category        *string                 `xml:"category"`
	Indicator       *MiniRulesIndicatorType `xml:"indicator"`
	MiniRules       []*MiniRulesDetailsType `xml:"miniRules"`
}

// MonetaryInformationDetailsTypeI ...
type MonetaryInformationDetailsTypeI struct {
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsType ...
type MonetaryInformationDetailsType struct {
	TypeQualifier string `xml:"typeQualifier"`
	Amount        string `xml:"amount"`
}

// MonetaryInformationDetailsType277475C ...
type MonetaryInformationDetailsType277475C struct {
	// XMLName    xml.Name `xml:"MonetaryInformationDetailsType_277475C"`
	AmountType *string `xml:"amountType"`
	Amount     string  `xml:"amount"`
	Currency   *string `xml:"currency"`
}

// MonetaryInformationDetailsType288107C ...
type MonetaryInformationDetailsType288107C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsType_288107C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        string  `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsType293903C ...
type MonetaryInformationDetailsType293903C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsType_293903C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        string  `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationTypeI ...
type MonetaryInformationTypeI struct {
	MonetaryDetails []*MonetaryInformationDetailsTypeI `xml:"monetaryDetails"`
}

// MonetaryInformationType ...
type MonetaryInformationType struct {
	MonetaryDetails      []*MonetaryInformationDetailsType `xml:"monetaryDetails"`
	OtherMonetaryDetails *MonetaryInformationDetailsType   `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType199534S ...
type MonetaryInformationType199534S struct {
	// XMLName        xml.Name                                 `xml:"MonetaryInformationType_199534S"`
	MonetaryDetail []*MonetaryInformationDetailsType277475C `xml:"monetaryDetail"`
}

// MonetaryInformationType204470S ...
type MonetaryInformationType204470S struct {
	// XMLName        xml.Name                                 `xml:"MonetaryInformationType_204470S"`
	MonetaryDetail []*MonetaryInformationDetailsType277475C `xml:"monetaryDetail"`
}

// MonetaryInformationType212668S ...
type MonetaryInformationType212668S struct {
	// XMLName         xml.Name                                 `xml:"MonetaryInformationType_212668S"`
	MonetaryDetails []*MonetaryInformationDetailsType293903C `xml:"monetaryDetails"`
}

// MonetaryInformationType212672S ...
type MonetaryInformationType212672S struct {
	// XMLName         xml.Name                                 `xml:"MonetaryInformationType_212672S"`
	MonetaryDetails []*MonetaryInformationDetailsType293903C `xml:"monetaryDetails"`
}

// MonetaryInformationType228104S ...
type MonetaryInformationType228104S struct {
	// XMLName        xml.Name                                 `xml:"MonetaryInformationType_228104S"`
	MonetaryDetail []*MonetaryInformationDetailsType277475C `xml:"monetaryDetail"`
}

// MultiDimensionalValueType ...
type MultiDimensionalValueType struct {
	Identifier      *string                `xml:"identifier"`
	DimensionDetail []*DimensionDetailType `xml:"dimensionDetail"`
}

// NumberOfUnitDetailsType ...
type NumberOfUnitDetailsType struct {
	NumberOfUnit  *string `xml:"numberOfUnit"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// NumberOfUnitsType ...
type NumberOfUnitsType struct {
	QuantityDetails []*NumberOfUnitDetailsType `xml:"quantityDetails"`
}

// OfferItemType ...
type OfferItemType struct {
	OfferItemId *string `xml:"offerItemId"`
	Status      *string `xml:"status"`
}

// OfferType ...
type OfferType struct {
	Reference            *string `xml:"reference"`
	OfferId              *string `xml:"offerId"`
	UniqueOfferReference *string `xml:"uniqueOfferReference"`
}

// OnTimePerformanceType ...
type OnTimePerformanceType struct {
	DateTimePeriod *string `xml:"dateTimePeriod"`
	Percentage     string  `xml:"percentage"`
	Accuracy       *string `xml:"accuracy"`
}

// OriginAndDestinationRequestType ...
type OriginAndDestinationRequestType struct {
	SegRef          *string                 `xml:"segRef"`
	LocationForcing []*ItineraryDetailsType `xml:"locationForcing"`
}

// OriginAndDestinationRequestType134833S ...
type OriginAndDestinationRequestType134833S struct {
	// XMLName xml.Name `xml:"OriginAndDestinationRequestType_134833S"`
	SegRef *string `xml:"segRef"`
}

// OriginatorIdentificationDetailsTypeI ...
type OriginatorIdentificationDetailsTypeI struct {
	OfficeName         *string `xml:"officeName"`
	AgentSignin        *string `xml:"agentSignin"`
	ConfidentialOffice *string `xml:"confidentialOffice"`
	OtherOffice        *string `xml:"otherOffice"`
}

// PricingTicketingInformationType ...
type PricingTicketingInformationType struct {
	PriceType []*string `xml:"priceType"`
}

// PricingTicketingSubsequentType ...
type PricingTicketingSubsequentType struct {
	PaxFareNum []*string `xml:"paxFareNum"`
}

// PricingTicketingSubsequentType228122S ...
type PricingTicketingSubsequentType228122S struct {
	// XMLName          xml.Name                                 `xml:"PricingTicketingSubsequentType_228122S"`
	PaxFareNum       *string                                  `xml:"paxFareNum"`
	TotalFareAmount  string                                   `xml:"totalFareAmount"`
	TotalTaxAmount   string                                   `xml:"totalTaxAmount"`
	CodeShareDetails []*CompanyRoleIdentificationType120783C  `xml:"codeShareDetails"`
	MonetaryDetails  []*MonetaryInformationDetailsType277475C `xml:"monetaryDetails"`
	PricingTicketing *PricingTicketingInformationType         `xml:"pricingTicketing"`
}

// ProcessingInformationType ...
type ProcessingInformationType struct {
	ActionQualifier    *string `xml:"actionQualifier"`
	ReferenceQualifier *string `xml:"referenceQualifier"`
	RefNum             *string `xml:"refNum"`
}

// ProductDateTimeType ...
type ProductDateTimeType struct {
	DateOfDeparture *string `xml:"dateOfDeparture"`
	TimeOfDeparture *string `xml:"timeOfDeparture"`
	DateOfArrival   *string `xml:"dateOfArrival"`
	TimeOfArrival   *string `xml:"timeOfArrival"`
	DateVariation   *string `xml:"dateVariation"`
}

// ProductDetailsType ...
type ProductDetailsType struct {
	Designator         *string   `xml:"designator"`
	AvailabilityStatus *string   `xml:"availabilityStatus"`
	SpecialService     *string   `xml:"specialService"`
	Option             []*string `xml:"option"`
}

// ProductFacilitiesType ...
type ProductFacilitiesType struct {
	LastSeatAvailable      *string   `xml:"lastSeatAvailable"`
	LevelOfAccess          *string   `xml:"levelOfAccess"`
	ElectronicTicketing    *string   `xml:"electronicTicketing"`
	OperationalSuffix      *string   `xml:"operationalSuffix"`
	ProductDetailQualifier *string   `xml:"productDetailQualifier"`
	FlightCharacteristic   []*string `xml:"flightCharacteristic"`
}

// ProductGroupInformationType ...
type ProductGroupInformationType struct {
	GroupType *string `xml:"groupType"`
}

// ProductIdentDetailsType ...
type ProductIdentDetailsType struct {
	Number *string `xml:"number"`
}

// ProductIdentificationType ...
type ProductIdentificationType struct {
	ProductData []*ProductIdentDetailsType `xml:"productData"`
}

// ProductInformationType ...
type ProductInformationType struct {
	ProductDetailsQualifier *string               `xml:"productDetailsQualifier"`
	BookingClassDetails     []*ProductDetailsType `xml:"bookingClassDetails"`
}

// ProductTypeDetailsType ...
type ProductTypeDetailsType struct {
	AvailabilityCnxType []*string `xml:"availabilityCnxType"`
}

// ProductTypeDetailsType205137C ...
type ProductTypeDetailsType205137C struct {
	// XMLName xml.Name  `xml:"ProductTypeDetailsType_205137C"`
	Avl []*string `xml:"avl"`
}

// ProgramCodeType ...
type ProgramCodeType struct {
	Program     *LabelType290117C `xml:"program"`
	TierLevel   []*LabelType      `xml:"tierLevel"`
	AccrualType []*AccrualType    `xml:"accrualType"`
}

// ProgramCodeType209757S ...
type ProgramCodeType209757S struct {
	// XMLName     xml.Name              `xml:"ProgramCodeType_209757S"`
	PaxFareNum  []*string             `xml:"paxFareNum"`
	Program     *LabelType290125C     `xml:"program"`
	TierLevel   []*LabelType290125C   `xml:"tierLevel"`
	AccrualType []*AccrualType290124C `xml:"accrualType"`
}

// ProposedSegmentDetailsType ...
type ProposedSegmentDetailsType struct {
	Ref           *string `xml:"ref"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// ProposedSegmentType ...
type ProposedSegmentType struct {
	FlightProposal       []*ProposedSegmentDetailsType      `xml:"flightProposal"`
	FlightCharacteristic *string                            `xml:"flightCharacteristic"`
	MajCabin             *string                            `xml:"majCabin"`
	WarningCode          []*ApplicationErrorInformationType `xml:"warningCode"`
	WarningFreeText      *string                            `xml:"warningFreeText"`
}

// QualityCriteriaType ...
type QualityCriteriaType struct {
	Risk        *string `xml:"risk"`
	Convenience string  `xml:"convenience"`
	Value       string  `xml:"value"`
}

// RangeDetailsType ...
type RangeDetailsType struct {
	RangeQualifier *string      `xml:"rangeQualifier"`
	RangeDetails   []*RangeType `xml:"rangeDetails"`
}

// RangeType ...
type RangeType struct {
	DataType *string `xml:"dataType"`
	Min      string  `xml:"min"`
	Max      string  `xml:"max"`
}

// ReferenceInfoType ...
type ReferenceInfoType struct {
	ReferencingDetail []*ReferencingDetailsType191583C `xml:"referencingDetail"`
	DummyNET          *interface{}                     `xml:"Dummy.NET"`
}

// ReferenceInfoType133176S ...
type ReferenceInfoType133176S struct {
	// XMLName           xml.Name                  `xml:"ReferenceInfoType_133176S"`
	ReferencingDetail []*ReferencingDetailsType `xml:"referencingDetail"`
}

// ReferenceInfoType134840S ...
type ReferenceInfoType134840S struct {
	// XMLName           xml.Name                         `xml:"refInfo"`
	ReferencingDetail []*ReferencingDetailsType195563C `xml:"referencingDetail"`
}

// ReferenceInfoType165972S ...
type ReferenceInfoType165972S struct {
	// XMLName          xml.Name                         `xml:"ReferenceInfoType_165972S"`
	ReferenceDetails []*ReferencingDetailsType234704C `xml:"referenceDetails"`
	DummyNET         *interface{}                     `xml:"Dummy.NET"`
}

// ReferenceInfoType212767S ...
type ReferenceInfoType212767S struct {
	// XMLName          xml.Name                         `xml:"ReferenceInfoType_212767S"`
	ReferenceDetails []*ReferencingDetailsType234704C `xml:"referenceDetails"`
}

// ReferenceInfoType214315S ...
type ReferenceInfoType214315S struct {
	// XMLName           xml.Name                         `xml:"ReferenceInfoType_214315S"`
	ReferencingDetail []*ReferencingDetailsType195563C `xml:"referencingDetail"`
}

// ReferenceInfoType224557S ...
type ReferenceInfoType224557S struct {
	// XMLName          xml.Name                         `xml:"ReferenceInfoType_224557S"`
	ReferenceDetails []*ReferencingDetailsType296031C `xml:"referenceDetails"`
}

// ReferenceInfoType227042S ...
type ReferenceInfoType227042S struct {
	// XMLName          xml.Name                         `xml:"flightRef"`
	ReferenceDetails []*ReferencingDetailsType234704C `xml:"referenceDetails"`
}

// ReferenceInfoType227141S ...
type ReferenceInfoType227141S struct {
	// XMLName           xml.Name                  `xml:"ReferenceInfoType_227141S"`
	ReferencingDetail []*ReferencingDetailsType `xml:"referencingDetail"`
}

// ReferenceInformationType ...
type ReferenceInformationType struct {
	ReferenceDetails []*ReferencingDetailsType296031C `xml:"referenceDetails"`
}

// ReferenceType ...
type ReferenceType struct {
	RefOfLeg            *string `xml:"refOfLeg"`
	FirstItemIdentifier *string `xml:"firstItemIdentifier"`
	LastItemIdentifier  *string `xml:"lastItemIdentifier"`
}

// ReferencingDetailsType ...
type ReferencingDetailsType struct {
	RefQualifier *string `xml:"refQualifier"`
	RefNumber    *string `xml:"refNumber"`
}

// ReferencingDetailsType191583C ...
type ReferencingDetailsType191583C struct {
	// XMLName      xml.Name `xml:"ReferencingDetailsType_191583C"`
	RefQualifier *string `xml:"refQualifier"`
	RefNumber    *string `xml:"refNumber"`
}

// ReferencingDetailsType195563C ...
type ReferencingDetailsType195563C struct {
	// XMLName      xml.Name `xml:"ReferencingDetailsType_195563C"`
	RefQualifier *string `xml:"refQualifier"`
	RefNumber    *string `xml:"refNumber"`
}

// ReferencingDetailsType234704C ...
type ReferencingDetailsType234704C struct {
	// XMLName xml.Name `xml:"referenceDetails"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType296031C ...
type ReferencingDetailsType296031C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsType_296031C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// SegmentRepetitionControlDetailsTypeI ...
type SegmentRepetitionControlDetailsTypeI struct {
	Quantity      *string `xml:"quantity"`
	NumberOfUnits *string `xml:"numberOfUnits"`
}

// SegmentRepetitionControlTypeI ...
type SegmentRepetitionControlTypeI struct {
	SegmentControlDetails []*SegmentRepetitionControlDetailsTypeI `xml:"segmentControlDetails"`
}

// SelectionDetailsInformationType ...
type SelectionDetailsInformationType struct {
	Type              *string `xml:"type"`
	OptionInformation *string `xml:"optionInformation"`
}

// SelectionDetailsType ...
type SelectionDetailsType struct {
	CarrierFeeDetails *SelectionDetailsInformationType `xml:"carrierFeeDetails"`
}

// SequenceDetailsTypeU ...
type SequenceDetailsTypeU struct {
	SequenceDetails *SequenceInformationTypeU `xml:"sequenceDetails"`
}

// SequenceInformationTypeU ...
type SequenceInformationTypeU struct {
	Number             *string `xml:"number"`
	IdentificationCode *string `xml:"identificationCode"`
}

// SequenceType ...
type SequenceType struct {
	Number *string `xml:"number"`
	ReqSgt *string `xml:"reqSgt"`
}

// ServiceDetailsType ...
type ServiceDetailsType struct {
	BookingClass *string   `xml:"bookingClass"`
	Service      []*string `xml:"service"`
}

// ServicesReferences ...
type ServicesReferences struct {
	Reference *string `xml:"reference"`
	Status    *string `xml:"status"`
	FromPrice *string `xml:"fromPrice"`
}

// SpecialRequirementsDataDetailsType ...
type SpecialRequirementsDataDetailsType struct {
	SeatCharacteristics []*string    `xml:"seatCharacteristics"`
	DummyNET            *interface{} `xml:"Dummy.NET"`
}

// SpecialRequirementsDetailsType ...
type SpecialRequirementsDetailsType struct {
	ServiceRequirementsInfo *SpecialRequirementsTypeDetailsType   `xml:"serviceRequirementsInfo"`
	SeatDetails             []*SpecialRequirementsDataDetailsType `xml:"seatDetails"`
}

// SpecialRequirementsTypeDetailsType ...
type SpecialRequirementsTypeDetailsType struct {
	ServiceClassification    *string   `xml:"serviceClassification"`
	ServiceStatus            *string   `xml:"serviceStatus"`
	ServiceNumberOfInstances *string   `xml:"serviceNumberOfInstances"`
	ServiceMarketingCarrier  *string   `xml:"serviceMarketingCarrier"`
	ServiceGroup             *string   `xml:"serviceGroup"`
	ServiceSubGroup          *string   `xml:"serviceSubGroup"`
	ServiceFreeText          []*string `xml:"serviceFreeText"`
}

// SpecificDataInformationType ...
type SpecificDataInformationType struct {
	DataTypeInformation *DataTypeInformationType `xml:"dataTypeInformation"`
	DataInformation     []*DataInformationType   `xml:"dataInformation"`
}

// SpecificTravellerDetailsType ...
type SpecificTravellerDetailsType struct {
	ReferenceNumber *string `xml:"referenceNumber"`
}

// SpecificTravellerType ...
type SpecificTravellerType struct {
	TravellerDetails []*SpecificTravellerDetailsType `xml:"travellerDetails"`
}

// StatusDetailsType ...
type StatusDetailsType struct {
	Indicator string `xml:"indicator"`
	Action    string `xml:"action"`
}

// StatusDetailsType308329C ...
type StatusDetailsType308329C struct {
	// XMLName          xml.Name `xml:"status"`
	AdvisoryTypeInfo *string `xml:"advisoryTypeInfo"`
	Notification     *string `xml:"notification"`
	Notification2    *string `xml:"notification2"`
	Description      *string `xml:"description"`
}

// StatusType ...
type StatusType struct {
	StatusInformation []*StatusDetailsType `xml:"statusInformation"`
}

// StatusType224999S ...
type StatusType224999S struct {
	// XMLName xml.Name                    `xml:"replyStatus"`
	Status []*StatusDetailsType308329C `xml:"status"`
}

// TaxDetailsType ...
type TaxDetailsType struct {
	Rate         *string   `xml:"rate"`
	CountryCode  *string   `xml:"countryCode"`
	CurrencyCode *string   `xml:"currencyCode"`
	Type         *string   `xml:"type"`
	Indicator    []*string `xml:"indicator"`
}

// TaxType ...
type TaxType struct {
	TaxCategory *string           `xml:"taxCategory"`
	TaxDetails  []*TaxDetailsType `xml:"taxDetails"`
}

// ThemeText ...
type ThemeText struct {
	Reference *string `xml:"reference"`
	Text      *string `xml:"text"`
}

// TopologyType ...
type TopologyType struct {
	Sequence []*SequenceType `xml:"sequence"`
}

// TransportIdentifierType ...
type TransportIdentifierType struct {
	CompanyIdentification *CompanyIdentificationTypeI `xml:"companyIdentification"`
}

// TravelProductType ...
type TravelProductType struct {
	ProductDateTime     *ProductDateTimeType                    `xml:"productDateTime"`
	Location            []*LocationIdentificationDetailsType    `xml:"location"`
	CompanyId           *CompanyIdentificationType              `xml:"companyId"`
	FlightOrtrainNumber *string                                 `xml:"flightOrtrainNumber"`
	ProductDetail       *AdditionalProductDetailsType           `xml:"productDetail"`
	AddProductDetail    *ProductFacilitiesType                  `xml:"addProductDetail"`
	AttributeDetails    []*CodedAttributeInformationType299767C `xml:"attributeDetails"`
}

// TravellerDetailsType ...
type TravellerDetailsType struct {
	Ref             *string `xml:"ref"`
	InfantIndicator *string `xml:"infantIndicator"`
}

// TravellerReferenceInformationType ...
type TravellerReferenceInformationType struct {
	Ptc       []*string               `xml:"ptc"`
	Traveller []*TravellerDetailsType `xml:"traveller"`
}

// UserIdentificationType ...
type UserIdentificationType struct {
	OfficeIdentification *OriginatorIdentificationDetailsTypeI `xml:"officeIdentification"`
	OfficeType           *string                               `xml:"officeType"`
	OfficeCode           *string                               `xml:"officeCode"`
}

// ValueSearchCriteriaType ...
type ValueSearchCriteriaType struct {
	Ref             *string                 `xml:"ref"`
	Value           *string                 `xml:"value"`
	CriteriaDetails []*CriteriaiDetaislType `xml:"criteriaDetails"`
}

// VirtualInterlineRecommendationType ...
type VirtualInterlineRecommendationType struct {
	Identifier      *string                                  `xml:"identifier"`
	TotalAmount     *string                                  `xml:"totalAmount"`
	TotalTaxes      *string                                  `xml:"totalTaxes"`
	OtherAmount     []*MonetaryInformationDetailsType288107C `xml:"otherAmount"`
	QualityCriteria *QualityCriteriaType                     `xml:"qualityCriteria"`
}

// VirtualInterliningCombinationType ...
type VirtualInterliningCombinationType struct {
	Number         *string               `xml:"number"`
	RecoRef        *string               `xml:"recoRef"`
	FlightProposal []*FlightProposalType `xml:"flightProposal"`
}
