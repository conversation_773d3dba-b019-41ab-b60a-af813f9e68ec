package pnr_repply

// PnrHeader ...
type PnrHeader struct {
	// XMLName                   xml.Name                           `xml:"pnrHeader"`
	ReservationInfo           *ReservationControlInformationType `xml:"reservationInfo"`
	ReferenceForRecordLocator *ReferenceInfoType                 `xml:"referenceForRecordLocator"`
}

// TechnicalData ...
type TechnicalData struct {
	// XMLName                       xml.Name                                 `xml:"technicalData"`
	EnveloppeNumberData           *SequenceDetailsTypeU                    `xml:"enveloppeNumberData"`
	LastTransmittedEnvelopeNumber *PnrHistoryDataType27157S                `xml:"lastTransmittedEnvelopeNumber"`
	PurgeDateData                 *StructuredDateTimeInformationType27086S `xml:"purgeDateData"`
	GeneralPNRInformation         *StatusTypeI32775S                       `xml:"generalPNRInformation"`
}

// PassengerData ...
type PassengerData struct {
	// XMLName              xml.Name                          `xml:"passengerData"`
	TravellerInformation *TravellerInformationType185946S  `xml:"travellerInformation"`
	GroupCounters        *NumberOfUnitsType76106S          `xml:"groupCounters"`
	DateOfBirth          *DateAndTimeInformationType32722S `xml:"dateOfBirth"`
}

// EnhancedPassengerData ...
type EnhancedPassengerData struct {
	// XMLName                        xml.Name                          `xml:"enhancedPassengerData"`
	EnhancedTravellerInformation   *EnhancedTravellerInformationType `xml:"enhancedTravellerInformation"`
	GroupCountersInEnhancedPaxData *NumberOfUnitsType76106S          `xml:"groupCountersInEnhancedPaxData"`
	DateOfBirthInEnhancedPaxData   *DateAndTimeInformationType32722S `xml:"dateOfBirthInEnhancedPaxData"`
}

// TravellerInfo ...
type TravellerInfo struct {
	// XMLName                    xml.Name                      `xml:"travellerInfo"`
	ElementManagementPassenger *ElementManagementSegmentType `xml:"elementManagementPassenger"`
	PassengerData              []*PassengerData              `xml:"passengerData"`
	EnhancedPassengerData      []*EnhancedPassengerData      `xml:"enhancedPassengerData"`
	NameError                  *ErrorGroupType223552G        `xml:"nameError"`
}

// CarbonDioxydeInfo ...
type CarbonDioxydeInfo struct {
	// XMLName                 xml.Name                 `xml:"carbonDioxydeInfo"`
	CarbonDioxydeAmount     *QuantityType            `xml:"carbonDioxydeAmount"`
	CarbonDioxydeInfoSource *FreeTextInformationType `xml:"carbonDioxydeInfoSource"`
}

// YieldGroup ...
type YieldGroup struct {
	// XMLName        xml.Name                  `xml:"yieldGroup"`
	YieldData      *ODKeyPerformanceDataType `xml:"yieldData"`
	YieldDataGroup *ONDType                  `xml:"yieldDataGroup"`
}

// LegInfo ...
type LegInfo struct {
	// XMLName             xml.Name                             `xml:"legInfo"`
	MarkerLegInfo       *FlightSegmentDetailsTypeI           `xml:"markerLegInfo"`
	LegTravelProduct    *TravelProductInformationTypeI99362S `xml:"legTravelProduct"`
	InteractiveFreeText []*InteractiveFreeTextTypeI99363S    `xml:"interactiveFreeText"`
}

// LccTypicalData ...
type LccTypicalData struct {
	// XMLName           xml.Name                            `xml:"lccTypicalData"`
	LccFareData       *TariffInformationTypeI28460S       `xml:"lccFareData"`
	LccConnectionData *ItemReferencesAndVersionsType6550S `xml:"lccConnectionData"`
}

// InsuranceInformation ...
type InsuranceInformation struct {
	// XMLName                      xml.Name                           `xml:"insuranceInformation"`
	InsuranceName                *InsuranceNameType                 `xml:"insuranceName"`
	InsuranceMonetaryInformation *MonetaryInformationTypeI1689S     `xml:"insuranceMonetaryInformation"`
	InsurancePremiumInfo         *TravellerInsuranceInformationType `xml:"insurancePremiumInfo"`
	InsuranceDocInfo             *TravellerDocumentInformationTypeU `xml:"insuranceDocInfo"`
}

// RoomRateDetails ...
type RoomRateDetails struct {
	// XMLName           xml.Name                `xml:"roomRateDetails"`
	RoomInformation   *HotelRoomType          `xml:"roomInformation"`
	Children          []*ChildrenGroupType    `xml:"children"`
	TariffDetails     *TariffInformationTypeI `xml:"tariffDetails"`
	RateCodeIndicator *RuleInformationTypeU   `xml:"rateCodeIndicator"`
}

// GuaranteeOrDeposit ...
type GuaranteeOrDeposit struct {
	// XMLName        xml.Name                  `xml:"guaranteeOrDeposit"`
	PaymentInfo    *PaymentInformationTypeI  `xml:"paymentInfo"`
	CreditCardInfo *FormOfPaymentTypeI29553S `xml:"creditCardInfo"`
}

// ArrivalFlightDetails ...
type ArrivalFlightDetails struct {
	// XMLName                    xml.Name                              `xml:"arrivalFlightDetails"`
	TravelProductInformation   *TravelProductInformationTypeI193102S `xml:"travelProductInformation"`
	AdditionalTransportDetails *AdditionalTransportDetailsTypeU      `xml:"additionalTransportDetails"`
}

// HotelReservationInfo ...
type HotelReservationInfo struct {
	// XMLName                    xml.Name                                       `xml:"hotelReservationInfo"`
	HotelPropertyInfo          *HotelPropertyType                             `xml:"hotelPropertyInfo"`
	CompanyIdentification      *CompanyInformationType                        `xml:"companyIdentification"`
	RequestedDates             *StructuredPeriodInformationType207579S        `xml:"requestedDates"`
	RoomRateDetails            *RoomRateDetails                               `xml:"roomRateDetails"`
	CancelOrConfirmNbr         []*ReservationControlInformationTypeI196503S   `xml:"cancelOrConfirmNbr"`
	RoomstayIndex              *ItemNumberTypeU33258S                         `xml:"roomstayIndex"`
	BookingSource              *UserIdentificationType215330S                 `xml:"bookingSource"`
	BillableInfo               *BillableInformationTypeU                      `xml:"billableInfo"`
	CustomerInfo               *ConsumerReferenceInformationTypeI             `xml:"customerInfo"`
	FrequentTravellerInfo      *FrequentTravellerIdentificationCodeType38226S `xml:"frequentTravellerInfo"`
	GuaranteeOrDeposit         *GuaranteeOrDeposit                            `xml:"guaranteeOrDeposit"`
	TextOptions                []*MiscellaneousRemarksType664S                `xml:"textOptions"`
	SavingAmountInfo           *MonetaryInformationTypeI1689S                 `xml:"savingAmountInfo"`
	WrittenConfirmationContact *ContactInformationTypeU                       `xml:"writtenConfirmationContact"`
	WrittenConfirmationInfo    *NameAndAddressBatchTypeU                      `xml:"writtenConfirmationInfo"`
	DocumentInformationDetails *DocumentInformationDetailsTypeI207581S        `xml:"documentInformationDetails"`
	ArrivalFlightDetails       *ArrivalFlightDetails                          `xml:"arrivalFlightDetails"`
	BookingIndicator           *StatusType99582S                              `xml:"bookingIndicator"`
}

// DeliveryAndCollection ...
type DeliveryAndCollection struct {
	// XMLName                   xml.Name                  `xml:"deliveryAndCollection"`
	AddressDeliveryCollection *AddressTypeU136710S      `xml:"addressDeliveryCollection"`
	PhoneNumber               *PhoneAndEmailAddressType `xml:"phoneNumber"`
}

// RateCodeGroup ...
type RateCodeGroup struct {
	// XMLName        xml.Name                        `xml:"rateCodeGroup"`
	RateCodeInfo   *FareQualifierDetailsTypeI      `xml:"rateCodeInfo"`
	AdditionalInfo *FreeTextInformationType136708S `xml:"additionalInfo"`
}

// ErrorWarning ...
type ErrorWarning struct {
	// XMLName          xml.Name                         `xml:"errorWarning"`
	ApplicationError *ApplicationErrorInformationType `xml:"applicationError"`
	ErrorFreeText    *FreeTextInformationType136708S  `xml:"errorFreeText"`
}

// SurchargePeriods ...
type SurchargePeriods struct {
	// XMLName               xml.Name                       `xml:"surchargePeriods"`
	Period                *RangeDetailsTypeI             `xml:"period"`
	SurchargePeriodTariff *TariffInformationTypeI136719S `xml:"surchargePeriodTariff"`
	MaximumUnitQualifier  *MeasurementsBatchTypeU        `xml:"maximumUnitQualifier"`
}

// TaxCovSurchargeGroup ...
type TaxCovSurchargeGroup struct {
	// XMLName                  xml.Name                        `xml:"taxCovSurchargeGroup"`
	TaxSurchargeCoverageInfo *TariffInformationTypeI136714S  `xml:"taxSurchargeCoverageInfo"`
	AdditionalInfo           *FreeTextInformationType136708S `xml:"additionalInfo"`
	SurchargePeriods         []*SurchargePeriods             `xml:"surchargePeriods"`
}

// OtherRulesGroup ...
type OtherRulesGroup struct {
	// XMLName      xml.Name                                  `xml:"otherRulesGroup"`
	OtherRules   *RuleInformationTypeU136720S              `xml:"otherRules"`
	DateTimeInfo []*StructuredPeriodInformationType207595S `xml:"dateTimeInfo"`
}

// PickupDropoffLocation ...
type PickupDropoffLocation struct {
	// XMLName      xml.Name                                  `xml:"pickupDropoffLocation"`
	LocationInfo *PlaceLocationIdentificationTypeU136722S  `xml:"locationInfo"`
	Address      *AddressTypeU136721S                      `xml:"address"`
	OpeningHours []*StructuredPeriodInformationType136724S `xml:"openingHours"`
	Phone        []*PhoneAndEmailAddressType136723S        `xml:"phone"`
}

// RangePeriod ...
type RangePeriod struct {
	// XMLName              xml.Name                `xml:"rangePeriod"`
	AgePeriod            *RangeDetailsTypeI      `xml:"agePeriod"`
	MaximumUnitQualifier *MeasurementsBatchTypeU `xml:"maximumUnitQualifier"`
}

// SpecialEquipmentDetails ...
type SpecialEquipmentDetails struct {
	// XMLName                xml.Name                         `xml:"specialEquipmentDetails"`
	Dummy2                 *DummySegmentTypeI               `xml:"dummy2"`
	RangePeriod            []*RangePeriod                   `xml:"rangePeriod"`
	AdditionalInfo         *FreeTextInformationType136715S  `xml:"additionalInfo"`
	SpecialEquipmentTariff []*TariffInformationTypeI136714S `xml:"specialEquipmentTariff"`
}

// RulesPoliciesGroup ...
type RulesPoliciesGroup struct {
	// XMLName                 xml.Name                        `xml:"rulesPoliciesGroup"`
	Dummy1                  *DummySegmentTypeI              `xml:"dummy1"`
	SourceLevel             *SelectionDetailsTypeI          `xml:"sourceLevel"`
	Remarks                 *FreeTextInformationType136708S `xml:"remarks"`
	TaxCovSurchargeGroup    []*TaxCovSurchargeGroup         `xml:"taxCovSurchargeGroup"`
	OtherRulesGroup         []*OtherRulesGroup              `xml:"otherRulesGroup"`
	PickupDropoffLocation   []*PickupDropoffLocation        `xml:"pickupDropoffLocation"`
	SpecialEquipmentDetails []*SpecialEquipmentDetails      `xml:"specialEquipmentDetails"`
}

// TypicalCarData ...
type TypicalCarData struct {
	// XMLName                 xml.Name                                     `xml:"typicalCarData"`
	VehicleInformation      *VehicleInformationType                      `xml:"vehicleInformation"`
	AdditionalInfo          *FreeTextInformationType136708S              `xml:"additionalInfo"`
	VoucherPrintAck         *ReferenceInformationTypeI136704S            `xml:"voucherPrintAck"`
	CompanyIdentification   *CompanyInformationType                      `xml:"companyIdentification"`
	LocationInfo            []*PlaceLocationIdentificationTypeU          `xml:"locationInfo"`
	DeliveryAndCollection   []*DeliveryAndCollection                     `xml:"deliveryAndCollection"`
	PickupDropoffTimes      *StructuredPeriodInformationType207595S      `xml:"pickupDropoffTimes"`
	CancelOrConfirmNbr      []*ReservationControlInformationTypeI196503S `xml:"cancelOrConfirmNbr"`
	RateCodeGroup           *RateCodeGroup                               `xml:"rateCodeGroup"`
	FFlyerNbr               *FrequentTravellerIdentificationCodeType     `xml:"fFlyerNbr"`
	CustomerInfo            *ConsumerReferenceInformationTypeI           `xml:"customerInfo"`
	RateInfo                []*TariffInformationTypeI136706S             `xml:"rateInfo"`
	ErrorWarning            *ErrorWarning                                `xml:"errorWarning"`
	RulesPoliciesGroup      *RulesPoliciesGroup                          `xml:"rulesPoliciesGroup"`
	Payment                 *FormOfPaymentTypeI                          `xml:"payment"`
	BillingData             *BillableInformationTypeU                    `xml:"billingData"`
	BookingSource           *AdditionalBusinessSourceInformationType     `xml:"bookingSource"`
	InclusiveTour           *TourInformationTypeI                        `xml:"inclusiveTour"`
	MarketingInfo           []*InteractiveFreeTextTypeI136698S           `xml:"marketingInfo"`
	SupleInfo               []*MiscellaneousRemarksType136700S           `xml:"supleInfo"`
	EstimatedDistance       []*QuantityTypeI                             `xml:"estimatedDistance"`
	AgentInformation        *NameTypeU136701S                            `xml:"agentInformation"`
	TrackingOpt             *AgreementIdentificationTypeU                `xml:"trackingOpt"`
	ElectronicVoucherNumber *TicketNumberTypeI                           `xml:"electronicVoucherNumber"`
	CustomerEmail           *CommunicationContactTypeU                   `xml:"customerEmail"`
	Attribute               *AttributeType                               `xml:"attribute"`
}

// ItineraryInfo ...
type ItineraryInfo struct {
	// XMLName                    xml.Name                                   `xml:"itineraryInfo"`
	ElementManagementItinerary *ElementManagementSegmentType              `xml:"elementManagementItinerary"`
	TravelProduct              *TravelProductInformationTypeI193100S      `xml:"travelProduct"`
	ItineraryMessageAction     *MessageActionDetailsType                  `xml:"itineraryMessageAction"`
	ItineraryReservationInfo   *ReservationControlInformationTypeI196503S `xml:"itineraryReservationInfo"`
	RelatedProduct             *RelatedProductInformationTypeI            `xml:"relatedProduct"`
	ElementsIndicators         *StatusType                                `xml:"elementsIndicators"`
	ReasonForIssuanceCode      *PricingOrTicketingSubsequentType          `xml:"reasonForIssuanceCode"`
	FlightDetail               *AdditionalProductDetailsTypeI             `xml:"flightDetail"`
	CabinDetails               *CabinDetailsType                          `xml:"cabinDetails"`
	SelectionDetails           *SelectionDetailsTypeI201122S              `xml:"selectionDetails"`
	SegmentAttribute           *CodedAttributeType213992S                 `xml:"segmentAttribute"`
	CarbonDioxydeInfo          *CarbonDioxydeInfo                         `xml:"carbonDioxydeInfo"`
	ItineraryfreeFormText      []*InteractiveFreeTextType                 `xml:"itineraryfreeFormText"`
	ItineraryFreetext          *LongFreeTextType                          `xml:"itineraryFreetext"`
	DistributionMethod         *MethodType                                `xml:"distributionMethod"`
	HotelProduct               *HotelProductInformationType               `xml:"hotelProduct"`
	RateInformations           *RateInformationType                       `xml:"rateInformations"`
	GeneralOption              []*GeneralOptionType                       `xml:"generalOption"`
	Country                    *CountryCodeListType                       `xml:"country"`
	TaxInformation             []*TaxTypeI                                `xml:"taxInformation"`
	CustomerTransactionData    *CustomerTransactionDataType               `xml:"customerTransactionData"`
	YieldGroup                 *YieldGroup                                `xml:"yieldGroup"`
	LegInfo                    []*LegInfo                                 `xml:"legInfo"`
	FlixInfo                   []*FLIXType                                `xml:"flixInfo"`
	DateTimeDetails            *DateAndTimeInformationType                `xml:"dateTimeDetails"`
	LccTypicalData             *LccTypicalData                            `xml:"lccTypicalData"`
	InsuranceInformation       []*InsuranceInformation                    `xml:"insuranceInformation"`
	InsuranceDetails           *InsuranceBusinessDataType                 `xml:"insuranceDetails"`
	HotelReservationInfo       *HotelReservationInfo                      `xml:"hotelReservationInfo"`
	TypicalCarData             *TypicalCarData                            `xml:"typicalCarData"`
	TypicalCruiseData          *CruiseBusinessDataType                    `xml:"typicalCruiseData"`
	RailInfo                   *TrainInformationType                      `xml:"railInfo"`
	MarkerRailTour             *DummySegmentTypeI                         `xml:"markerRailTour"`
	TourInfo                   *TourInformationType                       `xml:"tourInfo"`
	FerryLegInformation        *FerryBookingDescriptionType               `xml:"ferryLegInformation"`
	ErrorInfo                  *ErrorGroupType223552G                     `xml:"errorInfo"`
	ReferenceForSegment        *ReferenceInfoType                         `xml:"referenceForSegment"`
}

// OriginDestinationDetails ...
type OriginDestinationDetails struct {
	// XMLName           xml.Name                               `xml:"originDestinationDetails"`
	OriginDestination *OriginAndDestinationDetailsTypeI3061S `xml:"originDestination"`
	ItineraryInfo     []*ItineraryInfo                       `xml:"itineraryInfo"`
}

// SeatPaxInfo ...
type SeatPaxInfo struct {
	// XMLName          xml.Name                    `xml:"seatPaxInfo"`
	SeatPaxDetails   *SeatRequestParametersTypeI `xml:"seatPaxDetails"`
	SeatPaxIndicator *StatusTypeI217964S         `xml:"seatPaxIndicator"`
	CrossRef         *ReferenceInfoType227788S   `xml:"crossRef"`
}

// CityPair ...
type CityPair struct {
	// XMLName     xml.Name                                `xml:"cityPair"`
	DepLocation *PlaceLocationIdentificationTypeU35293S `xml:"depLocation"`
	ArrLocation *PlaceLocationIdentificationTypeU35293S `xml:"arrLocation"`
}

// RailSeatDetails ...
type RailSeatDetails struct {
	// XMLName                      xml.Name                          `xml:"railSeatDetails"`
	RailSeatReferenceInformation *RailSeatReferenceInformationType `xml:"railSeatReferenceInformation"`
	RailSeatDenomination         *FreeTextInformationType29860S    `xml:"railSeatDenomination"`
}

// ReferencedRecord ...
type ReferencedRecord struct {
	// XMLName                   xml.Name                                   `xml:"referencedRecord"`
	ReferencedReservationInfo *ReservationControlInformationTypeI196503S `xml:"referencedReservationInfo"`
	SecurityInformation       *ReservationSecurityInformationType        `xml:"securityInformation"`
}

// GroupOfFareElements ...
type GroupOfFareElements struct {
	// XMLName         xml.Name                      `xml:"groupOfFareElements"`
	SequenceNumber  *SequenceDetailsTypeU         `xml:"sequenceNumber"`
	FareElementData *FreeTextInformationType9865S `xml:"fareElementData"`
}

// McoRecord ...
type McoRecord struct {
	// XMLName             xml.Name                      `xml:"mcoRecord"`
	McoType             *MiscellaneousChargeOrderType `xml:"mcoType"`
	McoInformation      *FreeTextInformationType9865S `xml:"mcoInformation"`
	GroupOfFareElements []*GroupOfFareElements        `xml:"groupOfFareElements"`
}

// DataElementsIndiv ...
type DataElementsIndiv struct {
	// XMLName                       xml.Name                                 `xml:"dataElementsIndiv"`
	ElementManagementData         *ElementManagementSegmentType226873S     `xml:"elementManagementData"`
	PnrSecurity                   *IndividualPnrSecurityInformationType    `xml:"pnrSecurity"`
	Accounting                    *AccountingInformationElementType        `xml:"accounting"`
	MiscellaneousRemarks          *MiscellaneousRemarksType211S            `xml:"miscellaneousRemarks"`
	ExtendedRemark                *ExtendedRemarkType                      `xml:"extendedRemark"`
	ServiceRequest                *SpecialRequirementsDetailsTypeI         `xml:"serviceRequest"`
	SeatPaxInfo                   []*SeatPaxInfo                           `xml:"seatPaxInfo"`
	ReasonForIssuanceCode         *PricingOrTicketingSubsequentType195222S `xml:"reasonForIssuanceCode"`
	RailSeatPreferences           *RailSeatPreferencesType                 `xml:"railSeatPreferences"`
	CityPair                      *CityPair                                `xml:"cityPair"`
	RailSeatDetails               []*RailSeatDetails                       `xml:"railSeatDetails"`
	DateAndTimeInformation        *DateAndTimeInformationTypeI             `xml:"dateAndTimeInformation"`
	FrequentFlyerInformationGroup *FrequentFlyerInformationGroupType       `xml:"frequentFlyerInformationGroup"`
	TicketElement                 *TicketElementType                       `xml:"ticketElement"`
	ReferencedRecord              *ReferencedRecord                        `xml:"referencedRecord"`
	OptionElement                 *OptionElementType                       `xml:"optionElement"`
	OtherDataFreetext             []*LongFreeTextType                      `xml:"otherDataFreetext"`
	StructuredAddress             *StructuredAddressType                   `xml:"structuredAddress"`
	MonetaryInformation           []*MonetaryInformationTypeI1689S         `xml:"monetaryInformation"`
	ElementErrorInformation       *ErrorGroupType223552G                   `xml:"elementErrorInformation"`
	McoRecord                     *McoRecord                               `xml:"mcoRecord"`
	TotalPrice                    *TotalPriceType                          `xml:"totalPrice"`
	ElementsIndicators            []*StatusTypeI217964S                    `xml:"elementsIndicators"`
	EltPosDetails                 *POSGroupType150634G                     `xml:"eltPosDetails"`
	AccessLevel                   []*ExtendedOwnershipSecurityDetailsType  `xml:"accessLevel"`
	ReferenceForDataElement       *ReferenceInfoType227787S                `xml:"referenceForDataElement"`
	StructuredFop                 []*FOPRepresentationType                 `xml:"structuredFop"`
	SsrPackageInformation         []*SSRPackInformation                    `xml:"ssrPackageInformation"`
}

// DataElementsMaster ...
type DataElementsMaster struct {
	// XMLName           xml.Name             `xml:"dataElementsMaster"`
	Marker2           *DummySegmentTypeI   `xml:"marker2"`
	DataElementsIndiv []*DataElementsIndiv `xml:"dataElementsIndiv"`
}

// TstData ...
type TstData struct {
	// XMLName               xml.Name                      `xml:"tstData"`
	TstGeneralInformation *TstGeneralInformationType    `xml:"tstGeneralInformation"`
	TstFreetext           []*LongFreeTextType           `xml:"tstFreetext"`
	FareBasisInfo         *FareBasisCodesLineType       `xml:"fareBasisInfo"`
	FareData              *FareDataType                 `xml:"fareData"`
	SegmentAssociation    *SelectionDetailsTypeI201122S `xml:"segmentAssociation"`
	ReferenceForTstData   *ReferenceInfoType            `xml:"referenceForTstData"`
}

// PricingRecordGroup ...
type PricingRecordGroup struct {
	// XMLName                       xml.Name                     `xml:"pricingRecordGroup"`
	PricingRecordData             *PricingTicketingDetailsType `xml:"pricingRecordData"`
	ProductPricingQuotationRecord []*PPQRdataType              `xml:"productPricingQuotationRecord"`
}

// DcsSegmentInfo ...
type DcsSegmentInfo struct {
	// XMLName   xml.Name                       `xml:"dcsSegmentInfo"`
	Booking   *TravelProductInformationTypeI `xml:"booking"`
	PaxType   *ReferenceInformationTypeI     `xml:"paxType"`
	TypeOfCOP *CodedAttributeType127282S     `xml:"typeOfCOP"`
}

// SegmentSection ...
type SegmentSection struct {
	// XMLName                       xml.Name                             `xml:"segmentSection"`
	ElementManagementStructData   *ElementManagementSegmentType127983S `xml:"elementManagementStructData"`
	ReferenceForStructDataElement *ReferenceInfoType                   `xml:"referenceForStructDataElement"`
	DcsSegmentInfo                *DcsSegmentInfo                      `xml:"dcsSegmentInfo"`
}

// AccregReason ...
type AccregReason struct {
	// XMLName             xml.Name                   `xml:"accregReason"`
	Reasons             *CodedAttributeType127279S `xml:"reasons"`
	DeliveryInformation *InteractiveFreeTextTypeI  `xml:"deliveryInformation"`
}

// AcceptanceChannel ...
type AcceptanceChannel struct {
	// XMLName          xml.Name                       `xml:"acceptanceChannel"`
	AcceptanceOrigin *UserIdentificationType127265S `xml:"acceptanceOrigin"`
	ApplicationType  *ApplicationType               `xml:"applicationType"`
}

// DcsLegInfo ...
type DcsLegInfo struct {
	// XMLName           xml.Name                          `xml:"dcsLegInfo"`
	LegPosition       *TravelItineraryInformationTypeI  `xml:"legPosition"`
	Leg               *OriginAndDestinationDetailsTypeI `xml:"leg"`
	PaxType           *ReferenceInformationTypeI        `xml:"paxType"`
	SeatDelivery      *SpecialRequirementsDetailsType   `xml:"seatDelivery"`
	PaxStatus         *StatusTypeI127261S               `xml:"paxStatus"`
	AccregReason      []*AccregReason                   `xml:"accregReason"`
	RegradeCabin      *SegmentCabinIdentificationType   `xml:"regradeCabin"`
	AcceptanceChannel *AcceptanceChannel                `xml:"acceptanceChannel"`
	CompensationData  *CompensationType                 `xml:"compensationData"`
}

// LegSection ...
type LegSection struct {
	// XMLName                       xml.Name                             `xml:"legSection"`
	ElementManagementStructData   *ElementManagementSegmentType127983S `xml:"elementManagementStructData"`
	ReferenceForStructDataElement *ReferenceInfoType                   `xml:"referenceForStructDataElement"`
	DcsLegInfo                    *DcsLegInfo                          `xml:"dcsLegInfo"`
}

// DcsData ...
type DcsData struct {
	// XMLName        xml.Name                     `xml:"dcsData"`
	MarkerPax      *PassengerFlightDetailsTypeI `xml:"markerPax"`
	MarkerSegment  *PassengerFlightDetailsTypeI `xml:"markerSegment"`
	SegmentSection []*SegmentSection            `xml:"segmentSection"`
	MarkerLeg      *PassengerFlightDetailsTypeI `xml:"markerLeg"`
	LegSection     []*LegSection                `xml:"legSection"`
}

// PNRReply ...
type PNRReply struct {
	// XMLName                  xml.Name                                   `xml:"PNR_Reply"`
	GeneralErrorInfo         []*ErrorGroupType212789G                   `xml:"generalErrorInfo"`
	PnrHeader                []*PnrHeader                               `xml:"pnrHeader"`
	SecurityInformation      *ReservationSecurityInformationType204487S `xml:"securityInformation"`
	QueueInformations        *QueueType                                 `xml:"queueInformations"`
	NumberOfUnits            *NumberOfUnitsTypeI                        `xml:"numberOfUnits"`
	PnrType                  *CodedAttributeType                        `xml:"pnrType"`
	FreetextData             []*LongFreeTextType                        `xml:"freetextData"`
	PnrHeaderTag             *StatusType227567S                         `xml:"pnrHeaderTag"`
	FreeFormText             []*InteractiveFreeTextTypeI136698S         `xml:"freeFormText"`
	HistoryData              []*PnrHistoryDataType                      `xml:"historyData"`
	SbrPOSDetails            *POSGroupType                              `xml:"sbrPOSDetails"`
	SbrCreationPosDetails    *POSGroupType                              `xml:"sbrCreationPosDetails"`
	SbrUpdatorPosDetails     *POSGroupType                              `xml:"sbrUpdatorPosDetails"`
	TechnicalData            *TechnicalData                             `xml:"technicalData"`
	TravellerInfo            []*TravellerInfo                           `xml:"travellerInfo"`
	OriginDestinationDetails *OriginDestinationDetails                  `xml:"originDestinationDetails"`
	SegmentGroupingInfo      []*SegmentGroupingInformationType          `xml:"segmentGroupingInfo"`
	DataElementsMaster       *DataElementsMaster                        `xml:"dataElementsMaster"`
	TstData                  []*TstData                                 `xml:"tstData"`
	PricingRecordGroup       *PricingRecordGroup                        `xml:"pricingRecordGroup"`
	DcsData                  *DcsData                                   `xml:"dcsData"`
}

// ATCdataType is Other ATC Fares:  - Base Fare Balance (BEQ) - New Base Fare (BNF) - Old Base Fare (IBA) - Penalty (DPI) - Grand Total (GT) - Residual Value (RES) - TST Additional Collection (TST) - Tax Balance (BTA) - Ticket Difference (BTO)
type ATCdataType struct {
	AtcTotalAdditionalCollection *MonetaryInformationTypeI79012S   `xml:"atcTotalAdditionalCollection"`
	OtherAtcFares                []*MonetaryInformationTypeI79012S `xml:"otherAtcFares"`
}

// AccommodationAllocationInformationDetailsTypeU is Accommodation (room/compartment) code
type AccommodationAllocationInformationDetailsTypeU struct {
	ReferenceId *string `xml:"referenceId"`
	Code        *string `xml:"code"`
}

// AccommodationAllocationInformationTypeU is Allocated accommodation
type AccommodationAllocationInformationTypeU struct {
	AccommAllocation *AccommodationAllocationInformationDetailsTypeU `xml:"accommAllocation"`
}

// AccountingElementType is Client Reference Number
type AccountingElementType struct {
	Number          *string `xml:"number"`
	CostNumber      *string `xml:"costNumber"`
	CompanyNumber   *string `xml:"companyNumber"`
	ClientReference *string `xml:"clientReference"`
	GSTTaxDetails   *string `xml:"gSTTaxDetails"`
}

// AccountingInformationElementType is Number of units qualifier
type AccountingInformationElementType struct {
	Account              *AccountingElementType `xml:"account"`
	AccountNumberOfUnits *string                `xml:"accountNumberOfUnits"`
}

// ActionDetailsTypeI is Contains the details about the product knowledge
type ActionDetailsTypeI struct {
	NumberOfItemsDetails *ProcessingInformationTypeI `xml:"numberOfItemsDetails"`
}

// ActionIdentificationType is provides type of required action, coded.
type ActionIdentificationType struct {
	ActionRequestCode *string `xml:"actionRequestCode"`
}

// AdditionalBusinessSourceInformationType is ORIGINATOR DETAILS
type AdditionalBusinessSourceInformationType struct {
	OriginatorDetails *OriginatorIdentificationDetailsTypeI198179C `xml:"originatorDetails"`
}

// AdditionalProductDetailsTypeI is To convey additional information concerning an airline flight.
type AdditionalProductDetailsTypeI struct {
	ProductDetails       *AdditionalProductTypeI         `xml:"productDetails"`
	DepartureInformation *StationInformationTypeI        `xml:"departureInformation"`
	ArrivalStationInfo   *StationInformationTypeI119771C `xml:"arrivalStationInfo"`
	MileageTimeDetails   *MileageTimeDetailsTypeI        `xml:"mileageTimeDetails"`
	TimeDetail           *TravellerTimeDetailsTypeI      `xml:"timeDetail"`
	Facilities           []*ProductFacilitiesTypeI       `xml:"facilities"`
}

// AdditionalProductDetailsTypeU is The general product description
type AdditionalProductDetailsTypeU struct {
	ProductArea    *string                      `xml:"productArea"`
	ProductDetails *ProductDataInformationTypeU `xml:"productDetails"`
}

// AdditionalProductTypeI is Additional details describing a specific means of transport.
type AdditionalProductTypeI struct {
	Equipment  *string `xml:"equipment"`
	NumOfStops *int    `xml:"numOfStops"`
	Duration   *string `xml:"duration"`
	WeekDay    *int    `xml:"weekDay"`
}

// AdditionalTransportDetailsTypeU is Terminal Information
type AdditionalTransportDetailsTypeU struct {
	TerminalInformation []*TerminalInformationTypeU `xml:"terminalInformation"`
}

// AddressDetailsTypeU is Address Text. Any of the following address lines may start with a tag: Door number- Street- ExternalNumber- InternalNumber- County- Neighbourhood- State-
type AddressDetailsTypeU struct {
	Format *string `xml:"format"`
	Line1  *string `xml:"line1"`
	Line2  *string `xml:"line2"`
	Line3  *string `xml:"line3"`
	Line4  *string `xml:"line4"`
	Line5  *string `xml:"line5"`
	Line6  *string `xml:"line6"`
}

// AddressDetailsTypeU17987C is PO Box
type AddressDetailsTypeU17987C struct {
	// XMLName xml.Name `xml:"AddressDetailsTypeU_17987C"`
	Format *string `xml:"format"`
	Line1  *string `xml:"line1"`
	Line2  *string `xml:"line2"`
	Line4  *string `xml:"line4"`
}

// AddressDetailsTypeU198210C is Address Field in free flow text
type AddressDetailsTypeU198210C struct {
	// XMLName xml.Name `xml:"AddressDetailsTypeU_198210C"`
	Format *string `xml:"format"`
	Line1  *string `xml:"line1"`
}

// AddressDetailsTypeU198226C is address line 2
type AddressDetailsTypeU198226C struct {
	// XMLName xml.Name `xml:"AddressDetailsTypeU_198226C"`
	Format *string `xml:"format"`
	Line1  *string `xml:"line1"`
	Line2  *string `xml:"line2"`
}

// AddressTypeU is to specify the countryname
type AddressTypeU struct {
	AddressDetails  *AddressDetailsTypeU17987C    `xml:"addressDetails"`
	City            *string                       `xml:"city"`
	ZipCode         *string                       `xml:"zipCode"`
	RegionDetails   *CountrySubEntityDetailsTypeU `xml:"regionDetails"`
	LocationDetails *LocationIdentificationTypeU  `xml:"locationDetails"`
}

// AddressTypeU136710S is Format 2 - Site Delivery/Collection
type AddressTypeU136710S struct {
	// XMLName             xml.Name                             `xml:"AddressTypeU_136710S"`
	AddressUsageDetails *AddressUsageTypeU                   `xml:"addressUsageDetails"`
	AddressDetails      *AddressDetailsTypeU198210C          `xml:"addressDetails"`
	City                *string                              `xml:"city"`
	ZipCode             *string                              `xml:"zipCode"`
	CountryCode         *string                              `xml:"countryCode"`
	RegionDetails       *CountrySubEntityDetailsTypeU198213C `xml:"regionDetails"`
	LocationDetails     *LocationIdentificationTypeU198211C  `xml:"locationDetails"`
}

// AddressTypeU136721S is To convey a sub-entity within a country : region, states..
type AddressTypeU136721S struct {
	// XMLName        xml.Name                             `xml:"AddressTypeU_136721S"`
	AddressDetails *AddressDetailsTypeU198226C          `xml:"addressDetails"`
	City           *string                              `xml:"city"`
	ZipCode        *string                              `xml:"zipCode"`
	CountryCode    *string                              `xml:"countryCode"`
	RegionDetails  *CountrySubEntityDetailsTypeU198229C `xml:"regionDetails"`
}

// AddressType is Country code. ISO 3166 code for the country
type AddressType struct {
	AddressDetails *AddressDetailsTypeU `xml:"addressDetails"`
	City           *string              `xml:"city"`
	ZipCode        *string              `xml:"zipCode"`
	CountryCode    *string              `xml:"countryCode"`
}

// AddressUsageTypeU is Address Type: - DEL for Delivery - COL for Collection
type AddressUsageTypeU struct {
	Purpose *string `xml:"purpose"`
}

// AgreementIdentificationTypeU is Agreement identification
type AgreementIdentificationTypeU struct {
	AgreementDetails *AgreementTypeIdentificationTypeU `xml:"agreementDetails"`
}

// AgreementTypeIdentificationTypeU is Agreement description
type AgreementTypeIdentificationTypeU struct {
	Code        *string `xml:"code"`
	Description *string `xml:"description"`
}

// ApplicationErrorDetailType is Identification of a code list.
type ApplicationErrorDetailType struct {
	ErrorCode     *string `xml:"errorCode"`
	ErrorCategory *string `xml:"errorCategory"`
}

// ApplicationErrorDetailType198235C is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType198235C struct {
	// XMLName        xml.Name `xml:"ApplicationErrorDetailType_198235C"`
	ErrorCode      *string `xml:"errorCode"`
	ErrorCategory  *string `xml:"errorCategory"`
	ErrorCodeOwner *string `xml:"errorCodeOwner"`
}

// ApplicationErrorDetailType260187C is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType260187C struct {
	// XMLName        xml.Name `xml:"ApplicationErrorDetailType_260187C"`
	ErrorCode      *string `xml:"errorCode"`
	ErrorCategory  *string `xml:"errorCategory"`
	ErrorCodeOwner *string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType198235C `xml:"errorDetails"`
}

// ApplicationErrorInformationType185486S is Application error details.
type ApplicationErrorInformationType185486S struct {
	// XMLName      xml.Name                           `xml:"ApplicationErrorInformationType_185486S"`
	ErrorDetails *ApplicationErrorDetailType260187C `xml:"errorDetails"`
}

// ApplicationErrorInformationType94519S is Application error details.
type ApplicationErrorInformationType94519S struct {
	// XMLName      xml.Name                    `xml:"ApplicationErrorInformationType_94519S"`
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// ApplicationIdentificationType is Item Version Number
type ApplicationIdentificationType struct {
	InternalId    *string `xml:"internalId"`
	VersionNumber *string `xml:"versionNumber"`
}

// ApplicationType is provides information on application identification
type ApplicationType struct {
	ApplicationDetails *ApplicationIdentificationType `xml:"applicationDetails"`
}

// AssociatedChargesInformationTypeI is This data element is used to convey the voucher text (in case of voucher).
type AssociatedChargesInformationTypeI struct {
	Type          *string `xml:"type"`
	Amount        float64 `xml:"amount"`
	Description   *string `xml:"description"`
	NumberInParty *int    `xml:"numberInParty"`
	Currency      *string `xml:"currency"`
	Comment       *string `xml:"comment"`
}

// AssociatedChargesInformationTypeI198205C is Unstructured RG,RG and RQ rates.
type AssociatedChargesInformationTypeI198205C struct {
	// XMLName       xml.Name `xml:"AssociatedChargesInformationTypeI_198205C"`
	Type          *string `xml:"type"`
	Amount        float64 `xml:"amount"`
	Description   *string `xml:"description"`
	NumberInParty *int    `xml:"numberInParty"`
	PeriodType    *string `xml:"periodType"`
	Currency      *string `xml:"currency"`
	Comment       *string `xml:"comment"`
}

// AssociatedChargesInformationTypeI198218C is Policy name
type AssociatedChargesInformationTypeI198218C struct {
	// XMLName       xml.Name `xml:"AssociatedChargesInformationTypeI_198218C"`
	Type          *string `xml:"type"`
	Amount        float64 `xml:"amount"`
	Description   *string `xml:"description"`
	NumberInParty *int    `xml:"numberInParty"`
	PeriodType    *string `xml:"periodType"`
	Currency      *string `xml:"currency"`
	Comment       *string `xml:"comment"`
}

// AssociatedChargesInformationTypeI39535C is foreign currency.
type AssociatedChargesInformationTypeI39535C struct {
	// XMLName     xml.Name `xml:"AssociatedChargesInformationTypeI_39535C"`
	Type        *string `xml:"type"`
	Amount      float64 `xml:"amount"`
	Description *string `xml:"description"`
	Currency    *string `xml:"currency"`
}

// AssociatedChargesInformationTypeU is Commission's percentage
type AssociatedChargesInformationTypeU struct {
	ChargeUnitCode *string `xml:"chargeUnitCode"`
	Amount         float64 `xml:"amount"`
	Percentage     float64 `xml:"percentage"`
}

// AttributeInformationTypeU is The RFIC or RFISC description.
type AttributeInformationTypeU struct {
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// AttributeInformationTypeU142127C is value of the data
type AttributeInformationTypeU142127C struct {
	// XMLName              xml.Name `xml:"AttributeInformationTypeU_142127C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// AttributeInformationTypeU198185C is Not Used
type AttributeInformationTypeU198185C struct {
	// XMLName              xml.Name `xml:"AttributeInformationTypeU_198185C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// AttributeInformationTypeU36633C is This element is used to convey the service code of the service group of the ferry booking. The list of possible values depends of the Ferry provider.
type AttributeInformationTypeU36633C struct {
	// XMLName       xml.Name `xml:"AttributeInformationTypeU_36633C"`
	AttributeType *string `xml:"attributeType"`
}

// AttributeInformationTypeU45068C is The list of possible values is: ADT Adult CHD Child FDC Diplomatic corps FEU Disabled FFM Family FFR Free FIR Inter rail FJO Journalist FSL School pupil INF Infant MIL Military NAT Nato official REC Child resident RES Resident SRC Senior citizen STU Student YTH Young person
type AttributeInformationTypeU45068C struct {
	// XMLName       xml.Name `xml:"AttributeInformationTypeU_45068C"`
	AttributeType *string `xml:"attributeType"`
}

// AttributeTypeU is Service details.
type AttributeTypeU struct {
	AttributeFunction *string                          `xml:"attributeFunction"`
	AttributeDetails  *AttributeInformationTypeU36633C `xml:"attributeDetails"`
}

// AttributeTypeU24552S is provides details for the Attribute
type AttributeTypeU24552S struct {
	// XMLName           xml.Name                         `xml:"AttributeTypeU_24552S"`
	AttributeFunction *string                          `xml:"attributeFunction"`
	AttributeDetails  *AttributeInformationTypeU45068C `xml:"attributeDetails"`
}

// AttributeType is Details for the attribute type. LEI:Y for leisure booking CLP:Y for clip booking
type AttributeType struct {
	CriteriaSetType *string                             `xml:"criteriaSetType"`
	CriteriaDetails []*AttributeInformationTypeU198185C `xml:"criteriaDetails"`
}

// AttributeType195221S is Details for the message criteria (name, value).
type AttributeType195221S struct {
	// XMLName         xml.Name                            `xml:"AttributeType_195221S"`
	CriteriaDetails []*AttributeInformationTypeU142127C `xml:"criteriaDetails"`
	// DummyNET        *DummyNET                           `xml:"Dummy.NET"`
}

// AttributeType79011S ...
type AttributeType79011S struct {
	// XMLName         xml.Name                   `xml:"AttributeType_79011S"`
	CriteriaSetType *string                    `xml:"criteriaSetType"`
	CriteriaDetails *AttributeInformationTypeU `xml:"criteriaDetails"`
}

// AttributeType94514S ...
type AttributeType94514S struct {
	// XMLName         xml.Name                          `xml:"AttributeType_94514S"`
	CriteriaSetType *string                           `xml:"criteriaSetType"`
	CriteriaDetails *AttributeInformationTypeU142127C `xml:"criteriaDetails"`
}

// AttributeType94553S ...
type AttributeType94553S struct {
	// XMLName         xml.Name                          `xml:"AttributeType_94553S"`
	CriteriaSetType *string                           `xml:"criteriaSetType"`
	CriteriaDetails *AttributeInformationTypeU142127C `xml:"criteriaDetails"`
}

// AttributeType94576S ...
type AttributeType94576S struct {
	// XMLName         xml.Name                            `xml:"AttributeType_94576S"`
	CriteriaSetType *string                             `xml:"criteriaSetType"`
	CriteriaDetails []*AttributeInformationTypeU142127C `xml:"criteriaDetails"`
}

// AuthenticationDataType ...
type AuthenticationDataType struct {
	Veres                   *string `xml:"veres"`
	Pares                   *string `xml:"pares"`
	CreditCardCompany       *string `xml:"creditCardCompany"`
	AuthenticationIndicator *string `xml:"authenticationIndicator"`
	CaavAlgorithm           *int    `xml:"caavAlgorithm"`
}

// AuthorizationApprovalDataType ...
type AuthorizationApprovalDataType struct {
	ApprovalCode     *string `xml:"approvalCode"`
	SourceOfApproval *string `xml:"sourceOfApproval"`
}

// BaggageDetailsTypeI ...
type BaggageDetailsTypeI struct {
	FreeAllowance *int    `xml:"freeAllowance"`
	QuantityCode  *string `xml:"quantityCode"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// BillableInformationTypeU ...
type BillableInformationTypeU struct {
	BillingInfo []*DiagnosisTypeU `xml:"billingInfo"`
}

// BinaryDataType ...
type BinaryDataType struct {
	DataLength *int    `xml:"dataLength"`
	DataType   *string `xml:"dataType"`
	BinaryData *string `xml:"binaryData"`
}

// BrowserInformationType ...
type BrowserInformationType struct {
	DeviceCategory *int `xml:"deviceCategory"`
}

// CabinClassDesignationType ...
type CabinClassDesignationType struct {
	ClassDesignator *string `xml:"classDesignator"`
}

// CabinDetailsType ...
type CabinDetailsType struct {
	CabinDetails *CabinClassDesignationType `xml:"cabinDetails"`
}

// CardValidityType ...
type CardValidityType struct {
	Type     *string `xml:"type"`
	Form     *string `xml:"form"`
	Amount   float64 `xml:"amount"`
	Currency *string `xml:"currency"`
	FreeText *string `xml:"freeText"`
}

// ChildrenGroupType ...
type ChildrenGroupType struct {
	Age                   *QuantityTypeI65488S            `xml:"age"`
	ReferenceForPassenger *ReferenceInformationType65487S `xml:"referenceForPassenger"`
}

// ClassConfigurationDetailsType ...
type ClassConfigurationDetailsType struct {
	ClassDetails *ClassDetailsType `xml:"classDetails"`
}

// ClassDetailsType ...
type ClassDetailsType struct {
	Code          *string `xml:"code"`
	BookingClass  *string `xml:"bookingClass"`
	NumberOfSeats *int    `xml:"numberOfSeats"`
}

// ClassDetailsType52782C ...
type ClassDetailsType52782C struct {
	// XMLName      xml.Name `xml:"ClassDetailsType_52782C"`
	Code         *string `xml:"code"`
	BookingClass *string `xml:"bookingClass"`
}

// CoachProductInformationType ...
type CoachProductInformationType struct {
	CoachDetails  *ReferencingDetailsTypeI36941C `xml:"coachDetails"`
	EquipmentCode *string                        `xml:"equipmentCode"`
}

// CodedAttributeInformationType ...
type CodedAttributeInformationType struct {
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeInformationType122050C ...
type CodedAttributeInformationType122050C struct {
	// XMLName              xml.Name `xml:"CodedAttributeInformationType_122050C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeInformationType142108C ...
type CodedAttributeInformationType142108C struct {
	// XMLName       xml.Name `xml:"CodedAttributeInformationType_142108C"`
	AttributeType *string `xml:"attributeType"`
}

// CodedAttributeInformationType185753C ...
type CodedAttributeInformationType185753C struct {
	// XMLName              xml.Name `xml:"CodedAttributeInformationType_185753C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeInformationType285636C ...
type CodedAttributeInformationType285636C struct {
	// XMLName              xml.Name `xml:"CodedAttributeInformationType_285636C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeType ...
type CodedAttributeType struct {
	AttributeDetails []*CodedAttributeInformationType142108C `xml:"attributeDetails"`
}

// CodedAttributeType127279S ...
type CodedAttributeType127279S struct {
	// XMLName          xml.Name                              `xml:"CodedAttributeType_127279S"`
	AttributeDetails *CodedAttributeInformationType185753C `xml:"attributeDetails"`
}

// CodedAttributeType127282S ...
type CodedAttributeType127282S struct {
	// XMLName          xml.Name                              `xml:"CodedAttributeType_127282S"`
	AttributeDetails *CodedAttributeInformationType142108C `xml:"attributeDetails"`
}

// CodedAttributeType213992S ...
type CodedAttributeType213992S struct {
	// XMLName          xml.Name                                `xml:"CodedAttributeType_213992S"`
	AttributeDetails []*CodedAttributeInformationType285636C `xml:"attributeDetails"`
}

// CodedAttributeType79010S ...
type CodedAttributeType79010S struct {
	// XMLName          xml.Name                       `xml:"CodedAttributeType_79010S"`
	AttributeDetails *CodedAttributeInformationType `xml:"attributeDetails"`
}

// CodedAttributeType79464S ...
type CodedAttributeType79464S struct {
	// XMLName          xml.Name                                `xml:"CodedAttributeType_79464S"`
	AttributeDetails []*CodedAttributeInformationType122050C `xml:"attributeDetails"`
}

// CodedAttributeType94497S ...
type CodedAttributeType94497S struct {
	// XMLName           xml.Name                         `xml:"CodedAttributeType_94497S"`
	AttributeFunction *string                          `xml:"attributeFunction"`
	AttributeDetails  []*CodedAttributeInformationType `xml:"attributeDetails"`
}

// CodeshareFlightDataTypeI ...
type CodeshareFlightDataTypeI struct {
	Airline            *string `xml:"airline"`
	FlightNumber       *int    `xml:"flightNumber"`
	Inventory          *string `xml:"inventory"`
	SellingClass       *string `xml:"sellingClass"`
	Type               *string `xml:"type"`
	Suffix             *string `xml:"suffix"`
	CascadingIndicator *int    `xml:"cascadingIndicator"`
}

// CommissionDetailsTypeI ...
type CommissionDetailsTypeI struct {
	Type     *string `xml:"type"`
	Amount   float64 `xml:"amount"`
	Currency *string `xml:"currency"`
}

// CommissionDetailsType ...
type CommissionDetailsType struct {
	Type       *string `xml:"type"`
	Amount     float64 `xml:"amount"`
	Currency   *string `xml:"currency"`
	Rate       *int    `xml:"rate"`
	DealNumber *int    `xml:"dealNumber"`
}

// CommissionInformationTypeI ...
type CommissionInformationTypeI struct {
	CommissionDetails *CommissionDetailsTypeI `xml:"commissionDetails"`
	OtherComDetails   *CommissionDetailsTypeI `xml:"otherComDetails"`
}

// CommissionInformationType ...
type CommissionInformationType struct {
	CommissionDetails *CommissionDetailsType `xml:"commissionDetails"`
}

// CommunicationContactDetailsTypeU ...
type CommunicationContactDetailsTypeU struct {
	Email            *string `xml:"email"`
	ContactQualifier *string `xml:"contactQualifier"`
}

// CommunicationContactDetailsType ...
type CommunicationContactDetailsType struct {
	UrlAddress *string `xml:"urlAddress"`
	UrlType    *string `xml:"urlType"`
}

// CommunicationContactTypeU ...
type CommunicationContactTypeU struct {
	Contact *CommunicationContactDetailsTypeU `xml:"contact"`
}

// CommunicationContactType ...
type CommunicationContactType struct {
	Communication *CommunicationContactDetailsType `xml:"communication"`
}

// CompanyIdentificationTypeI ...
type CompanyIdentificationTypeI struct {
	MarketingCompany *string `xml:"marketingCompany"`
}

// CompanyIdentificationTypeI148289C ...
type CompanyIdentificationTypeI148289C struct {
	// XMLName          xml.Name `xml:"CompanyIdentificationTypeI_148289C"`
	OperatingCompany *string `xml:"operatingCompany"`
}

// CompanyIdentificationTypeI222513C ...
type CompanyIdentificationTypeI222513C struct {
	// XMLName      xml.Name `xml:"CompanyIdentificationTypeI_222513C"`
	OtherCompany *string `xml:"otherCompany"`
}

// CompanyIdentificationTypeI2785C ...
type CompanyIdentificationTypeI2785C struct {
	// XMLName              xml.Name `xml:"CompanyIdentificationTypeI_2785C"`
	Identification       *string `xml:"identification"`
	SecondIdentification *string `xml:"secondIdentification"`
	SourceCode           *string `xml:"sourceCode"`
}

// CompanyIdentificationTypeI46335C ...
type CompanyIdentificationTypeI46335C struct {
	// XMLName          xml.Name `xml:"CompanyIdentificationTypeI_46335C"`
	MarketingCompany *string `xml:"marketingCompany"`
}

// CompanyIdentificationTypeI46351C ...
type CompanyIdentificationTypeI46351C struct {
	// XMLName          xml.Name `xml:"CompanyIdentificationTypeI_46351C"`
	OperatingCompany *string `xml:"operatingCompany"`
}

// CompanyIdentificationTypeU ...
type CompanyIdentificationTypeU struct {
	ProviderName *string `xml:"providerName"`
}

// CompanyInformationType ...
type CompanyInformationType struct {
	TravelSector       *string `xml:"travelSector"`
	CompanyCodeContext *string `xml:"companyCodeContext"`
	CompanyCode        *string `xml:"companyCode"`
	CompanyName        *string `xml:"companyName"`
	AccessLevel        *string `xml:"accessLevel"`
}

// CompanyInformationType19450S ...
type CompanyInformationType19450S struct {
	// XMLName            xml.Name `xml:"CompanyInformationType_19450S"`
	CompanyCode        *string `xml:"companyCode"`
	CompanyNumericCode *int    `xml:"companyNumericCode"`
}

// CompanyInformationType20151S ...
type CompanyInformationType20151S struct {
	// XMLName      xml.Name `xml:"CompanyInformationType_20151S"`
	TravelSector *string `xml:"travelSector"`
	CompanyCode  *string `xml:"companyCode"`
	CompanyName  *string `xml:"companyName"`
}

// CompanyInformationType25420S ...
type CompanyInformationType25420S struct {
	// XMLName      xml.Name `xml:"CompanyInformationType_25420S"`
	TravelSector *string `xml:"travelSector"`
	CompanyCode  *string `xml:"companyCode"`
	CompanyName  *string `xml:"companyName"`
}

// CompanyInformationType26258S ...
type CompanyInformationType26258S struct {
	// XMLName            xml.Name `xml:"CompanyInformationType_26258S"`
	TravelSector       *string `xml:"travelSector"`
	CompanyCodeContext *string `xml:"companyCodeContext"`
	CompanyCode        *string `xml:"companyCode"`
	CompanyName        *string `xml:"companyName"`
}

// CompanyInformationType79020S ...
type CompanyInformationType79020S struct {
	// XMLName     xml.Name `xml:"CompanyInformationType_79020S"`
	CompanyCode *string `xml:"companyCode"`
}

// CompanyInformationType83550S ...
type CompanyInformationType83550S struct {
	// XMLName      xml.Name `xml:"CompanyInformationType_83550S"`
	TravelSector *string `xml:"travelSector"`
	CompanyCode  *string `xml:"companyCode"`
}

// CompanyInformationType8953S ...
type CompanyInformationType8953S struct {
	// XMLName            xml.Name `xml:"CompanyInformationType_8953S"`
	TravelSector       *string `xml:"travelSector"`
	CompanyCodeContext *string `xml:"companyCodeContext"`
	CompanyCode        *string `xml:"companyCode"`
	CompanyName        *string `xml:"companyName"`
}

// CompanyInformationType94554S ...
type CompanyInformationType94554S struct {
	// XMLName            xml.Name `xml:"CompanyInformationType_94554S"`
	CompanyCode        *string `xml:"companyCode"`
	CompanyNumericCode *string `xml:"companyNumericCode"`
}

// CompensationType ...
type CompensationType struct {
	CompensationDetails *CardValidityType `xml:"compensationDetails"`
}

// ConsumerReferenceIdentificationTypeI ...
type ConsumerReferenceIdentificationTypeI struct {
	ReferenceQualifier *string `xml:"referenceQualifier"`
	ReferenceNumber    *string `xml:"referenceNumber"`
}

// ConsumerReferenceInformationTypeI ...
type ConsumerReferenceInformationTypeI struct {
	CustomerReferences []*ConsumerReferenceIdentificationTypeI `xml:"customerReferences"`
}

// ContactInformationDetailsTypeU ...
type ContactInformationDetailsTypeU struct {
	PartyQualifier      *string `xml:"partyQualifier"`
	ComAddress          *string `xml:"comAddress"`
	ComChannelQualifier *string `xml:"comChannelQualifier"`
}

// ContactInformationTypeU ...
type ContactInformationTypeU struct {
	ContactInformation []*ContactInformationDetailsTypeU `xml:"contactInformation"`
}

// CountryCodeListType ...
type CountryCodeListType struct {
	DestinationCountryCode []*string `xml:"destinationCountryCode"`
}

// CountrySubEntityDetailsTypeU ...
type CountrySubEntityDetailsTypeU struct {
	Qualifier *string `xml:"qualifier"`
	Name      *string `xml:"name"`
}

// CountrySubEntityDetailsTypeU198213C ...
type CountrySubEntityDetailsTypeU198213C struct {
	// XMLName xml.Name `xml:"CountrySubEntityDetailsTypeU_198213C"`
	Code *string `xml:"code"`
}

// CountrySubEntityDetailsTypeU198229C ...
type CountrySubEntityDetailsTypeU198229C struct {
	// XMLName xml.Name `xml:"CountrySubEntityDetailsTypeU_198229C"`
	Code *string `xml:"code"`
}

// CountrydescriptionType ...
type CountrydescriptionType struct {
	GeographicalZone *string   `xml:"geographicalZone"`
	CountryCode      []*string `xml:"countryCode"`
}

// CouponDetailsType ...
type CouponDetailsType struct {
	ProductId *ReferenceInfoType145406S `xml:"productId"`
}

// CouponInformationDetailsTypeI ...
type CouponInformationDetailsTypeI struct {
	CpnNumber *string `xml:"cpnNumber"`
}

// CouponInformationTypeI ...
type CouponInformationTypeI struct {
	CouponDetails *CouponInformationDetailsTypeI `xml:"couponDetails"`
}

// CreditCardDataGroupType ...
type CreditCardDataGroupType struct {
	CreditCardDetails *CreditCardDataType                `xml:"creditCardDetails"`
	FortknoxIds       []*ReferenceInformationTypeI79009S `xml:"fortknoxIds"`
	CardHolderAddress *AddressType                       `xml:"cardHolderAddress"`
}

// CreditCardDataType ...
type CreditCardDataType struct {
	CcInfo *CreditCardInformationType `xml:"ccInfo"`
}

// CreditCardInformationTypeU ...
type CreditCardInformationTypeU struct {
	Name       *string `xml:"name"`
	CardNumber *int    `xml:"cardNumber"`
	ExpireDate *string `xml:"expireDate"`
}

// CreditCardInformationType ...
type CreditCardInformationType struct {
	VendorCode            *string `xml:"vendorCode"`
	VendorCodeSubType     *string `xml:"vendorCodeSubType"`
	CardNumber            *string `xml:"cardNumber"`
	SecurityId            *string `xml:"securityId"`
	ExpiryDate            *string `xml:"expiryDate"`
	StartDate             *string `xml:"startDate"`
	EndDate               *string `xml:"endDate"`
	CcHolderName          *string `xml:"ccHolderName"`
	IssuingBankName       *string `xml:"issuingBankName"`
	CardCountryOfIssuance *string `xml:"cardCountryOfIssuance"`
	IssueNumber           *int    `xml:"issueNumber"`
	IssuingBankLongName   *string `xml:"issuingBankLongName"`
	Track1                *string `xml:"track1"`
	Track2                *string `xml:"track2"`
	Track3                *string `xml:"track3"`
	PinCode               *string `xml:"pinCode"`
	RawTrackData          *string `xml:"rawTrackData"`
}

// CreditCardSecurityType ...
type CreditCardSecurityType struct {
	AuthenticationDataDetails *AuthenticationDataType `xml:"authenticationDataDetails"`
}

// BrowserData ...
type BrowserData struct {
	// XMLName             xml.Name                         `xml:"browserData"`
	BrowserProperties   *BrowserInformationType          `xml:"browserProperties"`
	FreeFlowBrowserData []*FreeTextInformationType94526S `xml:"freeFlowBrowserData"`
}

// CreditCardStatusGroupType ...
type CreditCardStatusGroupType struct {
	AuthorisationSupplementaryData *SpecificVisaLinkCreditCardInformationType  `xml:"authorisationSupplementaryData"`
	ApprovalDetails                *GenericAuthorisationResultType             `xml:"approvalDetails"`
	LocalDateTime                  []*StructuredDateTimeInformationType206505S `xml:"localDateTime"`
	AuthorisationInformation       *TransactionInformationForTicketingType     `xml:"authorisationInformation"`
	BrowserData                    *BrowserData                                `xml:"browserData"`
	TdsInformation                 *ThreeDomainSecureGroupType                 `xml:"tdsInformation"`
	CardSupplementaryData          []*AttributeType94514S                      `xml:"cardSupplementaryData"`
	TransactionStatus              []*ErrorGroupType                           `xml:"transactionStatus"`
}

// CreditCardType ...
type CreditCardType struct {
	CreditCardCompany *string `xml:"creditCardCompany"`
	CreditCardNumber  *string `xml:"creditCardNumber"`
	ExpirationDate    *string `xml:"expirationDate"`
}

// BookingDetails ...
type BookingDetails struct {
	// XMLName                    xml.Name                                 `xml:"bookingDetails"`
	CruiseBookingReferenceInfo *ReservationControlInformationTypeI8957S `xml:"cruiseBookingReferenceInfo"`
	BookingCompany             *CompanyInformationType26258S            `xml:"bookingCompany"`
}

// CruiseBusinessDataType ...
type CruiseBusinessDataType struct {
	SailingShipInformation     *ShipIdentificationType8952S              `xml:"sailingShipInformation"`
	SailingProviderInformation *CompanyInformationType8953S              `xml:"sailingProviderInformation"`
	SailingPortsInformation    *PlaceLocationIdentificationTypeU8954S    `xml:"sailingPortsInformation"`
	SailingDateInformation     *StructuredPeriodInformationType8955S     `xml:"sailingDateInformation"`
	PassengerInfo              []*TravellerInformationTypeI8956S         `xml:"passengerInfo"`
	BookingDetails             *BookingDetails                           `xml:"bookingDetails"`
	BookingDate                *StructuredDateTimeInformationType207596S `xml:"bookingDate"`
	SailingGroupInformation    *ItemReferencesAndVersionsType9271S       `xml:"sailingGroupInformation"`
}

// CustomerTransactionDataType ...
type CustomerTransactionDataType struct {
	Pos        *PointOfSaleDataTypeI     `xml:"pos"`
	Flight     *OtherSegmentDataTypeI    `xml:"flight"`
	Connection *int                      `xml:"connection"`
	CodeShare  *CodeshareFlightDataTypeI `xml:"codeShare"`
}

// DataInformationTypeI ...
type DataInformationTypeI struct {
	Indicator *string `xml:"indicator"`
}

// DataInformationTypeI35753C ...
type DataInformationTypeI35753C struct {
	// XMLName   xml.Name `xml:"DataInformationTypeI_35753C"`
	Indicator *string `xml:"indicator"`
	Value     *int    `xml:"value"`
}

// DataTypeInformationTypeI ...
type DataTypeInformationTypeI struct {
	Type *string `xml:"type"`
}

// DateAndTimeDetailsTypeI ...
type DateAndTimeDetailsTypeI struct {
	Qualifier *string `xml:"qualifier"`
	Date      *string `xml:"date"`
	Time      *int    `xml:"time"`
}

// DateAndTimeDetailsTypeI171497C ...
type DateAndTimeDetailsTypeI171497C struct {
	// XMLName                xml.Name `xml:"DateAndTimeDetailsTypeI_171497C"`
	float64                *string `xml:"float64"`
	MovementType           *string `xml:"movementType"`
	LocationIdentification *string `xml:"locationIdentification"`
}

// DateAndTimeDetailsTypeI56946C ...
type DateAndTimeDetailsTypeI56946C struct {
	// XMLName   xml.Name `xml:"DateAndTimeDetailsTypeI_56946C"`
	Qualifier *string `xml:"qualifier"`
	Date      *string `xml:"date"`
}

// DateAndTimeDetailsType ...
type DateAndTimeDetailsType struct {
	Qualifier *string `xml:"qualifier"`
	Date      *string `xml:"date"`
	Time      *string `xml:"time"`
}

// DateAndTimeInformationTypeI ...
type DateAndTimeInformationTypeI struct {
	DateAndTime *DateAndTimeDetailsTypeI171497C `xml:"dateAndTime"`
}

// DateAndTimeInformationTypeI79021S ...
type DateAndTimeInformationTypeI79021S struct {
	// XMLName            xml.Name                 `xml:"DateAndTimeInformationTypeI_79021S"`
	DateAndTimeDetails *DateAndTimeDetailsTypeI `xml:"dateAndTimeDetails"`
}

// DateAndTimeInformationType ...
type DateAndTimeInformationType struct {
	DateAndTimeDetails []*DateAndTimeDetailsType `xml:"dateAndTimeDetails"`
}

// DateAndTimeInformationType32722S ...
type DateAndTimeInformationType32722S struct {
	// XMLName            xml.Name                       `xml:"DateAndTimeInformationType_32722S"`
	DateAndTimeDetails *DateAndTimeDetailsTypeI56946C `xml:"dateAndTimeDetails"`
}

// DateRangeType ...
type DateRangeType struct {
	DateRangeNum *int `xml:"dateRangeNum"`
}

// DetailedPaymentDataType ...
type DetailedPaymentDataType struct {
	FopInformation         *FormOfPaymentType         `xml:"fopInformation"`
	Dummy                  *DummySegmentTypeI         `xml:"dummy"`
	CreditCardDetailedData *CreditCardStatusGroupType `xml:"creditCardDetailedData"`
}

// DeviceControlDetailsType ...
type DeviceControlDetailsType struct {
	DeviceIdentification *IdentificationNumberTypeI `xml:"deviceIdentification"`
}

// DiagnosisTypeU ...
type DiagnosisTypeU struct {
	BillingDetails   *string `xml:"billingDetails"`
	BillingQualifier *string `xml:"billingQualifier"`
}

// DiningIdentificationType ...
type DiningIdentificationType struct {
	DiningDescription *string `xml:"diningDescription"`
}

// DiningInformationType ...
type DiningInformationType struct {
	DiningIdentification *DiningIdentificationType `xml:"diningIdentification"`
}

// DiscountInformationDetailsType ...
type DiscountInformationDetailsType struct {
	DiscountCode *string `xml:"discountCode"`
}

// DiscountInformationDetailsType141679C ...
type DiscountInformationDetailsType141679C struct {
	// XMLName      xml.Name `xml:"DiscountInformationDetailsType_141679C"`
	DiscountCode *string `xml:"discountCode"`
	Percentage   *int    `xml:"percentage"`
	Beneficiary  *string `xml:"beneficiary"`
	UnitQuantity *int    `xml:"unitQuantity"`
}

// DiscountInformationType ...
type DiscountInformationType struct {
	DiscountDetails *DiscountInformationDetailsType `xml:"discountDetails"`
}

// DiscountInformationType94068S ...
type DiscountInformationType94068S struct {
	// XMLName              xml.Name                                 `xml:"DiscountInformationType_94068S"`
	DiscountDetails      *DiscountInformationDetailsType141679C   `xml:"discountDetails"`
	OtherDiscountDetails []*DiscountInformationDetailsType141679C `xml:"otherDiscountDetails"`
}

// DiscountPenaltyInformationType ...
type DiscountPenaltyInformationType struct {
	FareQualifier *string `xml:"fareQualifier"`
}

// DistributionChannelType ...
type DistributionChannelType struct {
	DistributionChannelField *int `xml:"distributionChannelField"`
	SubGroup                 *int `xml:"subGroup"`
	AccessType               *int `xml:"accessType"`
}

// DistributionMethodDetails ...
type DistributionMethodDetails struct {
	DistriProductCode *string `xml:"distriProductCode"`
}

// DocumentDetailsTypeI ...
type DocumentDetailsTypeI struct {
	Number *string `xml:"number"`
	Status *string `xml:"status"`
	Date   *string `xml:"date"`
}

// DocumentDetailsTypeI19732C ...
type DocumentDetailsTypeI19732C struct {
	// XMLName xml.Name `xml:"DocumentDetailsTypeI_19732C"`
	Number *int    `xml:"number"`
	Status *string `xml:"status"`
}

// DocumentDetailsType ...
type DocumentDetailsType struct {
	Type           *string `xml:"type"`
	Number         *string `xml:"number"`
	CountryOfIssue *string `xml:"countryOfIssue"`
	ExpiryDate     *string `xml:"expiryDate"`
	IssueDate      *string `xml:"issueDate"`
}

// DocumentInformationDetailsTypeI ...
type DocumentInformationDetailsTypeI struct {
	DocumentDetails *DocumentDetailsTypeI `xml:"documentDetails"`
}

// DocumentInformationDetailsTypeI207581S ...
type DocumentInformationDetailsTypeI207581S struct {
	// XMLName         xml.Name                    `xml:"DocumentInformationDetailsTypeI_207581S"`
	DocumentDetails *DocumentDetailsTypeI19732C `xml:"documentDetails"`
}

// DocumentInformationTypeU ...
type DocumentInformationTypeU struct {
	TypeOfDocument *string `xml:"typeOfDocument"`
	DocumentNumber *string `xml:"documentNumber"`
	CountryOfIssue *string `xml:"countryOfIssue"`
}

// DummySegmentTypeI ...
type DummySegmentTypeI struct {
}

// DutyTaxFeeDetailsType ...
type DutyTaxFeeDetailsType struct {
	TaxQualifier *string `xml:"taxQualifier"`
}

// ElementManagementSegmentType ...
type ElementManagementSegmentType struct {
	Status      *string                        `xml:"status"`
	Reference   *ReferencingDetailsType127526C `xml:"reference"`
	SegmentName *string                        `xml:"segmentName"`
	LineNumber  *int                           `xml:"lineNumber"`
}

// ElementManagementSegmentType127983S ...
type ElementManagementSegmentType127983S struct {
	// XMLName          xml.Name                       `xml:"ElementManagementSegmentType_127983S"`
	ElementReference *ReferencingDetailsType127526C `xml:"elementReference"`
	SegmentName      *string                        `xml:"segmentName"`
	LineNumber       *int                           `xml:"lineNumber"`
}

// ElementManagementSegmentType226873S ...
type ElementManagementSegmentType226873S struct {
	// XMLName     xml.Name                       `xml:"ElementManagementSegmentType_226873S"`
	Status      *string                        `xml:"status"`
	Reference   *ReferencingDetailsType127526C `xml:"reference"`
	SegmentName *string                        `xml:"segmentName"`
	LineNumber  *int                           `xml:"lineNumber"`
}

// ElementManagementSegmentType83559S ...
type ElementManagementSegmentType83559S struct {
	// XMLName   xml.Name                       `xml:"ElementManagementSegmentType_83559S"`
	Reference *ReferencingDetailsType127526C `xml:"reference"`
}

// EnhancedTravellerInformationType ...
type EnhancedTravellerInformationType struct {
	TravellerNameInfo    *TravellerNameInfoType      `xml:"travellerNameInfo"`
	OtherPaxNamesDetails []*TravellerNameDetailsType `xml:"otherPaxNamesDetails"`
}

// EquipmentDetailsTypeU ...
type EquipmentDetailsTypeU struct {
	Type            *string                    `xml:"type"`
	SizeTypeDetails *EquipmentTypeAndSizeTypeU `xml:"sizeTypeDetails"`
}

// EquipmentTypeAndSizeTypeU ...
type EquipmentTypeAndSizeTypeU struct {
	Description *string `xml:"description"`
}

// ErrorGroupType ...
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType94519S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType94495S         `xml:"errorWarningDescription"`
}

// ErrorGroupType212789G ...
type ErrorGroupType212789G struct {
	// XMLName                   xml.Name                                `xml:"ErrorGroupType_212789G"`
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType185486S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType185487S         `xml:"errorWarningDescription"`
}

// ErrorGroupType223552G ...
type ErrorGroupType223552G struct {
	// XMLName                   xml.Name                                `xml:"ErrorGroupType_223552G"`
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType185486S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType185487S         `xml:"errorWarningDescription"`
}

// ExcessBaggageDetailsTypeI ...
type ExcessBaggageDetailsTypeI struct {
	Currency *string `xml:"currency"`
	Amount   float64 `xml:"amount"`
}

// ExcessBaggageTypeI ...
type ExcessBaggageTypeI struct {
	ExcessBaggageDetails *ExcessBaggageDetailsTypeI `xml:"excessBaggageDetails"`
	BaggageDetails       *BaggageDetailsTypeI       `xml:"baggageDetails"`
}

// ExtendedOwnershipSecurityDetailsType ...
type ExtendedOwnershipSecurityDetailsType struct {
	SecurityDetails *ExtendedSecurityDetailsType `xml:"securityDetails"`
}

// ExtendedRemarkType ...
type ExtendedRemarkType struct {
	StructuredRemark *MiscellaneousRemarkType210666C `xml:"structuredRemark"`
}

// ExtendedSecurityDetailsType ...
type ExtendedSecurityDetailsType struct {
	TypeOfEntity          *string `xml:"typeOfEntity"`
	AccessMode            *string `xml:"accessMode"`
	InhouseIdentification *string `xml:"inhouseIdentification"`
}

// AirportGroup ...
type AirportGroup struct {
	// XMLName         xml.Name                      `xml:"airportGroup"`
	ImpactedAirport *TerminalTimeInformationTypeS `xml:"impactedAirport"`
}

// FLIXType ...
type FLIXType struct {
	FlixAndSourceTypes *ItemDescriptionType            `xml:"flixAndSourceTypes"`
	FlixComment        *FreeTextInformationType128813S `xml:"flixComment"`
	AirportGroup       *AirportGroup                   `xml:"airportGroup"`
}

// FOPRepresentationType ...
type FOPRepresentationType struct {
	FopPNRDetails        *TicketingFormOfPaymentType    `xml:"fopPNRDetails"`
	FopSequenceNumber    *SequenceDetailsTypeU94494S    `xml:"fopSequenceNumber"`
	FopFreeflow          *FreeTextInformationType94495S `xml:"fopFreeflow"`
	PnrSupplementaryData []*PNRSupplementaryDataType    `xml:"pnrSupplementaryData"`
	PaymentModule        *PaymentGroupType              `xml:"paymentModule"`
}

// FareBasisCodesLineType ...
type FareBasisCodesLineType struct {
	FareElement []*FareElementType `xml:"fareElement"`
}

// FareCategoryCodesTypeI ...
type FareCategoryCodesTypeI struct {
	FareType *string `xml:"fareType"`
}

// FareComponentDtlsType ...
type FareComponentDtlsType struct {
	FareComponentID      *ItemNumberType227073S                   `xml:"fareComponentID"`
	MarketFareComponent  *TravelProductInformationTypeI144078S    `xml:"marketFareComponent"`
	MonetaryInformation  *MonetaryInformationType                 `xml:"monetaryInformation"`
	ComponentClassInfo   *PricingOrTicketingSubsequentType145400S `xml:"componentClassInfo"`
	FareQualifiersDetail *FareQualifierDetailsType                `xml:"fareQualifiersDetail"`
	FareFamilyDetails    *FareFamilyType                          `xml:"fareFamilyDetails"`
	FareFamilyOwner      *TransportIdentifierType                 `xml:"fareFamilyOwner"`
	CouponDetailsGroup   []*CouponDetailsType                     `xml:"couponDetailsGroup"`
}

// FareDataType ...
type FareDataType struct {
	IssueIdentifier *string                                 `xml:"issueIdentifier"`
	MonetaryInfo    []*MonetaryInformationDetailsTypeI8308C `xml:"monetaryInfo"`
	TaxFields       []*TaxFieldsType                        `xml:"taxFields"`
}

// FareElementType ...
type FareElementType struct {
	PrimaryCode      *string `xml:"primaryCode"`
	Connection       *string `xml:"connection"`
	NotValidBefore   *string `xml:"notValidBefore"`
	NotValidAfter    *string `xml:"notValidAfter"`
	BaggageAllowance *string `xml:"baggageAllowance"`
	FareBasis        *string `xml:"fareBasis"`
	TicketDesignator *string `xml:"ticketDesignator"`
}

// FareFamilyDetailsType ...
type FareFamilyDetailsType struct {
	CommercialFamily *string `xml:"commercialFamily"`
}

// FareFamilyType ...
type FareFamilyType struct {
	FareFamilyname          *string                  `xml:"fareFamilyname"`
	Hierarchy               *int                     `xml:"hierarchy"`
	CommercialFamilyDetails []*FareFamilyDetailsType `xml:"commercialFamilyDetails"`
}

// FareQualifierDetailsTypeI ...
type FareQualifierDetailsTypeI struct {
	FareCategories *FareCategoryCodesTypeI `xml:"fareCategories"`
}

// FareQualifierDetailsType ...
type FareQualifierDetailsType struct {
	DiscountDetails []*DiscountPenaltyInformationType `xml:"discountDetails"`
}

// RoomInfoGroup ...
type RoomInfoGroup struct {
	// XMLName                xml.Name             `xml:"roomInfoGroup"`
	RoomDetailsInformation *HotelRoomType20177S `xml:"roomDetailsInformation"`
	NumberOfRooms          *NumberOfUnitsType   `xml:"numberOfRooms"`
}

// FerryAccomodationPackageDescriptionType ...
type FerryAccomodationPackageDescriptionType struct {
	PackageCode             *ProductInformationTypeI                  `xml:"packageCode"`
	HotelInformation        *HotelPropertyType26378S                  `xml:"hotelInformation"`
	HotelCheckInInformation *StructuredDateTimeInformationType207596S `xml:"hotelCheckInInformation"`
	AreaCodeInfo            *PlaceLocationIdentificationTypeU24573S   `xml:"areaCodeInfo"`
	NumberOfNights          *NumberOfUnitsType                        `xml:"numberOfNights"`
	HotelItemPrice          *TariffInformationTypeU                   `xml:"hotelItemPrice"`
	RoomInfoGroup           []*RoomInfoGroup                          `xml:"roomInfoGroup"`
}

// FerryBookingDescriptionType ...
type FerryBookingDescriptionType struct {
	FerryProviderInformation     *CompanyInformationType20151S              `xml:"ferryProviderInformation"`
	ItineraryInfoGroup           *FerryLegDescriptionType                   `xml:"itineraryInfoGroup"`
	AccomodationPackageInfoGroup []*FerryAccomodationPackageDescriptionType `xml:"accomodationPackageInfoGroup"`
	BookingNumberInformation     *ReservationControlInformationTypeI20153S  `xml:"bookingNumberInformation"`
}

// PriceInfoGroup ...
type PriceInfoGroup struct {
	// XMLName               xml.Name                `xml:"priceInfoGroup"`
	RoutePriceInformation *TariffInformationTypeU `xml:"routePriceInformation"`
	PassengerCategoryType *AttributeTypeU24552S   `xml:"passengerCategoryType"`
	NumberOfPassengers    *NumberOfUnitsType      `xml:"numberOfPassengers"`
}

// VehicleInfoGroup ...
type VehicleInfoGroup struct {
	// XMLName            xml.Name                `xml:"vehicleInfoGroup"`
	VehicleInformation *VehicleTypeU           `xml:"vehicleInformation"`
	NumberOfBicycles   *NumberOfUnitsType      `xml:"numberOfBicycles"`
	VehicleRoutePrice  *TariffInformationTypeU `xml:"vehicleRoutePrice"`
}

// ServiceInfoGroup ...
type ServiceInfoGroup struct {
	// XMLName            xml.Name                `xml:"serviceInfoGroup"`
	ServiceInformation *AttributeTypeU         `xml:"serviceInformation"`
	NumberOfServices   *NumberOfUnitsType      `xml:"numberOfServices"`
	ServiceRoutePrice  *TariffInformationTypeU `xml:"serviceRoutePrice"`
}

// AnimalInfoGroup ...
type AnimalInfoGroup struct {
	// XMLName           xml.Name                      `xml:"animalInfoGroup"`
	AnimalInformation *SpecificDataInformationTypeI `xml:"animalInformation"`
	AnimalRoutePrice  *TariffInformationTypeU       `xml:"animalRoutePrice"`
}

// FerryLegDescriptionType ...
type FerryLegDescriptionType struct {
	SailingDetails               *TravelProductInformationTypeU           `xml:"sailingDetails"`
	ShipDescription              *ShipIdentificationType                  `xml:"shipDescription"`
	SailingLegCheckInInformation *StructuredDateTimeInformationType21109S `xml:"sailingLegCheckInInformation"`
	PassengerAssociation         *ReferenceInformationTypeI25132S         `xml:"passengerAssociation"`
	PriceInfoGroup               []*PriceInfoGroup                        `xml:"priceInfoGroup"`
	VehicleInfoGroup             []*VehicleInfoGroup                      `xml:"vehicleInfoGroup"`
	ServiceInfoGroup             []*ServiceInfoGroup                      `xml:"serviceInfoGroup"`
	AnimalInfoGroup              []*AnimalInfoGroup                       `xml:"animalInfoGroup"`
}

// FlightSegmentDetailsTypeI ...
type FlightSegmentDetailsTypeI struct {
}

// FormOfPaymentDetailsTypeI ...
type FormOfPaymentDetailsTypeI struct {
	Type             *string `xml:"type"`
	VendorCode       *string `xml:"vendorCode"`
	CreditCardNumber *string `xml:"creditCardNumber"`
	ExpiryDate       *string `xml:"expiryDate"`
	ExtendedPayment  *string `xml:"extendedPayment"`
	FopFreeText      *string `xml:"fopFreeText"`
}

// FormOfPaymentDetailsTypeI20667C ...
type FormOfPaymentDetailsTypeI20667C struct {
	// XMLName             xml.Name `xml:"FormOfPaymentDetailsTypeI_20667C"`
	Type                *string `xml:"type"`
	Indicator           *string `xml:"indicator"`
	Amount              float64 `xml:"amount"`
	VendorCode          *string `xml:"vendorCode"`
	CreditCardNumber    *string `xml:"creditCardNumber"`
	ExpiryDate          *string `xml:"expiryDate"`
	ApprovalCode        *string `xml:"approvalCode"`
	SourceOfApproval    *string `xml:"sourceOfApproval"`
	AuthorisedAmount    float64 `xml:"authorisedAmount"`
	AddressVerification *string `xml:"addressVerification"`
	CustomerAccount     *string `xml:"customerAccount"`
	ExtendedPayment     *string `xml:"extendedPayment"`
	FopFreeText         *string `xml:"fopFreeText"`
	MembershipStatus    *string `xml:"membershipStatus"`
	TransactionInfo     *string `xml:"transactionInfo"`
}

// FormOfPaymentDetailsTypeI52343C ...
type FormOfPaymentDetailsTypeI52343C struct {
	// XMLName          xml.Name `xml:"FormOfPaymentDetailsTypeI_52343C"`
	Type             *string `xml:"type"`
	Indicator        *string `xml:"indicator"`
	VendorCode       *string `xml:"vendorCode"`
	CreditCardNumber *string `xml:"creditCardNumber"`
	ExpiryDate       *string `xml:"expiryDate"`
	ExtendedPayment  *string `xml:"extendedPayment"`
	FopFreeText      *string `xml:"fopFreeText"`
}

// FormOfPaymentDetailsType ...
type FormOfPaymentDetailsType struct {
	Type *string `xml:"type"`
}

// FormOfPaymentInformationType ...
type FormOfPaymentInformationType struct {
	FopCode              *string `xml:"fopCode"`
	FopMapTable          *string `xml:"fopMapTable"`
	FopBillingCode       *string `xml:"fopBillingCode"`
	FopStatus            *string `xml:"fopStatus"`
	FopEdiCode           *string `xml:"fopEdiCode"`
	FopReportingCode     *string `xml:"fopReportingCode"`
	FopPrintedCode       *string `xml:"fopPrintedCode"`
	FopElecTicketingCode *string `xml:"fopElecTicketingCode"`
}

// FormOfPaymentTypeI ...
type FormOfPaymentTypeI struct {
	FormOfPayment []*FormOfPaymentDetailsTypeI `xml:"formOfPayment"`
}

// FormOfPaymentTypeI16862S ...
type FormOfPaymentTypeI16862S struct {
	// XMLName            xml.Name                           `xml:"FormOfPaymentTypeI_16862S"`
	FormOfPayment      *FormOfPaymentDetailsTypeI20667C   `xml:"formOfPayment"`
	OtherFormOfPayment []*FormOfPaymentDetailsTypeI20667C `xml:"otherFormOfPayment"`
}

// FormOfPaymentTypeI29553S ...
type FormOfPaymentTypeI29553S struct {
	// XMLName       xml.Name                           `xml:"FormOfPaymentTypeI_29553S"`
	FormOfPayment []*FormOfPaymentDetailsTypeI52343C `xml:"formOfPayment"`
}

// FormOfPaymentType ...
type FormOfPaymentType struct {
	FormOfPayment *FormOfPaymentDetailsType `xml:"formOfPayment"`
}

// FraudScreeningGroupType ...
type FraudScreeningGroupType struct {
	FraudScreening    *StatusType94568S                         `xml:"fraudScreening"`
	IpAdress          *DeviceControlDetailsType                 `xml:"ipAdress"`
	MerchantURL       *CommunicationContactType                 `xml:"merchantURL"`
	PayerPhoneOrEmail []*PhoneAndEmailAddressType94565S         `xml:"payerPhoneOrEmail"`
	ShopperSession    *SystemDetailsInfoType94569S              `xml:"shopperSession"`
	PayerName         *TravellerInformationType94570S           `xml:"payerName"`
	PayerDateOfBirth  *StructuredDateTimeInformationType207582S `xml:"payerDateOfBirth"`
	BillingAddress    *AddressType                              `xml:"billingAddress"`
	FormOfIdDetails   []*ReferenceInfoType94566S                `xml:"formOfIdDetails"`
}

// FreeTextDetailsType ...
type FreeTextDetailsType struct {
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextDetailsType1309C ...
type FreeTextDetailsType1309C struct {
	// XMLName              xml.Name `xml:"FreeTextDetailsType_1309C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Status               *string `xml:"status"`
	CompanyId            *string `xml:"companyId"`
	Language             *string `xml:"language"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextDetailsType142141C ...
type FreeTextDetailsType142141C struct {
	// XMLName              xml.Name `xml:"FreeTextDetailsType_142141C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextDetailsType187698C ...
type FreeTextDetailsType187698C struct {
	// XMLName              xml.Name `xml:"FreeTextDetailsType_187698C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	Language             *string `xml:"language"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextDetailsType198207C ...
type FreeTextDetailsType198207C struct {
	// XMLName              xml.Name `xml:"FreeTextDetailsType_198207C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	CompanyId            *string `xml:"companyId"`
	Language             *string `xml:"language"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextDetailsType260188C ...
type FreeTextDetailsType260188C struct {
	// XMLName              xml.Name `xml:"FreeTextDetailsType_260188C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Language             *string `xml:"language"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextDetailsType46357C ...
type FreeTextDetailsType46357C struct {
	// XMLName              xml.Name `xml:"FreeTextDetailsType_46357C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextInformationType ...
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []*string            `xml:"freeText"`
}

// FreeTextInformationType128813S ...
type FreeTextInformationType128813S struct {
	// XMLName         xml.Name                    `xml:"FreeTextInformationType_128813S"`
	FreeTextDetails *FreeTextDetailsType187698C `xml:"freeTextDetails"`
	FreeText        []*string                   `xml:"freeText"`
}

// FreeTextInformationType136708S ...
type FreeTextInformationType136708S struct {
	// XMLName         xml.Name                    `xml:"FreeTextInformationType_136708S"`
	FreeTextDetails *FreeTextDetailsType198207C `xml:"freeTextDetails"`
	FreeText        []*string                   `xml:"freeText"`
}

// FreeTextInformationType136715S ...
type FreeTextInformationType136715S struct {
	// XMLName         xml.Name                    `xml:"FreeTextInformationType_136715S"`
	FreeTextDetails *FreeTextDetailsType198207C `xml:"freeTextDetails"`
	FreeText        []*string                   `xml:"freeText"`
}

// FreeTextInformationType185487S ...
type FreeTextInformationType185487S struct {
	// XMLName         xml.Name                    `xml:"FreeTextInformationType_185487S"`
	FreeTextDetails *FreeTextDetailsType260188C `xml:"freeTextDetails"`
	FreeText        []*string                   `xml:"freeText"`
}

// FreeTextInformationType20551S ...
type FreeTextInformationType20551S struct {
	// XMLName         xml.Name             `xml:"FreeTextInformationType_20551S"`
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []*string            `xml:"freeText"`
}

// FreeTextInformationType25445S ...
type FreeTextInformationType25445S struct {
	// XMLName         xml.Name                   `xml:"FreeTextInformationType_25445S"`
	FreeTextDetails *FreeTextDetailsType46357C `xml:"freeTextDetails"`
	FreeText        []*string                  `xml:"freeText"`
}

// FreeTextInformationType29860S ...
type FreeTextInformationType29860S struct {
	// XMLName         xml.Name                    `xml:"FreeTextInformationType_29860S"`
	FreeTextDetails *FreeTextDetailsType187698C `xml:"freeTextDetails"`
	FreeText        *string                     `xml:"freeText"`
}

// FreeTextInformationType6235S ...
type FreeTextInformationType6235S struct {
	// XMLName         xml.Name                    `xml:"FreeTextInformationType_6235S"`
	FreeTextDetails *FreeTextDetailsType187698C `xml:"freeTextDetails"`
	FreeText        []*string                   `xml:"freeText"`
}

// FreeTextInformationType79018S ...
type FreeTextInformationType79018S struct {
	// XMLName         xml.Name             `xml:"FreeTextInformationType_79018S"`
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []*string            `xml:"freeText"`
}

// FreeTextInformationType94495S ...
type FreeTextInformationType94495S struct {
	// XMLName         xml.Name             `xml:"FreeTextInformationType_94495S"`
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        *string              `xml:"freeText"`
}

// FreeTextInformationType94526S ...
type FreeTextInformationType94526S struct {
	// XMLName         xml.Name                    `xml:"FreeTextInformationType_94526S"`
	FreeTextDetails *FreeTextDetailsType142141C `xml:"freeTextDetails"`
	FreeText        []*string                   `xml:"freeText"`
}

// FreeTextInformationType9865S ...
type FreeTextInformationType9865S struct {
	// XMLName         xml.Name                  `xml:"FreeTextInformationType_9865S"`
	FreeTextDetails *FreeTextDetailsType1309C `xml:"freeTextDetails"`
	FreeText        *string                   `xml:"freeText"`
}

// FreeTextQualificationTypeI ...
type FreeTextQualificationTypeI struct {
	SubjectQualifier *string `xml:"subjectQualifier"`
	Type             *string `xml:"type"`
	CompanyId        *string `xml:"companyId"`
	Language         *string `xml:"language"`
}

// FreeTextQualificationTypeI148295C ...
type FreeTextQualificationTypeI148295C struct {
	// XMLName              xml.Name `xml:"FreeTextQualificationTypeI_148295C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Language             *string `xml:"language"`
}

// FreeTextQualificationTypeI185754C ...
type FreeTextQualificationTypeI185754C struct {
	// XMLName              xml.Name `xml:"FreeTextQualificationTypeI_185754C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	CompanyId            *string `xml:"companyId"`
}

// FreeTextQualificationTypeI274593C ...
type FreeTextQualificationTypeI274593C struct {
	// XMLName              xml.Name `xml:"FreeTextQualificationTypeI_274593C"`
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Status               *string `xml:"status"`
	CompanyId            *string `xml:"companyId"`
	Language             *string `xml:"language"`
}

// FreeTextQualificationType ...
type FreeTextQualificationType struct {
	SubjectQualifier *string `xml:"subjectQualifier"`
	Type             *string `xml:"type"`
	Status           *string `xml:"status"`
	CompanyId        *string `xml:"companyId"`
}

// FrequencyDetailsTypeU ...
type FrequencyDetailsTypeU struct {
	InstalmentsNumber         *int    `xml:"instalmentsNumber"`
	InstalmentsFrequency      *string `xml:"instalmentsFrequency"`
	InstalmentsStartDate      *string `xml:"instalmentsStartDate"`
	InstalmentsDatrDateFormat *string `xml:"instalmentsDatrDateFormat"`
}

// FrequencyTypeU ...
type FrequencyTypeU struct {
	ExtendedPaymentDetails *FrequencyDetailsTypeU `xml:"extendedPaymentDetails"`
}

// FrequencyType ...
type FrequencyType struct {
	Qualifier *string `xml:"qualifier"`
	Value     *string `xml:"value"`
}

// FrequentFlyerInformationGroupType ...
type FrequentFlyerInformationGroupType struct {
	FrequentTravellerInfo   *FrequentTravellerIdentificationCodeType74327S `xml:"frequentTravellerInfo"`
	DiscountInformation     *DiscountInformationType                       `xml:"discountInformation"`
	BookingClassInformation *ProductInformationType                        `xml:"bookingClassInformation"`
}

// FrequentTravellerIdentificationCodeType ...
type FrequentTravellerIdentificationCodeType struct {
	AirlineFrequentTraveler *FrequentTravellerIdentificationType198190C `xml:"airlineFrequentTraveler"`
}

// FrequentTravellerIdentificationCodeType38226S ...
type FrequentTravellerIdentificationCodeType38226S struct {
	// XMLName                  xml.Name                                   `xml:"FrequentTravellerIdentificationCodeType_38226S"`
	AirlineFrequentTraveler  *FrequentTravellerIdentificationType       `xml:"airlineFrequentTraveler"`
	AllianceFrequentTraveler *FrequentTravellerIdentificationType64816C `xml:"allianceFrequentTraveler"`
}

// FrequentTravellerIdentificationCodeType74327S ...
type FrequentTravellerIdentificationCodeType74327S struct {
	// XMLName               xml.Name                              `xml:"FrequentTravellerIdentificationCodeType_74327S"`
	FrequentTraveler      *FrequentTravellerIdentificationTypeI `xml:"frequentTraveler"`
	PriorityDetails       []*PriorityDetailsType                `xml:"priorityDetails"`
	RedemptionInformation *ProductAccountDetailsTypeI           `xml:"redemptionInformation"`
}

// FrequentTravellerIdentificationTypeI ...
type FrequentTravellerIdentificationTypeI struct {
	Company          *string `xml:"company"`
	MembershipNumber *string `xml:"membershipNumber"`
	CustomerValue    *int    `xml:"customerValue"`
}

// FrequentTravellerIdentificationType ...
type FrequentTravellerIdentificationType struct {
	Company          *string `xml:"company"`
	MembershipNumber *string `xml:"membershipNumber"`
	TierLevel        *string `xml:"tierLevel"`
	PriorityCode     *string `xml:"priorityCode"`
	TierDescription  *string `xml:"tierDescription"`
}

// FrequentTravellerIdentificationType198190C ...
type FrequentTravellerIdentificationType198190C struct {
	// XMLName          xml.Name `xml:"FrequentTravellerIdentificationType_198190C"`
	Company          *string `xml:"company"`
	MembershipNumber *string `xml:"membershipNumber"`
}

// FrequentTravellerIdentificationType64816C ...
type FrequentTravellerIdentificationType64816C struct {
	// XMLName         xml.Name `xml:"FrequentTravellerIdentificationType_64816C"`
	TierLevel       *string `xml:"tierLevel"`
	PriorityCode    *string `xml:"priorityCode"`
	TierDescription *string `xml:"tierDescription"`
	CompanyCode     *string `xml:"companyCode"`
}

// GategoryType ...
type GategoryType struct {
	CategoryNum1 *int    `xml:"categoryNum1"`
	CategoryName *string `xml:"categoryName"`
}

// GeneralOptionInformationType ...
type GeneralOptionInformationType struct {
	Type            *string   `xml:"type"`
	UpdateIndicator *string   `xml:"updateIndicator"`
	Freetext        []*string `xml:"freetext"`
}

// GeneralOptionType ...
type GeneralOptionType struct {
	OptionDetail *GeneralOptionInformationType `xml:"optionDetail"`
}

// GenericAuthorisationResultType ...
type GenericAuthorisationResultType struct {
	ApprovalCodeData *AuthorizationApprovalDataType `xml:"approvalCodeData"`
}

// GenericDetailsTypeI ...
type GenericDetailsTypeI struct {
	SeatCharacteristic []*string `xml:"seatCharacteristic"`
}

// HotelProductInformationType ...
type HotelProductInformationType struct {
	Property       *PropertyHeaderDetailsType `xml:"property"`
	HotelRoom      *RoomDetailsType           `xml:"hotelRoom"`
	Negotiated     []*RateCodeRestrictedType  `xml:"negotiated"`
	OtherHotelInfo *OtherHotelInformationType `xml:"otherHotelInfo"`
}

// HotelPropertyType ...
type HotelPropertyType struct {
	HotelReference      *HotelUniqueIdType `xml:"hotelReference"`
	HotelName           *string            `xml:"hotelName"`
	FireSafetyIndicator *string            `xml:"fireSafetyIndicator"`
}

// HotelPropertyType26378S ...
type HotelPropertyType26378S struct {
	// XMLName        xml.Name                 `xml:"HotelPropertyType_26378S"`
	HotelReference *HotelUniqueIdType47769C `xml:"hotelReference"`
}

// HotelRoomRateInformationType ...
type HotelRoomRateInformationType struct {
	RoomType           *string `xml:"roomType"`
	RatePlanCode       *string `xml:"ratePlanCode"`
	RateCategoryCode   *string `xml:"rateCategoryCode"`
	RateQualifiedIndic *string `xml:"rateQualifiedIndic"`
}

// HotelRoomRateInformationType46329C ...
type HotelRoomRateInformationType46329C struct {
	// XMLName  xml.Name `xml:"HotelRoomRateInformationType_46329C"`
	RoomType *string `xml:"roomType"`
}

// HotelRoomType ...
type HotelRoomType struct {
	RoomRateIdentifier *HotelRoomRateInformationType   `xml:"roomRateIdentifier"`
	BookingCode        *string                         `xml:"bookingCode"`
	GuestCountDetails  *NumberOfUnitDetailsTypeI18670C `xml:"guestCountDetails"`
	RoomTypeOverride   *string                         `xml:"roomTypeOverride"`
}

// HotelRoomType20177S ...
type HotelRoomType20177S struct {
	// XMLName          xml.Name `xml:"HotelRoomType_20177S"`
	RoomTypeOverride *string `xml:"roomTypeOverride"`
}

// HotelRoomType25429S ...
type HotelRoomType25429S struct {
	// XMLName            xml.Name                            `xml:"HotelRoomType_25429S"`
	RoomRateIdentifier *HotelRoomRateInformationType46329C `xml:"roomRateIdentifier"`
	BookingCode        *string                             `xml:"bookingCode"`
	GuestCountDetails  *NumberOfUnitDetailsTypeI46330C     `xml:"guestCountDetails"`
	RoomTypeOverride   *string                             `xml:"roomTypeOverride"`
}

// HotelUniqueIdType ...
type HotelUniqueIdType struct {
	ChainCode *string `xml:"chainCode"`
	CityCode  *string `xml:"cityCode"`
	HotelCode *string `xml:"hotelCode"`
}

// HotelUniqueIdType47769C ...
type HotelUniqueIdType47769C struct {
	// XMLName   xml.Name `xml:"HotelUniqueIdType_47769C"`
	HotelCode *string `xml:"hotelCode"`
}

// IdentificationNumberTypeI ...
type IdentificationNumberTypeI struct {
	Address   *string `xml:"address"`
	Qualifier *string `xml:"qualifier"`
}

// InConnectionWithType ...
type InConnectionWithType struct {
	Carrier        *TransportIdentifierType79027S `xml:"carrier"`
	Identification *TicketNumberTypeI79026S       `xml:"identification"`
	CouponList     []*CouponInformationTypeI      `xml:"couponList"`
}

// IndividualPnrSecurityInformationType ...
type IndividualPnrSecurityInformationType struct {
	Security     []*IndividualSecurityType3194C `xml:"security"`
	SecurityInfo *SecurityInformationType       `xml:"securityInfo"`
	Indicator    *string                        `xml:"indicator"`
}

// IndividualSecurityType ...
type IndividualSecurityType struct {
	Office           *string `xml:"office"`
	AccessMode       *string `xml:"accessMode"`
	OfficeIdentifier *string `xml:"officeIdentifier"`
}

// IndividualSecurityType3194C ...
type IndividualSecurityType3194C struct {
	// XMLName        xml.Name `xml:"IndividualSecurityType_3194C"`
	Identification *string `xml:"identification"`
	AccessMode     *string `xml:"accessMode"`
}

// ProductSection ...
type ProductSection struct {
	// XMLName          xml.Name                           `xml:"productSection"`
	ProductCode      *InsuranceProductDetailsType20774S `xml:"productCode"`
	InformationLines *FreeTextInformationType6235S      `xml:"informationLines"`
}

// PlanTypeDetails ...
type PlanTypeDetails struct {
	// XMLName     xml.Name                          `xml:"planTypeDetails"`
	PlanType    *InsuranceProviderAndProductsType `xml:"planType"`
	TravelValue *MonetaryInformationTypeI         `xml:"travelValue"`
}

// ContactDetails ...
type ContactDetails struct {
	// XMLName      xml.Name                        `xml:"contactDetails"`
	Miscelaneous *MiscellaneousRemarksType12240S `xml:"miscelaneous"`
	PhoneNumber  *PhoneAndEmailAddressType32298S `xml:"phoneNumber"`
	ContactName  *TravellerInformationTypeI      `xml:"contactName"`
}

// SubscriberAddressSection ...
type SubscriberAddressSection struct {
	// XMLName     xml.Name                        `xml:"subscriberAddressSection"`
	NameDetails *NameTypeU                      `xml:"nameDetails"`
	AddressInfo *AddressTypeU                   `xml:"addressInfo"`
	PhoneNumber *PhoneAndEmailAddressType32298S `xml:"phoneNumber"`
}

// CoverageInfo ...
type CoverageInfo struct {
	// XMLName        xml.Name                  `xml:"coverageInfo"`
	Coverage       *InsuranceCoverageType    `xml:"coverage"`
	CoverageValues *MonetaryInformationTypeI `xml:"coverageValues"`
}

// CoverageDetails ...
type CoverageDetails struct {
	// XMLName               xml.Name                                 `xml:"coverageDetails"`
	PolicyDetails         *InsurancePolicyType                     `xml:"policyDetails"`
	CoverageInfo          []*CoverageInfo                          `xml:"coverageInfo"`
	CoveredPassenger      []*TravellerInformationTypeI15923S       `xml:"coveredPassenger"`
	CoverageDates         *StructuredPeriodInformationType         `xml:"coverageDates"`
	SubscriptionDetails   *StructuredDateTimeInformationType20644S `xml:"subscriptionDetails"`
	AgentReferenceDetails *UserIdentificationType9456S             `xml:"agentReferenceDetails"`
}

// InsuranceFopSection ...
type InsuranceFopSection struct {
	// XMLName              xml.Name                  `xml:"insuranceFopSection"`
	FormOfPaymentSection *FormOfPaymentTypeI16862S `xml:"formOfPaymentSection"`
	FopExtendedData      []*StatusTypeI13270S      `xml:"fopExtendedData"`
}

// TravelerValueDetails ...
type TravelerValueDetails struct {
	// XMLName      xml.Name                  `xml:"travelerValueDetails"`
	TravelCost   *InsuranceCoverageType    `xml:"travelCost"`
	TravelAmount *MonetaryInformationTypeI `xml:"travelAmount"`
}

// PremiumPerTariffPerPax ...
type PremiumPerTariffPerPax struct {
	// XMLName                xml.Name                           `xml:"premiumPerTariffPerPax"`
	TariffCodeInfo         *InsuranceProductDetailsType20775S `xml:"tariffCodeInfo"`
	TariffCodePerPaxAmount *MonetaryInformationTypeI          `xml:"tariffCodePerPaxAmount"`
}

// PassengerDetails ...
type PassengerDetails struct {
	// XMLName                xml.Name                                  `xml:"passengerDetails"`
	PassengerAssociation   *ReferenceInformationType                 `xml:"passengerAssociation"`
	PerPaxProdKnowledge    []*ActionDetailsTypeI                     `xml:"perPaxProdKnowledge"`
	DateOfBirthInfo        *StructuredDateTimeInformationType13380S  `xml:"dateOfBirthInfo"`
	PassengerFeatures      []*TravellerInformationType               `xml:"passengerFeatures"`
	InsureeRemark          *MiscellaneousRemarksType                 `xml:"insureeRemark"`
	TravelerDocInfo        *PassengerDocumentDetailsType             `xml:"travelerDocInfo"`
	PolicyDetails          *InsurancePolicyType                      `xml:"policyDetails"`
	TravelerValueDetails   *TravelerValueDetails                     `xml:"travelerValueDetails"`
	PremiumPerTariffPerPax []*PremiumPerTariffPerPax                 `xml:"premiumPerTariffPerPax"`
	PremiumPerpaxInfo      *TariffInformationTypeI22057S             `xml:"premiumPerpaxInfo"`
	VoucherNumber          *ReservationControlInformationTypeU31804S `xml:"voucherNumber"`
}

// InsuranceBusinessDataType ...
type InsuranceBusinessDataType struct {
	ProviderProductDetails   *InsuranceProductDetailsType        `xml:"providerProductDetails"`
	SubstiteName             []*TravellerInformationTypeI        `xml:"substiteName"`
	ExtraPremium             *MonetaryInformationTypeI           `xml:"extraPremium"`
	ProductSection           []*ProductSection                   `xml:"productSection"`
	PlanCostInfo             *TariffInformationTypeI22057S       `xml:"planCostInfo"`
	PlanTypeDetails          *PlanTypeDetails                    `xml:"planTypeDetails"`
	ContactDetails           *ContactDetails                     `xml:"contactDetails"`
	SubscriberAddressSection *SubscriberAddressSection           `xml:"subscriberAddressSection"`
	CoverageDetails          *CoverageDetails                    `xml:"coverageDetails"`
	ComissionAmount          *CommissionInformationType          `xml:"comissionAmount"`
	InsuranceFopSection      *InsuranceFopSection                `xml:"insuranceFopSection"`
	ConfirmationNumber       *ReservationControlInformationTypeI `xml:"confirmationNumber"`
	ProductKnowledge         []*ActionDetailsTypeI               `xml:"productKnowledge"`
	PassengerDetails         []*PassengerDetails                 `xml:"passengerDetails"`
	PrintInformation         *DocumentInformationDetailsTypeI    `xml:"printInformation"`
}

// InsuranceCoverageType ...
type InsuranceCoverageType struct {
	CoverageIndicator []*string `xml:"coverageIndicator"`
}

// InsuranceCoverageType25483S ...
type InsuranceCoverageType25483S struct {
	// XMLName           xml.Name `xml:"InsuranceCoverageType_25483S"`
	CoverageIndicator *string `xml:"coverageIndicator"`
}

// InsuranceNameType ...
type InsuranceNameType struct {
	InsuranceTravelerDetails *SpecificTravellerDetailsType `xml:"insuranceTravelerDetails"`
	TravelerPerpaxDetails    []*TravelerPerpaxDetailsType  `xml:"travelerPerpaxDetails"`
}

// InsurancePolicyType ...
type InsurancePolicyType struct {
	FareDiscount *string `xml:"fareDiscount"`
}

// InsuranceProductDetailsType ...
type InsuranceProductDetailsType struct {
	CompanyCode    *string   `xml:"companyCode"`
	CountryCode    *string   `xml:"countryCode"`
	ExtraReference []*string `xml:"extraReference"`
}

// InsuranceProductDetailsType20774S ...
type InsuranceProductDetailsType20774S struct {
	// XMLName                 xml.Name                   `xml:"InsuranceProductDetailsType_20774S"`
	CompanyCode             *string                    `xml:"companyCode"`
	CountryCode             *string                    `xml:"countryCode"`
	ProductDetails          *ProviderInformationType   `xml:"productDetails"`
	ExtensionIdentification []*ProviderInformationType `xml:"extensionIdentification"`
	TariffCodeDetails       []*TariffcodeType          `xml:"tariffCodeDetails"`
}

// InsuranceProductDetailsType20775S ...
type InsuranceProductDetailsType20775S struct {
	// XMLName           xml.Name          `xml:"InsuranceProductDetailsType_20775S"`
	TariffCodeDetails []*TariffcodeType `xml:"tariffCodeDetails"`
}

// InsuranceProviderAndProductsType ...
type InsuranceProviderAndProductsType struct {
	TripType     *string                 `xml:"tripType"`
	TourOperator *string                 `xml:"tourOperator"`
	CountryInfo  *CountrydescriptionType `xml:"countryInfo"`
}

// InteractiveFreeTextTypeI ...
type InteractiveFreeTextTypeI struct {
	FreeTextQualification *FreeTextQualificationTypeI185754C `xml:"freeTextQualification"`
	FreeText              []*string                          `xml:"freeText"`
}

// InteractiveFreeTextTypeI136698S ...
type InteractiveFreeTextTypeI136698S struct {
	// XMLName        xml.Name                    `xml:"InteractiveFreeTextTypeI_136698S"`
	FreetextDetail *FreeTextQualificationTypeI `xml:"freetextDetail"`
	Text           []*string                   `xml:"text"`
}

// InteractiveFreeTextTypeI99363S ...
type InteractiveFreeTextTypeI99363S struct {
	// XMLName               xml.Name                           `xml:"InteractiveFreeTextTypeI_99363S"`
	FreeTextQualification *FreeTextQualificationTypeI148295C `xml:"freeTextQualification"`
	FreeText              *string                            `xml:"freeText"`
}

// InteractiveFreeTextType ...
type InteractiveFreeTextType struct {
	FreeTextQualification *FreeTextQualificationTypeI274593C `xml:"freeTextQualification"`
	FreeText              []*string                          `xml:"freeText"`
}

// InternalIDDetailsType ...
type InternalIDDetailsType struct {
	InhouseId *string `xml:"inhouseId"`
	Type      *string `xml:"type"`
}

// ItemDescriptionType ...
type ItemDescriptionType struct {
	ItemCharacteristic *string `xml:"itemCharacteristic"`
}

// ItemNumberIdentificationTypeU ...
type ItemNumberIdentificationTypeU struct {
	Number *string `xml:"number"`
}

// ItemNumberIdentificationTypeU46320C ...
type ItemNumberIdentificationTypeU46320C struct {
	// XMLName         xml.Name `xml:"ItemNumberIdentificationTypeU_46320C"`
	ItemID          *int    `xml:"itemID"`
	ItemIDQualifier *string `xml:"itemIDQualifier"`
}

// ItemNumberIdentificationType ...
type ItemNumberIdentificationType struct {
	Number *string `xml:"number"`
}

// ItemNumberIdentificationType311115C ...
type ItemNumberIdentificationType311115C struct {
	// XMLName xml.Name `xml:"ItemNumberIdentificationType_311115C"`
	Number *string `xml:"number"`
	Type   *string `xml:"type"`
}

// ItemNumberTypeU ...
type ItemNumberTypeU struct {
	ItemIdentification *ItemNumberIdentificationTypeU46320C `xml:"itemIdentification"`
}

// ItemNumberTypeU33258S ...
type ItemNumberTypeU33258S struct {
	// XMLName           xml.Name                       `xml:"ItemNumberTypeU_33258S"`
	ItemNumberDetails *ItemNumberIdentificationTypeU `xml:"itemNumberDetails"`
}

// ItemNumberType ...
type ItemNumberType struct {
	ItemNumberDetails []*ItemNumberIdentificationType `xml:"itemNumberDetails"`
}

// ItemNumberType227073S ...
type ItemNumberType227073S struct {
	// XMLName           xml.Name                               `xml:"ItemNumberType_227073S"`
	ItemNumberDetails []*ItemNumberIdentificationType311115C `xml:"itemNumberDetails"`
}

// ItemReferencesAndVersionsType ...
type ItemReferencesAndVersionsType struct {
	ReferenceType   *string `xml:"referenceType"`
	UniqueReference *string `xml:"uniqueReference"`
}

// ItemReferencesAndVersionsType6550S ...
type ItemReferencesAndVersionsType6550S struct {
	// XMLName   xml.Name                 `xml:"ItemReferencesAndVersionsType_6550S"`
	IDSection *UniqueIdDescriptionType `xml:"iDSection"`
}

// ItemReferencesAndVersionsType9271S ...
type ItemReferencesAndVersionsType9271S struct {
	// XMLName         xml.Name `xml:"ItemReferencesAndVersionsType_9271S"`
	ReferenceType   *string `xml:"referenceType"`
	UniqueReference *string `xml:"uniqueReference"`
}

// ItemReferencesAndVersionsType94069S ...
type ItemReferencesAndVersionsType94069S struct {
	// XMLName         xml.Name                          `xml:"ItemReferencesAndVersionsType_94069S"`
	ReferenceType   *string                           `xml:"referenceType"`
	UniqueReference *string                           `xml:"uniqueReference"`
	ActionCategory  *string                           `xml:"actionCategory"`
	IdSection       []*UniqueIdDescriptionType141680C `xml:"idSection"`
}

// ItemReferencesAndVersionsType94556S ...
type ItemReferencesAndVersionsType94556S struct {
	// XMLName         xml.Name `xml:"ItemReferencesAndVersionsType_94556S"`
	ReferenceType   *string `xml:"referenceType"`
	UniqueReference *string `xml:"uniqueReference"`
}

// LocationIdentificationBatchTypeU ...
type LocationIdentificationBatchTypeU struct {
	Code *string `xml:"code"`
	Name *string `xml:"name"`
}

// LocationIdentificationBatchTypeU198230C ...
type LocationIdentificationBatchTypeU198230C struct {
	// XMLName xml.Name `xml:"LocationIdentificationBatchTypeU_198230C"`
	Code *string `xml:"code"`
	Name *string `xml:"name"`
}

// LocationIdentificationBatchTypeU46344C ...
type LocationIdentificationBatchTypeU46344C struct {
	// XMLName   xml.Name `xml:"LocationIdentificationBatchTypeU_46344C"`
	Code      *string `xml:"code"`
	Qualifier *string `xml:"qualifier"`
	Name      *string `xml:"name"`
}

// LocationIdentificationBatchTypeU56454C ...
type LocationIdentificationBatchTypeU56454C struct {
	// XMLName   xml.Name `xml:"LocationIdentificationBatchTypeU_56454C"`
	Code      *string `xml:"code"`
	Qualifier *string `xml:"qualifier"`
	Name      *string `xml:"name"`
}

// LocationIdentificationBatchTypeU60738C ...
type LocationIdentificationBatchTypeU60738C struct {
	// XMLName   xml.Name `xml:"LocationIdentificationBatchTypeU_60738C"`
	Code      *string `xml:"code"`
	Qualifier *string `xml:"qualifier"`
}

// LocationIdentificationBatchType ...
type LocationIdentificationBatchType struct {
	Code *string `xml:"code"`
}

// LocationIdentificationTypeS ...
type LocationIdentificationTypeS struct {
	CityCode *string `xml:"cityCode"`
}

// LocationIdentificationTypeU ...
type LocationIdentificationTypeU struct {
	Qualifier *string `xml:"qualifier"`
	Name      *string `xml:"name"`
}

// LocationIdentificationTypeU198211C ...
type LocationIdentificationTypeU198211C struct {
	// XMLName xml.Name `xml:"LocationIdentificationTypeU_198211C"`
	Code *string `xml:"code"`
	Name *string `xml:"name"`
}

// LocationTypeI ...
type LocationTypeI struct {
	TrueLocationId *string `xml:"trueLocationId"`
}

// LocationTypeI208252C ...
type LocationTypeI208252C struct {
	// XMLName        xml.Name `xml:"LocationTypeI_208252C"`
	TrueLocationId *string `xml:"trueLocationId"`
}

// LocationTypeI2784C ...
type LocationTypeI2784C struct {
	// XMLName  xml.Name `xml:"LocationTypeI_2784C"`
	CityCode *string `xml:"cityCode"`
	CityName *string `xml:"cityName"`
}

// LocationTypeU ...
type LocationTypeU struct {
	Code      *string `xml:"code"`
	Name      *string `xml:"name"`
	Qualifier *string `xml:"qualifier"`
}

// LocationTypeU46324C ...
type LocationTypeU46324C struct {
	// XMLName   xml.Name `xml:"LocationTypeU_46324C"`
	Code      *string `xml:"code"`
	Name      *string `xml:"name"`
	Country   *string `xml:"country"`
	Qualifier *string `xml:"qualifier"`
}

// LongFreeTextType ...
type LongFreeTextType struct {
	FreetextDetail *FreeTextQualificationType `xml:"freetextDetail"`
	LongFreetext   *string                    `xml:"longFreetext"`
}

// MeanOfPaymentDataType ...
type MeanOfPaymentDataType struct {
	FopInformation *FormOfPaymentType       `xml:"fopInformation"`
	Dummy          *DummySegmentTypeI       `xml:"dummy"`
	CreditCardData *CreditCardDataGroupType `xml:"creditCardData"`
}

// MeasurementsBatchTypeU ...
type MeasurementsBatchTypeU struct {
	MeasurementQualifier *string          `xml:"measurementQualifier"`
	ValueRange           *ValueRangeTypeU `xml:"valueRange"`
}

// MessageActionDetailsType ...
type MessageActionDetailsType struct {
	Business *MessageFunctionBusinessDetailsType `xml:"business"`
}

// MessageFunctionBusinessDetailsType ...
type MessageFunctionBusinessDetailsType struct {
	Function *string `xml:"function"`
}

// MessageReferenceType ...
type MessageReferenceType struct {
	RetrievalReferenceNumber      *string `xml:"retrievalReferenceNumber"`
	AuthorCharacteristicIndicator *string `xml:"authorCharacteristicIndicator"`
	AuthorResponseCode            *string `xml:"authorResponseCode"`
	CardLevelResult               *string `xml:"cardLevelResult"`
	TerminalType                  *string `xml:"terminalType"`
}

// MethodType ...
type MethodType struct {
	DistributionMethodDetails *DistributionMethodDetails `xml:"distributionMethodDetails"`
}

// MileageTimeDetailsTypeI ...
type MileageTimeDetailsTypeI struct {
	FlightLegMileage *int    `xml:"flightLegMileage"`
	UnitQualifier    *string `xml:"unitQualifier"`
}

// MiscellaneousChargeOrderType ...
type MiscellaneousChargeOrderType struct {
	Type *string `xml:"type"`
}

// MiscellaneousRemarkType ...
type MiscellaneousRemarkType struct {
	Type             *string `xml:"type"`
	Freetext         *string `xml:"freetext"`
	BusinessFunction *string `xml:"businessFunction"`
	Language         *string `xml:"language"`
	Source           *string `xml:"source"`
	Encoding         *string `xml:"encoding"`
}

// MiscellaneousRemarkType151C ...
type MiscellaneousRemarkType151C struct {
	// XMLName      xml.Name `xml:"MiscellaneousRemarkType_151C"`
	Type         *string `xml:"type"`
	Category     *string `xml:"category"`
	Freetext     *string `xml:"freetext"`
	ProviderType *string `xml:"providerType"`
}

// MiscellaneousRemarkType18076C ...
type MiscellaneousRemarkType18076C struct {
	// XMLName  xml.Name `xml:"MiscellaneousRemarkType_18076C"`
	Type     *string `xml:"type"`
	Freetext *string `xml:"freetext"`
}

// MiscellaneousRemarkType198195C ...
type MiscellaneousRemarkType198195C struct {
	// XMLName          xml.Name `xml:"MiscellaneousRemarkType_198195C"`
	Type             *string `xml:"type"`
	Freetext         *string `xml:"freetext"`
	BusinessFunction *string `xml:"businessFunction"`
	Language         *string `xml:"language"`
	Source           *string `xml:"source"`
	Encoding         *string `xml:"encoding"`
}

// MiscellaneousRemarkType210666C ...
type MiscellaneousRemarkType210666C struct {
	// XMLName  xml.Name `xml:"MiscellaneousRemarkType_210666C"`
	Type     *string `xml:"type"`
	Category *string `xml:"category"`
	Freetext *string `xml:"freetext"`
	Status   *string `xml:"status"`
	Encoding *string `xml:"encoding"`
}

// MiscellaneousRemarkType861C ...
type MiscellaneousRemarkType861C struct {
	// XMLName  xml.Name `xml:"MiscellaneousRemarkType_861C"`
	Type     *string `xml:"type"`
	Freetext *string `xml:"freetext"`
}

// MiscellaneousRemarksType ...
type MiscellaneousRemarksType struct {
	RemarkDetails *MiscellaneousRemarkType861C `xml:"remarkDetails"`
}

// MiscellaneousRemarksType12240S ...
type MiscellaneousRemarksType12240S struct {
	// XMLName       xml.Name                       `xml:"MiscellaneousRemarksType_12240S"`
	RemarkDetails *MiscellaneousRemarkType18076C `xml:"remarkDetails"`
}

// MiscellaneousRemarksType136700S ...
type MiscellaneousRemarksType136700S struct {
	// XMLName       xml.Name                        `xml:"MiscellaneousRemarksType_136700S"`
	RemarkDetails *MiscellaneousRemarkType198195C `xml:"remarkDetails"`
}

// MiscellaneousRemarksType211S ...
type MiscellaneousRemarksType211S struct {
	// XMLName            xml.Name                     `xml:"MiscellaneousRemarksType_211S"`
	Remarks            *MiscellaneousRemarkType151C `xml:"remarks"`
	IndividualSecurity []*IndividualSecurityType    `xml:"individualSecurity"`
}

// MiscellaneousRemarksType664S ...
type MiscellaneousRemarksType664S struct {
	// XMLName       xml.Name                 `xml:"MiscellaneousRemarksType_664S"`
	RemarkDetails *MiscellaneousRemarkType `xml:"remarkDetails"`
}

// MonetaryInformationDetailsTypeI ...
type MonetaryInformationDetailsTypeI struct {
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        float64 `xml:"amount"`
}

// MonetaryInformationDetailsTypeI121351C ...
type MonetaryInformationDetailsTypeI121351C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsTypeI_121351C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsTypeI17849C ...
type MonetaryInformationDetailsTypeI17849C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsTypeI_17849C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsTypeI4220C ...
type MonetaryInformationDetailsTypeI4220C struct {
	// XMLName      xml.Name `xml:"MonetaryInformationDetailsTypeI_4220C"`
	Qualifier    *string `xml:"qualifier"`
	Amount       float64 `xml:"amount"`
	CurrencyCode *string `xml:"currencyCode"`
}

// MonetaryInformationDetailsTypeI8308C ...
type MonetaryInformationDetailsTypeI8308C struct {
	// XMLName      xml.Name `xml:"MonetaryInformationDetailsTypeI_8308C"`
	Qualifier    *string `xml:"qualifier"`
	Amount       *string `xml:"amount"`
	CurrencyCode *string `xml:"currencyCode"`
}

// MonetaryInformationDetailsTypeI86190C ...
type MonetaryInformationDetailsTypeI86190C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsTypeI_86190C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
}

// MonetaryInformationDetailsType ...
type MonetaryInformationDetailsType struct {
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        float64 `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsType223822C ...
type MonetaryInformationDetailsType223822C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsType_223822C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsType270802C ...
type MonetaryInformationDetailsType270802C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsType_270802C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        float64 `xml:"amount"`
	Currency      *string `xml:"currency"`
	Location      *string `xml:"location"`
}

// MonetaryInformationTypeI ...
type MonetaryInformationTypeI struct {
	MonetaryDetails      *MonetaryInformationDetailsTypeI17849C `xml:"monetaryDetails"`
	OtherMonetaryDetails *MonetaryInformationDetailsTypeI17849C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationTypeI1689S ...
type MonetaryInformationTypeI1689S struct {
	// XMLName     xml.Name                              `xml:"MonetaryInformationTypeI_1689S"`
	Information *MonetaryInformationDetailsTypeI4220C `xml:"information"`
}

// MonetaryInformationTypeI53012S ...
type MonetaryInformationTypeI53012S struct {
	// XMLName              xml.Name                               `xml:"MonetaryInformationTypeI_53012S"`
	MonetaryDetails      *MonetaryInformationDetailsTypeI86190C `xml:"monetaryDetails"`
	OtherMonetaryDetails *MonetaryInformationDetailsTypeI86190C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationTypeI79012S ...
type MonetaryInformationTypeI79012S struct {
	// XMLName         xml.Name                                `xml:"MonetaryInformationTypeI_79012S"`
	MonetaryDetails *MonetaryInformationDetailsTypeI121351C `xml:"monetaryDetails"`
}

// MonetaryInformationType ...
type MonetaryInformationType struct {
	MonetaryDetails      *MonetaryInformationDetailsType223822C   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType223822C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType193831S ...
type MonetaryInformationType193831S struct {
	// XMLName              xml.Name                                 `xml:"MonetaryInformationType_193831S"`
	MonetaryDetails      *MonetaryInformationDetailsType270802C   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType270802C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType76537S ...
type MonetaryInformationType76537S struct {
	// XMLName              xml.Name                           `xml:"MonetaryInformationType_76537S"`
	MonetaryDetails      *MonetaryInformationDetailsTypeI   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsTypeI `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType94557S ...
type MonetaryInformationType94557S struct {
	// XMLName              xml.Name                          `xml:"MonetaryInformationType_94557S"`
	MonetaryDetails      *MonetaryInformationDetailsType   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType `xml:"otherMonetaryDetails"`
}

// NameAndAddressBatchTypeU ...
type NameAndAddressBatchTypeU struct {
	PartyQualifier   *string                     `xml:"partyQualifier"`
	AddressDetails   *NameAndAddressDetailsTypeU `xml:"addressDetails"`
	PartyNameDetails *PartyNameBatchTypeU        `xml:"partyNameDetails"`
}

// NameAndAddressDetailsTypeU ...
type NameAndAddressDetailsTypeU struct {
	Line1 *string `xml:"line1"`
	Line2 *string `xml:"line2"`
}

// NameInformationTypeU ...
type NameInformationTypeU struct {
	Qualifier *string `xml:"qualifier"`
	Name      *string `xml:"name"`
}

// NameInformationTypeU9747C ...
type NameInformationTypeU9747C struct {
	// XMLName    xml.Name `xml:"NameInformationTypeU_9747C"`
	Qualifier  *string `xml:"qualifier"`
	Name       *string `xml:"name"`
	Identifier *string `xml:"identifier"`
}

// NameTypeU ...
type NameTypeU struct {
	NameInformation *NameInformationTypeU9747C `xml:"nameInformation"`
}

// NameTypeU136701S ...
type NameTypeU136701S struct {
	// XMLName         xml.Name              `xml:"NameTypeU_136701S"`
	NameInformation *NameInformationTypeU `xml:"nameInformation"`
}

// NegoDataType ...
type NegoDataType struct {
	SchemeIndicator       *PricingTicketingDetailsTypeI79032S `xml:"schemeIndicator"`
	NegoSellingFare       *MonetaryInformationTypeI79012S     `xml:"negoSellingFare"`
	NegoOtherFares        []*MonetaryInformationTypeI79012S   `xml:"negoOtherFares"`
	CommissionInformation *CommissionInformationTypeI         `xml:"commissionInformation"`
	TourInformation       *TourInformationTypeI79029S         `xml:"tourInformation"`
	NegoReferences        []*ReferenceInformationTypeI79033S  `xml:"negoReferences"`
}

// NumberOfUnitDetailsTypeI ...
type NumberOfUnitDetailsTypeI struct {
	NumberOfUnit  *int    `xml:"numberOfUnit"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// NumberOfUnitDetailsTypeI18670C ...
type NumberOfUnitDetailsTypeI18670C struct {
	// XMLName       xml.Name `xml:"NumberOfUnitDetailsTypeI_18670C"`
	NumberOfUnit  *int    `xml:"numberOfUnit"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// NumberOfUnitDetailsTypeI2755C ...
type NumberOfUnitDetailsTypeI2755C struct {
	// XMLName   xml.Name `xml:"NumberOfUnitDetailsTypeI_2755C"`
	Number    *int    `xml:"number"`
	Qualifier *string `xml:"qualifier"`
}

// NumberOfUnitDetailsTypeI35712C ...
type NumberOfUnitDetailsTypeI35712C struct {
	// XMLName      xml.Name `xml:"NumberOfUnitDetailsTypeI_35712C"`
	NumberOfUnit *int `xml:"numberOfUnit"`
}

// NumberOfUnitDetailsTypeI46330C ...
type NumberOfUnitDetailsTypeI46330C struct {
	// XMLName       xml.Name `xml:"NumberOfUnitDetailsTypeI_46330C"`
	NumberOfUnit  *int    `xml:"numberOfUnit"`
	UnitQualifier *string `xml:"unitQualifier"`
}

// NumberOfUnitsTypeI ...
type NumberOfUnitsTypeI struct {
	NumberDetail *NumberOfUnitDetailsTypeI2755C `xml:"numberDetail"`
}

// NumberOfUnitsType ...
type NumberOfUnitsType struct {
	QuantityDetails *NumberOfUnitDetailsTypeI35712C `xml:"quantityDetails"`
}

// NumberOfUnitsType76106S ...
type NumberOfUnitsType76106S struct {
	// XMLName         xml.Name                    `xml:"NumberOfUnitsType_76106S"`
	QuantityDetails []*NumberOfUnitDetailsTypeI `xml:"quantityDetails"`
}

// OBfeesGroupType ...
type OBfeesGroupType struct {
	CarrierFee         *SpecificDataInformationTypeI79035S `xml:"carrierFee"`
	FeeDescription     *CodedAttributeType79464S           `xml:"feeDescription"`
	FeeAmount          *MonetaryInformationTypeI79012S     `xml:"feeAmount"`
	FeeTax             []*TaxTypeI79038S                   `xml:"feeTax"`
	VatPropertiesGroup *VatPropertiesGroupType             `xml:"vatPropertiesGroup"`
}

// ODKeyPerformanceDataType ...
type ODKeyPerformanceDataType struct {
	ScheduleChange *string           `xml:"scheduleChange"`
	Oversale       *OversaleDataType `xml:"oversale"`
}

// ONDType ...
type ONDType struct {
	YieldInformations   *MonetaryInformationType76537S          `xml:"yieldInformations"`
	ClassCombinaison    *ProductInformationTypeI76271S          `xml:"classCombinaison"`
	Ondyield            *OriginAndDestinationDetailsTypeI76268S `xml:"ondyield"`
	TripOnD             *OriginAndDestinationDetailsTypeI76268S `xml:"tripOnD"`
	PointOfCommencement *PointOfCommencementTypeI               `xml:"pointOfCommencement"`
}

// OptionElementInformationType ...
type OptionElementInformationType struct {
	MainOffice *string `xml:"mainOffice"`
	Date       *string `xml:"date"`
	Queue      *int    `xml:"queue"`
	Category   *int    `xml:"category"`
	Freetext   *string `xml:"freetext"`
	Time       *string `xml:"time"`
}

// OptionElementType ...
type OptionElementType struct {
	OptionElementInfo  *OptionElementInformationType `xml:"optionElementInfo"`
	IndividualSecurity []*IndividualSecurityType     `xml:"individualSecurity"`
}

// OriginAndDestinationDetailsTypeI ...
type OriginAndDestinationDetailsTypeI struct {
	Origin      *string `xml:"origin"`
	Destination *string `xml:"destination"`
}

// OriginAndDestinationDetailsTypeI3061S ...
type OriginAndDestinationDetailsTypeI3061S struct {
	// XMLName     xml.Name `xml:"OriginAndDestinationDetailsTypeI_3061S"`
	Origin      *string `xml:"origin"`
	Destination *string `xml:"destination"`
}

// OriginAndDestinationDetailsTypeI76268S ...
type OriginAndDestinationDetailsTypeI76268S struct {
	// XMLName     xml.Name `xml:"OriginAndDestinationDetailsTypeI_76268S"`
	Origin      *string `xml:"origin"`
	Destination *string `xml:"destination"`
}

// OriginAndDestinationDetailsTypeI79034S ...
type OriginAndDestinationDetailsTypeI79034S struct {
	// XMLName     xml.Name `xml:"OriginAndDestinationDetailsTypeI_79034S"`
	Origin      *string `xml:"origin"`
	Destination *string `xml:"destination"`
}

// OriginatorDetailsTypeI ...
type OriginatorDetailsTypeI struct {
	CodedCountry  *string `xml:"codedCountry"`
	CodedCurrency *string `xml:"codedCurrency"`
	CodedLanguage *string `xml:"codedLanguage"`
}

// OriginatorIdentificationDetailsTypeI ...
type OriginatorIdentificationDetailsTypeI struct {
	OriginatorId           *int    `xml:"originatorId"`
	InHouseIdentification1 *string `xml:"inHouseIdentification1"`
	InHouseIdentification2 *string `xml:"inHouseIdentification2"`
}

// OriginatorIdentificationDetailsTypeI170735C ...
type OriginatorIdentificationDetailsTypeI170735C struct {
	// XMLName                xml.Name `xml:"OriginatorIdentificationDetailsTypeI_170735C"`
	OriginatorId           *int    `xml:"originatorId"`
	InHouseIdentification1 *string `xml:"inHouseIdentification1"`
	InHouseIdentification2 *int    `xml:"inHouseIdentification2"`
}

// OriginatorIdentificationDetailsTypeI198179C ...
type OriginatorIdentificationDetailsTypeI198179C struct {
	// XMLName      xml.Name `xml:"OriginatorIdentificationDetailsTypeI_198179C"`
	OriginatorId *string `xml:"originatorId"`
}

// OriginatorIdentificationDetailsTypeI297346C ...
type OriginatorIdentificationDetailsTypeI297346C struct {
	// XMLName      xml.Name `xml:"OriginatorIdentificationDetailsTypeI_297346C"`
	OriginatorId string `xml:"originatorId"`
}

// OriginatorIdentificationDetailsTypeI46358C ...
type OriginatorIdentificationDetailsTypeI46358C struct {
	// XMLName                xml.Name `xml:"OriginatorIdentificationDetailsTypeI_46358C"`
	InHouseIdentification1 *string `xml:"inHouseIdentification1"`
	InHouseIdentification2 *string `xml:"inHouseIdentification2"`
}

// OriginatorIdentificationDetailsType ...
type OriginatorIdentificationDetailsType struct {
	OriginatorId           *string `xml:"originatorId"`
	InHouseIdentification1 *string `xml:"inHouseIdentification1"`
	InHouseIdentification2 *int    `xml:"inHouseIdentification2"`
}

// OtherHotelInformationType ...
type OtherHotelInformationType struct {
	CurrencyCode *string `xml:"currencyCode"`
}

// OtherInformationType ...
type OtherInformationType struct {
	Indicator *string `xml:"indicator"`
	QueueType *string `xml:"queueType"`
}

// OtherSegmentDataTypeI ...
type OtherSegmentDataTypeI struct {
	Cabin       *string `xml:"cabin"`
	Subclass    *int    `xml:"subclass"`
	FlightType  *string `xml:"flightType"`
	Overbooking *string `xml:"overbooking"`
}

// OversaleDataType ...
type OversaleDataType struct {
	OversaleNumber    float64   `xml:"oversaleNumber"`
	OversaleIndicator []*string `xml:"oversaleIndicator"`
}

// PNRSupplementaryDataType ...
type PNRSupplementaryDataType struct {
	DataAndSwitchMap *AttributeType94576S `xml:"dataAndSwitchMap"`
}

// POSGroupType ...
type POSGroupType struct {
	SbrUserIdentificationOwn *UserIdentificationType      `xml:"sbrUserIdentificationOwn"`
	SbrSystemDetails         *SystemDetailsInfoType33158S `xml:"sbrSystemDetails"`
	SbrPreferences           *UserPreferencesType         `xml:"sbrPreferences"`
}

// POSGroupType150634G ...
type POSGroupType150634G struct {
	// XMLName                    xml.Name                           `xml:"POSGroupType_150634G"`
	PointOfSaleInformationType *PointOfSaleInformationType        `xml:"pointOfSaleInformationType"`
	SbrUserIdentification      *UserIdentificationType132824S     `xml:"sbrUserIdentification"`
	SbrSystemDetails           *SystemDetailsInfoType             `xml:"sbrSystemDetails"`
	SbrPreferences             *UserPreferencesType               `xml:"sbrPreferences"`
	AgentId                    *TicketAgentInfoType               `xml:"agentId"`
	PointOfSaleDate            *StructuredDateTimeInformationType `xml:"pointOfSaleDate"`
}

// CouponTaxDetailsGroup ...
type CouponTaxDetailsGroup struct {
	// XMLName        xml.Name                         `xml:"couponTaxDetailsGroup"`
	TaxTriggerInfo *DutyTaxFeeDetailsType           `xml:"taxTriggerInfo"`
	TaxDetails     *TaxType                         `xml:"taxDetails"`
	MonetaryInfo   *MonetaryInformationType193831S  `xml:"monetaryInfo"`
	LocationInfo   *PlaceLocationIdentificationType `xml:"locationInfo"`
}

// CouponDetailsGroup ...
type CouponDetailsGroup struct {
	// XMLName               xml.Name                                `xml:"couponDetailsGroup"`
	ProductId             *ReferenceInformationTypeI79009S        `xml:"productId"`
	Rfisc                 *AttributeType79011S                    `xml:"rfisc"`
	FeeOwner              *CompanyInformationType79020S           `xml:"feeOwner"`
	CouponValue           *MonetaryInformationTypeI79012S         `xml:"couponValue"`
	Icw                   []*InConnectionWithType                 `xml:"icw"`
	CouponFlags           []*StatusTypeI                          `xml:"couponFlags"`
	PresentToAtAndRemarks []*FreeTextInformationType79018S        `xml:"presentToAtAndRemarks"`
	FlightConnectionType  *TravelProductInformationTypeI79024S    `xml:"flightConnectionType"`
	FareQualifier         *PricingOrTicketingSubsequentType79023S `xml:"fareQualifier"`
	ValidityDates         []*DateAndTimeInformationTypeI79021S    `xml:"validityDates"`
	BaggageInformation    *ExcessBaggageTypeI                     `xml:"baggageInformation"`
	CouponTaxDetailsGroup []*CouponTaxDetailsGroup                `xml:"couponTaxDetailsGroup"`
}

// DocumentDetailsGroup ...
type DocumentDetailsGroup struct {
	// XMLName                   xml.Name                                   `xml:"documentDetailsGroup"`
	TotalFare                 *MonetaryInformationTypeI79012S            `xml:"totalFare"`
	OtherFares                []*MonetaryInformationTypeI79012S          `xml:"otherFares"`
	TaxInformation            []*TaxTypeI79017S                          `xml:"taxInformation"`
	IssueIdentifier           *PricingTicketingDetailsTypeI              `xml:"issueIdentifier"`
	OriginDestination         *OriginAndDestinationDetailsTypeI79034S    `xml:"originDestination"`
	Rfics                     []*AttributeType79011S                     `xml:"rfics"`
	ManualIndicator           *StatusTypeI                               `xml:"manualIndicator"`
	Flags                     []*StatusTypeI                             `xml:"flags"`
	GeneralIndicators         []*CodedAttributeType79010S                `xml:"generalIndicators"`
	FareCalcRemarks           []*FreeTextInformationType                 `xml:"fareCalcRemarks"`
	OfficeInformation         *UserIdentificationType79019S              `xml:"officeInformation"`
	NegoDetails               *NegoDataType                              `xml:"negoDetails"`
	CreationDate              *StructuredDateTimeInformationType79014S   `xml:"creationDate"`
	OtherDates                []*StructuredDateTimeInformationType79014S `xml:"otherDates"`
	AtcFares                  *ATCdataType                               `xml:"atcFares"`
	AirlineServiceFeeGroup    []*OBfeesGroupType                         `xml:"airlineServiceFeeGroup"`
	CouponDetailsGroup        []*CouponDetailsGroup                      `xml:"couponDetailsGroup"`
	FareComponentDetailsGroup []*FareComponentDtlsType                   `xml:"fareComponentDetailsGroup"`
}

// PPQRdataType ...
type PPQRdataType struct {
	PricingRecordId      *ItemReferencesAndVersionsType94069S `xml:"pricingRecordId"`
	PassengerTattoos     []*ReferenceInformationType65487S    `xml:"passengerTattoos"`
	PtcDiscountCode      []*DiscountInformationType94068S     `xml:"ptcDiscountCode"`
	FareIds              []*ReferenceInformationTypeI79009S   `xml:"fareIds"`
	DocumentDetailsGroup *DocumentDetailsGroup                `xml:"documentDetailsGroup"`
}

// PackageDescriptionType ...
type PackageDescriptionType struct {
	PackageType    *string                    `xml:"packageType"`
	PackageDetails *PackageIdentificationType `xml:"packageDetails"`
}

// PackageIdentificationType ...
type PackageIdentificationType struct {
	PackageDesc *string `xml:"packageDesc"`
}

// PartyIdentifierType ...
type PartyIdentifierType struct {
	PartyCodeQualifier *string `xml:"partyCodeQualifier"`
}

// PartyNameBatchTypeU ...
type PartyNameBatchTypeU struct {
	Name1 *string `xml:"name1"`
}

// PassengerDocumentDetailsType ...
type PassengerDocumentDetailsType struct {
	BirthDate       *string              `xml:"birthDate"`
	DocumentDetails *DocumentDetailsType `xml:"documentDetails"`
}

// PassengerFlightDetailsTypeI ...
type PassengerFlightDetailsTypeI struct {
}

// PaymentDataGroupType ...
type PaymentDataGroupType struct {
	MerchantInformation            *CompanyInformationType94554S             `xml:"merchantInformation"`
	MonetaryInformation            []*MonetaryInformationType94557S          `xml:"monetaryInformation"`
	PaymentId                      []*ItemReferencesAndVersionsType94556S    `xml:"paymentId"`
	ExtendedPaymentInfo            *FrequencyTypeU                           `xml:"extendedPaymentInfo"`
	TransactionDateTime            *StructuredDateTimeInformationType206504S `xml:"transactionDateTime"`
	ExpirationPeriod               *QuantityType94558S                       `xml:"expirationPeriod"`
	DistributionChannelInformation *TerminalIdentificationDescriptionType    `xml:"distributionChannelInformation"`
	PurchaseDescription            *FreeTextInformationType79018S            `xml:"purchaseDescription"`
	FraudScreeningData             *FraudScreeningGroupType                  `xml:"fraudScreeningData"`
	PaymentDataMap                 []*AttributeType94553S                    `xml:"paymentDataMap"`
}

// PaymentDetailsTypeI ...
type PaymentDetailsTypeI struct {
	FormOfPaymentCode *string `xml:"formOfPaymentCode"`
	PaymentType       *string `xml:"paymentType"`
	ServiceToPay      *string `xml:"serviceToPay"`
	ReferenceNumber   *string `xml:"referenceNumber"`
}

// PaymentDetailsTypeU ...
type PaymentDetailsTypeU struct {
	MethodCode   *string `xml:"methodCode"`
	PurposeCode  *string `xml:"purposeCode"`
	Amount       float64 `xml:"amount"`
	CurrencyCode *string `xml:"currencyCode"`
	Date         *string `xml:"date"`
}

// PaymentGroupType ...
type PaymentGroupType struct {
	GroupUsage               *CodedAttributeType127282S  `xml:"groupUsage"`
	PaymentData              *PaymentDataGroupType       `xml:"paymentData"`
	PaymentSupplementaryData []*CodedAttributeType94497S `xml:"paymentSupplementaryData"`
	MopInformation           *MeanOfPaymentDataType      `xml:"mopInformation"`
	Dummy                    *DummySegmentTypeI          `xml:"dummy"`
	MopDetailedData          *DetailedPaymentDataType    `xml:"mopDetailedData"`
}

// PaymentInformationTypeI ...
type PaymentInformationTypeI struct {
	PaymentDetails *PaymentDetailsTypeI `xml:"paymentDetails"`
}

// PaymentInformationTypeU ...
type PaymentInformationTypeU struct {
	PaymentDetails        *PaymentDetailsTypeU        `xml:"paymentDetails"`
	CreditCardInformation *CreditCardInformationTypeU `xml:"creditCardInformation"`
}

// PhoneAndEmailAddressType ...
type PhoneAndEmailAddressType struct {
	PhoneOrEmailType       *string                               `xml:"phoneOrEmailType"`
	TelephoneNumberDetails *StructuredTelephoneNumberType198214C `xml:"telephoneNumberDetails"`
}

// PhoneAndEmailAddressType136723S ...
type PhoneAndEmailAddressType136723S struct {
	// XMLName          xml.Name `xml:"PhoneAndEmailAddressType_136723S"`
	PhoneOrEmailType *string `xml:"phoneOrEmailType"`
	EmailAddress     *string `xml:"emailAddress"`
}

// PhoneAndEmailAddressType32298S ...
type PhoneAndEmailAddressType32298S struct {
	// XMLName          xml.Name                             `xml:"PhoneAndEmailAddressType_32298S"`
	PhoneOrEmailType *string                              `xml:"phoneOrEmailType"`
	TelephoneNumber  *StructuredTelephoneNumberType48448C `xml:"telephoneNumber"`
	EmailAddress     *string                              `xml:"emailAddress"`
}

// PhoneAndEmailAddressType94565S ...
type PhoneAndEmailAddressType94565S struct {
	// XMLName                xml.Name                       `xml:"PhoneAndEmailAddressType_94565S"`
	PhoneOrEmailType       *string                        `xml:"phoneOrEmailType"`
	TelephoneNumberDetails *StructuredTelephoneNumberType `xml:"telephoneNumberDetails"`
	EmailAddress           *string                        `xml:"emailAddress"`
}

// PlaceLocationIdentificationTypeU ...
type PlaceLocationIdentificationTypeU struct {
	LocationType         *string                                       `xml:"locationType"`
	LocationDescription  *LocationIdentificationBatchTypeU             `xml:"locationDescription"`
	FirstLocationDetails *RelatedLocationOneIdentificationTypeU198193C `xml:"firstLocationDetails"`
}

// PlaceLocationIdentificationTypeU136722S ...
type PlaceLocationIdentificationTypeU136722S struct {
	// XMLName             xml.Name                                 `xml:"PlaceLocationIdentificationTypeU_136722S"`
	LocationType        *string                                  `xml:"locationType"`
	LocationDescription *LocationIdentificationBatchTypeU198230C `xml:"locationDescription"`
}

// PlaceLocationIdentificationTypeU24573S ...
type PlaceLocationIdentificationTypeU24573S struct {
	// XMLName              xml.Name                                     `xml:"PlaceLocationIdentificationTypeU_24573S"`
	LocationType         *string                                      `xml:"locationType"`
	FirstLocationDetails *RelatedLocationOneIdentificationTypeU45087C `xml:"firstLocationDetails"`
}

// PlaceLocationIdentificationTypeU25436S ...
type PlaceLocationIdentificationTypeU25436S struct {
	// XMLName              xml.Name                                     `xml:"PlaceLocationIdentificationTypeU_25436S"`
	LocationType         *string                                      `xml:"locationType"`
	LocationDescription  *LocationIdentificationBatchTypeU46344C      `xml:"locationDescription"`
	FirstLocationDetails *RelatedLocationOneIdentificationTypeU46345C `xml:"firstLocationDetails"`
}

// PlaceLocationIdentificationTypeU32347S ...
type PlaceLocationIdentificationTypeU32347S struct {
	// XMLName              xml.Name                                     `xml:"PlaceLocationIdentificationTypeU_32347S"`
	LocationType         *string                                      `xml:"locationType"`
	LocationDescription  *LocationIdentificationBatchTypeU56454C      `xml:"locationDescription"`
	FirstLocationDetails *RelatedLocationOneIdentificationTypeU56455C `xml:"firstLocationDetails"`
}

// PlaceLocationIdentificationTypeU35293S ...
type PlaceLocationIdentificationTypeU35293S struct {
	// XMLName              xml.Name                                     `xml:"PlaceLocationIdentificationTypeU_35293S"`
	LocationType         *string                                      `xml:"locationType"`
	LocationDescription  *LocationIdentificationBatchTypeU60738C      `xml:"locationDescription"`
	FirstLocationDetails *RelatedLocationOneIdentificationTypeU56455C `xml:"firstLocationDetails"`
}

// PlaceLocationIdentificationTypeU8954S ...
type PlaceLocationIdentificationTypeU8954S struct {
	// XMLName               xml.Name                               `xml:"PlaceLocationIdentificationTypeU_8954S"`
	FirstLocationDetails  *RelatedLocationOneIdentificationTypeU `xml:"firstLocationDetails"`
	SecondLocationDetails *RelatedLocationTwoIdentificationTypeU `xml:"secondLocationDetails"`
}

// PlaceLocationIdentificationType ...
type PlaceLocationIdentificationType struct {
	LocationType        *string                          `xml:"locationType"`
	LocationDescription *LocationIdentificationBatchType `xml:"locationDescription"`
}

// PnrHistoryDataType ...
type PnrHistoryDataType struct {
	PreviousRecord *int    `xml:"previousRecord"`
	CurrentRecord  *int    `xml:"currentRecord"`
	ElementType    *string `xml:"elementType"`
	ElementData    *string `xml:"elementData"`
}

// PnrHistoryDataType27157S ...
type PnrHistoryDataType27157S struct {
	// XMLName       xml.Name `xml:"PnrHistoryDataType_27157S"`
	CurrentRecord *int `xml:"currentRecord"`
}

// PointOfCommencementTypeI ...
type PointOfCommencementTypeI struct {
	Location *string `xml:"location"`
}

// PointOfSaleDataTypeI ...
type PointOfSaleDataTypeI struct {
	Classification     *string `xml:"classification"`
	Crs                *string `xml:"crs"`
	PointOfSaleCountry *string `xml:"pointOfSaleCountry"`
}

// PointOfSaleInformationType ...
type PointOfSaleInformationType struct {
	PointOfSale *PartyIdentifierType `xml:"pointOfSale"`
}

// PricingOrTicketingSubsequentType ...
type PricingOrTicketingSubsequentType struct {
	SpecialCondition      *string `xml:"specialCondition"`
	OtherSpecialCondition *string `xml:"otherSpecialCondition"`
}

// PricingOrTicketingSubsequentType145400S ...
type PricingOrTicketingSubsequentType145400S struct {
	// XMLName          xml.Name                        `xml:"PricingOrTicketingSubsequentType_145400S"`
	FareBasisDetails *RateTariffClassInformationType `xml:"fareBasisDetails"`
}

// PricingOrTicketingSubsequentType195222S ...
type PricingOrTicketingSubsequentType195222S struct {
	// XMLName               xml.Name `xml:"PricingOrTicketingSubsequentType_195222S"`
	ItemNumber            *string `xml:"itemNumber"`
	SpecialCondition      *string `xml:"specialCondition"`
	OtherSpecialCondition *string `xml:"otherSpecialCondition"`
}

// PricingOrTicketingSubsequentType79023S ...
type PricingOrTicketingSubsequentType79023S struct {
	// XMLName          xml.Name                         `xml:"PricingOrTicketingSubsequentType_79023S"`
	FareBasisDetails *RateTariffClassInformationTypeI `xml:"fareBasisDetails"`
}

// PricingTicketingDetailsTypeI ...
type PricingTicketingDetailsTypeI struct {
	PriceTicketDetails *PricingTicketingInformationTypeI `xml:"priceTicketDetails"`
	PriceTariffType    *string                           `xml:"priceTariffType"`
}

// PricingTicketingDetailsTypeI79032S ...
type PricingTicketingDetailsTypeI79032S struct {
	// XMLName            xml.Name                          `xml:"PricingTicketingDetailsTypeI_79032S"`
	PriceTicketDetails *PricingTicketingInformationTypeI `xml:"priceTicketDetails"`
}

// PricingTicketingDetailsType ...
type PricingTicketingDetailsType struct {
	IdNumber *string `xml:"idNumber"`
}

// PricingTicketingInformationTypeI ...
type PricingTicketingInformationTypeI struct {
	Indicators *string `xml:"indicators"`
}

// PriorityDetailsType ...
type PriorityDetailsType struct {
	Qualifier       *string `xml:"qualifier"`
	PriorityCode    *string `xml:"priorityCode"`
	TierLevel       *string `xml:"tierLevel"`
	TierDescription *string `xml:"tierDescription"`
}

// ProcessingInformationTypeI ...
type ProcessingInformationTypeI struct {
	ActionQualifier    *string `xml:"actionQualifier"`
	ReferenceQualifier *string `xml:"referenceQualifier"`
}

// ProductAccountDetailsTypeI ...
type ProductAccountDetailsTypeI struct {
	Category       *string `xml:"category"`
	SequenceNumber *string `xml:"sequenceNumber"`
	VersionNumber  *string `xml:"versionNumber"`
	RateClass      *string `xml:"rateClass"`
	ApprovalCode   *string `xml:"approvalCode"`
}

// ProductDataInformationTypeU ...
type ProductDataInformationTypeU struct {
	ProductCategory    *string `xml:"productCategory"`
	ProductCode        *string `xml:"productCode"`
	AddOnIndicator     *int    `xml:"addOnIndicator"`
	ProductDescription *string `xml:"productDescription"`
}

// ProductDateAndTimeTypeU ...
type ProductDateAndTimeTypeU struct {
	DepartureDate *string `xml:"departureDate"`
	DepartureTime *string `xml:"departureTime"`
	ArrivalDate   *string `xml:"arrivalDate"`
	ArrivalTime   *string `xml:"arrivalTime"`
}

// ProductDateAndTimeTypeU46325C ...
type ProductDateAndTimeTypeU46325C struct {
	// XMLName       xml.Name `xml:"ProductDateAndTimeTypeU_46325C"`
	DepartureDate *string `xml:"departureDate"`
	DepartureTime *string `xml:"departureTime"`
	ArrivalDate   *string `xml:"arrivalDate"`
	ArrivalTime   *string `xml:"arrivalTime"`
}

// ProductDateTimeTypeI ...
type ProductDateTimeTypeI struct {
	DepartureDate *string `xml:"departureDate"`
	DepartureTime *string `xml:"departureTime"`
	ArrivalDate   *string `xml:"arrivalDate"`
	ArrivalTime   *string `xml:"arrivalTime"`
}

// ProductDateTimeTypeI270055C ...
type ProductDateTimeTypeI270055C struct {
	// XMLName            xml.Name `xml:"ProductDateTimeTypeI_270055C"`
	DepDate            *string `xml:"depDate"`
	DepTime            *string `xml:"depTime"`
	ArrDate            *string `xml:"arrDate"`
	ArrTime            *string `xml:"arrTime"`
	DayChangeIndicator *int    `xml:"dayChangeIndicator"`
}

// ProductDateTimeTypeI270056C ...
type ProductDateTimeTypeI270056C struct {
	// XMLName            xml.Name `xml:"ProductDateTimeTypeI_270056C"`
	DepDate            *string `xml:"depDate"`
	DepTime            *string `xml:"depTime"`
	ArrDate            *string `xml:"arrDate"`
	ArrTime            *string `xml:"arrTime"`
	DayChangeIndicator *int    `xml:"dayChangeIndicator"`
}

// ProductDateTimeTypeI46338C ...
type ProductDateTimeTypeI46338C struct {
	// XMLName       xml.Name `xml:"ProductDateTimeTypeI_46338C"`
	DepartureDate *string `xml:"departureDate"`
	DepartureTime *string `xml:"departureTime"`
	ArrivalDate   *string `xml:"arrivalDate"`
	ArrivalTime   *string `xml:"arrivalTime"`
}

// ProductDetailsTypeI ...
type ProductDetailsTypeI struct {
	Designator         *string `xml:"designator"`
	AvailabilityStatus *string `xml:"availabilityStatus"`
}

// ProductDetailsTypeI36664C ...
type ProductDetailsTypeI36664C struct {
	// XMLName    xml.Name `xml:"ProductDetailsTypeI_36664C"`
	Designator *string `xml:"designator"`
}

// ProductDetailsType ...
type ProductDetailsType struct {
	Designator *string `xml:"designator"`
	Option     *string `xml:"option"`
}

// ProductFacilitiesTypeI ...
type ProductFacilitiesTypeI struct {
	Entertainement            *string   `xml:"entertainement"`
	EntertainementDescription *string   `xml:"entertainementDescription"`
	ProductQualifier          *string   `xml:"productQualifier"`
	ProductExtensionCode      []*string `xml:"productExtensionCode"`
}

// ProductIdentificationDetailsTypeI ...
type ProductIdentificationDetailsTypeI struct {
	FlightNumber *string `xml:"flightNumber"`
}

// ProductIdentificationDetailsTypeI2786C ...
type ProductIdentificationDetailsTypeI2786C struct {
	// XMLName        xml.Name `xml:"ProductIdentificationDetailsTypeI_2786C"`
	Identification *string `xml:"identification"`
	ClassOfService *string `xml:"classOfService"`
	Subtype        *string `xml:"subtype"`
	Description    *string `xml:"description"`
}

// ProductIdentificationDetailsTypeI46336C ...
type ProductIdentificationDetailsTypeI46336C struct {
	// XMLName      xml.Name `xml:"ProductIdentificationDetailsTypeI_46336C"`
	FlightNumber *string `xml:"flightNumber"`
	BookingClass *string `xml:"bookingClass"`
}

// ProductIdentificationDetailsTypeU ...
type ProductIdentificationDetailsTypeU struct {
	Number *string `xml:"number"`
	Name   *string `xml:"name"`
}

// ProductIdentificationDetailsTypeU46327C ...
type ProductIdentificationDetailsTypeU46327C struct {
	// XMLName     xml.Name `xml:"ProductIdentificationDetailsTypeU_46327C"`
	Code        *string `xml:"code"`
	Type        *string `xml:"type"`
	SubType     *string `xml:"subType"`
	Description *string `xml:"description"`
}

// ProductIdentificationTypeU ...
type ProductIdentificationTypeU struct {
	ProductData *ProductIdentificationDetailsTypeU `xml:"productData"`
}

// ProductInformationTypeI ...
type ProductInformationTypeI struct {
	BookingClassDetails *ProductDetailsTypeI36664C `xml:"bookingClassDetails"`
}

// ProductInformationTypeI76271S ...
type ProductInformationTypeI76271S struct {
	// XMLName             xml.Name               `xml:"ProductInformationTypeI_76271S"`
	BookingClassDetails []*ProductDetailsTypeI `xml:"bookingClassDetails"`
}

// ProductInformationType ...
type ProductInformationType struct {
	BookingClassDetails *ProductDetailsType `xml:"bookingClassDetails"`
}

// ProductTypeDetailsTypeI ...
type ProductTypeDetailsTypeI struct {
	FlightIndicator *string `xml:"flightIndicator"`
}

// ProductTypeDetailsTypeI2787C ...
type ProductTypeDetailsTypeI2787C struct {
	// XMLName xml.Name `xml:"ProductTypeDetailsTypeI_2787C"`
	Detail *string `xml:"detail"`
}

// ProductTypeDetailsTypeI46337C ...
type ProductTypeDetailsTypeI46337C struct {
	// XMLName         xml.Name `xml:"ProductTypeDetailsTypeI_46337C"`
	FlightIndicator *string `xml:"flightIndicator"`
}

// PropertyHeaderDetailsType ...
type PropertyHeaderDetailsType struct {
	ProviderName *string `xml:"providerName"`
	Code         *string `xml:"code"`
	Name         *string `xml:"name"`
}

// ProviderInformationType ...
type ProviderInformationType struct {
	Code              *string `xml:"code"`
	Name              *string `xml:"name"`
	ProductFamilyCode *string `xml:"productFamilyCode"`
}

// QuantityAndActionDetailsTypeU ...
type QuantityAndActionDetailsTypeU struct {
	Quantity   *int    `xml:"quantity"`
	StatusCode *string `xml:"statusCode"`
}

// QuantityAndActionDetailsTypeU56796C ...
type QuantityAndActionDetailsTypeU56796C struct {
	// XMLName    xml.Name `xml:"QuantityAndActionDetailsTypeU_56796C"`
	StatusCode *string `xml:"statusCode"`
}

// QuantityAndActionTypeU ...
type QuantityAndActionTypeU struct {
	QuantityActionDetails []*QuantityAndActionDetailsTypeU `xml:"quantityActionDetails"`
}

// QuantityAndActionTypeU32609S ...
type QuantityAndActionTypeU32609S struct {
	// XMLName    xml.Name                             `xml:"QuantityAndActionTypeU_32609S"`
	AccoStatus *QuantityAndActionDetailsTypeU56796C `xml:"accoStatus"`
}

// QuantityDetailsTypeI ...
type QuantityDetailsTypeI struct {
	Qualifier *string `xml:"qualifier"`
	Value     *int    `xml:"value"`
}

// QuantityDetailsTypeI142179C ...
type QuantityDetailsTypeI142179C struct {
	// XMLName   xml.Name `xml:"QuantityDetailsTypeI_142179C"`
	Qualifier *string `xml:"qualifier"`
	Value     *int    `xml:"value"`
	Unit      *string `xml:"unit"`
}

// QuantityDetailsTypeI198209C ...
type QuantityDetailsTypeI198209C struct {
	// XMLName   xml.Name `xml:"QuantityDetailsTypeI_198209C"`
	Qualifier *string `xml:"qualifier"`
	Value     *int    `xml:"value"`
	Unit      *string `xml:"unit"`
}

// QuantityDetailsTypeI46334C ...
type QuantityDetailsTypeI46334C struct {
	// XMLName   xml.Name `xml:"QuantityDetailsTypeI_46334C"`
	Qualifier *string `xml:"qualifier"`
	Value     *int    `xml:"value"`
	Unit      *string `xml:"unit"`
}

// QuantityDetailsType ...
type QuantityDetailsType struct {
	Qualifier *string `xml:"qualifier"`
	Value     float64 `xml:"value"`
	Unit      *string `xml:"unit"`
}

// QuantityTypeI ...
type QuantityTypeI struct {
	QuantityDetails *QuantityDetailsTypeI142179C `xml:"quantityDetails"`
}

// QuantityTypeI65488S ...
type QuantityTypeI65488S struct {
	// XMLName         xml.Name              `xml:"QuantityTypeI_65488S"`
	QuantityDetails *QuantityDetailsTypeI `xml:"quantityDetails"`
}

// QuantityType ...
type QuantityType struct {
	QuantityDetails *QuantityDetailsType `xml:"quantityDetails"`
}

// QuantityType25433S ...
type QuantityType25433S struct {
	// XMLName         xml.Name                    `xml:"QuantityType_25433S"`
	QuantityDetails *QuantityDetailsTypeI46334C `xml:"quantityDetails"`
}

// QuantityType94558S ...
type QuantityType94558S struct {
	// XMLName         xml.Name                       `xml:"QuantityType_94558S"`
	QuantityDetails []*QuantityDetailsTypeI142179C `xml:"quantityDetails"`
}

// QueueDetailsType ...
type QueueDetailsType struct {
	QueueNum1 *int    `xml:"queueNum1"`
	QueueName *string `xml:"queueName"`
}

// QueueType ...
type QueueType struct {
	QueueDetail    *QueueDetailsType     `xml:"queueDetail"`
	CategoryDetail *GategoryType         `xml:"categoryDetail"`
	DateRange      *DateRangeType        `xml:"dateRange"`
	Informations   *OtherInformationType `xml:"informations"`
}

// QuotaRelatedInformationType ...
type QuotaRelatedInformationType struct {
	QuotaCounterName        *string `xml:"quotaCounterName"`
	MaxQuantity             *int    `xml:"maxQuantity"`
	Availability            *int    `xml:"availability"`
	QuotaReachedReplyStatus *string `xml:"quotaReachedReplyStatus"`
	QuotaReachedMsgNb       *string `xml:"quotaReachedMsgNb"`
	QuotaNotReachedMsgNb    *string `xml:"quotaNotReachedMsgNb"`
	Counter                 *int    `xml:"counter"`
	QuotaType               *string `xml:"quotaType"`
	QuotaGrade              *string `xml:"quotaGrade"`
}

// RailLegDataType ...
type RailLegDataType struct {
	TrainProductInfo *TrainProductInformationType32331S         `xml:"trainProductInfo"`
	ReservableStatus *QuantityAndActionTypeU32609S              `xml:"reservableStatus"`
	LegDateTime      []*StructuredDateTimeInformationType32362S `xml:"legDateTime"`
	DepLocation      *PlaceLocationIdentificationTypeU32347S    `xml:"depLocation"`
	ArrLocation      *PlaceLocationIdentificationTypeU32347S    `xml:"arrLocation"`
	LegReference     *ItemNumberTypeU33258S                     `xml:"legReference"`
}

// RailSeatConfigurationType ...
type RailSeatConfigurationType struct {
	SeatSpace            *string   `xml:"seatSpace"`
	CoachType            *string   `xml:"coachType"`
	SeatEquipment        *string   `xml:"seatEquipment"`
	SeatPosition         *string   `xml:"seatPosition"`
	SeatDirection        *string   `xml:"seatDirection"`
	SeatDeck             *string   `xml:"seatDeck"`
	SpecialPassengerType []*string `xml:"specialPassengerType"`
}

// RailSeatPreferencesType ...
type RailSeatPreferencesType struct {
	SeatRequestFunction *string                     `xml:"seatRequestFunction"`
	SmokingIndicator    *string                     `xml:"smokingIndicator"`
	ClassDetails        *ClassDetailsType52782C     `xml:"classDetails"`
	SeatConfiguration   *RailSeatConfigurationType  `xml:"seatConfiguration"`
	SleeperDescription  *RailSleeperDescriptionType `xml:"sleeperDescription"`
}

// RailSeatReferenceInformationType ...
type RailSeatReferenceInformationType struct {
	RailSeatReferenceDetails *SeatReferenceInformationType `xml:"railSeatReferenceDetails"`
}

// RailSleeperDescriptionType ...
type RailSleeperDescriptionType struct {
	BerthDeck      *string `xml:"berthDeck"`
	CabinPosition  *string `xml:"cabinPosition"`
	CabinShareType *string `xml:"cabinShareType"`
	CabinOccupancy *string `xml:"cabinOccupancy"`
}

// RangeDetailsTypeI ...
type RangeDetailsTypeI struct {
	RangeQualifier *string     `xml:"rangeQualifier"`
	RangeDetails   *RangeTypeI `xml:"rangeDetails"`
}

// RangeDetailsTypeU ...
type RangeDetailsTypeU struct {
	RangeQualifier *string     `xml:"rangeQualifier"`
	RangeDetails   *RangeTypeU `xml:"rangeDetails"`
}

// RangeTypeI ...
type RangeTypeI struct {
	DataType *string `xml:"dataType"`
	Min      *int    `xml:"min"`
	Max      *int    `xml:"max"`
}

// RangeTypeU ...
type RangeTypeU struct {
	DataType     *string `xml:"dataType"`
	MinOccupancy *int    `xml:"minOccupancy"`
	MaxOccupancy *int    `xml:"maxOccupancy"`
}

// RateCodeRestrictedType ...
type RateCodeRestrictedType struct {
	RateCode *string `xml:"rateCode"`
}

// RateIndicatorsType ...
type RateIndicatorsType struct {
	RateChangeIndicator *string `xml:"rateChangeIndicator"`
}

// RateInformationDetailsType ...
type RateInformationDetailsType struct {
	RatePlan *string `xml:"ratePlan"`
}

// RateInformationTypeI ...
type RateInformationTypeI struct {
	Category *string `xml:"category"`
}

// RateInformationTypeI198204C ...
type RateInformationTypeI198204C struct {
	// XMLName  xml.Name `xml:"RateInformationTypeI_198204C"`
	Category *string `xml:"category"`
}

// RateInformationTypeI50732C ...
type RateInformationTypeI50732C struct {
	// XMLName   xml.Name `xml:"RateInformationTypeI_50732C"`
	FareGroup *string `xml:"fareGroup"`
}

// RateInformationType ...
type RateInformationType struct {
	RatePrice     *RatePriceType              `xml:"ratePrice"`
	RateInfo      *RateInformationDetailsType `xml:"rateInfo"`
	RateIndicator *RateIndicatorsType         `xml:"rateIndicator"`
}

// RatePriceType ...
type RatePriceType struct {
	RateAmount float64 `xml:"rateAmount"`
}

// RateTariffClassInformationTypeI ...
type RateTariffClassInformationTypeI struct {
	RateTariffClass          *string `xml:"rateTariffClass"`
	RateTariffIndicator      *string `xml:"rateTariffIndicator"`
	OtherRateTariffClass     *string `xml:"otherRateTariffClass"`
	OtherRateTariffIndicator *string `xml:"otherRateTariffIndicator"`
}

// RateTariffClassInformationType ...
type RateTariffClassInformationType struct {
	RateTariffClass      *string `xml:"rateTariffClass"`
	OtherRateTariffClass *string `xml:"otherRateTariffClass"`
}

// RateTypesTypeU ...
type RateTypesTypeU struct {
	RateCode *string `xml:"rateCode"`
}

// ReferenceInfoType ...
type ReferenceInfoType struct {
	Reference []*ReferencingDetailsType `xml:"reference"`
}

// ReferenceInfoType145406S ...
type ReferenceInfoType145406S struct {
	// XMLName          xml.Name                       `xml:"ReferenceInfoType_145406S"`
	ReferenceDetails *ReferencingDetailsType209980C `xml:"referenceDetails"`
}

// ReferenceInfoType195220S ...
type ReferenceInfoType195220S struct {
	// XMLName          xml.Name                         `xml:"ReferenceInfoType_195220S"`
	ReferenceDetails []*ReferencingDetailsType272902C `xml:"referenceDetails"`
}

// ReferenceInfoType227787S ...
type ReferenceInfoType227787S struct {
	// XMLName   xml.Name                  `xml:"ReferenceInfoType_227787S"`
	Reference []*ReferencingDetailsType `xml:"reference"`
}

// ReferenceInfoType227788S ...
type ReferenceInfoType227788S struct {
	// XMLName   xml.Name                         `xml:"ReferenceInfoType_227788S"`
	Reference []*ReferencingDetailsType311955C `xml:"reference"`
}

// ReferenceInfoType25422S ...
type ReferenceInfoType25422S struct {
	// XMLName          xml.Name                       `xml:"ReferenceInfoType_25422S"`
	ReferenceDetails *ReferencingDetailsTypeI46317C `xml:"referenceDetails"`
}

// ReferenceInfoType94524S ...
type ReferenceInfoType94524S struct {
	// XMLName          xml.Name                       `xml:"ReferenceInfoType_94524S"`
	ReferenceDetails *ReferencingDetailsType142140C `xml:"referenceDetails"`
}

// ReferenceInfoType94566S ...
type ReferenceInfoType94566S struct {
	// XMLName          xml.Name                       `xml:"ReferenceInfoType_94566S"`
	ReferenceDetails *ReferencingDetailsType142187C `xml:"referenceDetails"`
}

// ReferenceInformationTypeI ...
type ReferenceInformationTypeI struct {
	ReferenceDetails *ReferencingDetailsTypeI185716C `xml:"referenceDetails"`
}

// ReferenceInformationTypeI136704S ...
type ReferenceInformationTypeI136704S struct {
	// XMLName          xml.Name                        `xml:"ReferenceInformationTypeI_136704S"`
	ReferenceDetails *ReferencingDetailsTypeI198199C `xml:"referenceDetails"`
}

// ReferenceInformationTypeI25132S ...
type ReferenceInformationTypeI25132S struct {
	// XMLName          xml.Name                         `xml:"ReferenceInformationTypeI_25132S"`
	ReferenceDetails []*ReferencingDetailsTypeI45901C `xml:"referenceDetails"`
}

// ReferenceInformationTypeI79009S ...
type ReferenceInformationTypeI79009S struct {
	// XMLName          xml.Name                 `xml:"ReferenceInformationTypeI_79009S"`
	ReferenceDetails *ReferencingDetailsTypeI `xml:"referenceDetails"`
}

// ReferenceInformationTypeI79033S ...
type ReferenceInformationTypeI79033S struct {
	// XMLName          xml.Name                        `xml:"ReferenceInformationTypeI_79033S"`
	ReferenceDetails *ReferencingDetailsTypeI121390C `xml:"referenceDetails"`
}

// ReferenceInformationTypeI83551S ...
type ReferenceInformationTypeI83551S struct {
	// XMLName          xml.Name                        `xml:"ReferenceInformationTypeI_83551S"`
	ReferenceDetails *ReferencingDetailsTypeI127514C `xml:"referenceDetails"`
}

// ReferenceInformationType ...
type ReferenceInformationType struct {
	ReferenceDetails *ReferencingDetailsTypeI17164C `xml:"referenceDetails"`
}

// ReferenceInformationType65487S ...
type ReferenceInformationType65487S struct {
	// XMLName            xml.Name                 `xml:"ReferenceInformationType_65487S"`
	PassengerReference *ReferencingDetailsTypeI `xml:"passengerReference"`
}

// ReferencingDetailsTypeI ...
type ReferencingDetailsTypeI struct {
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsTypeI121390C ...
type ReferencingDetailsTypeI121390C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_121390C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsTypeI127514C ...
type ReferencingDetailsTypeI127514C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_127514C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsTypeI17164C ...
type ReferencingDetailsTypeI17164C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_17164C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsTypeI185716C ...
type ReferencingDetailsTypeI185716C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_185716C"`
	Type *string `xml:"type"`
}

// ReferencingDetailsTypeI198199C ...
type ReferencingDetailsTypeI198199C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_198199C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsTypeI36941C ...
type ReferencingDetailsTypeI36941C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_36941C"`
	Value *string `xml:"value"`
}

// ReferencingDetailsTypeI45901C ...
type ReferencingDetailsTypeI45901C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_45901C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsTypeI46317C ...
type ReferencingDetailsTypeI46317C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsTypeI_46317C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType ...
type ReferencingDetailsType struct {
	Qualifier *string `xml:"qualifier"`
	Number    *string `xml:"number"`
}

// ReferencingDetailsType127526C ...
type ReferencingDetailsType127526C struct {
	// XMLName   xml.Name `xml:"ReferencingDetailsType_127526C"`
	Qualifier *string `xml:"qualifier"`
	Number    *int    `xml:"number"`
}

// ReferencingDetailsType142140C ...
type ReferencingDetailsType142140C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsType_142140C"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType142187C ...
type ReferencingDetailsType142187C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsType_142187C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType209980C ...
type ReferencingDetailsType209980C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsType_209980C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType272902C ...
type ReferencingDetailsType272902C struct {
	// XMLName xml.Name `xml:"ReferencingDetailsType_272902C"`
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType2780C ...
type ReferencingDetailsType2780C struct {
	// XMLName           xml.Name `xml:"ReferencingDetailsType_2780C"`
	MarriageQualifier *string `xml:"marriageQualifier"`
	TatooNum          *string `xml:"tatooNum"`
}

// ReferencingDetailsType311955C ...
type ReferencingDetailsType311955C struct {
	// XMLName   xml.Name `xml:"ReferencingDetailsType_311955C"`
	Qualifier *string `xml:"qualifier"`
	Number    *string `xml:"number"`
}

// RelatedLocationOneIdentificationTypeU ...
type RelatedLocationOneIdentificationTypeU struct {
	Code *string `xml:"code"`
}

// RelatedLocationOneIdentificationTypeU198193C ...
type RelatedLocationOneIdentificationTypeU198193C struct {
	// XMLName   xml.Name `xml:"RelatedLocationOneIdentificationTypeU_198193C"`
	Code      *string `xml:"code"`
	Qualifier *string `xml:"qualifier"`
	Agency    *string `xml:"agency"`
}

// RelatedLocationOneIdentificationTypeU45087C ...
type RelatedLocationOneIdentificationTypeU45087C struct {
	// XMLName xml.Name `xml:"RelatedLocationOneIdentificationTypeU_45087C"`
	Code *string `xml:"code"`
}

// RelatedLocationOneIdentificationTypeU46345C ...
type RelatedLocationOneIdentificationTypeU46345C struct {
	// XMLName   xml.Name `xml:"RelatedLocationOneIdentificationTypeU_46345C"`
	Code      *string `xml:"code"`
	Qualifier *string `xml:"qualifier"`
}

// RelatedLocationOneIdentificationTypeU56455C ...
type RelatedLocationOneIdentificationTypeU56455C struct {
	// XMLName   xml.Name `xml:"RelatedLocationOneIdentificationTypeU_56455C"`
	Code      *string `xml:"code"`
	Qualifier *string `xml:"qualifier"`
}

// RelatedLocationTwoIdentificationTypeU ...
type RelatedLocationTwoIdentificationTypeU struct {
	Code *string `xml:"code"`
}

// RelatedProductInformationTypeI ...
type RelatedProductInformationTypeI struct {
	Quantity *int   `xml:"quantity"`
	Status   string `xml:"status"`
}

// ReservationControlInformationDetailsTypeI ...
type ReservationControlInformationDetailsTypeI struct {
	CompanyId     *string `xml:"companyId"`
	ControlNumber *string `xml:"controlNumber"`
	ControlType   *string `xml:"controlType"`
	Date          *string `xml:"date"`
	Time          *string `xml:"time"`
}

// ReservationControlInformationDetailsTypeI16352C ...
type ReservationControlInformationDetailsTypeI16352C struct {
	// XMLName       xml.Name `xml:"ReservationControlInformationDetailsTypeI_16352C"`
	ControlNumber *string `xml:"controlNumber"`
	ControlType   *string `xml:"controlType"`
}

// ReservationControlInformationDetailsTypeI18446C ...
type ReservationControlInformationDetailsTypeI18446C struct {
	// XMLName       xml.Name `xml:"ReservationControlInformationDetailsTypeI_18446C"`
	ControlNumber *string `xml:"controlNumber"`
}

// ReservationControlInformationDetailsTypeI35709C ...
type ReservationControlInformationDetailsTypeI35709C struct {
	// XMLName       xml.Name `xml:"ReservationControlInformationDetailsTypeI_35709C"`
	ControlNumber *string `xml:"controlNumber"`
}

// ReservationControlInformationDetailsTypeU ...
type ReservationControlInformationDetailsTypeU struct {
	TourOperatorCode             *string `xml:"tourOperatorCode"`
	ReservationControlNumberQual *string `xml:"reservationControlNumberQual"`
	ReservationControlNumber     *string `xml:"reservationControlNumber"`
}

// ReservationControlInformationDetailsTypeU55378C ...
type ReservationControlInformationDetailsTypeU55378C struct {
	// XMLName xml.Name `xml:"ReservationControlInformationDetailsTypeU_55378C"`
	Value *string `xml:"value"`
}

// ReservationControlInformationDetailsType ...
type ReservationControlInformationDetailsType struct {
	CompanyId     *string `xml:"companyId"`
	ControlNumber *string `xml:"controlNumber"`
	ControlType   *string `xml:"controlType"`
	Date          *string `xml:"date"`
	Time          *int    `xml:"time"`
}

// ReservationControlInformationTypeI ...
type ReservationControlInformationTypeI struct {
	Reservation *ReservationControlInformationDetailsTypeI18446C `xml:"reservation"`
}

// ReservationControlInformationTypeI196503S ...
type ReservationControlInformationTypeI196503S struct {
	// XMLName     xml.Name                                   `xml:"ReservationControlInformationTypeI_196503S"`
	Reservation *ReservationControlInformationDetailsTypeI `xml:"reservation"`
}

// ReservationControlInformationTypeI20153S ...
type ReservationControlInformationTypeI20153S struct {
	// XMLName     xml.Name                                         `xml:"ReservationControlInformationTypeI_20153S"`
	Reservation *ReservationControlInformationDetailsTypeI35709C `xml:"reservation"`
}

// ReservationControlInformationTypeI8957S ...
type ReservationControlInformationTypeI8957S struct {
	// XMLName     xml.Name                                           `xml:"ReservationControlInformationTypeI_8957S"`
	Reservation []*ReservationControlInformationDetailsTypeI16352C `xml:"reservation"`
}

// ReservationControlInformationTypeU ...
type ReservationControlInformationTypeU struct {
	ReservationControlId *ReservationControlInformationDetailsTypeU `xml:"reservationControlId"`
}

// ReservationControlInformationTypeU31804S ...
type ReservationControlInformationTypeU31804S struct {
	// XMLName          xml.Name                                         `xml:"ReservationControlInformationTypeU_31804S"`
	ReferenceDetails *ReservationControlInformationDetailsTypeU55378C `xml:"referenceDetails"`
}

// ReservationControlInformationType ...
type ReservationControlInformationType struct {
	Reservation []*ReservationControlInformationDetailsType `xml:"reservation"`
}

// ReservationSecurityInformationType ...
type ReservationSecurityInformationType struct {
	ResponsibilityInformation *ResponsibilityInformationType6835C `xml:"responsibilityInformation"`
	QueueingInformation       *TicketInformationType5120C         `xml:"queueingInformation"`
	CityCode                  *string                             `xml:"cityCode"`
	SecondRpInformation       *SecondRpLineInformationType        `xml:"secondRpInformation"`
}

// ReservationSecurityInformationType204487S ...
type ReservationSecurityInformationType204487S struct {
	// XMLName                   xml.Name                            `xml:"ReservationSecurityInformationType_204487S"`
	ResponsibilityInformation *ResponsibilityInformationType      `xml:"responsibilityInformation"`
	QueueingInformation       *TicketInformationType5120C         `xml:"queueingInformation"`
	CityCode                  *string                             `xml:"cityCode"`
	SecondRpInformation       *SecondRpLineInformationType283891C `xml:"secondRpInformation"`
}

// ResponseIdentificationType ...
type ResponseIdentificationType struct {
	TransacIdentifier *string `xml:"transacIdentifier"`
	ValidationCode    *string `xml:"validationCode"`
	BanknetRefNumber  *string `xml:"banknetRefNumber"`
	BanknetDate       *string `xml:"banknetDate"`
}

// ResponsibilityInformationType ...
type ResponsibilityInformationType struct {
	TypeOfPnrElement *string `xml:"typeOfPnrElement"`
	AgentId          *string `xml:"agentId"`
	OfficeId         *string `xml:"officeId"`
	IataCode         *string `xml:"iataCode"`
}

// ResponsibilityInformationType6835C ...
type ResponsibilityInformationType6835C struct {
	// XMLName          xml.Name `xml:"ResponsibilityInformationType_6835C"`
	TypeOfPnrElement *string `xml:"typeOfPnrElement"`
	AgentId          *string `xml:"agentId"`
	OfficeId         *string `xml:"officeId"`
	IataCode         *int    `xml:"iataCode"`
}

// RoomDetailsType ...
type RoomDetailsType struct {
	Occupancy *int    `xml:"occupancy"`
	TypeCode  *string `xml:"typeCode"`
}

// RuleDetailsTypeU ...
type RuleDetailsTypeU struct {
	Type         *string `xml:"type"`
	Quantity     *int    `xml:"quantity"`
	QuantityUnit *string `xml:"quantityUnit"`
}

// RuleDetailsTypeU198224C ...
type RuleDetailsTypeU198224C struct {
	// XMLName         xml.Name `xml:"RuleDetailsTypeU_198224C"`
	Type            *string `xml:"type"`
	Quantity        *int    `xml:"quantity"`
	QuantityUnit    *string `xml:"quantityUnit"`
	Qualifier       *string `xml:"qualifier"`
	DaysOfOperation *string `xml:"daysOfOperation"`
	Amount          *int    `xml:"amount"`
	Currency        *string `xml:"currency"`
}

// RuleInformationTypeU ...
type RuleInformationTypeU struct {
	RuleDetails       *RuleDetailsTypeU `xml:"ruleDetails"`
	RuleStatusDetails *RuleStatusTypeU  `xml:"ruleStatusDetails"`
}

// RuleInformationTypeU136720S ...
type RuleInformationTypeU136720S struct {
	// XMLName     xml.Name                   `xml:"RuleInformationTypeU_136720S"`
	RuleDetails []*RuleDetailsTypeU198224C `xml:"ruleDetails"`
	RuleText    *RuleTextTypeU             `xml:"ruleText"`
}

// RuleStatusTypeU ...
type RuleStatusTypeU struct {
	StatusType       *string `xml:"statusType"`
	ProcessIndicator *string `xml:"processIndicator"`
}

// RuleTextTypeU ...
type RuleTextTypeU struct {
	TextType *string   `xml:"textType"`
	FreeText []*string `xml:"freeText"`
}

// SSRPackInformation ...
type SSRPackInformation struct {
	ElementCounter     *ItemNumberType                          `xml:"elementCounter"`
	BookingMethod      *ActionIdentificationType                `xml:"bookingMethod"`
	PricingInformation *PricingOrTicketingSubsequentType195222S `xml:"pricingInformation"`
	ServiceIdentifier  *SpecialRequirementsDetailsType195223S   `xml:"serviceIdentifier"`
	TableValues        []*AttributeType195221S                  `xml:"tableValues"`
	ElementReference   *ReferenceInfoType195220S                `xml:"elementReference"`
}

// SeatReferenceInformationType ...
type SeatReferenceInformationType struct {
	CoachNumber *string `xml:"coachNumber"`
	DeckNumber  *string `xml:"deckNumber"`
	SeatNumber  *string `xml:"seatNumber"`
}

// SeatRequestParametersTypeI ...
type SeatRequestParametersTypeI struct {
	GenericDetails *GenericDetailsTypeI `xml:"genericDetails"`
}

// SecondRpLineInformationType ...
type SecondRpLineInformationType struct {
	CreationOfficeId *string `xml:"creationOfficeId"`
	AgentSignature   *string `xml:"agentSignature"`
	CreationDate     *string `xml:"creationDate"`
	CreatorIataCode  *int    `xml:"creatorIataCode"`
	CreationTime     *string `xml:"creationTime"`
}

// SecondRpLineInformationType283891C ...
type SecondRpLineInformationType283891C struct {
	// XMLName          xml.Name `xml:"SecondRpLineInformationType_283891C"`
	CreationOfficeId *string `xml:"creationOfficeId"`
	AgentSignature   *string `xml:"agentSignature"`
	CreationDate     *string `xml:"creationDate"`
	CreatorIataCode  *string `xml:"creatorIataCode"`
	CreationTime     *string `xml:"creationTime"`
}

// SecurityInformationType ...
type SecurityInformationType struct {
	CreationDate *string `xml:"creationDate"`
	AgentCode    *string `xml:"agentCode"`
	OfficeId     *string `xml:"officeId"`
}

// SegmentCabinIdentificationType ...
type SegmentCabinIdentificationType struct {
	CabinCode *string `xml:"cabinCode"`
}

// SegmentGroupingInformationType ...
type SegmentGroupingInformationType struct {
	GroupingCode   *string                        `xml:"groupingCode"`
	MarriageDetail []*ReferencingDetailsType2780C `xml:"marriageDetail"`
}

// SelectionDetailsInformationTypeI ...
type SelectionDetailsInformationTypeI struct {
	Option            *string `xml:"option"`
	OptionInformation *string `xml:"optionInformation"`
}

// SelectionDetailsInformationTypeI198215C ...
type SelectionDetailsInformationTypeI198215C struct {
	// XMLName xml.Name `xml:"SelectionDetailsInformationTypeI_198215C"`
	Option *string `xml:"option"`
}

// SelectionDetailsTypeI ...
type SelectionDetailsTypeI struct {
	SelectionDetails *SelectionDetailsInformationTypeI198215C `xml:"selectionDetails"`
}

// SelectionDetailsTypeI201122S ...
type SelectionDetailsTypeI201122S struct {
	// XMLName   xml.Name                            `xml:"SelectionDetailsTypeI_201122S"`
	Selection []*SelectionDetailsInformationTypeI `xml:"selection"`
}

// SequenceDetailsTypeU ...
type SequenceDetailsTypeU struct {
	ActionRequest   *string                         `xml:"actionRequest"`
	SequenceDetails *SequenceInformationTypeU24073C `xml:"sequenceDetails"`
}

// SequenceDetailsTypeU94494S ...
type SequenceDetailsTypeU94494S struct {
	// XMLName         xml.Name                  `xml:"SequenceDetailsTypeU_94494S"`
	SequenceDetails *SequenceInformationTypeU `xml:"sequenceDetails"`
}

// SequenceInformationTypeU ...
type SequenceInformationTypeU struct {
	Number *int `xml:"number"`
}

// SequenceInformationTypeU24073C ...
type SequenceInformationTypeU24073C struct {
	// XMLName xml.Name `xml:"SequenceInformationTypeU_24073C"`
	Number *string `xml:"number"`
}

// ShipIdentificationDetailsType ...
type ShipIdentificationDetailsType struct {
	Code           *string `xml:"code"`
	Name           *string `xml:"name"`
	CruiseLineCode *string `xml:"cruiseLineCode"`
}

// ShipIdentificationDetailsType45069C ...
type ShipIdentificationDetailsType45069C struct {
	// XMLName xml.Name `xml:"ShipIdentificationDetailsType_45069C"`
	Name *string `xml:"name"`
}

// ShipIdentificationType ...
type ShipIdentificationType struct {
	ShipDetails *ShipIdentificationDetailsType45069C `xml:"shipDetails"`
}

// ShipIdentificationType8952S ...
type ShipIdentificationType8952S struct {
	// XMLName     xml.Name                       `xml:"ShipIdentificationType_8952S"`
	ShipDetails *ShipIdentificationDetailsType `xml:"shipDetails"`
}

// SpecialRequirementsDataDetailsTypeI ...
type SpecialRequirementsDataDetailsTypeI struct {
	Data     *string   `xml:"data"`
	CrossRef *string   `xml:"crossRef"`
	SeatType []*string `xml:"seatType"`
}

// SpecialRequirementsDataDetailsType ...
type SpecialRequirementsDataDetailsType struct {
	SeatNumber         *string   `xml:"seatNumber"`
	SeatCharacteristic []*string `xml:"seatCharacteristic"`
}

// SpecialRequirementsDataDetailsType272905C ...
type SpecialRequirementsDataDetailsType272905C struct {
	// XMLName              xml.Name  `xml:"SpecialRequirementsDataDetailsType_272905C"`
	SeatNumber           *string   `xml:"seatNumber"`
	MeasureUnitQualifier *string   `xml:"measureUnitQualifier"`
	CrossRef             *string   `xml:"crossRef"`
	SeatCharacteristics  []*string `xml:"seatCharacteristics"`
}

// SpecialRequirementsDetailsTypeI ...
type SpecialRequirementsDetailsTypeI struct {
	Ssr  *SpecialRequirementsTypeDetailsTypeI   `xml:"ssr"`
	Ssrb []*SpecialRequirementsDataDetailsTypeI `xml:"ssrb"`
}

// SpecialRequirementsDetailsType ...
type SpecialRequirementsDetailsType struct {
	SeatDetails *SpecialRequirementsDataDetailsType `xml:"seatDetails"`
}

// SpecialRequirementsDetailsType195223S ...
type SpecialRequirementsDetailsType195223S struct {
	// XMLName                 xml.Name                                   `xml:"SpecialRequirementsDetailsType_195223S"`
	SpecialRequirementsInfo *SpecialRequirementsTypeDetailsType        `xml:"specialRequirementsInfo"`
	SeatDetails             *SpecialRequirementsDataDetailsType272905C `xml:"seatDetails"`
	QuotaInfo               *QuotaRelatedInformationType               `xml:"quotaInfo"`
}

// SpecialRequirementsTypeDetailsTypeI ...
type SpecialRequirementsTypeDetailsTypeI struct {
	Type                *string   `xml:"type"`
	Status              *string   `xml:"status"`
	Quantity            *int      `xml:"quantity"`
	CompanyId           *string   `xml:"companyId"`
	Indicator           *string   `xml:"indicator"`
	ProcessingIndicator *string   `xml:"processingIndicator"`
	Boardpoint          *string   `xml:"boardpoint"`
	Offpoint            *string   `xml:"offpoint"`
	FreeText            []*string `xml:"freeText"`
}

// SpecialRequirementsTypeDetailsType ...
type SpecialRequirementsTypeDetailsType struct {
	SsrCode          *string   `xml:"ssrCode"`
	ActionCode       *string   `xml:"actionCode"`
	NumberInParty    *int      `xml:"numberInParty"`
	AirlineCode      *string   `xml:"airlineCode"`
	ServiceType      *string   `xml:"serviceType"`
	OtherServiceType *string   `xml:"otherServiceType"`
	BoardPoint       *string   `xml:"boardPoint"`
	OffPoint         *string   `xml:"offPoint"`
	ServiceFreeText  []*string `xml:"serviceFreeText"`
}

// SpecificDataInformationTypeI ...
type SpecificDataInformationTypeI struct {
	DataTypeInformation *DataTypeInformationTypeI   `xml:"dataTypeInformation"`
	DataInformation     *DataInformationTypeI35753C `xml:"dataInformation"`
}

// SpecificDataInformationTypeI79035S ...
type SpecificDataInformationTypeI79035S struct {
	// XMLName             xml.Name                  `xml:"SpecificDataInformationTypeI_79035S"`
	DataTypeInformation *DataTypeInformationTypeI `xml:"dataTypeInformation"`
	DataInformation     []*DataInformationTypeI   `xml:"dataInformation"`
}

// SpecificTravellerDetailsType ...
type SpecificTravellerDetailsType struct {
	PassengerType            *string `xml:"passengerType"`
	TravellerSurname         *string `xml:"travellerSurname"`
	TravellerGivenName       *string `xml:"travellerGivenName"`
	TravellerReferenceNumber *string `xml:"travellerReferenceNumber"`
	PassengerBirthdate       *string `xml:"passengerBirthdate"`
}

// SpecificVisaLinkCreditCardInformationType ...
type SpecificVisaLinkCreditCardInformationType struct {
	MsgRef             *MessageReferenceType       `xml:"msgRef"`
	RespIdentification *ResponseIdentificationType `xml:"respIdentification"`
}

// StationInformationTypeI ...
type StationInformationTypeI struct {
	DepartTerminal *string `xml:"departTerminal"`
}

// StationInformationTypeI119771C ...
type StationInformationTypeI119771C struct {
	// XMLName  xml.Name `xml:"StationInformationTypeI_119771C"`
	Terminal *string `xml:"terminal"`
}

// StatusDetailsTypeI ...
type StatusDetailsTypeI struct {
	Indicator *string `xml:"indicator"`
	Action    *string `xml:"action"`
}

// StatusDetailsTypeI185722C ...
type StatusDetailsTypeI185722C struct {
	// XMLName   xml.Name `xml:"StatusDetailsTypeI_185722C"`
	Indicator *string `xml:"indicator"`
	Type      *string `xml:"type"`
}

// StatusDetailsTypeI20684C ...
type StatusDetailsTypeI20684C struct {
	// XMLName     xml.Name `xml:"StatusDetailsTypeI_20684C"`
	Indicator   *string `xml:"indicator"`
	Description *string `xml:"description"`
}

// StatusDetailsTypeI300441C ...
type StatusDetailsTypeI300441C struct {
	// XMLName   xml.Name `xml:"StatusDetailsTypeI_300441C"`
	Indicator *string `xml:"indicator"`
}

// StatusDetailsTypeI37285C ...
type StatusDetailsTypeI37285C struct {
	// XMLName   xml.Name `xml:"StatusDetailsTypeI_37285C"`
	Indicator *string `xml:"indicator"`
}

// StatusDetailsTypeI57035C ...
type StatusDetailsTypeI57035C struct {
	// XMLName               xml.Name `xml:"StatusDetailsTypeI_57035C"`
	Indicator             *string `xml:"indicator"`
	IsPNRModifDuringTrans *string `xml:"isPNRModifDuringTrans"`
}

// StatusDetailsType ...
type StatusDetailsType struct {
	Indicator *string `xml:"indicator"`
	Action    *string `xml:"action"`
}

// StatusDetailsType148479C ...
type StatusDetailsType148479C struct {
	// XMLName   xml.Name `xml:"StatusDetailsType_148479C"`
	Indicator *string `xml:"indicator"`
}

// StatusDetailsType300442C ...
type StatusDetailsType300442C struct {
	// XMLName   xml.Name `xml:"StatusDetailsType_300442C"`
	Indicator *string `xml:"indicator"`
}

// StatusDetailsType311686C ...
type StatusDetailsType311686C struct {
	// XMLName     xml.Name `xml:"StatusDetailsType_311686C"`
	Indicator   *string `xml:"indicator"`
	Description *string `xml:"description"`
}

// StatusTypeI ...
type StatusTypeI struct {
	StatusDetails *StatusDetailsTypeI `xml:"statusDetails"`
}

// StatusTypeI127261S ...
type StatusTypeI127261S struct {
	// XMLName       xml.Name                     `xml:"StatusTypeI_127261S"`
	StatusDetails []*StatusDetailsTypeI185722C `xml:"statusDetails"`
}

// StatusTypeI13270S ...
type StatusTypeI13270S struct {
	// XMLName            xml.Name                    `xml:"StatusTypeI_13270S"`
	StatusDetails      *StatusDetailsTypeI20684C   `xml:"statusDetails"`
	OtherStatusDetails []*StatusDetailsTypeI20684C `xml:"otherStatusDetails"`
}

// StatusTypeI20923S ...
type StatusTypeI20923S struct {
	// XMLName       xml.Name                  `xml:"StatusTypeI_20923S"`
	StatusDetails *StatusDetailsTypeI37285C `xml:"statusDetails"`
}

// StatusTypeI217964S ...
type StatusTypeI217964S struct {
	// XMLName       xml.Name                   `xml:"StatusTypeI_217964S"`
	StatusDetails *StatusDetailsTypeI300441C `xml:"statusDetails"`
}

// StatusTypeI32775S ...
type StatusTypeI32775S struct {
	// XMLName       xml.Name                  `xml:"StatusTypeI_32775S"`
	StatusDetails *StatusDetailsTypeI57035C `xml:"statusDetails"`
}

// StatusType ...
type StatusType struct {
	StatusInformation *StatusDetailsType300442C `xml:"statusInformation"`
}

// StatusType227567S ...
type StatusType227567S struct {
	// XMLName           xml.Name                    `xml:"StatusType_227567S"`
	StatusInformation []*StatusDetailsType311686C `xml:"statusInformation"`
}

// StatusType94568S ...
type StatusType94568S struct {
	// XMLName           xml.Name           `xml:"StatusType_94568S"`
	StatusInformation *StatusDetailsType `xml:"statusInformation"`
}

// StatusType99582S ...
type StatusType99582S struct {
	// XMLName           xml.Name                    `xml:"StatusType_99582S"`
	StatusInformation []*StatusDetailsType148479C `xml:"statusInformation"`
}

// StructuredAddressInformationType ...
type StructuredAddressInformationType struct {
	Option     *string `xml:"option"`
	OptionText *string `xml:"optionText"`
}

// StructuredAddressType ...
type StructuredAddressType struct {
	InformationType *string                             `xml:"informationType"`
	Address         []*StructuredAddressInformationType `xml:"address"`
}

// StructuredDateTimeInformationType ...
type StructuredDateTimeInformationType struct {
	TimeMode *string                        `xml:"timeMode"`
	DateTime *StructuredDateTimeType192687C `xml:"dateTime"`
}

// StructuredDateTimeInformationType13380S ...
type StructuredDateTimeInformationType13380S struct {
	// XMLName  xml.Name                      `xml:"StructuredDateTimeInformationType_13380S"`
	DateTime *StructuredDateTimeType17997C `xml:"dateTime"`
}

// StructuredDateTimeInformationType20644S ...
type StructuredDateTimeInformationType20644S struct {
	// XMLName  xml.Name                      `xml:"StructuredDateTimeInformationType_20644S"`
	DateTime *StructuredDateTimeType36775C `xml:"dateTime"`
}

// StructuredDateTimeInformationType206504S ...
type StructuredDateTimeInformationType206504S struct {
	// XMLName          xml.Name                       `xml:"StructuredDateTimeInformationType_206504S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType285975C `xml:"dateTime"`
}

// StructuredDateTimeInformationType206505S ...
type StructuredDateTimeInformationType206505S struct {
	// XMLName          xml.Name                       `xml:"StructuredDateTimeInformationType_206505S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	TimeMode         *string                        `xml:"timeMode"`
	DateTime         *StructuredDateTimeType285976C `xml:"dateTime"`
	TimeZoneInfo     *TimeZoneIinformationType      `xml:"timeZoneInfo"`
}

// StructuredDateTimeInformationType207582S ...
type StructuredDateTimeInformationType207582S struct {
	// XMLName  xml.Name                       `xml:"StructuredDateTimeInformationType_207582S"`
	DateTime *StructuredDateTimeType287335C `xml:"dateTime"`
}

// StructuredDateTimeInformationType207596S ...
type StructuredDateTimeInformationType207596S struct {
	// XMLName  xml.Name                       `xml:"StructuredDateTimeInformationType_207596S"`
	DateTime *StructuredDateTimeType287346C `xml:"dateTime"`
}

// StructuredDateTimeInformationType21109S ...
type StructuredDateTimeInformationType21109S struct {
	// XMLName  xml.Name                      `xml:"StructuredDateTimeInformationType_21109S"`
	DateTime *StructuredDateTimeType35730C `xml:"dateTime"`
}

// StructuredDateTimeInformationType25444S ...
type StructuredDateTimeInformationType25444S struct {
	// XMLName          xml.Name                       `xml:"StructuredDateTimeInformationType_25444S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType287346C `xml:"dateTime"`
}

// StructuredDateTimeInformationType27086S ...
type StructuredDateTimeInformationType27086S struct {
	// XMLName  xml.Name                      `xml:"StructuredDateTimeInformationType_27086S"`
	DateTime *StructuredDateTimeType16347C `xml:"dateTime"`
}

// StructuredDateTimeInformationType32362S ...
type StructuredDateTimeInformationType32362S struct {
	// XMLName          xml.Name                      `xml:"StructuredDateTimeInformationType_32362S"`
	BusinessSemantic *string                       `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType56472C `xml:"dateTime"`
}

// StructuredDateTimeInformationType79014S ...
type StructuredDateTimeInformationType79014S struct {
	// XMLName          xml.Name                `xml:"StructuredDateTimeInformationType_79014S"`
	BusinessSemantic *string                 `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType `xml:"dateTime"`
}

// StructuredDateTimeInformationType83553S ...
type StructuredDateTimeInformationType83553S struct {
	// XMLName          xml.Name                       `xml:"StructuredDateTimeInformationType_83553S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType127515C `xml:"dateTime"`
}

// StructuredDateTimeType ...
type StructuredDateTimeType struct {
	Year         *string `xml:"year"`
	Month        *string `xml:"month"`
	Day          *string `xml:"day"`
	Hour         *string `xml:"hour"`
	Minutes      *string `xml:"minutes"`
	Seconds      *int    `xml:"seconds"`
	Milliseconds *int    `xml:"milliseconds"`
}

// StructuredDateTimeType127515C ...
type StructuredDateTimeType127515C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_127515C"`
	Year    *string `xml:"year"`
	Month   *string `xml:"month"`
	Day     *string `xml:"day"`
	Hour    *string `xml:"hour"`
	Minutes *string `xml:"minutes"`
	Seconds *int    `xml:"seconds"`
}

// StructuredDateTimeType16347C ...
type StructuredDateTimeType16347C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_16347C"`
	Year  *int    `xml:"year"`
	Month *string `xml:"month"`
	Day   *string `xml:"day"`
}

// StructuredDateTimeType17997C ...
type StructuredDateTimeType17997C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_17997C"`
	Year  *string `xml:"year"`
	Month *string `xml:"month"`
	Day   *string `xml:"day"`
}

// StructuredDateTimeType192687C ...
type StructuredDateTimeType192687C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_192687C"`
	Year    *string `xml:"year"`
	Month   *string `xml:"month"`
	Day     *string `xml:"day"`
	Hour    *string `xml:"hour"`
	Minutes *string `xml:"minutes"`
}

// StructuredDateTimeType198234C ...
type StructuredDateTimeType198234C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_198234C"`
	Hour    *int `xml:"hour"`
	Minutes *int `xml:"minutes"`
}

// StructuredDateTimeType285975C ...
type StructuredDateTimeType285975C struct {
	// XMLName      xml.Name `xml:"StructuredDateTimeType_285975C"`
	Year         *string `xml:"year"`
	Month        *string `xml:"month"`
	Day          *string `xml:"day"`
	Hour         *string `xml:"hour"`
	Minutes      *string `xml:"minutes"`
	Seconds      *int    `xml:"seconds"`
	Milliseconds *int    `xml:"milliseconds"`
}

// StructuredDateTimeType285976C ...
type StructuredDateTimeType285976C struct {
	// XMLName      xml.Name `xml:"StructuredDateTimeType_285976C"`
	Year         *string `xml:"year"`
	Month        *int    `xml:"month"`
	Day          *int    `xml:"day"`
	Hour         *int    `xml:"hour"`
	Minutes      *int    `xml:"minutes"`
	Seconds      *int    `xml:"seconds"`
	Milliseconds *int    `xml:"milliseconds"`
}

// StructuredDateTimeType287335C ...
type StructuredDateTimeType287335C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_287335C"`
	Year  *string `xml:"year"`
	Month *int    `xml:"month"`
	Day   *int    `xml:"day"`
}

// StructuredDateTimeType287345C ...
type StructuredDateTimeType287345C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_287345C"`
	Year    *string `xml:"year"`
	Month   *int    `xml:"month"`
	Day     *int    `xml:"day"`
	Hour    *int    `xml:"hour"`
	Minutes *int    `xml:"minutes"`
}

// StructuredDateTimeType287346C ...
type StructuredDateTimeType287346C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_287346C"`
	Year  *string `xml:"year"`
	Month *string `xml:"month"`
	Day   *string `xml:"day"`
}

// StructuredDateTimeType35730C ...
type StructuredDateTimeType35730C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_35730C"`
	Hour    *string `xml:"hour"`
	Minutes *string `xml:"minutes"`
}

// StructuredDateTimeType36775C ...
type StructuredDateTimeType36775C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_36775C"`
	Year    *string `xml:"year"`
	Month   *string `xml:"month"`
	Day     *string `xml:"day"`
	Hour    *string `xml:"hour"`
	Minutes *string `xml:"minutes"`
	Seconds *int    `xml:"seconds"`
}

// StructuredDateTimeType56472C ...
type StructuredDateTimeType56472C struct {
	// XMLName xml.Name `xml:"StructuredDateTimeType_56472C"`
	Year    *string `xml:"year"`
	Month   *string `xml:"month"`
	Day     *string `xml:"day"`
	Hour    *string `xml:"hour"`
	Minutes *string `xml:"minutes"`
}

// StructuredPeriodInformationType ...
type StructuredPeriodInformationType struct {
	BeginDateTime *StructuredDateTimeType17997C `xml:"beginDateTime"`
	EndDateTime   *StructuredDateTimeType17997C `xml:"endDateTime"`
}

// StructuredPeriodInformationType136724S ...
type StructuredPeriodInformationType136724S struct {
	// XMLName          xml.Name                       `xml:"StructuredPeriodInformationType_136724S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	TimeMode         *string                        `xml:"timeMode"`
	BeginDateTime    *StructuredDateTimeType198234C `xml:"beginDateTime"`
	EndDateTime      *StructuredDateTimeType198234C `xml:"endDateTime"`
	Frequency        *FrequencyType                 `xml:"frequency"`
}

// StructuredPeriodInformationType207579S ...
type StructuredPeriodInformationType207579S struct {
	// XMLName          xml.Name                       `xml:"StructuredPeriodInformationType_207579S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	TimeMode         *string                        `xml:"timeMode"`
	BeginDateTime    *StructuredDateTimeType192687C `xml:"beginDateTime"`
	EndDateTime      *StructuredDateTimeType192687C `xml:"endDateTime"`
}

// StructuredPeriodInformationType207595S ...
type StructuredPeriodInformationType207595S struct {
	// XMLName          xml.Name                       `xml:"StructuredPeriodInformationType_207595S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	TimeMode         *string                        `xml:"timeMode"`
	BeginDateTime    *StructuredDateTimeType287345C `xml:"beginDateTime"`
	EndDateTime      *StructuredDateTimeType287345C `xml:"endDateTime"`
}

// StructuredPeriodInformationType8955S ...
type StructuredPeriodInformationType8955S struct {
	// XMLName       xml.Name                      `xml:"StructuredPeriodInformationType_8955S"`
	BeginDateTime *StructuredDateTimeType16347C `xml:"beginDateTime"`
	EndDateTime   *StructuredDateTimeType16347C `xml:"endDateTime"`
}

// StructuredTelephoneNumberType ...
type StructuredTelephoneNumberType struct {
	TelephoneNumber *string `xml:"telephoneNumber"`
}

// StructuredTelephoneNumberType198214C ...
type StructuredTelephoneNumberType198214C struct {
	// XMLName         xml.Name `xml:"StructuredTelephoneNumberType_198214C"`
	TelephoneNumber *string `xml:"telephoneNumber"`
}

// StructuredTelephoneNumberType48448C ...
type StructuredTelephoneNumberType48448C struct {
	// XMLName               xml.Name `xml:"StructuredTelephoneNumberType_48448C"`
	InternationalDialCode *string `xml:"internationalDialCode"`
	LocalPrefixCode       *string `xml:"localPrefixCode"`
	AreaCode              *string `xml:"areaCode"`
	TelephoneNumber       *string `xml:"telephoneNumber"`
}

// SystemDetailsInfoType ...
type SystemDetailsInfoType struct {
	WorkstationId    *string                    `xml:"workstationId"`
	DeliveringSystem *SystemDetailsTypeI192689C `xml:"deliveringSystem"`
}

// SystemDetailsInfoType25482S ...
type SystemDetailsInfoType25482S struct {
	// XMLName         xml.Name                  `xml:"SystemDetailsInfoType_25482S"`
	CascadingSystem *SystemDetailsTypeI46415C `xml:"cascadingSystem"`
}

// SystemDetailsInfoType33158S ...
type SystemDetailsInfoType33158S struct {
	// XMLName          xml.Name                  `xml:"SystemDetailsInfoType_33158S"`
	DeliveringSystem *SystemDetailsTypeI57708C `xml:"deliveringSystem"`
}

// SystemDetailsInfoType94569S ...
type SystemDetailsInfoType94569S struct {
	// XMLName          xml.Name            `xml:"SystemDetailsInfoType_94569S"`
	WorkstationId    *string             `xml:"workstationId"`
	DeliveringSystem *SystemDetailsTypeI `xml:"deliveringSystem"`
}

// SystemDetailsTypeI ...
type SystemDetailsTypeI struct {
	CompanyId *string `xml:"companyId"`
}

// SystemDetailsTypeI192689C ...
type SystemDetailsTypeI192689C struct {
	// XMLName    xml.Name `xml:"SystemDetailsTypeI_192689C"`
	CompanyId  *string `xml:"companyId"`
	LocationId *string `xml:"locationId"`
}

// SystemDetailsTypeI46415C ...
type SystemDetailsTypeI46415C struct {
	// XMLName   xml.Name `xml:"SystemDetailsTypeI_46415C"`
	CompanyId *string `xml:"companyId"`
}

// SystemDetailsTypeI57708C ...
type SystemDetailsTypeI57708C struct {
	// XMLName    xml.Name `xml:"SystemDetailsTypeI_57708C"`
	CompanyId  *string `xml:"companyId"`
	LocationId *string `xml:"locationId"`
}

// TariffInformationDetailsTypeI ...
type TariffInformationDetailsTypeI struct {
	RateType            *string `xml:"rateType"`
	Amount              float64 `xml:"amount"`
	Currency            *string `xml:"currency"`
	RatePlanIndicator   *string `xml:"ratePlanIndicator"`
	AmountType          *string `xml:"amountType"`
	RateChangeIndicator *string `xml:"rateChangeIndicator"`
}

// TariffInformationDetailsTypeI198216C ...
type TariffInformationDetailsTypeI198216C struct {
	// XMLName             xml.Name `xml:"TariffInformationDetailsTypeI_198216C"`
	RateChangeIndicator *string `xml:"rateChangeIndicator"`
}

// TariffInformationDetailsTypeI39533C ...
type TariffInformationDetailsTypeI39533C struct {
	// XMLName     xml.Name `xml:"TariffInformationDetailsTypeI_39533C"`
	Amount      float64 `xml:"amount"`
	Currency    *string `xml:"currency"`
	AmountType  *string `xml:"amountType"`
	TotalAmount float64 `xml:"totalAmount"`
}

// TariffInformationDetailsTypeI50731C ...
type TariffInformationDetailsTypeI50731C struct {
	// XMLName        xml.Name `xml:"TariffInformationDetailsTypeI_50731C"`
	FareBasisCode  *string `xml:"fareBasisCode"`
	FareBaseAmount float64 `xml:"fareBaseAmount"`
	CurrencyCode   *string `xml:"currencyCode"`
}

// TariffInformationDetailsTypeU ...
type TariffInformationDetailsTypeU struct {
	PriceAmount    float64 `xml:"priceAmount"`
	CurrencyCode   *string `xml:"currencyCode"`
	PriceQualifier *string `xml:"priceQualifier"`
}

// TariffInformationDetailsTypeU127523C ...
type TariffInformationDetailsTypeU127523C struct {
	// XMLName        xml.Name `xml:"TariffInformationDetailsTypeU_127523C"`
	PriceAmount    float64 `xml:"priceAmount"`
	PriceQualifier *string `xml:"priceQualifier"`
}

// TariffInformationDetailsTypeU45479C ...
type TariffInformationDetailsTypeU45479C struct {
	// XMLName        xml.Name `xml:"TariffInformationDetailsTypeU_45479C"`
	PriceAmount    float64 `xml:"priceAmount"`
	CurrencyCode   *string `xml:"currencyCode"`
	PriceQualifier *string `xml:"priceQualifier"`
}

// TariffInformationDetailsTypeU46314C ...
type TariffInformationDetailsTypeU46314C struct {
	// XMLName         xml.Name `xml:"TariffInformationDetailsTypeU_46314C"`
	RateIdentifier  *string `xml:"rateIdentifier"`
	UnitaryAmount   float64 `xml:"unitaryAmount"`
	CurrencyCode    *string `xml:"currencyCode"`
	TariffQualifier *string `xml:"tariffQualifier"`
	TotalAmount     float64 `xml:"totalAmount"`
	Quantity        *int    `xml:"quantity"`
	TariffStatus    *string `xml:"tariffStatus"`
}

// TariffInformationTypeI ...
type TariffInformationTypeI struct {
	TariffInfo      *TariffInformationDetailsTypeI       `xml:"tariffInfo"`
	RateInformation *RateInformationTypeI                `xml:"rateInformation"`
	ChargeDetails   []*AssociatedChargesInformationTypeI `xml:"chargeDetails"`
}

// TariffInformationTypeI136706S ...
type TariffInformationTypeI136706S struct {
	// XMLName         xml.Name                                    `xml:"TariffInformationTypeI_136706S"`
	TariffInfo      *TariffInformationDetailsTypeI              `xml:"tariffInfo"`
	RateInformation *RateInformationTypeI198204C                `xml:"rateInformation"`
	ChargeDetails   []*AssociatedChargesInformationTypeI198205C `xml:"chargeDetails"`
}

// TariffInformationTypeI136714S ...
type TariffInformationTypeI136714S struct {
	// XMLName       xml.Name                                    `xml:"TariffInformationTypeI_136714S"`
	TariffInfo    *TariffInformationDetailsTypeI198216C       `xml:"tariffInfo"`
	ChargeDetails []*AssociatedChargesInformationTypeI198218C `xml:"chargeDetails"`
}

// TariffInformationTypeI136719S ...
type TariffInformationTypeI136719S struct {
	// XMLName       xml.Name                                    `xml:"TariffInformationTypeI_136719S"`
	TariffInfo    *TariffInformationDetailsTypeI198216C       `xml:"tariffInfo"`
	ChargeDetails []*AssociatedChargesInformationTypeI198218C `xml:"chargeDetails"`
}

// TariffInformationTypeI22057S ...
type TariffInformationTypeI22057S struct {
	// XMLName       xml.Name                                   `xml:"TariffInformationTypeI_22057S"`
	TariffInfo    *TariffInformationDetailsTypeI39533C       `xml:"tariffInfo"`
	ChargeDetails []*AssociatedChargesInformationTypeI39535C `xml:"chargeDetails"`
}

// TariffInformationTypeI28460S ...
type TariffInformationTypeI28460S struct {
	// XMLName         xml.Name                             `xml:"TariffInformationTypeI_28460S"`
	TariffInfo      *TariffInformationDetailsTypeI50731C `xml:"tariffInfo"`
	RateInformation *RateInformationTypeI50732C          `xml:"rateInformation"`
}

// TariffInformationTypeU ...
type TariffInformationTypeU struct {
	PriceDetails *TariffInformationDetailsTypeU45479C `xml:"priceDetails"`
}

// TariffInformationTypeU25419S ...
type TariffInformationTypeU25419S struct {
	// XMLName                      xml.Name                               `xml:"TariffInformationTypeU_25419S"`
	TariffInformation            []*TariffInformationDetailsTypeU46314C `xml:"tariffInformation"`
	AssociatedChargesInformation *AssociatedChargesInformationTypeU     `xml:"associatedChargesInformation"`
}

// TariffInformationType ...
type TariffInformationType struct {
	PriceDetails *TariffInformationDetailsTypeU `xml:"priceDetails"`
}

// TariffInformationType83558S ...
type TariffInformationType83558S struct {
	// XMLName      xml.Name                              `xml:"TariffInformationType_83558S"`
	PriceDetails *TariffInformationDetailsTypeU127523C `xml:"priceDetails"`
}

// TariffcodeType ...
type TariffcodeType struct {
	TariffCode     *string `xml:"tariffCode"`
	TariffCodeType *string `xml:"tariffCodeType"`
}

// TaxDetailsTypeI ...
type TaxDetailsTypeI struct {
	Rate         *string `xml:"rate"`
	CountryCode  *string `xml:"countryCode"`
	CurrencyCode *string `xml:"currencyCode"`
	Type         *string `xml:"type"`
}

// TaxDetailsTypeI121395C ...
type TaxDetailsTypeI121395C struct {
	// XMLName      xml.Name `xml:"TaxDetailsTypeI_121395C"`
	Rate         *string `xml:"rate"`
	CountryCode  *string `xml:"countryCode"`
	CurrencyCode *string `xml:"currencyCode"`
	Type         *string `xml:"type"`
}

// TaxDetailsTypeI12177C ...
type TaxDetailsTypeI12177C struct {
	// XMLName  xml.Name  `xml:"TaxDetailsTypeI_12177C"`
	TaxRate  *string   `xml:"taxRate"`
	CurrCode *string   `xml:"currCode"`
	TaxType  []*string `xml:"taxType"`
}

// TaxDetailsTypeU ...
type TaxDetailsTypeU struct {
	Qualifier *string `xml:"qualifier"`
	Amount    float64 `xml:"amount"`
}

// TaxDetailsType ...
type TaxDetailsType struct {
	CountryCode *string   `xml:"countryCode"`
	Type        []*string `xml:"type"`
}

// TaxFieldsType ...
type TaxFieldsType struct {
	TaxIndicator   *string `xml:"taxIndicator"`
	TaxCurrency    *string `xml:"taxCurrency"`
	TaxAmount      *string `xml:"taxAmount"`
	TaxCountryCode *string `xml:"taxCountryCode"`
	TaxNatureCode  *string `xml:"taxNatureCode"`
}

// TaxTypeI ...
type TaxTypeI struct {
	TaxDetails []*TaxDetailsTypeI12177C `xml:"taxDetails"`
	// DummyNET   *DummyNET                `xml:"Dummy.NET"`
}

// TaxTypeI79017S ...
type TaxTypeI79017S struct {
	// XMLName     xml.Name         `xml:"TaxTypeI_79017S"`
	TaxCategory *string          `xml:"taxCategory"`
	TaxDetails  *TaxDetailsTypeI `xml:"taxDetails"`
}

// TaxTypeI79038S ...
type TaxTypeI79038S struct {
	// XMLName     xml.Name                `xml:"TaxTypeI_79038S"`
	TaxCategory *string                 `xml:"taxCategory"`
	TaxDetails  *TaxDetailsTypeI121395C `xml:"taxDetails"`
}

// TaxType ...
type TaxType struct {
	TaxCategory *string         `xml:"taxCategory"`
	TaxDetails  *TaxDetailsType `xml:"taxDetails"`
}

// TaxesType ...
type TaxesType struct {
	AdditionnalCharge *TaxDetailsTypeU `xml:"additionnalCharge"`
}

// TerminalIdentificationDescriptionType ...
type TerminalIdentificationDescriptionType struct {
	TerminalID          *string                  `xml:"terminalID"`
	DistributionChannel *DistributionChannelType `xml:"distributionChannel"`
}

// TerminalInformationTypeU ...
type TerminalInformationTypeU struct {
	ArrivalTerminal *string `xml:"arrivalTerminal"`
}

// TerminalTimeInformationTypeS ...
type TerminalTimeInformationTypeS struct {
	LocationDetails *LocationIdentificationTypeS `xml:"locationDetails"`
}

// TdsBlobData ...
type TdsBlobData struct {
	// XMLName          xml.Name                 `xml:"tdsBlobData"`
	TdsBlbIdentifier *ReferenceInfoType94524S `xml:"tdsBlbIdentifier"`
	TdsBlbData       *BinaryDataType          `xml:"tdsBlbData"`
}

// ThreeDomainSecureGroupType ...
type ThreeDomainSecureGroupType struct {
	AuthenticationData *CreditCardSecurityType   `xml:"authenticationData"`
	AcsURL             *CommunicationContactType `xml:"acsURL"`
	TdsBlobData        []*TdsBlobData            `xml:"tdsBlobData"`
}

// TicketAgentInfoType ...
type TicketAgentInfoType struct {
	CompanyIdNumber   *string                  `xml:"companyIdNumber"`
	InternalIdDetails []*InternalIDDetailsType `xml:"internalIdDetails"`
}

// TicketElementType ...
type TicketElementType struct {
	PassengerType *string                `xml:"passengerType"`
	Ticket        *TicketInformationType `xml:"ticket"`
	PrintOptions  *string                `xml:"printOptions"`
}

// TicketInformationType ...
type TicketInformationType struct {
	Indicator            *string   `xml:"indicator"`
	Date                 *string   `xml:"date"`
	Time                 *string   `xml:"time"`
	OfficeId             *string   `xml:"officeId"`
	Freetext             *string   `xml:"freetext"`
	TransactionFlag      *string   `xml:"transactionFlag"`
	ElectronicTicketFlag *string   `xml:"electronicTicketFlag"`
	AirlineCode          *string   `xml:"airlineCode"`
	QueueNumber          *string   `xml:"queueNumber"`
	QueueCategory        *string   `xml:"queueCategory"`
	SitaAddress          []*string `xml:"sitaAddress"`
}

// TicketInformationType5120C ...
type TicketInformationType5120C struct {
	// XMLName          xml.Name `xml:"TicketInformationType_5120C"`
	QueueingOfficeId *string `xml:"queueingOfficeId"`
	Location         *string `xml:"location"`
}

// TicketNumberDetailsTypeI ...
type TicketNumberDetailsTypeI struct {
	Number *string `xml:"number"`
	Type   *string `xml:"type"`
}

// TicketNumberDetailsTypeI198206C ...
type TicketNumberDetailsTypeI198206C struct {
	// XMLName xml.Name `xml:"TicketNumberDetailsTypeI_198206C"`
	Number *string `xml:"number"`
}

// TicketNumberTypeI ...
type TicketNumberTypeI struct {
	DocumentDetails *TicketNumberDetailsTypeI198206C `xml:"documentDetails"`
}

// TicketNumberTypeI79026S ...
type TicketNumberTypeI79026S struct {
	// XMLName         xml.Name                  `xml:"TicketNumberTypeI_79026S"`
	DocumentDetails *TicketNumberDetailsTypeI `xml:"documentDetails"`
}

// TicketingFormOfPaymentType ...
type TicketingFormOfPaymentType struct {
	FopDetails []*FormOfPaymentInformationType `xml:"fopDetails"`
}

// TimeZoneIinformationType ...
type TimeZoneIinformationType struct {
	CountryCode *string `xml:"countryCode"`
	Code        *int    `xml:"code"`
	Suffix      *string `xml:"suffix"`
}

// MethodOfDelivery ...
type MethodOfDelivery struct {
	// XMLName           xml.Name                            `xml:"methodOfDelivery"`
	ElementManagement *ElementManagementSegmentType83559S `xml:"elementManagement"`
	DeliveryDetails   *PackageDescriptionType             `xml:"deliveryDetails"`
}

// ProductDescription ...
type ProductDescription struct {
	// XMLName            xml.Name                         `xml:"productDescription"`
	Product            *ProductIdentificationTypeU      `xml:"product"`
	ProductRestriction []*TrafficRestrictionDetailsType `xml:"productRestriction"`
}

// TotalPriceType ...
type TotalPriceType struct {
	ProviderCode                 *CompanyInformationType83550S            `xml:"providerCode"`
	ExternalRef                  *ReferenceInformationTypeI83551S         `xml:"externalRef"`
	MethodOfDelivery             *MethodOfDelivery                        `xml:"methodOfDelivery"`
	MainPrice                    *TariffInformationType                   `xml:"mainPrice"`
	OtherPrices                  []*TariffInformationType83558S           `xml:"otherPrices"`
	ProductDescription           *ProductDescription                      `xml:"productDescription"`
	AdditionnalChargeInformation []*TaxesType                             `xml:"additionnalChargeInformation"`
	RateCodeInformation          *RateTypesTypeU                          `xml:"rateCodeInformation"`
	OptionalBooking              *StructuredDateTimeInformationType83553S `xml:"optionalBooking"`
}

// RemainingAmountsDetails ...
type RemainingAmountsDetails struct {
	// XMLName         xml.Name                      `xml:"remainingAmountsDetails"`
	ProviderCode    *CompanyInformationType25420S `xml:"providerCode"`
	RemainingAmount *TariffInformationTypeU25419S `xml:"remainingAmount"`
}

// TourDetailedPriceInfo ...
type TourDetailedPriceInfo struct {
	// XMLName            xml.Name                      `xml:"tourDetailedPriceInfo"`
	MarkerSpecificRead *DummySegmentTypeI            `xml:"markerSpecificRead"`
	ProductId          *ReferenceInfoType25422S      `xml:"productId"`
	ProductPrice       *TariffInformationTypeU25419S `xml:"productPrice"`
}

// PaymentInformation ...
type PaymentInformation struct {
	// XMLName      xml.Name                      `xml:"paymentInformation"`
	Payment      *PaymentInformationTypeU      `xml:"payment"`
	OperatorCode *CompanyInformationType25420S `xml:"operatorCode"`
}

// TourAccountDetailsType ...
type TourAccountDetailsType struct {
	TourTotalPrices         []*TariffInformationTypeU25419S `xml:"tourTotalPrices"`
	RemainingAmountsDetails []*RemainingAmountsDetails      `xml:"remainingAmountsDetails"`
	TourDetailedPriceInfo   []*TourDetailedPriceInfo        `xml:"tourDetailedPriceInfo"`
	PaymentInformation      []*PaymentInformation           `xml:"paymentInformation"`
}

// TourDetailsTypeI ...
type TourDetailsTypeI struct {
	TourCode *string `xml:"tourCode"`
}

// TourDetailsTypeI198183C ...
type TourDetailsTypeI198183C struct {
	// XMLName  xml.Name `xml:"TourDetailsTypeI_198183C"`
	TourCode *string `xml:"tourCode"`
}

// TourInformationTypeI ...
type TourInformationTypeI struct {
	TourInformationDetails *TourDetailsTypeI198183C `xml:"tourInformationDetails"`
}

// TourInformationTypeI79029S ...
type TourInformationTypeI79029S struct {
	// XMLName                xml.Name          `xml:"TourInformationTypeI_79029S"`
	TourInformationDetails *TourDetailsTypeI `xml:"tourInformationDetails"`
}

// TourInformationType ...
type TourInformationType struct {
	BookingSummaryInfo     *TravelProductInformationTypeU25428S     `xml:"bookingSummaryInfo"`
	BookingDurationInfo    *QuantityType25433S                      `xml:"bookingDurationInfo"`
	StayingInfo            *PlaceLocationIdentificationTypeU25436S  `xml:"stayingInfo"`
	TourDescriptionInfo    *AdditionalProductDetailsTypeU           `xml:"tourDescriptionInfo"`
	BookingReferenceInfo   []*ReservationControlInformationTypeU    `xml:"bookingReferenceInfo"`
	StatusInfo             *QuantityAndActionTypeU                  `xml:"statusInfo"`
	InsuranceIndication    *InsuranceCoverageType25483S             `xml:"insuranceIndication"`
	PassengerInfo          []*TravellerInformationType25441S        `xml:"passengerInfo"`
	ExpireInfo             *StructuredDateTimeInformationType25444S `xml:"expireInfo"`
	BookingDescriptionInfo []*FreeTextInformationType25445S         `xml:"bookingDescriptionInfo"`
	SystemProviderInfo     *TransportIdentifierType25440S           `xml:"systemProviderInfo"`
	TourOperatorInfo       []*CompanyInformationType25420S          `xml:"tourOperatorInfo"`
	BookingSource          *UserIdentificationType25447S            `xml:"bookingSource"`
	PassengerAssocation    []*ReferenceInfoType25422S               `xml:"passengerAssocation"`
	TourAccountDetails     *TourAccountDetailsType                  `xml:"tourAccountDetails"`
	TourProductDetails     []*TourServiceDetailsType                `xml:"tourProductDetails"`
}

// AccomodationDetails ...
type AccomodationDetails struct {
	// XMLName              xml.Name                   `xml:"accomodationDetails"`
	RoomInfo             *HotelRoomType25429S       `xml:"roomInfo"`
	PassengerAssociation []*ReferenceInfoType25422S `xml:"passengerAssociation"`
	RoomMealPlanInfo     *DiningInformationType     `xml:"roomMealPlanInfo"`
	OccupancynInfo       *RangeDetailsTypeU         `xml:"occupancynInfo"`
}

// VehiculeDetails ...
type VehiculeDetails struct {
	// XMLName      xml.Name            `xml:"vehiculeDetails"`
	VehiculeInfo *VehicleTypeU25502S `xml:"vehiculeInfo"`
}

// TransportationDetails ...
type TransportationDetails struct {
	// XMLName                    xml.Name                                `xml:"transportationDetails"`
	DepartureInfo              *PlaceLocationIdentificationTypeU25436S `xml:"departureInfo"`
	ArrivalInfo                *PlaceLocationIdentificationTypeU25436S `xml:"arrivalInfo"`
	TransportationInfo         *TravelProductInformationTypeI25434S    `xml:"transportationInfo"`
	TransportationDuration     *QuantityType25433S                     `xml:"transportationDuration"`
	EquipmentInfo              *EquipmentDetailsTypeU                  `xml:"equipmentInfo"`
	TransportationMealPlanInfo *DiningInformationType                  `xml:"transportationMealPlanInfo"`
}

// ProductBCSDetails ...
type ProductBCSDetails struct {
	// XMLName                 xml.Name                      `xml:"productBCSDetails"`
	AgentIdentification     *UserIdentificationType25447S `xml:"agentIdentification"`
	DistributionChannelData *SystemDetailsInfoType25482S  `xml:"distributionChannelData"`
}

// ServiceDetails ...
type ServiceDetails struct {
	// XMLName               xml.Name                             `xml:"serviceDetails"`
	ServiceInfo           *TravelProductInformationTypeU25428S `xml:"serviceInfo"`
	ServiceDurationInfo   *QuantityType25433S                  `xml:"serviceDurationInfo"`
	AccomodationDetails   []*AccomodationDetails               `xml:"accomodationDetails"`
	VehiculeDetails       *VehiculeDetails                     `xml:"vehiculeDetails"`
	TransportationDetails []*TransportationDetails             `xml:"transportationDetails"`
	ProductBCSDetails     *ProductBCSDetails                   `xml:"productBCSDetails"`
}

// TourServiceDetailsType ...
type TourServiceDetailsType struct {
	SequenceNumberInfo   *ItemNumberTypeU                    `xml:"sequenceNumberInfo"`
	StatusQuantityInfo   *QuantityAndActionTypeU             `xml:"statusQuantityInfo"`
	ProductInfo          *AdditionalProductDetailsTypeU      `xml:"productInfo"`
	ConfirmationInfo     *ReservationControlInformationTypeU `xml:"confirmationInfo"`
	PassengerAssociation []*ReferenceInfoType25422S          `xml:"passengerAssociation"`
	ServiceDetails       []*ServiceDetails                   `xml:"serviceDetails"`
}

// TrafficRestrictionDetailsTypeU ...
type TrafficRestrictionDetailsTypeU struct {
	Code        *string `xml:"code"`
	Type        *string `xml:"type"`
	Description *string `xml:"description"`
}

// TrafficRestrictionDetailsType ...
type TrafficRestrictionDetailsType struct {
	RestrictionDetails *TrafficRestrictionDetailsTypeU `xml:"restrictionDetails"`
}

// TrainDataType ...
type TrainDataType struct {
	TrainProductInfo *TrainProductInformationType               `xml:"trainProductInfo"`
	TripDateTime     []*StructuredDateTimeInformationType32362S `xml:"tripDateTime"`
	DepLocation      *PlaceLocationIdentificationTypeU32347S    `xml:"depLocation"`
	ArrLocation      *PlaceLocationIdentificationTypeU32347S    `xml:"arrLocation"`
	RailLeg          []*RailLegDataType                         `xml:"railLeg"`
}

// TrainDetailsType ...
type TrainDetailsType struct {
	Code   *string `xml:"code"`
	Number *string `xml:"number"`
}

// TrainInformationType ...
type TrainInformationType struct {
	CompanyInfo       *CompanyInformationType19450S            `xml:"companyInfo"`
	UpdatePermission  *StatusTypeI20923S                       `xml:"updatePermission"`
	TripDetails       *TrainDataType                           `xml:"tripDetails"`
	OpenSegment       *StatusTypeI217964S                      `xml:"openSegment"`
	JourneyDirection  *TravelItineraryInformationTypeI33275S   `xml:"journeyDirection"`
	ProviderTattoo    *ItemReferencesAndVersionsType           `xml:"providerTattoo"`
	ServiceInfo       *FreeTextInformationType20551S           `xml:"serviceInfo"`
	ClassInfo         *ClassConfigurationDetailsType           `xml:"classInfo"`
	AccommodationInfo *AccommodationAllocationInformationTypeU `xml:"accommodationInfo"`
	CoachInfo         *CoachProductInformationType             `xml:"coachInfo"`
	ReservableStatus  *QuantityAndActionTypeU32609S            `xml:"reservableStatus"`
}

// TrainProductInformationType ...
type TrainProductInformationType struct {
	TrainDetails *TrainDetailsType `xml:"trainDetails"`
	Type         *string           `xml:"type"`
}

// TrainProductInformationType32331S ...
type TrainProductInformationType32331S struct {
	// XMLName      xml.Name          `xml:"TrainProductInformationType_32331S"`
	RailCompany  *string           `xml:"railCompany"`
	TrainDetails *TrainDetailsType `xml:"trainDetails"`
	Type         *string           `xml:"type"`
}

// TransactionInformationForTicketingType ...
type TransactionInformationForTicketingType struct {
	TransactionDetails *TransactionInformationsType `xml:"transactionDetails"`
}

// TransactionInformationsType ...
type TransactionInformationsType struct {
	Code                      *string `xml:"code"`
	Type                      *string `xml:"type"`
	IssueIndicator            *string `xml:"issueIndicator"`
	TransmissionControlNumber *string `xml:"transmissionControlNumber"`
}

// TransportIdentifierType ...
type TransportIdentifierType struct {
	CompanyIdentification *CompanyIdentificationTypeI222513C `xml:"companyIdentification"`
}

// TransportIdentifierType25440S ...
type TransportIdentifierType25440S struct {
	// XMLName               xml.Name                          `xml:"TransportIdentifierType_25440S"`
	CompanyIdentification *CompanyIdentificationTypeI46351C `xml:"companyIdentification"`
}

// TransportIdentifierType79027S ...
type TransportIdentifierType79027S struct {
	// XMLName               xml.Name                    `xml:"TransportIdentifierType_79027S"`
	CompanyIdentification *CompanyIdentificationTypeI `xml:"companyIdentification"`
}

// TravelItineraryInformationTypeI ...
type TravelItineraryInformationTypeI struct {
	ItemNumber *int `xml:"itemNumber"`
}

// TravelItineraryInformationTypeI33275S ...
type TravelItineraryInformationTypeI33275S struct {
	// XMLName      xml.Name `xml:"TravelItineraryInformationTypeI_33275S"`
	MovementType *string `xml:"movementType"`
}

// TravelProductInformationTypeI ...
type TravelProductInformationTypeI struct {
}

// TravelProductInformationTypeI144078S ...
type TravelProductInformationTypeI144078S struct {
	// XMLName           xml.Name              `xml:"TravelProductInformationTypeI_144078S"`
	BoardPointDetails *LocationTypeI208252C `xml:"boardPointDetails"`
	OffpointDetails   *LocationTypeI208252C `xml:"offpointDetails"`
}

// TravelProductInformationTypeI193100S ...
type TravelProductInformationTypeI193100S struct {
	// XMLName             xml.Name                                `xml:"TravelProductInformationTypeI_193100S"`
	Product             *ProductDateTimeTypeI270055C            `xml:"product"`
	BoardpointDetail    *LocationTypeI2784C                     `xml:"boardpointDetail"`
	OffpointDetail      *LocationTypeI2784C                     `xml:"offpointDetail"`
	CompanyDetail       *CompanyIdentificationTypeI2785C        `xml:"companyDetail"`
	ProductDetails      *ProductIdentificationDetailsTypeI2786C `xml:"productDetails"`
	TypeDetail          *ProductTypeDetailsTypeI2787C           `xml:"typeDetail"`
	ProcessingIndicator *string                                 `xml:"processingIndicator"`
}

// TravelProductInformationTypeI193102S ...
type TravelProductInformationTypeI193102S struct {
	// XMLName             xml.Name                                `xml:"TravelProductInformationTypeI_193102S"`
	Product             *ProductDateTimeTypeI270056C            `xml:"product"`
	BoardpointDetail    *LocationTypeI2784C                     `xml:"boardpointDetail"`
	OffpointDetail      *LocationTypeI2784C                     `xml:"offpointDetail"`
	CompanyDetail       *CompanyIdentificationTypeI2785C        `xml:"companyDetail"`
	ProductDetails      *ProductIdentificationDetailsTypeI2786C `xml:"productDetails"`
	TypeDetail          *ProductTypeDetailsTypeI2787C           `xml:"typeDetail"`
	ProcessingIndicator *string                                 `xml:"processingIndicator"`
}

// TravelProductInformationTypeI25434S ...
type TravelProductInformationTypeI25434S struct {
	// XMLName              xml.Name                                 `xml:"TravelProductInformationTypeI_25434S"`
	FlightDate           *ProductDateTimeTypeI46338C              `xml:"flightDate"`
	CompanyDetails       *CompanyIdentificationTypeI46335C        `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsTypeI46336C `xml:"flightIdentification"`
	FlightTypeDetails    *ProductTypeDetailsTypeI46337C           `xml:"flightTypeDetails"`
}

// TravelProductInformationTypeI79024S ...
type TravelProductInformationTypeI79024S struct {
	// XMLName           xml.Name                 `xml:"TravelProductInformationTypeI_79024S"`
	FlightTypeDetails *ProductTypeDetailsTypeI `xml:"flightTypeDetails"`
}

// TravelProductInformationTypeI99362S ...
type TravelProductInformationTypeI99362S struct {
	// XMLName              xml.Name                           `xml:"TravelProductInformationTypeI_99362S"`
	FlightDate           *ProductDateTimeTypeI              `xml:"flightDate"`
	BoardPointDetails    *LocationTypeI                     `xml:"boardPointDetails"`
	OffpointDetails      *LocationTypeI                     `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationTypeI148289C `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsTypeI `xml:"flightIdentification"`
}

// TravelProductInformationTypeU ...
type TravelProductInformationTypeU struct {
	ItineraryDateTimeInfo *ProductDateAndTimeTypeU `xml:"itineraryDateTimeInfo"`
	BoardPortDetails      []*LocationTypeU         `xml:"boardPortDetails"`
	LineNumber            *string                  `xml:"lineNumber"`
}

// TravelProductInformationTypeU25428S ...
type TravelProductInformationTypeU25428S struct {
	// XMLName             xml.Name                                 `xml:"TravelProductInformationTypeU_25428S"`
	DateTimeInformation *ProductDateAndTimeTypeU46325C           `xml:"dateTimeInformation"`
	LocationInformation []*LocationTypeU46324C                   `xml:"locationInformation"`
	CompanyInformation  *CompanyIdentificationTypeU              `xml:"companyInformation"`
	ProductDetails      *ProductIdentificationDetailsTypeU46327C `xml:"productDetails"`
}

// TravelerPerpaxDetailsType ...
type TravelerPerpaxDetailsType struct {
	PerpaxMask          *string `xml:"perpaxMask"`
	PerpaxMaskIndicator *string `xml:"perpaxMaskIndicator"`
}

// TravellerDetailsTypeI ...
type TravellerDetailsTypeI struct {
	GivenName *string `xml:"givenName"`
	Title     *string `xml:"title"`
}

// TravellerDetailsTypeI18004C ...
type TravellerDetailsTypeI18004C struct {
	// XMLName   xml.Name `xml:"TravellerDetailsTypeI_18004C"`
	GivenName *string `xml:"givenName"`
}

// TravellerDetailsTypeI27968C ...
type TravellerDetailsTypeI27968C struct {
	// XMLName   xml.Name `xml:"TravellerDetailsTypeI_27968C"`
	GivenName *string `xml:"givenName"`
	Title     *string `xml:"title"`
}

// TravellerDetailsTypeI46354C ...
type TravellerDetailsTypeI46354C struct {
	// XMLName   xml.Name `xml:"TravellerDetailsTypeI_46354C"`
	GivenName *string `xml:"givenName"`
	Title     *string `xml:"title"`
}

// TravellerDetailsType ...
type TravellerDetailsType struct {
	GivenName *string `xml:"givenName"`
}

// TravellerDetailsType260694C ...
type TravellerDetailsType260694C struct {
	// XMLName            xml.Name `xml:"TravellerDetailsType_260694C"`
	FirstName          *string `xml:"firstName"`
	Type               *string `xml:"type"`
	InfantIndicator    *string `xml:"infantIndicator"`
	IdentificationCode *string `xml:"identificationCode"`
}

// TravellerDocumentInformationTypeU ...
type TravellerDocumentInformationTypeU struct {
	DocumentInformation *DocumentInformationTypeU `xml:"documentInformation"`
	DatesOfValidity     *ValidityDatesTypeU       `xml:"datesOfValidity"`
}

// TravellerInformationTypeI ...
type TravellerInformationTypeI struct {
	PaxDetails      *TravellerSurnameInformationTypeI18003C `xml:"paxDetails"`
	OtherPaxDetails *TravellerDetailsTypeI18004C            `xml:"otherPaxDetails"`
}

// TravellerInformationTypeI15923S ...
type TravellerInformationTypeI15923S struct {
	// XMLName         xml.Name                                `xml:"TravellerInformationTypeI_15923S"`
	PaxDetails      *TravellerSurnameInformationTypeI18003C `xml:"paxDetails"`
	OtherPaxDetails *TravellerDetailsTypeI27968C            `xml:"otherPaxDetails"`
}

// TravellerInformationTypeI8956S ...
type TravellerInformationTypeI8956S struct {
	// XMLName         xml.Name                          `xml:"TravellerInformationTypeI_8956S"`
	PaxDetails      *TravellerSurnameInformationTypeI `xml:"paxDetails"`
	OtherPaxDetails *TravellerDetailsTypeI            `xml:"otherPaxDetails"`
}

// TravellerInformationType ...
type TravellerInformationType struct {
	PaxDetails      *TravellerSurnameInformationType858C `xml:"paxDetails"`
	OtherPaxDetails *TravellerDetailsTypeI18004C         `xml:"otherPaxDetails"`
}

// TravellerInformationType185946S ...
type TravellerInformationType185946S struct {
	// XMLName   xml.Name                                `xml:"TravellerInformationType_185946S"`
	Traveller *TravellerSurnameInformationType260693C `xml:"traveller"`
	Passenger []*TravellerDetailsType260694C          `xml:"passenger"`
}

// TravellerInformationType25441S ...
type TravellerInformationType25441S struct {
	// XMLName         xml.Name                               `xml:"TravellerInformationType_25441S"`
	PaxDetails      *TravellerSurnameInformationType46353C `xml:"paxDetails"`
	OtherPaxDetails *TravellerDetailsTypeI46354C           `xml:"otherPaxDetails"`
}

// TravellerInformationType94570S ...
type TravellerInformationType94570S struct {
	// XMLName         xml.Name                         `xml:"TravellerInformationType_94570S"`
	PaxDetails      *TravellerSurnameInformationType `xml:"paxDetails"`
	OtherPaxDetails *TravellerDetailsType            `xml:"otherPaxDetails"`
}

// TravellerInsuranceInformationType ...
type TravellerInsuranceInformationType struct {
	Currency                 *string           `xml:"currency"`
	Amount                   float64           `xml:"amount"`
	SupplementaryInformation *string           `xml:"supplementaryInformation"`
	SexCode                  *string           `xml:"sexCode"`
	CreditCardDetails        []*CreditCardType `xml:"creditCardDetails"`
	TotalPremiumCurrency     *string           `xml:"totalPremiumCurrency"`
	TotalPremium             float64           `xml:"totalPremium"`
	FutureCurrency           *string           `xml:"futureCurrency"`
	FutureAmount             float64           `xml:"futureAmount"`
	FareType                 *string           `xml:"fareType"`
	TravelerName             *string           `xml:"travelerName"`
}

// TravellerNameDetailsType ...
type TravellerNameDetailsType struct {
	NameType           *string `xml:"nameType"`
	ReferenceName      *string `xml:"referenceName"`
	DisplayedName      *string `xml:"displayedName"`
	RomanizationMethod *string `xml:"romanizationMethod"`
	Surname            string  `xml:"surname"`
	GivenName          string  `xml:"givenName"`
	Title              *string `xml:"title"`
}

// TravellerNameInfoType ...
type TravellerNameInfoType struct {
	Qualifier                   *string `xml:"qualifier"`
	Quantity                    *int    `xml:"quantity"`
	Type                        string  `xml:"type"`
	OtherType                   *string `xml:"otherType"`
	InfantIndicator             *string `xml:"infantIndicator"`
	TravellerIdentificationCode *string `xml:"travellerIdentificationCode"`
	Age                         *int    `xml:"age"`
}

// TravellerSurnameInformationTypeI ...
type TravellerSurnameInformationTypeI struct {
	Surname *string `xml:"surname"`
}

// TravellerSurnameInformationTypeI18003C ...
type TravellerSurnameInformationTypeI18003C struct {
	// XMLName xml.Name `xml:"TravellerSurnameInformationTypeI_18003C"`
	Surname *string `xml:"surname"`
	Type    *string `xml:"type"`
}

// TravellerSurnameInformationType ...
type TravellerSurnameInformationType struct {
	Surname *string `xml:"surname"`
}

// TravellerSurnameInformationType260693C ...
type TravellerSurnameInformationType260693C struct {
	// XMLName   xml.Name `xml:"TravellerSurnameInformationType_260693C"`
	Surname   *string `xml:"surname"`
	Qualifier *string `xml:"qualifier"`
	Quantity  *int    `xml:"quantity"`
	StaffType *string `xml:"staffType"`
}

// TravellerSurnameInformationType46353C ...
type TravellerSurnameInformationType46353C struct {
	// XMLName xml.Name `xml:"TravellerSurnameInformationType_46353C"`
	Surname *string `xml:"surname"`
}

// TravellerSurnameInformationType858C ...
type TravellerSurnameInformationType858C struct {
	// XMLName xml.Name `xml:"TravellerSurnameInformationType_858C"`
	Surname *string `xml:"surname"`
	Type    *string `xml:"type"`
	Gender  *string `xml:"gender"`
}

// TravellerTimeDetailsTypeI ...
type TravellerTimeDetailsTypeI struct {
	CheckinTime *string `xml:"checkinTime"`
}

// TstGeneralInformationDetailsType ...
type TstGeneralInformationDetailsType struct {
	TstReferenceNumber *string `xml:"tstReferenceNumber"`
	TstCreationDate    *string `xml:"tstCreationDate"`
	SalesIndicator     *string `xml:"salesIndicator"`
}

// TstGeneralInformationType ...
type TstGeneralInformationType struct {
	GeneralInformation *TstGeneralInformationDetailsType `xml:"generalInformation"`
}

// UniqueIdDescriptionType ...
type UniqueIdDescriptionType struct {
	IDSequenceNumber *int    `xml:"iDSequenceNumber"`
	IDQualifier      *string `xml:"iDQualifier"`
}

// UniqueIdDescriptionType141680C ...
type UniqueIdDescriptionType141680C struct {
	// XMLName            xml.Name `xml:"UniqueIdDescriptionType_141680C"`
	SystemQualifier    *string `xml:"systemQualifier"`
	VersionNumber      *string `xml:"versionNumber"`
	ReferenceQualifier *string `xml:"referenceQualifier"`
	PrimeId            *string `xml:"primeId"`
	SecondaryId        *string `xml:"secondaryId"`
	Status             *string `xml:"status"`
	CreationYear       *string `xml:"creationYear"`
	CreationMonth      *string `xml:"creationMonth"`
	CreationDay        *string `xml:"creationDay"`
	CreationHour       *string `xml:"creationHour"`
	CreationMinutes    *string `xml:"creationMinutes"`
	Description        *string `xml:"description"`
}

// UserIdentificationType ...
type UserIdentificationType struct {
	OriginIdentification *OriginatorIdentificationDetailsTypeI170735C `xml:"originIdentification"`
	OriginatorTypeCode   *string                                      `xml:"originatorTypeCode"`
}

// UserIdentificationType127265S ...
type UserIdentificationType127265S struct {
	// XMLName            xml.Name `xml:"UserIdentificationType_127265S"`
	OriginatorTypeCode *string `xml:"originatorTypeCode"`
}

// UserIdentificationType132824S ...
type UserIdentificationType132824S struct {
	// XMLName              xml.Name                             `xml:"UserIdentificationType_132824S"`
	OriginIdentification *OriginatorIdentificationDetailsType `xml:"originIdentification"`
	OriginatorTypeCode   *string                              `xml:"originatorTypeCode"`
}

// UserIdentificationType215330S ...
type UserIdentificationType215330S struct {
	// XMLName              xml.Name                                     `xml:"UserIdentificationType_215330S"`
	OriginIdentification *OriginatorIdentificationDetailsTypeI297346C `xml:"originIdentification"`
}

// UserIdentificationType25447S ...
type UserIdentificationType25447S struct {
	// XMLName              xml.Name                                    `xml:"UserIdentificationType_25447S"`
	OriginIdentification *OriginatorIdentificationDetailsTypeI46358C `xml:"originIdentification"`
	Originator           *string                                     `xml:"originator"`
}

// UserIdentificationType79019S ...
type UserIdentificationType79019S struct {
	// XMLName              xml.Name                              `xml:"UserIdentificationType_79019S"`
	OriginIdentification *OriginatorIdentificationDetailsTypeI `xml:"originIdentification"`
}

// UserIdentificationType9456S ...
type UserIdentificationType9456S struct {
	// XMLName    xml.Name `xml:"UserIdentificationType_9456S"`
	Originator *string `xml:"originator"`
}

// UserPreferencesType ...
type UserPreferencesType struct {
	UserPreferences *OriginatorDetailsTypeI `xml:"userPreferences"`
}

// ValidityDatesTypeU ...
type ValidityDatesTypeU struct {
	IssueDate      *string `xml:"issueDate"`
	ExpirationDate *string `xml:"expirationDate"`
}

// ValueRangeTypeU ...
type ValueRangeTypeU struct {
	MeasureUnitQualifier *string `xml:"measureUnitQualifier"`
}

// VatPropertiesGroupType ...
type VatPropertiesGroupType struct {
	VatRateAndAmount *MonetaryInformationTypeI53012S `xml:"vatRateAndAmount"`
	FareFiling       *SelectionDetailsTypeI          `xml:"fareFiling"`
}

// VehicleInformationTypeU ...
type VehicleInformationTypeU struct {
	MakeAndModel *string `xml:"makeAndModel"`
}

// VehicleInformationTypeU46439C ...
type VehicleInformationTypeU46439C struct {
	// XMLName   xml.Name `xml:"VehicleInformationTypeU_46439C"`
	Occupancy *int `xml:"occupancy"`
}

// VehicleInformationType ...
type VehicleInformationType struct {
	VehicleCharacteristic *VehicleTypeOptionType         `xml:"vehicleCharacteristic"`
	VehSpecialEquipment   []*string                      `xml:"vehSpecialEquipment"`
	VehicleInfo           []*QuantityDetailsTypeI198209C `xml:"vehicleInfo"`
	FreeTextDetails       *FreeTextDetailsType198207C    `xml:"freeTextDetails"`
	CarModel              *string                        `xml:"carModel"`
}

// VehicleTypeOptionType ...
type VehicleTypeOptionType struct {
	VehicleTypeOwner      *string   `xml:"vehicleTypeOwner"`
	VehicleRentalPrefType []*string `xml:"vehicleRentalPrefType"`
}

// VehicleTypeU ...
type VehicleTypeU struct {
	Category       *string                  `xml:"category"`
	VehicleDetails *VehicleInformationTypeU `xml:"vehicleDetails"`
}

// VehicleTypeU25502S ...
type VehicleTypeU25502S struct {
	// XMLName             xml.Name                       `xml:"VehicleTypeU_25502S"`
	VehiculeDescription *VehicleInformationTypeU46439C `xml:"vehiculeDescription"`
}

// NumericDecimalLength1To18 ...
type NumericDecimalLength1To18 float64

// NumericDecimalLength1To15 ...
type NumericDecimalLength1To15 float64

// NumericDecimalLength1To11 ...
type NumericDecimalLength1To11 float64

// NumericDecimalLength1To3 ...
type NumericDecimalLength1To3 float64

// NumericDecimalLength1To12 ...
type NumericDecimalLength1To12 float64

// NumericStringLength5To8 ...
type NumericStringLength5To8 string

// NumericDecimalLength1To9 ...
type NumericDecimalLength1To9 float64

// NumericDecimalLength1To30 ...
type NumericDecimalLength1To30 float64

// NumericDecimalLength1To20 ...
type NumericDecimalLength1To20 float64

// NumericDecimalLength1To10 ...
type NumericDecimalLength1To10 float64

// NumericDecimalLength1To35 ...
type NumericDecimalLength1To35 float64
