package issue_ticket

import "encoding/xml"

// DocIssuanceIssueTicketReply ...
type DocIssuanceIssueTicketReply struct {
	XMLName          xml.Name                     `xml:"DocIssuance_IssueTicketReply"`
	ProcessingStatus *ResponseAnalysisDetailsType `xml:"processingStatus"`
	ErrorGroup       *ErrorGroupType              `xml:"errorGroup"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// ErrorGroupType is The desciption of warning or error.
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType         `xml:"errorWarningDescription"`
}

// FreeTextDetailsType is encoding
type FreeTextDetailsType struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	Source               string `xml:"source"`
	Encoding             string `xml:"encoding"`
}

// FreeTextInformationType is Free text and message sequence numbers of the remarks.
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        string               `xml:"freeText"`
}

// ResponseAnalysisDetailsType is type of the answer
type ResponseAnalysisDetailsType struct {
	StatusCode string `xml:"statusCode"`
}

// AlphaStringLength1To6 is Format limitations: a..6
type AlphaStringLength1To6 string

// AlphaNumericStringLength1To199 is Format limitations: an..199
type AlphaNumericStringLength1To199 string

// AlphaNumericStringLength1To5 is Format limitations: an..5
type AlphaNumericStringLength1To5 string

// AlphaNumericStringLength1To3 is Format limitations: an..3
type AlphaNumericStringLength1To3 string
