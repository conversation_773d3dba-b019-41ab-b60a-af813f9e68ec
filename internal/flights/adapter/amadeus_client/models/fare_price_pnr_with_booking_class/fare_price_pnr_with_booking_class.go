package fare_price_pnr_with_booking_class

import "encoding/xml"

// ErrorAtMessageLevel ...
// TaxInformation ...
type TaxInformation struct {
	// XMLName       xml.Name                  `xml:"taxInformation"`
	TaxDetails    *DutyTaxFeeDetailsTypeU   `xml:"taxDetails"`
	AmountDetails *MonetaryInformationTypeI `xml:"amountDetails"`
}

// PassengerInformation ...
type PassengerInformation struct {
	// XMLName            xml.Name                                 `xml:"passengerInformation"`
	PenDisInformation  *DiscountAndPenaltyInformationTypeI6128S `xml:"penDisInformation"`
	PassengerReference *ReferenceInformationTypeI               `xml:"passengerReference"`
}

// CabinGroup ...
type CabinGroup struct {
	// XMLName      xml.Name                 `xml:"cabinGroup"`
	CabinSegment *ProductInformationTypeI `xml:"cabinSegment"`
}

// SegmentInformation ...
type SegmentInformation struct {
	// XMLName                 xml.Name                             `xml:"segmentInformation"`
	ConnexInformation       *ConnectionTypeI                     `xml:"connexInformation"`
	SegDetails              *TravelProductInformationTypeI26322S `xml:"segDetails"`
	FareQualifier           *FareQualifierDetailsTypeI           `xml:"fareQualifier"`
	CabinGroup              []*CabinGroup                        `xml:"cabinGroup"`
	ValidityInformation     []*StructuredDateTimeInformationType `xml:"validityInformation"`
	BagAllowanceInformation *ExcessBaggageTypeI                  `xml:"bagAllowanceInformation"`
	SegmentReference        *ReferenceInformationTypeI           `xml:"segmentReference"`
	SequenceInformation     *ItemReferencesAndVersionsType       `xml:"sequenceInformation"`
}

// WarningInformation ...
type WarningInformation struct {
	// XMLName     xml.Name                         `xml:"warningInformation"`
	WarningCode *ApplicationErrorInformationType `xml:"warningCode"`
	WarningText *InteractiveFreeTextTypeI6759S   `xml:"warningText"`
}

// PaperCouponRange ...
type PaperCouponRange struct {
	// XMLName    xml.Name                `xml:"paperCouponRange"`
	TicketInfo *TicketNumberTypeI      `xml:"ticketInfo"`
	CouponInfo *CouponInformationTypeI `xml:"couponInfo"`
}

// FirstDpiGroup ...
type FirstDpiGroup struct {
	// XMLName            xml.Name                            `xml:"firstDpiGroup"`
	ReIssuePenalty     *DiscountAndPenaltyInformationTypeI `xml:"reIssuePenalty"`
	ReissueInfo        *MonetaryInformationTypeI20897S     `xml:"reissueInfo"`
	OldTaxInfo         *MonetaryInformationTypeI20897S     `xml:"oldTaxInfo"`
	ReissueBalanceInfo *MonetaryInformationTypeI20897S     `xml:"reissueBalanceInfo"`
}

// SecondDpiGroup ...
type SecondDpiGroup struct {
	// XMLName           xml.Name                            `xml:"secondDpiGroup"`
	Penalty           *DiscountAndPenaltyInformationTypeI `xml:"penalty"`
	ResidualValueInfo *MonetaryInformationTypeI20897S     `xml:"residualValueInfo"`
	OldTaxInfo        *MonetaryInformationTypeI20897S     `xml:"oldTaxInfo"`
	IssueBalanceInfo  *MonetaryInformationTypeI20897S     `xml:"issueBalanceInfo"`
}

// AutomaticReissueInfo ...
type AutomaticReissueInfo struct {
	// XMLName           xml.Name                        `xml:"automaticReissueInfo"`
	TicketInfo        *TicketNumberTypeI              `xml:"ticketInfo"`
	CouponInfo        *CouponInformationTypeI         `xml:"couponInfo"`
	PaperCouponRange  *PaperCouponRange               `xml:"paperCouponRange"`
	BaseFareInfo      *MonetaryInformationTypeI20897S `xml:"baseFareInfo"`
	FirstDpiGroup     *FirstDpiGroup                  `xml:"firstDpiGroup"`
	SecondDpiGroup    *SecondDpiGroup                 `xml:"secondDpiGroup"`
	ReissueAttributes *CodedAttributeType             `xml:"reissueAttributes"`
}

// FeeDetails ...
type FeeDetails struct {
	// XMLName        xml.Name                        `xml:"feeDetails"`
	FeeInfo        *SpecificDataInformationTypeI   `xml:"feeInfo"`
	FeeDescription *InteractiveFreeTextTypeI       `xml:"feeDescription"`
	FeeAmounts     *MonetaryInformationTypeI39230S `xml:"feeAmounts"`
	FeeTaxes       []*TaxTypeI                     `xml:"feeTaxes"`
}

// FeeBreakdown ...
type FeeBreakdown struct {
	// XMLName    xml.Name               `xml:"feeBreakdown"`
	FeeType    *SelectionDetailsTypeI `xml:"feeType"`
	FeeDetails []*FeeDetails          `xml:"feeDetails"`
}

// FareList ...
type FareList struct {
	// XMLName                   xml.Name                                  `xml:"fareList"`
	PricingInformation        *PricingTicketingSubsequentTypeI          `xml:"pricingInformation"`
	FareReference             *ItemReferencesAndVersionsType94584S      `xml:"fareReference"`
	FareIndicators            *FareInformationType                      `xml:"fareIndicators"`
	LastTktDate               *StructuredDateTimeInformationType199533S `xml:"lastTktDate"`
	ValidatingCarrier         *TransportIdentifierType                  `xml:"validatingCarrier"`
	PaxSegReference           *ReferenceInformationTypeI                `xml:"paxSegReference"`
	FareDataInformation       *MonetaryInformationType198917S           `xml:"fareDataInformation"`
	OfferReferences           *OfferReferencesType                      `xml:"offerReferences"`
	TaxInformation            []*TaxInformation                         `xml:"taxInformation"`
	BankerRates               *ConversionRateTypeI                      `xml:"bankerRates"`
	PassengerInformation      []*PassengerInformation                   `xml:"passengerInformation"`
	OriginDestination         *OriginAndDestinationDetailsTypeI         `xml:"originDestination"`
	SegmentInformation        []*SegmentInformation                     `xml:"segmentInformation"`
	OtherPricingInfo          []*CodedAttributeType39223S               `xml:"otherPricingInfo"`
	WarningInformation        []*WarningInformation                     `xml:"warningInformation"`
	AutomaticReissueInfo      *AutomaticReissueInfo                     `xml:"automaticReissueInfo"`
	CorporateInfo             *CorporateFareInformationType             `xml:"corporateInfo"`
	FeeBreakdown              []*FeeBreakdown                           `xml:"feeBreakdown"`
	Mileage                   *AdditionalProductDetailsTypeI            `xml:"mileage"`
	FareComponentDetailsGroup []*FareComponentDetailsType               `xml:"fareComponentDetailsGroup"`
	EndFareList               *DummySegmentTypeI                        `xml:"endFareList"`
}

// FarePricePNRWithBookingClassReply ...
type FarePricePNRWithBookingClassReply struct {
	// XMLName          xml.Name                            `xml:"Fare_PricePNRWithBookingClassReply"`
	ApplicationError *ErrorGroupType                     `xml:"applicationError"`
	PnrLocatorData   *ReservationControlInformationTypeI `xml:"pnrLocatorData"`
	FareList         []*FareList                         `xml:"fareList"`
}

// AdditionalFareQualifierDetailsTypeI is For any query : discount ticket designator to be assigned by Fare Quote server. For any response : priced PTCs
type AdditionalFareQualifierDetailsTypeI struct {
	PrimaryCode       *string `xml:"primaryCode"`
	FareBasisCode     *string `xml:"fareBasisCode"`
	TicketDesignator  *string `xml:"ticketDesignator"`
	DiscTktDesignator *string `xml:"discTktDesignator"`
}

// AdditionalProductDetailsTypeI is To convey additional information concerning an airline flight.
type AdditionalProductDetailsTypeI struct {
	MileageTimeDetails *MileageTimeDetailsTypeI `xml:"mileageTimeDetails"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      *string `xml:"errorCode"`
	ErrorCategory  *string `xml:"errorCategory"`
	ErrorCodeOwner *string `xml:"errorCodeOwner"`
}

// ApplicationErrorDetailType48648C is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType48648C struct {
	// XMLName                   xml.Name `xml:"ApplicationErrorDetailType_48648C"`
	ApplicationErrorCode      *string `xml:"applicationErrorCode"`
	CodeListQualifier         *string `xml:"codeListQualifier"`
	CodeListResponsibleAgency *string `xml:"codeListResponsibleAgency"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ApplicationErrorDetail *ApplicationErrorDetailType48648C `xml:"applicationErrorDetail"`
}

// ApplicationErrorInformationType84497S is Application error details.
type ApplicationErrorInformationType84497S struct {
	// XMLName      xml.Name                    `xml:"ApplicationErrorInformationType_84497S"`
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// BaggageDetailsTypeI is Measurement unit for weighing baggage allowance
type BaggageDetailsTypeI struct {
	BaggageQuantity *int    `xml:"baggageQuantity"`
	BaggageWeight   *int    `xml:"baggageWeight"`
	BaggageType     *string `xml:"baggageType"`
	MeasureUnit     *string `xml:"measureUnit"`
}

// CodedAttributeInformationType is provides a description for the attribute
type CodedAttributeInformationType struct {
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeInformationType66047C is provides a description for the attribute
type CodedAttributeInformationType66047C struct {
	// XMLName              xml.Name `xml:"CodedAttributeInformationType_66047C"`
	AttributeType        *string `xml:"attributeType"`
	AttributeDescription *string `xml:"attributeDescription"`
}

// CodedAttributeType is provides details for the Attribute
type CodedAttributeType struct {
	AttributeDetails []*CodedAttributeInformationType `xml:"attributeDetails"`
}

// CodedAttributeType39223S is provides details for the Attribute
type CodedAttributeType39223S struct {
	// XMLName          xml.Name                               `xml:"CodedAttributeType_39223S"`
	AttributeDetails []*CodedAttributeInformationType66047C `xml:"attributeDetails"`
	// DummyNET         *DummyNET                              `xml:"Dummy.NET"`
}

// CompanyIdentificationTypeI ...
type CompanyIdentificationTypeI struct {
	CarrierCode *string `xml:"carrierCode"`
}

// CompanyIdentificationTypeI222513C ...
type CompanyIdentificationTypeI222513C struct {
	// XMLName      xml.Name `xml:"CompanyIdentificationTypeI_222513C"`
	OtherCompany *string `xml:"otherCompany"`
}

// ConnectionDetailsTypeI ...
type ConnectionDetailsTypeI struct {
	RoutingInformation *string `xml:"routingInformation"`
	ConnexType         *string `xml:"connexType"`
}

// ConnectionTypeI ...
type ConnectionTypeI struct {
	ConnecDetails *ConnectionDetailsTypeI `xml:"connecDetails"`
}

// ConversionRateDetailsTypeI ...
type ConversionRateDetailsTypeI struct {
	CurrencyCode *string `xml:"currencyCode"`
	Amount       float64 `xml:"amount"`
}

// ConversionRateTypeI ...
type ConversionRateTypeI struct {
	FirstRateDetail  *ConversionRateDetailsTypeI `xml:"firstRateDetail"`
	SecondRateDetail *ConversionRateDetailsTypeI `xml:"secondRateDetail"`
}

// CorporateFareIdentifiersTypeI ...
type CorporateFareIdentifiersTypeI struct {
	FareQualifier *string   `xml:"fareQualifier"`
	CorporateID   []*string `xml:"corporateID"`
}

// CorporateFareInformationType ...
type CorporateFareInformationType struct {
	CorporateFareIdentifiers []*CorporateFareIdentifiersTypeI `xml:"corporateFareIdentifiers"`
}

// CouponTaxDetailsGroup ...
type CouponTaxDetailsGroup struct {
	// XMLName        xml.Name                         `xml:"couponTaxDetailsGroup"`
	TaxTriggerInfo *DutyTaxFeeDetailsType           `xml:"taxTriggerInfo"`
	TaxDetails     *TaxType                         `xml:"taxDetails"`
	MonetaryInfo   *MonetaryInformationType         `xml:"monetaryInfo"`
	LocationInfo   *PlaceLocationIdentificationType `xml:"locationInfo"`
}

// CouponDetailsType ...
type CouponDetailsType struct {
	ProductId             *ReferenceInfoType            `xml:"productId"`
	FlightConnectionType  *TravelProductInformationType `xml:"flightConnectionType"`
	CouponTaxDetailsGroup []*CouponTaxDetailsGroup      `xml:"couponTaxDetailsGroup"`
}

// CouponInformationDetailsTypeI ...
type CouponInformationDetailsTypeI struct {
	CpnNumber *string `xml:"cpnNumber"`
}

// CouponInformationTypeI ...
type CouponInformationTypeI struct {
	CouponDetails      *CouponInformationDetailsTypeI   `xml:"couponDetails"`
	OtherCouponDetails []*CouponInformationDetailsTypeI `xml:"otherCouponDetails"`
}

// DataInformationTypeI ...
type DataInformationTypeI struct {
	Indicator *string `xml:"indicator"`
}

// DataTypeInformationTypeI ...
type DataTypeInformationTypeI struct {
	Type *string `xml:"type"`
}

// DiscountAndPenaltyInformationTypeI ...
type DiscountAndPenaltyInformationTypeI struct {
	PenDisData *DiscountPenaltyMonetaryInformationTypeI29792C `xml:"penDisData"`
}

// DiscountAndPenaltyInformationTypeI6128S ...
type DiscountAndPenaltyInformationTypeI6128S struct {
	// XMLName       xml.Name                                   `xml:"DiscountAndPenaltyInformationTypeI_6128S"`
	InfoQualifier *string                                    `xml:"infoQualifier"`
	PenDisData    []*DiscountPenaltyMonetaryInformationTypeI `xml:"penDisData"`
}

// DiscountPenaltyInformationTypeI ...
type DiscountPenaltyInformationTypeI struct {
	ZapOffType       *string `xml:"zapOffType"`
	ZapOffAmount     float64 `xml:"zapOffAmount"`
	ZapOffPercentage *int    `xml:"zapOffPercentage"`
}

// DiscountPenaltyInformationType ...
type DiscountPenaltyInformationType struct {
	FareQualifier *string `xml:"fareQualifier"`
}

// DiscountPenaltyMonetaryInformationTypeI ...
type DiscountPenaltyMonetaryInformationTypeI struct {
	PenaltyType      *string `xml:"penaltyType"`
	PenaltyQualifier *string `xml:"penaltyQualifier"`
	PenaltyAmount    float64 `xml:"penaltyAmount"`
	DiscountCode     *string `xml:"discountCode"`
	PenaltyCurrency  *string `xml:"penaltyCurrency"`
}

// DiscountPenaltyMonetaryInformationTypeI29792C ...
type DiscountPenaltyMonetaryInformationTypeI29792C struct {
	// XMLName          xml.Name `xml:"DiscountPenaltyMonetaryInformationTypeI_29792C"`
	PenaltyQualifier *string `xml:"penaltyQualifier"`
	PenaltyAmount    float64 `xml:"penaltyAmount"`
	PenaltyCurrency  *string `xml:"penaltyCurrency"`
}

// DummySegmentTypeI ...
type DummySegmentTypeI struct {
}

// DutyTaxFeeAccountDetailTypeU ...
type DutyTaxFeeAccountDetailTypeU struct {
	IsoCountry string `xml:"isoCountry"`
}

// DutyTaxFeeDetailsTypeU ...
type DutyTaxFeeDetailsTypeU struct {
	TaxQualifier      *string                       `xml:"taxQualifier"`
	TaxIdentification *DutyTaxFeeTypeDetailsTypeU   `xml:"taxIdentification"`
	TaxType           *DutyTaxFeeAccountDetailTypeU `xml:"taxType"`
	TaxNature         string                        `xml:"taxNature"`
	TaxExempt         *string                       `xml:"taxExempt"`
}

// DutyTaxFeeDetailsType ...
type DutyTaxFeeDetailsType struct {
	TaxQualifier *string `xml:"taxQualifier"`
}

// DutyTaxFeeTypeDetailsTypeU ...
type DutyTaxFeeTypeDetailsTypeU struct {
	TaxIdentifier *string `xml:"taxIdentifier"`
}

// ErrorGroupType ...
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType84497S `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType               `xml:"errorWarningDescription"`
}

// ExcessBaggageTypeI ...
type ExcessBaggageTypeI struct {
	BagAllowanceDetails *BaggageDetailsTypeI `xml:"bagAllowanceDetails"`
}

// FareComponentDetailsType ...
type FareComponentDetailsType struct {
	FareComponentID      *ItemNumberType                   `xml:"fareComponentID"`
	MarketFareComponent  *TravelProductInformationTypeI    `xml:"marketFareComponent"`
	MonetaryInformation  *MonetaryInformationType198918S   `xml:"monetaryInformation"`
	ComponentClassInfo   *PricingOrTicketingSubsequentType `xml:"componentClassInfo"`
	FareQualifiersDetail *FareQualifierDetailsType         `xml:"fareQualifiersDetail"`
	FareFamilyDetails    *FareFamilyType                   `xml:"fareFamilyDetails"`
	FareFamilyOwner      *TransportIdentifierType156079S   `xml:"fareFamilyOwner"`
	CouponDetailsGroup   []*CouponDetailsType              `xml:"couponDetailsGroup"`
}

// FareDetailsType ...
type FareDetailsType struct {
	FareCategory *string `xml:"fareCategory"`
}

// FareFamilyDetailsType ...
type FareFamilyDetailsType struct {
	CommercialFamily *string `xml:"commercialFamily"`
}

// FareFamilyType ...
type FareFamilyType struct {
	FareFamilyname          *string                  `xml:"fareFamilyname"`
	Hierarchy               *int                     `xml:"hierarchy"`
	CommercialFamilyDetails []*FareFamilyDetailsType `xml:"commercialFamilyDetails"`
}

// FareInformationType ...
type FareInformationType struct {
	FareDetails *FareDetailsType `xml:"fareDetails"`
}

// FareQualifierDetailsTypeI ...
type FareQualifierDetailsTypeI struct {
	MovementType     *string                              `xml:"movementType"`
	FareBasisDetails *AdditionalFareQualifierDetailsTypeI `xml:"fareBasisDetails"`
	ZapOffDetails    *DiscountPenaltyInformationTypeI     `xml:"zapOffDetails"`
}

// FareQualifierDetailsType ...
type FareQualifierDetailsType struct {
	DiscountDetails []*DiscountPenaltyInformationType `xml:"discountDetails"`
}

// FreeTextDetailsType ...
type FreeTextDetailsType struct {
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
	InformationType      *string `xml:"informationType"`
	Status               *string `xml:"status"`
	CompanyId            *string `xml:"companyId"`
	Language             *string `xml:"language"`
	Source               *string `xml:"source"`
	Encoding             *string `xml:"encoding"`
}

// FreeTextInformationType ...
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType `xml:"freeTextDetails"`
	FreeText        []*string            `xml:"freeText"`
}

// FreeTextQualificationTypeI ...
type FreeTextQualificationTypeI struct {
	TextSubjectQualifier *string `xml:"textSubjectQualifier"`
}

// InteractiveFreeTextTypeI ...
type InteractiveFreeTextTypeI struct {
	FreeTextQualification *FreeTextQualificationTypeI `xml:"freeTextQualification"`
	FreeText              *string                     `xml:"freeText"`
}

// InteractiveFreeTextTypeI6759S ...
type InteractiveFreeTextTypeI6759S struct {
	// XMLName       xml.Name `xml:"InteractiveFreeTextTypeI_6759S"`
	ErrorFreeText *string `xml:"errorFreeText"`
}

// ItemNumberIdentificationType ...
type ItemNumberIdentificationType struct {
	Number *string `xml:"number"`
	Type   *string `xml:"type"`
}

// ItemNumberType ...
type ItemNumberType struct {
	ItemNumberDetails []*ItemNumberIdentificationType `xml:"itemNumberDetails"`
}

// ItemReferencesAndVersionsType ...
type ItemReferencesAndVersionsType struct {
	SequenceSection *UniqueIdDescriptionType `xml:"sequenceSection"`
}

// ItemReferencesAndVersionsType94584S ...
type ItemReferencesAndVersionsType94584S struct {
	// XMLName         xml.Name `xml:"ItemReferencesAndVersionsType_94584S"`
	ReferenceType   *string `xml:"referenceType"`
	UniqueReference *int    `xml:"uniqueReference"`
}

// LocationIdentificationBatchType ...
type LocationIdentificationBatchType struct {
	Code *string `xml:"code"`
}

// LocationTypeI ...
type LocationTypeI struct {
	TrueLocationId *string `xml:"trueLocationId"`
}

// LocationTypeI47688C ...
type LocationTypeI47688C struct {
	// XMLName  xml.Name `xml:"LocationTypeI_47688C"`
	CityCode *string `xml:"cityCode"`
}

// MileageTimeDetailsTypeI ...
type MileageTimeDetailsTypeI struct {
	TotalMileage *int `xml:"totalMileage"`
}

// MonetaryInformationDetailsTypeI ...
type MonetaryInformationDetailsTypeI struct {
	FareDataQualifier *string `xml:"fareDataQualifier"`
	FareAmount        string  `xml:"fareAmount"`
	FareCurrency      *string `xml:"fareCurrency"`
	FareLocation      *string `xml:"fareLocation"`
}

// MonetaryInformationDetailsTypeI37257C ...
type MonetaryInformationDetailsTypeI37257C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsTypeI_37257C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
	Location      *string `xml:"location"`
}

// MonetaryInformationDetailsTypeI63727C ...
type MonetaryInformationDetailsTypeI63727C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsTypeI_63727C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
	Location      *string `xml:"location"`
}

// MonetaryInformationDetailsType ...
type MonetaryInformationDetailsType struct {
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        *string `xml:"amount"`
	Currency      *string `xml:"currency"`
}

// MonetaryInformationDetailsType262564C ...
type MonetaryInformationDetailsType262564C struct {
	// XMLName           xml.Name `xml:"MonetaryInformationDetailsType_262564C"`
	FareDataQualifier *string `xml:"fareDataQualifier"`
	FareAmount        *string `xml:"fareAmount"`
	FareCurrency      *string `xml:"fareCurrency"`
	FareLocation      *string `xml:"fareLocation"`
}

// MonetaryInformationDetailsType270392C ...
type MonetaryInformationDetailsType270392C struct {
	// XMLName       xml.Name `xml:"MonetaryInformationDetailsType_270392C"`
	TypeQualifier *string `xml:"typeQualifier"`
	Amount        float64 `xml:"amount"`
	Currency      *string `xml:"currency"`
	Location      *string `xml:"location"`
}

// MonetaryInformationTypeI ...
type MonetaryInformationTypeI struct {
	FareDataMainInformation *MonetaryInformationDetailsTypeI   `xml:"fareDataMainInformation"`
	FareDataSupInformation  []*MonetaryInformationDetailsTypeI `xml:"fareDataSupInformation"`
}

// MonetaryInformationTypeI20897S ...
type MonetaryInformationTypeI20897S struct {
	// XMLName              xml.Name                                 `xml:"MonetaryInformationTypeI_20897S"`
	MonetaryDetails      *MonetaryInformationDetailsTypeI37257C   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsTypeI37257C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationTypeI39230S ...
type MonetaryInformationTypeI39230S struct {
	// XMLName         xml.Name                                 `xml:"MonetaryInformationTypeI_39230S"`
	MonetaryDetails []*MonetaryInformationDetailsTypeI63727C `xml:"monetaryDetails"`
}

// MonetaryInformationType ...
type MonetaryInformationType struct {
	MonetaryDetails      *MonetaryInformationDetailsType270392C   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType270392C `xml:"otherMonetaryDetails"`
}

// MonetaryInformationType198917S ...
type MonetaryInformationType198917S struct {
	// XMLName                 xml.Name                                 `xml:"MonetaryInformationType_198917S"`
	FareDataMainInformation *MonetaryInformationDetailsType262564C   `xml:"fareDataMainInformation"`
	FareDataSupInformation  []*MonetaryInformationDetailsType262564C `xml:"fareDataSupInformation"`
}

// MonetaryInformationType198918S ...
type MonetaryInformationType198918S struct {
	// XMLName              xml.Name                          `xml:"MonetaryInformationType_198918S"`
	MonetaryDetails      *MonetaryInformationDetailsType   `xml:"monetaryDetails"`
	OtherMonetaryDetails []*MonetaryInformationDetailsType `xml:"otherMonetaryDetails"`
}

// OfferReferencesType ...
type OfferReferencesType struct {
	OfferIdentifier *OfferType                  `xml:"offerIdentifier"`
	References      []*ReferenceInfoType218150S `xml:"references"`
}

// OfferType ...
type OfferType struct {
	Reference            *string `xml:"reference"`
	OfferId              *string `xml:"offerId"`
	UniqueOfferReference *string `xml:"uniqueOfferReference"`
}

// OriginAndDestinationDetailsTypeI ...
type OriginAndDestinationDetailsTypeI struct {
	CityCode []*string `xml:"cityCode"`
}

// PlaceLocationIdentificationType ...
type PlaceLocationIdentificationType struct {
	LocationType        *string                          `xml:"locationType"`
	LocationDescription *LocationIdentificationBatchType `xml:"locationDescription"`
}

// PricingOrTicketingSubsequentType ...
type PricingOrTicketingSubsequentType struct {
	FareBasisDetails *RateTariffClassInformationType `xml:"fareBasisDetails"`
}

// PricingTicketingSubsequentTypeI ...
type PricingTicketingSubsequentTypeI struct {
	TstInformation *RateTariffClassInformationTypeI `xml:"tstInformation"`
	SalesIndicator *string                          `xml:"salesIndicator"`
	Fcmi           *string                          `xml:"fcmi"`
	BestFareType   *string                          `xml:"bestFareType"`
}

// ProductDetailsTypeI ...
type ProductDetailsTypeI struct {
	Designator         *string   `xml:"designator"`
	AvailabilityStatus *string   `xml:"availabilityStatus"`
	SpecialService     *string   `xml:"specialService"`
	Option             []*string `xml:"option"`
}

// ProductIdentificationDetailsTypeI ...
type ProductIdentificationDetailsTypeI struct {
	Identification *string `xml:"identification"`
	BookingClass   *string `xml:"bookingClass"`
	ClassOfService *string `xml:"classOfService"`
}

// ProductInformationTypeI ...
type ProductInformationTypeI struct {
	ProductDetailsQualifier *string                `xml:"productDetailsQualifier"`
	BookingClassDetails     []*ProductDetailsTypeI `xml:"bookingClassDetails"`
}

// ProductTypeDetailsType ...
type ProductTypeDetailsType struct {
	FlightIndicator *string `xml:"flightIndicator"`
}

// RateTariffClassInformationTypeI ...
type RateTariffClassInformationTypeI struct {
	TstIndicator *string `xml:"tstIndicator"`
}

// RateTariffClassInformationType ...
type RateTariffClassInformationType struct {
	RateTariffClass      *string `xml:"rateTariffClass"`
	OtherRateTariffClass *string `xml:"otherRateTariffClass"`
}

// ReferenceInfoType ...
type ReferenceInfoType struct {
	ReferenceDetails *ReferencingDetailsType `xml:"referenceDetails"`
}

// ReferenceInfoType218150S ...
type ReferenceInfoType218150S struct {
	// XMLName          xml.Name                         `xml:"ReferenceInfoType_218150S"`
	ReferenceDetails []*ReferencingDetailsType300632C `xml:"referenceDetails"`
	// DummyNET         *DummyNET                        `xml:"Dummy.NET"`
}

// ReferenceInformationTypeI ...
type ReferenceInformationTypeI struct {
	RefDetails []*ReferencingDetailsTypeI `xml:"refDetails"`
}

// ReferencingDetailsTypeI ...
type ReferencingDetailsTypeI struct {
	RefQualifier string `xml:"refQualifier"`
	RefNumber    int    `xml:"refNumber"`
}

// ReferencingDetailsType ...
type ReferencingDetailsType struct {
	Type  *string `xml:"type"`
	Value *string `xml:"value"`
}

// ReferencingDetailsType300632C ...
type ReferencingDetailsType300632C struct {
	XMLName xml.Name `xml:"ReferencingDetailsType_300632C"`
	Type    *string  `xml:"type"`
	Value   *string  `xml:"value"`
}

// ReservationControlInformationDetailsTypeI ...
type ReservationControlInformationDetailsTypeI struct {
	ControlNumber *string `xml:"controlNumber"`
}

// ReservationControlInformationTypeI ...
type ReservationControlInformationTypeI struct {
	ReservationInformation *ReservationControlInformationDetailsTypeI `xml:"reservationInformation"`
}

// SelectionDetailsInformationTypeI ...
type SelectionDetailsInformationTypeI struct {
	Option *string `xml:"option"`
}

// SelectionDetailsTypeI ...
type SelectionDetailsTypeI struct {
	SelectionDetails *SelectionDetailsInformationTypeI `xml:"selectionDetails"`
}

// SpecificDataInformationTypeI ...
type SpecificDataInformationTypeI struct {
	DataTypeInformation *DataTypeInformationTypeI `xml:"dataTypeInformation"`
	DataInformation     []*DataInformationTypeI   `xml:"dataInformation"`
}

// StructuredDateTimeInformationType ...
type StructuredDateTimeInformationType struct {
	BusinessSemantic *string                 `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType `xml:"dateTime"`
}

// StructuredDateTimeInformationType199533S ...
type StructuredDateTimeInformationType199533S struct {
	// XMLName          xml.Name                       `xml:"StructuredDateTimeInformationType_199533S"`
	BusinessSemantic *string                        `xml:"businessSemantic"`
	DateTime         *StructuredDateTimeType277474C `xml:"dateTime"`
}

// StructuredDateTimeType ...
type StructuredDateTimeType struct {
	Year  *int    `xml:"year"`
	Month *string `xml:"month"`
	Day   *string `xml:"day"`
}

// StructuredDateTimeType277474C ...
type StructuredDateTimeType277474C struct {
	// XMLName      xml.Name `xml:"StructuredDateTimeType_277474C"`
	Year         *int    `xml:"year"`
	Month        *string `xml:"month"`
	Day          *string `xml:"day"`
	Hour         *string `xml:"hour"`
	Minutes      *string `xml:"minutes"`
	Seconds      *int    `xml:"seconds"`
	Milliseconds *int    `xml:"milliseconds"`
}

// TaxDetailsTypeI ...
type TaxDetailsTypeI struct {
	Rate         *string `xml:"rate"`
	CountryCode  *string `xml:"countryCode"`
	CurrencyCode *string `xml:"currencyCode"`
	Type         *string `xml:"type"`
	SecondType   *string `xml:"secondType"`
}

// TaxDetailsType ...
type TaxDetailsType struct {
	CountryCode *string   `xml:"countryCode"`
	Type        []*string `xml:"type"`
}

// TaxTypeI ...
type TaxTypeI struct {
	TaxDetails []*TaxDetailsTypeI `xml:"taxDetails"`
	// DummyNET   *DummyNET          `xml:"Dummy.NET"`
}

// TaxType ...
type TaxType struct {
	TaxCategory *string         `xml:"taxCategory"`
	TaxDetails  *TaxDetailsType `xml:"taxDetails"`
}

// TicketNumberDetailsTypeI ...
type TicketNumberDetailsTypeI struct {
	Number *string `xml:"number"`
	Type   *string `xml:"type"`
}

// TicketNumberTypeI ...
type TicketNumberTypeI struct {
	DocumentDetails *TicketNumberDetailsTypeI `xml:"documentDetails"`
}

// TransportIdentifierType ...
type TransportIdentifierType struct {
	CarrierInformation *CompanyIdentificationTypeI `xml:"carrierInformation"`
}

// TransportIdentifierType156079S ...
type TransportIdentifierType156079S struct {
	// XMLName               xml.Name                           `xml:"TransportIdentifierType_156079S"`
	CompanyIdentification *CompanyIdentificationTypeI222513C `xml:"companyIdentification"`
}

// TravelProductInformationTypeI ...
type TravelProductInformationTypeI struct {
	BoardPointDetails *LocationTypeI `xml:"boardPointDetails"`
	OffpointDetails   *LocationTypeI `xml:"offpointDetails"`
}

// TravelProductInformationTypeI26322S ...
type TravelProductInformationTypeI26322S struct {
	// XMLName         xml.Name                           `xml:"TravelProductInformationTypeI_26322S"`
	DepartureCity   *LocationTypeI47688C               `xml:"departureCity"`
	ArrivalCity     *LocationTypeI47688C               `xml:"arrivalCity"`
	AirlineDetail   *CompanyIdentificationTypeI        `xml:"airlineDetail"`
	SegmentDetail   *ProductIdentificationDetailsTypeI `xml:"segmentDetail"`
	TicketingStatus *string                            `xml:"ticketingStatus"`
}

// TravelProductInformationType ...
type TravelProductInformationType struct {
	BoardPointDetails *LocationTypeI          `xml:"boardPointDetails"`
	OffpointDetails   *LocationTypeI          `xml:"offpointDetails"`
	FlightTypeDetails *ProductTypeDetailsType `xml:"flightTypeDetails"`
}

// UniqueIdDescriptionType ...
type UniqueIdDescriptionType struct {
	SequenceNumber *int `xml:"sequenceNumber"`
}

// NumericDecimalLength1To11 ...
type NumericDecimalLength1To11 float64

// NumericDecimalLength1To18 ...
type NumericDecimalLength1To18 float64

// NumericDecimalLength1To35 ...
type NumericDecimalLength1To35 float64
