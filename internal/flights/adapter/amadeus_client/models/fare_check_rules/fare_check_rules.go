package fare_check_rules

// MessageFunctionDetails ...
type MessageFunctionDetails struct {
	// XMLName         xml.Name `xml:"messageFunctionDetails"`
	MessageFunction string `xml:"messageFunction"`
}

// TransactionType ...
type TransactionType struct {
	// XMLName                xml.Name                `xml:"transactionType"`
	MessageFunctionDetails *MessageFunctionDetails `xml:"messageFunctionDetails"`
}

// StatusDetails ...
type StatusDetails struct {
	// XMLName   xml.Name `xml:"statusDetails"`
	Indicator string `xml:"indicator"`
}

// OtherDetails ...
type OtherDetails struct {
	// XMLName   xml.Name `xml:"otherDetails"`
	Indicator string `xml:"indicator"`
}

// StatusInfo ...
type StatusInfo struct {
	// XMLName       xml.Name        `xml:"statusInfo"`
	StatusDetails *StatusDetails  `xml:"statusDetails"`
	OtherDetails  []*OtherDetails `xml:"otherDetails"`
}

// FareQualifierDetails ...
type FareQualifierDetails struct {
	// XMLName       xml.Name `xml:"fareQualifierDetails"`
	FareQualifier []string `xml:"fareQualifier"`
}

// XsDecimal ...
type XsDecimal float64

// ValidityPeriod ...
type ValidityPeriod struct {
	// XMLName    xml.Name `xml:"validityPeriod"`
	FirstDate  *float64 `xml:"firstDate"`
	SecondDate *float64 `xml:"secondDate"`
}

// FareRouteInfo ...
type FareRouteInfo struct {
	// XMLName              xml.Name              `xml:"fareRouteInfo"`
	DayOfWeek            string                `xml:"dayOfWeek"`
	FareQualifierDetails *FareQualifierDetails `xml:"fareQualifierDetails"`
	IdentificationNumber string                `xml:"identificationNumber"`
	ValidityPeriod       *ValidityPeriod       `xml:"validityPeriod"`
}

// FreeTextQualification ...
type FreeTextQualification struct {
	// XMLName              xml.Name `xml:"freeTextQualification"`
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
}

// InfoText ...
type InfoText struct {
	// XMLName               xml.Name               `xml:"infoText"`
	FreeTextQualification *FreeTextQualification `xml:"freeTextQualification"`
	FreeText              []string               `xml:"freeText"`
}

// ErrorDetails ...
type ErrorDetails struct {
	// XMLName   xml.Name `xml:"errorDetails"`
	ErrorCode string `xml:"errorCode"`
}

// RejectErrorCode ...
type RejectErrorCode struct {
	// XMLName      xml.Name      `xml:"rejectErrorCode"`
	ErrorDetails *ErrorDetails `xml:"errorDetails"`
}

// ErrorFreeText ...
type ErrorFreeText struct {
	// XMLName               xml.Name               `xml:"errorFreeText"`
	FreeTextQualification *FreeTextQualification `xml:"freeTextQualification"`
	FreeText              []string               `xml:"freeText"`
}

// ErrorInfo ...
type ErrorInfo struct {
	// XMLName         xml.Name         `xml:"errorInfo"`
	RejectErrorCode *RejectErrorCode `xml:"rejectErrorCode"`
	ErrorFreeText   *ErrorFreeText   `xml:"errorFreeText"`
}

// CompanyDetails ...
type CompanyDetails struct {
	// XMLName          xml.Name `xml:"companyDetails"`
	MarketingCompany string `xml:"marketingCompany"`
	Operatingcompany string `xml:"operatingcompany"`
	OtherCompany     string `xml:"otherCompany"`
}

// FareRuleInfo ...
type FareRuleInfo struct {
	// XMLName            xml.Name        `xml:"fareRuleInfo"`
	RuleSectionLocalId string          `xml:"ruleSectionLocalId"`
	CompanyDetails     *CompanyDetails `xml:"companyDetails"`
	RuleCategoryCode   string          `xml:"ruleCategoryCode"`
}

// FareRuleText ...
type FareRuleText struct {
	// XMLName               xml.Name               `xml:"fareRuleText"`
	FreeTextQualification *FreeTextQualification `xml:"freeTextQualification"`
	FreeText              []string               `xml:"freeText"`
}

// TariffInfo ...
type TariffInfo struct {
	// XMLName      xml.Name        `xml:"tariffInfo"`
	FareRuleInfo *FareRuleInfo   `xml:"fareRuleInfo"`
	FareRuleText []*FareRuleText `xml:"fareRuleText"`
}

// SegmentControlDetails ...
type SegmentControlDetails struct {
	// XMLName            xml.Name `xml:"segmentControlDetails"`
	Quantity           *float64 `xml:"quantity"`
	NumberOfUnits      *float64 `xml:"numberOfUnits"`
	TotalNumberOfItems *float64 `xml:"totalNumberOfItems"`
}

// NbOfSegments ...
type NbOfSegments struct {
	// XMLName               xml.Name                 `xml:"nbOfSegments"`
	SegmentControlDetails []*SegmentControlDetails `xml:"segmentControlDetails"`
}

// ConversionRateDetails ...
type ConversionRateDetails struct {
	// XMLName                 xml.Name `xml:"conversionRateDetails"`
	ConversionType          string   `xml:"conversionType"`
	Currency                string   `xml:"currency"`
	RateType                string   `xml:"rateType"`
	PricingAmount           *float64 `xml:"pricingAmount"`
	MeasurementValue        *float64 `xml:"measurementValue"`
	MeasurementSignificance string   `xml:"measurementSignificance"`
}

// OtherConversionRateDetails ...
type OtherConversionRateDetails struct {
	// XMLName                 xml.Name `xml:"otherConversionRateDetails"`
	ConversionType          string   `xml:"conversionType"`
	Currency                string   `xml:"currency"`
	RateType                string   `xml:"rateType"`
	PricingAmount           *float64 `xml:"pricingAmount"`
	MeasurementValue        *float64 `xml:"measurementValue"`
	MeasurementSignificance string   `xml:"measurementSignificance"`
}

// AmountConversion ...
type AmountConversion struct {
	// XMLName                    xml.Name                      `xml:"amountConversion"`
	ConversionRateDetails      *ConversionRateDetails        `xml:"conversionRateDetails"`
	OtherConversionRateDetails []*OtherConversionRateDetails `xml:"otherConversionRateDetails"`
}

// QuantityDetails ...
type QuantityDetails struct {
	// XMLName   xml.Name `xml:"quantityDetails"`
	Qualifier string   `xml:"qualifier"`
	Value     *float64 `xml:"value"`
	Unit      string   `xml:"unit"`
}

// QuantityValue ...
type QuantityValue struct {
	// XMLName         xml.Name  `xml:"quantityValue"`
	QuantityDetails []*string `xml:"quantityDetails"`
}

// ProductDateTimeDetails ...
type ProductDateTimeDetails struct {
	// XMLName       xml.Name `xml:"productDateTimeDetails"`
	DepartureDate string `xml:"departureDate"`
	ArrivalDate   string `xml:"arrivalDate"`
}

// LocationDetails ...
type LocationDetails struct {
	// XMLName xml.Name `xml:"locationDetails"`
	City    string `xml:"city"`
	Country string `xml:"country"`
}

// OtherLocationDetails ...
type OtherLocationDetails struct {
	// XMLName xml.Name `xml:"otherLocationDetails"`
	City    string `xml:"city"`
	Country string `xml:"country"`
}

// PricingAndDateInfo ...
type PricingAndDateInfo struct {
	// XMLName                xml.Name                `xml:"pricingAndDateInfo"`
	ProductDateTimeDetails *ProductDateTimeDetails `xml:"productDateTimeDetails"`
	LocationDetails        *LocationDetails        `xml:"locationDetails"`
	OtherLocationDetails   *OtherLocationDetails   `xml:"otherLocationDetails"`
	IdNumber               string                  `xml:"idNumber"`
}

// FareCategories ...
type FareCategories struct {
	// XMLName  xml.Name `xml:"fareCategories"`
	FareType []string `xml:"fareType"`
}

// FareDetails ...
type FareDetails struct {
	// XMLName      xml.Name `xml:"fareDetails"`
	Qualifier    string   `xml:"qualifier"`
	Rate         *float64 `xml:"rate"`
	Country      string   `xml:"country"`
	FareCategory string   `xml:"fareCategory"`
}

// AdditionalFareDetails ...
type AdditionalFareDetails struct {
	// XMLName           xml.Name `xml:"additionalFareDetails"`
	RateClass         string   `xml:"rateClass"`
	CommodityCategory string   `xml:"commodityCategory"`
	FareClass         []string `xml:"fareClass"`
}

// DiscountDetails ...
type DiscountDetails struct {
	// XMLName       xml.Name `xml:"discountDetails"`
	FareQualifier string   `xml:"fareQualifier"`
	RateCategory  string   `xml:"rateCategory"`
	Amount        *float64 `xml:"amount"`
	Percentage    *float64 `xml:"percentage"`
}

// QualificationFareDetails ...
type QualificationFareDetails struct {
	// XMLName               xml.Name               `xml:"qualificationFareDetails"`
	MovementType          string                 `xml:"movementType"`
	FareCategories        *FareCategories        `xml:"fareCategories"`
	FareDetails           *FareDetails           `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareDetails `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountDetails     `xml:"discountDetails"`
}

// CompanyIdentification ...
type CompanyIdentification struct {
	// XMLName          xml.Name `xml:"companyIdentification"`
	MarketingCompany string `xml:"marketingCompany"`
	Operatingcompany string `xml:"operatingcompany"`
	OtherCompany     string `xml:"otherCompany"`
}

// ProductIdentificationDetails ...
type ProductIdentificationDetails struct {
	// XMLName           xml.Name `xml:"productIdentificationDetails"`
	FlightNumber      string `xml:"flightNumber"`
	OperationalSuffix string `xml:"operationalSuffix"`
}

// TransportService ...
type TransportService struct {
	// XMLName                      xml.Name                        `xml:"transportService"`
	CompanyIdentification        *CompanyIdentification          `xml:"companyIdentification"`
	ProductIdentificationDetails []*ProductIdentificationDetails `xml:"productIdentificationDetails"`
}

// FlightErrorCode ...
type FlightErrorCode struct {
	// XMLName               xml.Name               `xml:"flightErrorCode"`
	FreeTextQualification *FreeTextQualification `xml:"freeTextQualification"`
	FreeText              []string               `xml:"freeText"`
}

// BookingClassDetails ...
type BookingClassDetails struct {
	// XMLName    xml.Name `xml:"bookingClassDetails"`
	Designator string   `xml:"designator"`
	Option     []string `xml:"option"`
}

// ProductDetails ...
type ProductDetails struct {
	// XMLName                 xml.Name               `xml:"productDetails"`
	ProductDetailsQualifier string                 `xml:"productDetailsQualifier"`
	BookingClassDetails     []*BookingClassDetails `xml:"bookingClassDetails"`
}

// ProductErrorCode ...
type ProductErrorCode struct {
	// XMLName               xml.Name               `xml:"productErrorCode"`
	FreeTextQualification *FreeTextQualification `xml:"freeTextQualification"`
	FreeText              []string               `xml:"freeText"`
}

// ProductInfo ...
type ProductInfo struct {
	// XMLName          xml.Name            `xml:"productInfo"`
	ProductDetails   *ProductDetails     `xml:"productDetails"`
	ProductErrorCode []*ProductErrorCode `xml:"productErrorCode"`
}

// MonetaryDetails ...
type MonetaryDetails struct {
	// XMLName       xml.Name `xml:"monetaryDetails"`
	TypeQualifier string `xml:"typeQualifier"`
	Amount        string `xml:"amount"`
	Currency      string `xml:"currency"`
}

// AmountTwo ...
type AmountTwo struct {
	// XMLName       xml.Name `xml:"amountTwo"`
	TypeQualifier string `xml:"typeQualifier"`
	Amount        string `xml:"amount"`
	Currency      string `xml:"currency"`
}

// MonetaryRates ...
type MonetaryRates struct {
	// XMLName         xml.Name         `xml:"monetaryRates"`
	MonetaryDetails *MonetaryDetails `xml:"monetaryDetails"`
	AmountTwo       []*AmountTwo     `xml:"amountTwo"`
}

// TaxDetails ...
type TaxDetails struct {
	// XMLName      xml.Name `xml:"taxDetails"`
	Rate         string   `xml:"rate"`
	CountryCode  string   `xml:"countryCode"`
	CurrencyCode string   `xml:"currencyCode"`
	Type         []string `xml:"type"`
}

// TaxAmount ...
type TaxAmount struct {
	// XMLName    xml.Name      `xml:"taxAmount"`
	TaxDetails []*TaxDetails `xml:"taxDetails"`
}

// FareDetailQualif ...
type FareDetailQualif struct {
	// XMLName               xml.Name               `xml:"fareDetailQualif"`
	MovementType          string                 `xml:"movementType"`
	FareCategories        *FareCategories        `xml:"fareCategories"`
	FareDetails           *FareDetails           `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareDetails `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountDetails     `xml:"discountDetails"`
}

// DateAndTimeDetails ...
type DateAndTimeDetails struct {
	// XMLName   xml.Name `xml:"dateAndTimeDetails"`
	Qualifier string `xml:"qualifier"`
	Date      string `xml:"date"`
}

// FlightMovementDate ...
type FlightMovementDate struct {
	// XMLName            xml.Name              `xml:"flightMovementDate"`
	DateAndTimeDetails []*DateAndTimeDetails `xml:"dateAndTimeDetails"`
}

// FaraRulesInfo ...
type FaraRulesInfo struct {
	// XMLName            xml.Name        `xml:"faraRulesInfo"`
	RuleSectionLocalId string          `xml:"ruleSectionLocalId"`
	CompanyDetails     *CompanyDetails `xml:"companyDetails"`
	RuleCategoryCode   string          `xml:"ruleCategoryCode"`
}

// SelectionDetails ...
type SelectionDetails struct {
	// XMLName           xml.Name `xml:"selectionDetails"`
	Option            string `xml:"option"`
	OptionInformation string `xml:"optionInformation"`
}

// SelectionDetailsTwo ...
type SelectionDetailsTwo struct {
	// XMLName           xml.Name `xml:"selectionDetailsTwo"`
	Option            string `xml:"option"`
	OptionInformation string `xml:"optionInformation"`
}

// SelectionMakingDetails ...
type SelectionMakingDetails struct {
	// XMLName             xml.Name               `xml:"selectionMakingDetails"`
	SelectionDetails    *SelectionDetails      `xml:"selectionDetails"`
	SelectionDetailsTwo []*SelectionDetailsTwo `xml:"selectionDetailsTwo"`
}

// AmountConvDetails ...
type AmountConvDetails struct {
	// XMLName                    xml.Name                      `xml:"amountConvDetails"`
	ConversionRateDetails      *ConversionRateDetails        `xml:"conversionRateDetails"`
	OtherConversionRateDetails []*OtherConversionRateDetails `xml:"otherConversionRateDetails"`
}

// FareTypeInfo ...
type FareTypeInfo struct {
	// XMLName                xml.Name                `xml:"fareTypeInfo"`
	FareDetailQualif       *FareDetailQualif       `xml:"fareDetailQualif"`
	FlightMovementDate     *FlightMovementDate     `xml:"flightMovementDate"`
	FaraRulesInfo          *FaraRulesInfo          `xml:"faraRulesInfo"`
	SelectionMakingDetails *SelectionMakingDetails `xml:"selectionMakingDetails"`
	AmountConvDetails      *AmountConvDetails      `xml:"amountConvDetails"`
}

// PriceInfo ...
type PriceInfo struct {
	// XMLName       xml.Name        `xml:"priceInfo"`
	MonetaryRates *MonetaryRates  `xml:"monetaryRates"`
	TaxAmount     *TaxAmount      `xml:"taxAmount"`
	FareTypeInfo  []*FareTypeInfo `xml:"fareTypeInfo"`
}

// NbOfUnits ...
type NbOfUnits struct {
	// XMLName         xml.Name           `xml:"nbOfUnits"`
	QuantityDetails []*QuantityDetails `xml:"quantityDetails"`
}

// PricingPlusDateInfo ...
type PricingPlusDateInfo struct {
	// XMLName                xml.Name                `xml:"pricingPlusDateInfo"`
	ProductDateTimeDetails *ProductDateTimeDetails `xml:"productDateTimeDetails"`
	LocationDetails        *LocationDetails        `xml:"locationDetails"`
	OtherLocationDetails   *OtherLocationDetails   `xml:"otherLocationDetails"`
	IdNumber               string                  `xml:"idNumber"`
}

// FareTypeGrouping ...
type FareTypeGrouping struct {
	// XMLName      xml.Name `xml:"fareTypeGrouping"`
	PricingGroup []string `xml:"pricingGroup"`
}

// FareDeatilInfo ...
type FareDeatilInfo struct {
	// XMLName          xml.Name          `xml:"fareDeatilInfo"`
	FareTypeGrouping *FareTypeGrouping `xml:"fareTypeGrouping"`
}

// FareDetailInfo ...
type FareDetailInfo struct {
	// XMLName             xml.Name             `xml:"fareDetailInfo"`
	NbOfUnits           *NbOfUnits           `xml:"nbOfUnits"`
	PricingPlusDateInfo *PricingPlusDateInfo `xml:"pricingPlusDateInfo"`
	FareDeatilInfo      *FareDeatilInfo      `xml:"fareDeatilInfo"`
}

// OriginDestination ...
type OriginDestination struct {
	// XMLName     xml.Name `xml:"originDestination"`
	Origin      string `xml:"origin"`
	Destination string `xml:"destination"`
}

// FlightDateAndTime ...
type FlightDateAndTime struct {
	// XMLName            xml.Name              `xml:"flightDateAndTime"`
	DateAndTimeDetails []*DateAndTimeDetails `xml:"dateAndTimeDetails"`
}

// FlightErrorText ...
type FlightErrorText struct {
	// XMLName               xml.Name               `xml:"flightErrorText"`
	FreeTextQualification *FreeTextQualification `xml:"freeTextQualification"`
	FreeText              []string               `xml:"freeText"`
}

// MonetaryValues ...
type MonetaryValues struct {
	// XMLName         xml.Name         `xml:"monetaryValues"`
	MonetaryDetails *MonetaryDetails `xml:"monetaryDetails"`
	AmountTwo       []*AmountTwo     `xml:"amountTwo"`
}

// FareQualif ...
type FareQualif struct {
	// XMLName               xml.Name               `xml:"fareQualif"`
	MovementType          string                 `xml:"movementType"`
	FareCategories        *FareCategories        `xml:"fareCategories"`
	FareDetails           *FareDetails           `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareDetails `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountDetails     `xml:"discountDetails"`
}

// AmountCvtRate ...
type AmountCvtRate struct {
	// XMLName                    xml.Name                      `xml:"amountCvtRate"`
	ConversionRateDetails      *ConversionRateDetails        `xml:"conversionRateDetails"`
	OtherConversionRateDetails []*OtherConversionRateDetails `xml:"otherConversionRateDetails"`
}

// FareDetailGrp ...
type FareDetailGrp struct {
	// XMLName       xml.Name       `xml:"fareDetailGrp"`
	FareQualif    *FareQualif    `xml:"fareQualif"`
	AmountCvtRate *AmountCvtRate `xml:"amountCvtRate"`
}

// MonGrp ...
type MonGrp struct {
	// XMLName        xml.Name         `xml:"monGrp"`
	MonetaryValues *MonetaryValues  `xml:"monetaryValues"`
	FareDetailGrp  []*FareDetailGrp `xml:"fareDetailGrp"`
}

// RoutingDetails ...
type RoutingDetails struct {
	// XMLName      xml.Name `xml:"routingDetails"`
	Station      string `xml:"station"`
	OtherStation string `xml:"otherStation"`
	Qualifier    string `xml:"qualifier"`
}

// RoutingInfo ...
type RoutingInfo struct {
	// XMLName        xml.Name          `xml:"routingInfo"`
	RoutingDetails []*RoutingDetails `xml:"routingDetails"`
}

// ServiceTransport ...
type ServiceTransport struct {
	// XMLName                      xml.Name                        `xml:"serviceTransport"`
	CompanyIdentification        *CompanyIdentification          `xml:"companyIdentification"`
	ProductIdentificationDetails []*ProductIdentificationDetails `xml:"productIdentificationDetails"`
}

// QualificationOfFare ...
type QualificationOfFare struct {
	// XMLName               xml.Name               `xml:"qualificationOfFare"`
	MovementType          string                 `xml:"movementType"`
	FareCategories        *FareCategories        `xml:"fareCategories"`
	FareDetails           *FareDetails           `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareDetails `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountDetails     `xml:"discountDetails"`
}

// PertinentQuantity ...
type PertinentQuantity struct {
	// XMLName         xml.Name           `xml:"pertinentQuantity"`
	QuantityDetails []*QuantityDetails `xml:"quantityDetails"`
}

// RoutingGrp ...
type RoutingGrp struct {
	// XMLName             xml.Name             `xml:"routingGrp"`
	RoutingInfo         *RoutingInfo         `xml:"routingInfo"`
	ServiceTransport    *ServiceTransport    `xml:"serviceTransport"`
	QualificationOfFare *QualificationOfFare `xml:"qualificationOfFare"`
	PertinentQuantity   *PertinentQuantity   `xml:"pertinentQuantity"`
}

// FlightDate ...
type FlightDate struct {
	// XMLName       xml.Name `xml:"flightDate"`
	DepartureDate string `xml:"departureDate"`
	ArrivalDate   string `xml:"arrivalDate"`
}

// BoardPointDetails ...
type BoardPointDetails struct {
	// XMLName        xml.Name `xml:"boardPointDetails"`
	TrueLocationId string `xml:"trueLocationId"`
	TrueLocation   string `xml:"trueLocation"`
}

// OffpointDetails ...
type OffpointDetails struct {
	// XMLName        xml.Name `xml:"offpointDetails"`
	TrueLocationId string `xml:"trueLocationId"`
	TrueLocation   string `xml:"trueLocation"`
}

// FlightIdentification ...
type FlightIdentification struct {
	// XMLName           xml.Name `xml:"flightIdentification"`
	FlightNumber      string `xml:"flightNumber"`
	OperationalSuffix string `xml:"operationalSuffix"`
}

// FlightTypeDetails ...
type FlightTypeDetails struct {
	// XMLName          xml.Name `xml:"flightTypeDetails"`
	FlightIndicator  string   `xml:"flightIndicator"`
	SecondSequenceNb []string `xml:"secondSequenceNb"`
}

// MarriageDetails ...
type MarriageDetails struct {
	// XMLName            xml.Name `xml:"marriageDetails"`
	Relation           string   `xml:"relation"`
	MarriageIdentifier *float64 `xml:"marriageIdentifier"`
	LineNumber         *float64 `xml:"lineNumber"`
	OtherRelation      string   `xml:"otherRelation"`
	CarrierCode        string   `xml:"carrierCode"`
}

// TravelProductInfo ...
type TravelProductInfo struct {
	// XMLName              xml.Name              `xml:"travelProductInfo"`
	FlightDate           *FlightDate           `xml:"flightDate"`
	BoardPointDetails    *BoardPointDetails    `xml:"boardPointDetails"`
	OffpointDetails      *OffpointDetails      `xml:"offpointDetails"`
	CompanyDetails       *CompanyDetails       `xml:"companyDetails"`
	FlightIdentification *FlightIdentification `xml:"flightIdentification"`
	FlightTypeDetails    *FlightTypeDetails    `xml:"flightTypeDetails"`
	ItemNumber           *float64              `xml:"itemNumber"`
	SpecialSegment       string                `xml:"specialSegment"`
	MarriageDetails      *MarriageDetails      `xml:"marriageDetails"`
}

// TransportServiceChange ...
type TransportServiceChange struct {
	// XMLName                      xml.Name                        `xml:"transportServiceChange"`
	CompanyIdentification        *CompanyIdentification          `xml:"companyIdentification"`
	ProductIdentificationDetails []*ProductIdentificationDetails `xml:"productIdentificationDetails"`
}

// TravelProductGrp ...
type TravelProductGrp struct {
	// XMLName           xml.Name           `xml:"travelProductGrp"`
	TravelProductInfo *TravelProductInfo `xml:"travelProductInfo"`
	RoutingGrp        []*RoutingGrp      `xml:"routingGrp"`
}

// OdiGrp ...
type OdiGrp struct {
	// XMLName           xml.Name             `xml:"odiGrp"`
	OriginDestination *OriginDestination   `xml:"originDestination"`
	FlightDateAndTime []*FlightDateAndTime `xml:"flightDateAndTime"`
	FlightErrorText   *FlightErrorText     `xml:"flightErrorText"`
	MonGrp            []*MonGrp            `xml:"monGrp"`
	RoutingGrp        []*RoutingGrp        `xml:"routingGrp"`
	TravelProductGrp  []*TravelProductGrp  `xml:"travelProductGrp"`
}

// ReferenceDetails ...
type ReferenceDetails struct {
	// XMLName xml.Name `xml:"referenceDetails"`
	Type  string `xml:"type"`
	Value string `xml:"value"`
}

// TravellerIdentRef ...
type TravellerIdentRef struct {
	// XMLName          xml.Name            `xml:"travellerIdentRef"`
	ReferenceDetails []*ReferenceDetails `xml:"referenceDetails"`
}

// FareRulesDetails ...
type FareRulesDetails struct {
	// XMLName        xml.Name        `xml:"fareRulesDetails"`
	TariffClassId  string          `xml:"tariffClassId"`
	CompanyDetails *CompanyDetails `xml:"companyDetails"`
	RuleSectionId  []string        `xml:"ruleSectionId"`
}

// FlightMovementDateInfo ...
type FlightMovementDateInfo struct {
	// XMLName            xml.Name              `xml:"flightMovementDateInfo"`
	DateAndTimeDetails []*DateAndTimeDetails `xml:"dateAndTimeDetails"`
}

// TravellerGrp ...
type TravellerGrp struct {
	// XMLName                xml.Name                `xml:"travellerGrp"`
	TravellerIdentRef      *TravellerIdentRef      `xml:"travellerIdentRef"`
	FareRulesDetails       *FareRulesDetails       `xml:"fareRulesDetails"`
	FlightMovementDateInfo *FlightMovementDateInfo `xml:"flightMovementDateInfo"`
}

// JourneyOriginAndDestination ...
type JourneyOriginAndDestination struct {
	// XMLName     xml.Name `xml:"journeyOriginAndDestination"`
	Origin      string `xml:"origin"`
	Destination string `xml:"destination"`
}

// JourneyProductInfo ...
type JourneyProductInfo struct {
	// XMLName              xml.Name              `xml:"journeyProductInfo"`
	FlightDate           *FlightDate           `xml:"flightDate"`
	BoardPointDetails    *BoardPointDetails    `xml:"boardPointDetails"`
	OffpointDetails      *OffpointDetails      `xml:"offpointDetails"`
	CompanyDetails       *CompanyDetails       `xml:"companyDetails"`
	FlightIdentification *FlightIdentification `xml:"flightIdentification"`
	FlightTypeDetails    *FlightTypeDetails    `xml:"flightTypeDetails"`
	ItemNumber           *float64              `xml:"itemNumber"`
	SpecialSegment       string                `xml:"specialSegment"`
	MarriageDetails      *MarriageDetails      `xml:"marriageDetails"`
}

// JourneyRoutingInfo ...
type JourneyRoutingInfo struct {
	// XMLName        xml.Name          `xml:"journeyRoutingInfo"`
	RoutingDetails []*RoutingDetails `xml:"routingDetails"`
}

// JourneyTransportService ...
type JourneyTransportService struct {
	// XMLName                      xml.Name                        `xml:"journeyTransportService"`
	CompanyIdentification        *CompanyIdentification          `xml:"companyIdentification"`
	ProductIdentificationDetails []*ProductIdentificationDetails `xml:"productIdentificationDetails"`
}

// JourneyRoutingGrp ...
type JourneyRoutingGrp struct {
	// XMLName                 xml.Name                 `xml:"journeyRoutingGrp"`
	JourneyRoutingInfo      *JourneyRoutingInfo      `xml:"journeyRoutingInfo"`
	JourneyTransportService *JourneyTransportService `xml:"journeyTransportService"`
}

// JourneyProductGrp ...
type JourneyProductGrp struct {
	// XMLName            xml.Name             `xml:"journeyProductGrp"`
	JourneyProductInfo *JourneyProductInfo  `xml:"journeyProductInfo"`
	JourneyRoutingGrp  []*JourneyRoutingGrp `xml:"journeyRoutingGrp"`
}

// JourneyGrp ...
type JourneyGrp struct {
	// XMLName                     xml.Name                     `xml:"journeyGrp"`
	JourneyOriginAndDestination *JourneyOriginAndDestination `xml:"journeyOriginAndDestination"`
	JourneyProductGrp           []*JourneyProductGrp         `xml:"journeyProductGrp"`
}

// FareRouteGrp ...
type FareRouteGrp struct {
	// XMLName       xml.Name       `xml:"fareRouteGrp"`
	FareRouteInfo *FareRouteInfo `xml:"fareRouteInfo"`
	JourneyGrp    []*JourneyGrp  `xml:"journeyGrp"`
}

// ItemNumberDetails ...
type ItemNumberDetails struct {
	// XMLName xml.Name `xml:"itemNumberDetails"`
	Number string `xml:"number"`
	Type   string `xml:"type"`
}

// ItemNb ...
type ItemNb struct {
	// XMLName           xml.Name             `xml:"itemNb"`
	ItemNumberDetails []*ItemNumberDetails `xml:"itemNumberDetails"`
}

// ProductAvailabilityStatus ...
type ProductAvailabilityStatus struct {
	// XMLName                 xml.Name               `xml:"productAvailabilityStatus"`
	ProductDetailsQualifier string                 `xml:"productDetailsQualifier"`
	BookingClassDetails     []*BookingClassDetails `xml:"bookingClassDetails"`
}

// QuantityItem ...
type QuantityItem struct {
	// XMLName         xml.Name           `xml:"quantityItem"`
	QuantityDetails []*QuantityDetails `xml:"quantityDetails"`
}

// TransportServiceItem ...
type TransportServiceItem struct {
	// XMLName                      xml.Name                        `xml:"transportServiceItem"`
	CompanyIdentification        *CompanyIdentification          `xml:"companyIdentification"`
	ProductIdentificationDetails []*ProductIdentificationDetails `xml:"productIdentificationDetails"`
}

// FreeTextItem ...
type FreeTextItem struct {
	// XMLName               xml.Name               `xml:"freeTextItem"`
	FreeTextQualification *FreeTextQualification `xml:"freeTextQualification"`
	FreeText              []string               `xml:"freeText"`
}

// FareQualifItem ...
type FareQualifItem struct {
	// XMLName               xml.Name               `xml:"fareQualifItem"`
	MovementType          string                 `xml:"movementType"`
	FareCategories        *FareCategories        `xml:"fareCategories"`
	FareDetails           *FareDetails           `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareDetails `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountDetails     `xml:"discountDetails"`
}

// OriginDestOfJourney ...
type OriginDestOfJourney struct {
	// XMLName     xml.Name `xml:"originDestOfJourney"`
	Origin      string `xml:"origin"`
	Destination string `xml:"destination"`
}

// DateForMovements ...
type DateForMovements struct {
	// XMLName            xml.Name              `xml:"dateForMovements"`
	DateAndTimeDetails []*DateAndTimeDetails `xml:"dateAndTimeDetails"`
}

// RoutingForJourney ...
type RoutingForJourney struct {
	// XMLName        xml.Name          `xml:"routingForJourney"`
	RoutingDetails []*RoutingDetails `xml:"routingDetails"`
}

// OriginDestinationGrp ...
type OriginDestinationGrp struct {
	// XMLName             xml.Name             `xml:"originDestinationGrp"`
	OriginDestOfJourney *OriginDestOfJourney `xml:"originDestOfJourney"`
	DateForMovements    *DateForMovements    `xml:"dateForMovements"`
	RoutingForJourney   *RoutingForJourney   `xml:"routingForJourney"`
}

// UnitPricingAndDateInfo ...
type UnitPricingAndDateInfo struct {
	// XMLName                xml.Name                `xml:"unitPricingAndDateInfo"`
	ProductDateTimeDetails *ProductDateTimeDetails `xml:"productDateTimeDetails"`
	LocationDetails        *LocationDetails        `xml:"locationDetails"`
	OtherLocationDetails   *OtherLocationDetails   `xml:"otherLocationDetails"`
	IdNumber               string                  `xml:"idNumber"`
}

// UnitFareDetails ...
type UnitFareDetails struct {
	// XMLName          xml.Name          `xml:"unitFareDetails"`
	FareTypeGrouping *FareTypeGrouping `xml:"fareTypeGrouping"`
}

// UnitGrp ...
type UnitGrp struct {
	// XMLName                xml.Name                `xml:"unitGrp"`
	NbOfUnits              *NbOfUnits              `xml:"nbOfUnits"`
	UnitPricingAndDateInfo *UnitPricingAndDateInfo `xml:"unitPricingAndDateInfo"`
	UnitFareDetails        *UnitFareDetails        `xml:"unitFareDetails"`
}

// MonetFareRuleValues ...
type MonetFareRuleValues struct {
	// XMLName            xml.Name        `xml:"monetFareRuleValues"`
	RuleSectionLocalId string          `xml:"ruleSectionLocalId"`
	CompanyDetails     *CompanyDetails `xml:"companyDetails"`
	RuleCategoryCode   string          `xml:"ruleCategoryCode"`
}

// MonetTravellerRef ...
type MonetTravellerRef struct {
	// XMLName          xml.Name            `xml:"monetTravellerRef"`
	ReferenceDetails []*ReferenceDetails `xml:"referenceDetails"`
}

// MonetTicketPriceAndDate ...
type MonetTicketPriceAndDate struct {
	// XMLName                xml.Name                `xml:"monetTicketPriceAndDate"`
	ProductDateTimeDetails *ProductDateTimeDetails `xml:"productDateTimeDetails"`
	LocationDetails        *LocationDetails        `xml:"locationDetails"`
	OtherLocationDetails   *OtherLocationDetails   `xml:"otherLocationDetails"`
	IdNumber               string                  `xml:"idNumber"`
}

// MonetTaxValues ...
type MonetTaxValues struct {
	// XMLName    xml.Name      `xml:"monetTaxValues"`
	TaxDetails []*TaxDetails `xml:"taxDetails"`
}

// QualificationFare ...
type QualificationFare struct {
	// XMLName               xml.Name               `xml:"qualificationFare"`
	MovementType          string                 `xml:"movementType"`
	FareCategories        *FareCategories        `xml:"fareCategories"`
	FareDetails           *FareDetails           `xml:"fareDetails"`
	AdditionalFareDetails *AdditionalFareDetails `xml:"additionalFareDetails"`
	DiscountDetails       []*DiscountDetails     `xml:"discountDetails"`
}

// QualifSelection ...
type QualifSelection struct {
	// XMLName             xml.Name               `xml:"qualifSelection"`
	SelectionDetails    *SelectionDetails      `xml:"selectionDetails"`
	SelectionDetailsTwo []*SelectionDetailsTwo `xml:"selectionDetailsTwo"`
}

// QualifDateFlightMovement ...
type QualifDateFlightMovement struct {
	// XMLName            xml.Name              `xml:"qualifDateFlightMovement"`
	DateAndTimeDetails []*DateAndTimeDetails `xml:"dateAndTimeDetails"`
}

// QualifConversionRate ...
type QualifConversionRate struct {
	// XMLName                    xml.Name                      `xml:"qualifConversionRate"`
	ConversionRateDetails      *ConversionRateDetails        `xml:"conversionRateDetails"`
	OtherConversionRateDetails []*OtherConversionRateDetails `xml:"otherConversionRateDetails"`
}

// QualifGrp ...
type QualifGrp struct {
	// XMLName                  xml.Name                  `xml:"qualifGrp"`
	QualificationFare        *QualificationFare        `xml:"qualificationFare"`
	QualifSelection          *QualifSelection          `xml:"qualifSelection"`
	QualifDateFlightMovement *QualifDateFlightMovement `xml:"qualifDateFlightMovement"`
	QualifConversionRate     *QualifConversionRate     `xml:"qualifConversionRate"`
}

// MonetaryGrp ...
type MonetaryGrp struct {
	// XMLName                 xml.Name                 `xml:"monetaryGrp"`
	MonetaryValues          *MonetaryValues          `xml:"monetaryValues"`
	MonetFareRuleValues     *MonetFareRuleValues     `xml:"monetFareRuleValues"`
	MonetTravellerRef       *MonetTravellerRef       `xml:"monetTravellerRef"`
	MonetTicketPriceAndDate *MonetTicketPriceAndDate `xml:"monetTicketPriceAndDate"`
	MonetTaxValues          *MonetTaxValues          `xml:"monetTaxValues"`
	QualifGrp               []*QualifGrp             `xml:"qualifGrp"`
}

// InfoForFareRoute ...
type InfoForFareRoute struct {
	// XMLName              xml.Name              `xml:"infoForFareRoute"`
	DayOfWeek            string                `xml:"dayOfWeek"`
	FareQualifierDetails *FareQualifierDetails `xml:"fareQualifierDetails"`
	IdentificationNumber string                `xml:"identificationNumber"`
	ValidityPeriod       *ValidityPeriod       `xml:"validityPeriod"`
}

// FarerouteTransportService ...
type FarerouteTransportService struct {
	// XMLName                      xml.Name                        `xml:"farerouteTransportService"`
	CompanyIdentification        *CompanyIdentification          `xml:"companyIdentification"`
	ProductIdentificationDetails []*ProductIdentificationDetails `xml:"productIdentificationDetails"`
}

// FinalOriginDestination ...
type FinalOriginDestination struct {
	// XMLName     xml.Name `xml:"finalOriginDestination"`
	Origin      string `xml:"origin"`
	Destination string `xml:"destination"`
}

// LastOdiRoutingInfo ...
type LastOdiRoutingInfo struct {
	// XMLName        xml.Name          `xml:"lastOdiRoutingInfo"`
	RoutingDetails []*RoutingDetails `xml:"routingDetails"`
}

// LastOdiDateFlightMovement ...
type LastOdiDateFlightMovement struct {
	// XMLName            xml.Name              `xml:"lastOdiDateFlightMovement"`
	DateAndTimeDetails []*DateAndTimeDetails `xml:"dateAndTimeDetails"`
}

// FinalOdiGrp ...
type FinalOdiGrp struct {
	// XMLName                   xml.Name                   `xml:"finalOdiGrp"`
	FinalOriginDestination    *FinalOriginDestination    `xml:"finalOriginDestination"`
	LastOdiRoutingInfo        *LastOdiRoutingInfo        `xml:"lastOdiRoutingInfo"`
	LastOdiDateFlightMovement *LastOdiDateFlightMovement `xml:"lastOdiDateFlightMovement"`
}

// FarerouteGrp ...
type FarerouteGrp struct {
	// XMLName                   xml.Name                     `xml:"farerouteGrp"`
	InfoForFareRoute          *InfoForFareRoute            `xml:"infoForFareRoute"`
	FarerouteTransportService []*FarerouteTransportService `xml:"farerouteTransportService"`
	FinalOdiGrp               []*FinalOdiGrp               `xml:"finalOdiGrp"`
}

// ItemGrp ...
type ItemGrp struct {
	// XMLName                   xml.Name                   `xml:"itemGrp"`
	ItemNb                    *ItemNb                    `xml:"itemNb"`
	ProductAvailabilityStatus *ProductAvailabilityStatus `xml:"productAvailabilityStatus"`
	QuantityItem              *QuantityItem              `xml:"quantityItem"`
	TransportServiceItem      []*TransportServiceItem    `xml:"transportServiceItem"`
	FreeTextItem              []*FreeTextItem            `xml:"freeTextItem"`
	FareQualifItem            []*FareQualifItem          `xml:"fareQualifItem"`
	OriginDestinationGrp      []*OriginDestinationGrp    `xml:"originDestinationGrp"`
	UnitGrp                   []*UnitGrp                 `xml:"unitGrp"`
	MonetaryGrp               []*MonetaryGrp             `xml:"monetaryGrp"`
	FarerouteGrp              []*FarerouteGrp            `xml:"farerouteGrp"`
}

// FlightDetails ...
type FlightDetails struct {
	// XMLName                  xml.Name                    `xml:"flightDetails"`
	NbOfSegments             *NbOfSegments               `xml:"nbOfSegments"`
	AmountConversion         *AmountConversion           `xml:"amountConversion"`
	QuantityValue            *QuantityValue              `xml:"quantityValue"`
	PricingAndDateInfo       *PricingAndDateInfo         `xml:"pricingAndDateInfo"`
	QualificationFareDetails []*QualificationFareDetails `xml:"qualificationFareDetails"`
	TransportService         []*TransportService         `xml:"transportService"`
	FlightErrorCode          []*FlightErrorCode          `xml:"flightErrorCode"`
	ProductInfo              []*ProductInfo              `xml:"productInfo"`
	PriceInfo                []*PriceInfo                `xml:"priceInfo"`
	FareDetailInfo           []*FareDetailInfo           `xml:"fareDetailInfo"`
	OdiGrp                   []*OdiGrp                   `xml:"odiGrp"`
	TravellerGrp             []*TravellerGrp             `xml:"travellerGrp"`
	FareRouteGrp             []*FareRouteGrp             `xml:"fareRouteGrp"`
	ItemGrp                  []*ItemGrp                  `xml:"itemGrp"`
}

// FareCheckRulesReply ...
type FareCheckRulesReply struct {
	// XMLName         xml.Name         `xml:"Fare_CheckRulesReply"`
	TransactionType *TransactionType `xml:"transactionType"`
	StatusInfo      *StatusInfo      `xml:"statusInfo"`
	FareRouteInfo   *FareRouteInfo   `xml:"fareRouteInfo"`
	InfoText        []*InfoText      `xml:"infoText"`
	ErrorInfo       *ErrorInfo       `xml:"errorInfo"`
	TariffInfo      []*TariffInfo    `xml:"tariffInfo"`
	FlightDetails   []*FlightDetails `xml:"flightDetails"`
}

// Date is Format limitations: an..6
type Date string
