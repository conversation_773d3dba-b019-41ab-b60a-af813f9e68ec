package models

import "gitlab.deepgate.io/apps/common/enum"

type AmadeusResponse struct {
	IsSuccess bool    `json:"is_success,omitempty"`
	Data      *Data   `json:"data,omitempty"`
	ErrorCode *string `json:"error_code,omitempty"`
	Message   *string `json:"message,omitempty"`
}

type Data struct {
	Session *Session `json:"session,omitempty"`
	Data    string   `json:"data,omitempty"`
}

type Session struct {
	SessionID      string `json:"sessionId,omitempty"`
	SequenceNumber int    `json:"sequenceNumber,omitempty"`
	SecurityToken  string `json:"securityToken,omitempty"`
}

type EMDInfo struct {
	EMDNumber    string
	SeatCode     string
	StartPoint   string
	ArrivalPoint string
}
type EticketInfo struct {
	Number       string
	Surname      string
	GivenName    string
	Gender       enum.GenderType
	StartPoint   string
	ArrivalPoint string
	StartDate    int64
}

type ReservationInfo struct {
	StartPoint      string
	ArrivalPoint    string
	StartDate       int64
	ReservationCode string
}
