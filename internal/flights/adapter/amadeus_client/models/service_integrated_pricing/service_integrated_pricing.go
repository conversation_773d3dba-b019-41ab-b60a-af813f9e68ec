// Code generated by xgen. DO NOT EDIT.

package service_integrated_pricing

// ServiceIntegratedPricingReply ...
type ServiceIntegratedPricingReply struct {
	//  XMLName           xml.Name          `xml:"Service_IntegratedPricingReply"`
	ErrorGroup        []*ErrorGroupType `xml:"errorGroup"`
	AllFaresInfoGroup []*FaresInfoType  `xml:"allFaresInfoGroup"`
}

// ActionDetailsType is Range of segments
type ActionDetailsType struct {
	NumberOfItemsDetails *ProcessingInformationType `xml:"numberOfItemsDetails"`
	LastItemsDetails     []*ReferenceType           `xml:"lastItemsDetails"`
}

// AdditionalProductDetailsTypeI is Leg details
type AdditionalProductDetailsTypeI struct {
	LegDetails *AdditionalProductTypeI `xml:"legDetails"`
}

// AdditionalProductTypeI is Equipment
type AdditionalProductTypeI struct {
	Equipment string `xml:"equipment"`
}

// ApplicationErrorDetailTypeI is Error coded
type ApplicationErrorDetailTypeI struct {
	ErrorCode string `xml:"errorCode"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationTypeI is Application Error Details
type ApplicationErrorInformationTypeI struct {
	ErrorDetails *ApplicationErrorDetailTypeI `xml:"errorDetails"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// AttributeInformationType is Attribute description
type AttributeInformationType struct {
	AttributeType        string  `xml:"attributeType"`
	AttributeDescription string `xml:"attributeDescription"`
}

// AttributeType is Criteria details
type AttributeType struct {
	AttributeDetails []*AttributeInformationType `xml:"attributeDetails"`
}

// AttributeType67628S is Criteria details
type AttributeType67628S struct {
	//  XMLName            xml.Name                      `xml:"AttributeType_67628S"`
	AttributeQualifier string `xml:"attributeQualifier"`
	AttributeDetails   []*AttributeInformationType   `xml:"attributeDetails"`
}

// CompanyIdentificationNumbersTypeI is Other identifier
type CompanyIdentificationNumbersTypeI struct {
	Identifier      string `xml:"identifier"`
	OtherIdentifier string `xml:"otherIdentifier"`
}

// CompanyIdentificationTypeI is Other company
type CompanyIdentificationTypeI struct {
	MarketingCompany string `xml:"marketingCompany"`
	OperatingCompany string `xml:"operatingCompany"`
	OtherCompany     string `xml:"otherCompany"`
}

// CompanyIdentificationType is Operating company
type CompanyIdentificationType struct {
	MarketingCompany string `xml:"marketingCompany"`
	OperatingCompany string `xml:"operatingCompany"`
}

// CompanyIdentificationType231301C is Marketing company
type CompanyIdentificationType231301C struct {
	//  XMLName          xml.Name                      `xml:"CompanyIdentificationType_231301C"`
	MarketingCompany string `xml:"marketingCompany"`
}

// ConversionRateDetailsTypeI is Currency
type ConversionRateDetailsTypeI struct {
	ConversionType string `xml:"conversionType"`
	Currency       string `xml:"currency"`
}

// ConversionRateDetailsTypeI231308C is Conversion rate
type ConversionRateDetailsTypeI231308C struct {
	//  XMLName        xml.Name                      `xml:"ConversionRateDetailsTypeI_231308C"`
	Currency       string `xml:"currency"`
	ConversionRate float64                       `xml:"conversionRate"`
}

// ConversionRateDetailsType is Currency
type ConversionRateDetailsType struct {
	ConversionType string `xml:"conversionType"`
	Currency       string `xml:"currency"`
}

// ConversionRateType is Conversion rate details
type ConversionRateType struct {
	ConversionRateDetails []*ConversionRateDetailsType `xml:"conversionRateDetails"`
}

// ConversionRateType163191S is Conversion rate details
type ConversionRateType163191S struct {
	//  XMLName               xml.Name                      `xml:"ConversionRateType_163191S"`
	ConversionRateDetails []*ConversionRateDetailsTypeI `xml:"conversionRateDetails"`
}

// ConversionRateType163222S is Conversion rate details
type ConversionRateType163222S struct {
	//  XMLName               xml.Name                             `xml:"ConversionRateType_163222S"`
	ConversionRateDetails []*ConversionRateDetailsTypeI231308C `xml:"conversionRateDetails"`
}

// CorporateFareIdentifiersType is Corporate contract number or name
type CorporateFareIdentifiersType struct {
	FareQualifier  string    `xml:"fareQualifier"`
	IdentifyNumber []string `xml:"identifyNumber"`
}

// CorporateFareInformationType is Corporate fare identifiers
type CorporateFareInformationType struct {
	CorporateFareIdentifiers *CorporateFareIdentifiersType `xml:"corporateFareIdentifiers"`
}

// DataTypeInformationType is Status (automated, manually added, exempted). Default is automated
type DataTypeInformationType struct {
	SubType string `xml:"subType"`
	Status  string `xml:"status"`
}

// DateAndTimeDetailsType is Location
type DateAndTimeDetailsType struct {
	Qualifier      string  `xml:"qualifier"`
	Date           string                    `xml:"date"`
	Time           string                    `xml:"time"`
	OtherQualifier string  `xml:"otherQualifier"`
	OtherTime      string                    `xml:"otherTime"`
	MovementType   string  `xml:"movementType"`
	Location       string `xml:"location"`
}

// DateAndTimeDetailsType231262C is Time
type DateAndTimeDetailsType231262C struct {
	//  XMLName xml.Name    `xml:"DateAndTimeDetailsType_231262C"`
	Date    string `xml:"date"`
	Time    string `xml:"time"`
}

// DateAndTimeInformationType is Date and time details
type DateAndTimeInformationType struct {
	DateAndTimeDetails []*DateAndTimeDetailsType231262C `xml:"dateAndTimeDetails"`
}

// DateAndTimeInformationType67653S is Date and time details
type DateAndTimeInformationType67653S struct {
	//  XMLName            xml.Name                  `xml:"DateAndTimeInformationType_67653S"`
	DateAndTimeDetails []*DateAndTimeDetailsType `xml:"dateAndTimeDetails"`
}

// DummySegmentTypeI is To serve the purpose of a mandatory segment at the beginning of a group and to avoid segment collision.
type DummySegmentTypeI struct {
}

// ErrorGroupType is The desciption of warning or error.
type ErrorGroupType struct {
	ErrorOrWarningCodeDetails *ApplicationErrorInformationType `xml:"errorOrWarningCodeDetails"`
	ErrorWarningDescription   *FreeTextInformationType         `xml:"errorWarningDescription"`
}

// FareCategoryCodesTypeI is Fare type
type FareCategoryCodesTypeI struct {
	FareType string `xml:"fareType"`
}

// FareComponentDetailsType is Input designator
type FareComponentDetailsType struct {
	DataType        string  `xml:"dataType"`
	Count           int     `xml:"count"`
	PricingDate     string `xml:"pricingDate"`
	AccountCode     string `xml:"accountCode"`
	InputDesignator string `xml:"inputDesignator"`
}

// FareComponentInfoType is Fare component pricing and ticketing details
type FareComponentInfoType struct {
	ComponentIdentInfo             *ItemNumberType163213S              `xml:"componentIdentInfo"`
	SubItineraryInfo               []*SubItineraryInfoType             `xml:"subItineraryInfo"`
	MonetaryInfo                   *MonetaryInformationType67627S      `xml:"monetaryInfo"`
	FareComponentFareRulesInfo     *FareRulesInformationTypeI          `xml:"fareComponentFareRulesInfo"`
	FareQualifierDetails           *FareQualifierDetailsType163215S    `xml:"fareQualifierDetails"`
	CorporateInfo                  *CorporateFareInformationType       `xml:"corporateInfo"`
	FareComponentMatchedSeqInfo    *ReferenceInfoType                  `xml:"fareComponentMatchedSeqInfo"`
	FareComponentParameterValue    *NumberOfUnitsType163220S           `xml:"fareComponentParameterValue"`
	RangeInfo                      []*RangeDetailsType                 `xml:"rangeInfo"`
	FareComponentRuleInfoGroup     []*FareComponentRuleInfoType        `xml:"fareComponentRuleInfoGroup"`
	ProductInfo                    []*ProductInformationType           `xml:"productInfo"`
	FareComponentServiceAttributes *AttributeType                      `xml:"fareComponentServiceAttributes"`
	FareComponentConversionRate    *ConversionRateType163222S          `xml:"fareComponentConversionRate"`
	FareComponentFeeInfo           *SpecificDataInformationType        `xml:"fareComponentFeeInfo"`
	FareComponentServiceDescInfo   string            `xml:"fareComponentServiceDescInfo"`
	PricingTicketingDetails        *PricingTicketingDetailsType163225S `xml:"pricingTicketingDetails"`
}

// FareComponentInformationType is Ticket document number
type FareComponentInformationType struct {
	FareComponentDetails *FareComponentDetailsType      `xml:"fareComponentDetails"`
	TicketNumber         string `xml:"ticketNumber"`
}

// FareComponentRuleInfoType is Rule number information
type FareComponentRuleInfoType struct {
	FareComponentSegmentInfo    *TravellerInsuranceInformationType163226S `xml:"fareComponentSegmentInfo"`
	FareComponentFareRulesInfo  *FareRulesInformationTypeI                `xml:"fareComponentFareRulesInfo"`
	FareComponentRuleNumberInfo *ReferenceInfoType163228S                 `xml:"fareComponentRuleNumberInfo"`
}

// FareComponentsInfoType is Pricing unit information group
type FareComponentsInfoType struct {
	ConstructionContextInfo *FareComponentInformationType `xml:"constructionContextInfo"`
	PricingUnitInfoGroup    []*PricingUnitDataType        `xml:"pricingUnitInfoGroup"`
}

// FareDetailsTypeI is Qualifier
type FareDetailsTypeI struct {
	Qualifier string `xml:"qualifier"`
}

// FareInformationType is Value qualifier
type FareInformationType struct {
	ValueQualifier string `xml:"valueQualifier"`
}

// FareProductDataType is Passenger type information group
type FareProductDataType struct {
	GeneralAndIdInfo         *SegmentRepetitionControlType `xml:"generalAndIdInfo"`
	SpecificTravellerDetails []*SpecificTravellerTypeI     `xml:"specificTravellerDetails"`
	PassengerTypeInfoGroup   []*PassengerDetailsInfoType   `xml:"passengerTypeInfoGroup"`
}

// FareQualifierDetailsType is Fare categories
type FareQualifierDetailsType struct {
	FareCategories *FareCategoryCodesTypeI `xml:"fareCategories"`
}

// FareQualifierDetailsType163215S is Fare details
type FareQualifierDetailsType163215S struct {
	//  XMLName     xml.Name          `xml:"FareQualifierDetailsType_163215S"`
	FareDetails *FareDetailsTypeI `xml:"fareDetails"`
}

// FareRulesInformationTypeI is Rule section id
type FareRulesInformationTypeI struct {
	TariffClassId string   `xml:"tariffClassId"`
	RuleSectionId []string `xml:"ruleSectionId"`
}

// FaresInfoType is Error information group at service request level
type FaresInfoType struct {
	MarkerAllFares            *DummySegmentTypeI        `xml:"markerAllFares"`
	ResponseTypeInfo          *StatusType               `xml:"responseTypeInfo"`
	ResponseIdentInfo         *ItemNumberType           `xml:"responseIdentInfo"`
	InvolvedCurrenciesInfo    *ConversionRateType       `xml:"involvedCurrenciesInfo"`
	MonetaryInfo              *MonetaryInformationType  `xml:"monetaryInfo"`
	ComputedTaxInfoGroup      []*GeneralTaxInfoType     `xml:"computedTaxInfoGroup"`
	TicketingInfoGroup        *TicketingInfoType        `xml:"ticketingInfoGroup"`
	FlightInfoGroup           []*FlightInformationType  `xml:"flightInfoGroup"`
	ServiceInformationGroup   *ServiceInfoType          `xml:"serviceInformationGroup"`
	FareProductInfoGroup      []*FareProductDataType    `xml:"fareProductInfoGroup"`
	ServiceRequestRejectGroup *ServiceRequestRejectType `xml:"serviceRequestRejectGroup"`
}

// FlightInformationType is Itinerary information group
type FlightInformationType struct {
	InvolvedFlightInfo   *TravelProductInformationType   `xml:"involvedFlightInfo"`
	RelatedProductInfo   *RelatedProductInformationTypeI `xml:"relatedProductInfo"`
	AdditionalFlightInfo *AdditionalProductDetailsTypeI  `xml:"additionalFlightInfo"`
	ItineraryInfoGroup   []*ItineraryInfoType            `xml:"itineraryInfoGroup"`
}

// FreeTextDetailsType ...
type FreeTextDetailsType struct {
	TextSubjectQualifier string  `xml:"textSubjectQualifier"`
	InformationType      string  `xml:"informationType"`
	Status               string  `xml:"status"`
	CompanyId            string `xml:"companyId"`
	Language             string  `xml:"language"`
	Source               string  `xml:"source"`
	Encoding             string  `xml:"encoding"`
}

// FreeTextInformationType is Free text and message sequence numbers of the remarks.
type FreeTextInformationType struct {
	FreeTextDetails *FreeTextDetailsType              `xml:"freeTextDetails"`
	FreeText        []string `xml:"freeText"`
}

// FreeTextQualificationTypeI is Warning Message coded
type FreeTextQualificationTypeI struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
}

// FreeTextQualificationType is Add code sets in the list
type FreeTextQualificationType struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
}

// GeneralTaxInfoType is Tax sub details for ZP and PFC
type GeneralTaxInfoType struct {
	ComputedTaxListInfo   *TaxDetailsType163207S            `xml:"computedTaxListInfo"`
	ComputedTaxSubDetails []*MonetaryInformationType163167S `xml:"computedTaxSubDetails"`
}

// GenericDetailsTypeI is Compartment designator
type GenericDetailsTypeI struct {
	CompartmentDesignator string `xml:"compartmentDesignator"`
}

// stringI is Text associated to the code 9980
type stringI struct {
	FreeTextQualification *FreeTextQualificationTypeI      `xml:"freeTextQualification"`
	FreeText              []string `xml:"freeText"`
}

// string is Free text
type string struct {
	FreeTextQualification *FreeTextQualificationType       `xml:"freeTextQualification"`
	FreeText              []string `xml:"freeText"`
}

// ItemNumberIdentificationType is Type
type ItemNumberIdentificationType struct {
	Number string `xml:"number"`
	Type   string `xml:"type"`
}

// ItemNumberIdentificationType231296C is Fare component number
type ItemNumberIdentificationType231296C struct {
	//  XMLName xml.Name                      `xml:"ItemNumberIdentificationType_231296C"`
	Number  string `xml:"number"`
}

// ItemNumberType is Item number details
type ItemNumberType struct {
	ItemNumberDetails []*ItemNumberIdentificationType `xml:"itemNumberDetails"`
}

// ItemNumberType163213S is Item number details
type ItemNumberType163213S struct {
	//  XMLName           xml.Name                               `xml:"ItemNumberType_163213S"`
	ItemNumberDetails []*ItemNumberIdentificationType231296C `xml:"itemNumberDetails"`
}

// ItineraryInfoType is Information related to the flight in the itinerary
type ItineraryInfoType struct {
	TravelItineraryInfo *TravellerInsuranceInformationType `xml:"travelItineraryInfo"`
}

// LocationDetailsTypeI is Country
type LocationDetailsTypeI struct {
	City    string `xml:"city"`
	Country string  `xml:"country"`
}

// LocationType is True location id
type LocationType struct {
	TrueLocationId string `xml:"trueLocationId"`
}

// MonetaryInformationDetailsType is Location
type MonetaryInformationDetailsType struct {
	TypeQualifier string  `xml:"typeQualifier"`
	Amount        string `xml:"amount"`
	Currency      string  `xml:"currency"`
	Location      string  `xml:"location"`
}

// MonetaryInformationDetailsType231248C is Currency
type MonetaryInformationDetailsType231248C struct {
	//  XMLName       xml.Name                       `xml:"MonetaryInformationDetailsType_231248C"`
	TypeQualifier string  `xml:"typeQualifier"`
	Amount        string `xml:"amount"`
	Currency      string  `xml:"currency"`
}

// MonetaryInformationDetailsType231249C is Location
type MonetaryInformationDetailsType231249C struct {
	//  XMLName       xml.Name                       `xml:"MonetaryInformationDetailsType_231249C"`
	TypeQualifier string  `xml:"typeQualifier"`
	Amount        string `xml:"amount"`
	Location      string  `xml:"location"`
}

// MonetaryInformationType is Monetary details
type MonetaryInformationType struct {
	MonetaryDetails []*MonetaryInformationDetailsType231248C `xml:"monetaryDetails"`
}

// MonetaryInformationType163167S is Monetary details
type MonetaryInformationType163167S struct {
	//  XMLName         xml.Name                                 `xml:"MonetaryInformationType_163167S"`
	MonetaryDetails []*MonetaryInformationDetailsType231249C `xml:"monetaryDetails"`
	string        string                                `xml:"Dummy.NET"`
}

// MonetaryInformationType67627S ...
type MonetaryInformationType67627S struct {
	//  XMLName         xml.Name                          `xml:"MonetaryInformationType_67627S"`
	MonetaryDetails []*MonetaryInformationDetailsType `xml:"monetaryDetails"`
}

// NumberOfUnitDetailsType ...
type NumberOfUnitDetailsType struct {
	NumberOfUnit  int    `xml:"numberOfUnit"`
	UnitQualifier string `xml:"unitQualifier"`
}

// NumberOfUnitDetailsType231306C ...
type NumberOfUnitDetailsType231306C struct {
	//  XMLName       xml.Name                      `xml:"NumberOfUnitDetailsType_231306C"`
	NumberOfUnit  int    `xml:"numberOfUnit"`
	UnitQualifier string `xml:"unitQualifier"`
}

// NumberOfUnitsType ...
type NumberOfUnitsType struct {
	QuantityDetails []*NumberOfUnitDetailsType `xml:"quantityDetails"`
}

// NumberOfUnitsType163220S ...
type NumberOfUnitsType163220S struct {
	//  XMLName         xml.Name                          `xml:"NumberOfUnitsType_163220S"`
	QuantityDetails []*NumberOfUnitDetailsType231306C `xml:"quantityDetails"`
}

// OriginatorIdentificationDetailsTypeI ...
type OriginatorIdentificationDetailsTypeI struct {
	InHouseIdentification1 string `xml:"inHouseIdentification1"`
}

// PassengerDetailsInfoType ...
type PassengerDetailsInfoType struct {
	FareInfo                   *FareInformationType                `xml:"fareInfo"`
	PricingUnitIdentInfo       *NumberOfUnitsType                  `xml:"pricingUnitIdentInfo"`
	PricingticketingDetails    *PricingTicketingDetailsType163190S `xml:"pricingticketingDetails"`
	ConversionRate             *ConversionRateType163191S          `xml:"conversionRate"`
	MonetaryInfo               *MonetaryInformationType            `xml:"monetaryInfo"`
	TaxDetailsInfoGroup        []*TaxDetailsInfoType               `xml:"taxDetailsInfoGroup"`
	FareComponentsInfoGroup    *FareComponentsInfoType             `xml:"fareComponentsInfoGroup"`
	ProposedServiceInformation []*ProposedServiceInfoType          `xml:"proposedServiceInformation"`
}

// PortionProposedServiceInfoType ...
type PortionProposedServiceInfoType struct {
	ProposedServicePortionRef   *ActionDetailsType            `xml:"proposedServicePortionRef"`
	ProposedServiceDetailsGroup []*ProposedServiceDetailsType `xml:"proposedServiceDetailsGroup"`
}

// PricingTicketingDetailsType ...
type PricingTicketingDetailsType struct {
	PriceTicketDetails     *PricingTicketingInformationType `xml:"priceTicketDetails"`
	SellingLocationDetails *LocationDetailsTypeI            `xml:"sellingLocationDetails"`
}

// PricingTicketingDetailsType163190S ...
type PricingTicketingDetailsType163190S struct {
	//  XMLName            xml.Name                         `xml:"PricingTicketingDetailsType_163190S"`
	PriceTicketDetails *PricingTicketingInformationType `xml:"priceTicketDetails"`
	PriceTariffType    string    `xml:"priceTariffType"`
}

// PricingTicketingDetailsType163225S ...
type PricingTicketingDetailsType163225S struct {
	//  XMLName                xml.Name                           `xml:"PricingTicketingDetailsType_163225S"`
	ProductDateTimeDetails *ProductDateTimeTypeI              `xml:"productDateTimeDetails"`
	CompanyDetails         *CompanyIdentificationTypeI        `xml:"companyDetails"`
	CompanyNumberDetails   *CompanyIdentificationNumbersTypeI `xml:"companyNumberDetails"`
}

// PricingTicketingInformationType ...
type PricingTicketingInformationType struct {
	Indicators []string `xml:"indicators"`
}

// PricingUnitDataType ...
type PricingUnitDataType struct {
	PricingUnitIdentInfo *NumberOfUnitsType       `xml:"pricingUnitIdentInfo"`
	FareComponentInfo    []*FareComponentInfoType `xml:"fareComponentInfo"`
}

// ProcessingInformationType ...
type ProcessingInformationType struct {
	ReferenceQualifier string `xml:"referenceQualifier"`
}

// ProductDateTimeTypeI ...
type ProductDateTimeTypeI struct {
	DepartureDate string `xml:"departureDate"`
}

// ProductDateTimeType ...
type ProductDateTimeType struct {
	DepartureDate string               `xml:"departureDate"`
	DepartureTime string               `xml:"departureTime"`
	ArrivalDate   string               `xml:"arrivalDate"`
	ArrivalTime   string               `xml:"arrivalTime"`
	DateVariation int `xml:"dateVariation"`
}

// ProductDateTimeType231252C ...
type ProductDateTimeType231252C struct {
	//  XMLName       xml.Name    `xml:"ProductDateTimeType_231252C"`
	DepartureDate string `xml:"departureDate"`
	DepartureTime string `xml:"departureTime"`
	ArrivalDate   string `xml:"arrivalDate"`
	ArrivalTime   string `xml:"arrivalTime"`
}

// ProductDetailsType ...
type ProductDetailsType struct {
	Designator string `xml:"designator"`
}

// ProductDetailsType231316C ...
type ProductDetailsType231316C struct {
	//  XMLName    xml.Name                      `xml:"ProductDetailsType_231316C"`
	Designator string `xml:"designator"`
}

// ProductIdentificationDetailsType ...
type ProductIdentificationDetailsType struct {
	FlightNumber      string   `xml:"flightNumber"`
	BookingClass      string   `xml:"bookingClass"`
	OperationalSuffix string   `xml:"operationalSuffix"`
	Modifier          []string `xml:"modifier"`
}

// ProductIdentificationDetailsType231256C ...
type ProductIdentificationDetailsType231256C struct {
	//  XMLName      xml.Name                      `xml:"ProductIdentificationDetailsType_231256C"`
	FlightNumber string `xml:"flightNumber"`
	BookingClass string `xml:"bookingClass"`
}

// ProductInformationType ...
type ProductInformationType struct {
	ProductDetailsQualifier string `xml:"productDetailsQualifier"`
	BookingClassDetails     []*ProductDetailsType         `xml:"bookingClassDetails"`
}

// ProductInformationType163234S ...
type ProductInformationType163234S struct {
	//  XMLName             xml.Name                     `xml:"ProductInformationType_163234S"`
	BookingClassDetails []*ProductDetailsType231316C `xml:"bookingClassDetails"`
}

// ProductTypeDetailsType ...
type ProductTypeDetailsType struct {
	FlightIndicator []string `xml:"flightIndicator"`
}

// ProposedServiceDetailsType ...
type ProposedServiceDetailsType struct {
	ProposedServiceDetails        *SpecialRequirementsDetailsType163236S `xml:"proposedServiceDetails"`
	ProposedServiceRefInformation *ReferenceInfoType163228S              `xml:"proposedServiceRefInformation"`
	ProposedServiceProductInfo    *ProductInformationType163234S         `xml:"proposedServiceProductInfo"`
	ProposedServiceParameters     *SeatRequestParametersTypeI            `xml:"proposedServiceParameters"`
}

// ProposedServiceInfoType ...
type ProposedServiceInfoType struct {
	ServicePricingAggregation     *AttributeType67628S              `xml:"servicePricingAggregation"`
	ServiceParameterValueInfo     *NumberOfUnitsType                `xml:"serviceParameterValueInfo"`
	DateAndTimeAggregation        *DateAndTimeInformationType67653S `xml:"dateAndTimeAggregation"`
	CorporateInfo                 *CorporateFareInformationType     `xml:"corporateInfo"`
	ProposedServiceReqPortionInfo []*PortionProposedServiceInfoType `xml:"proposedServiceReqPortionInfo"`
}

// RangeDetailsType ...
type RangeDetailsType struct {
	RangeQualifier string `xml:"rangeQualifier"`
	RangeDetails   []*RangeType                  `xml:"rangeDetails"`
}

// RangeType ...
type RangeType struct {
	DataType string `xml:"dataType"`
	Min      float64                       `xml:"min"`
	Max      float64                       `xml:"max"`
}

// ReferenceInfoType ...
type ReferenceInfoType struct {
	ReferenceDetails []*ReferencingDetailsType `xml:"referenceDetails"`
}

// ReferenceInfoType163228S ...
type ReferenceInfoType163228S struct {
	//  XMLName          xml.Name                         `xml:"ReferenceInfoType_163228S"`
	ReferenceDetails []*ReferencingDetailsType231312C `xml:"referenceDetails"`
}

// ReferenceType ...
type ReferenceType struct {
	FirstItemIdentifier int `xml:"firstItemIdentifier"`
	LastItemIdentifier  int `xml:"lastItemIdentifier"`
}

// ReferencingDetailsType ...
type ReferencingDetailsType struct {
	Value string `xml:"value"`
}

// ReferencingDetailsType231312C ...
type ReferencingDetailsType231312C struct {
	//  XMLName xml.Name                       `xml:"ReferencingDetailsType_231312C"`
	Type    string  `xml:"type"`
	Value   string `xml:"value"`
}

// RelatedProductInformationTypeI ...
type RelatedProductInformationTypeI struct {
	StatusCode []string `xml:"statusCode"`
}

// SeatRequestParametersTypeI ...
type SeatRequestParametersTypeI struct {
	GenericDetails *GenericDetailsTypeI `xml:"genericDetails"`
}

// SegmentRepetitionControlDetailsType ...
type SegmentRepetitionControlDetailsType struct {
	NumberOfPAx int `xml:"numberOfPAx"`
}

// SegmentRepetitionControlType ...
type SegmentRepetitionControlType struct {
	SegmentControlDetails []*SegmentRepetitionControlDetailsType `xml:"segmentControlDetails"`
}

// SelectionDetailsInformationType ...
type SelectionDetailsInformationType struct {
	Type string `xml:"type"`
}

// SelectionDetailsType ...
type SelectionDetailsType struct {
	CarrierFeeDetails *SelectionDetailsInformationType `xml:"carrierFeeDetails"`
}

// ServiceInfoDetailsType ...
type ServiceInfoDetailsType struct {
	ServiceInfo                *SpecificDataInformationType    `xml:"serviceInfo"`
	ServiceOwner               *UserIdentificationType         `xml:"serviceOwner"`
	ServiceDateAndTimeVersion  *DateAndTimeInformationType     `xml:"serviceDateAndTimeVersion"`
	ServiceDetails             *SpecialRequirementsDetailsType `xml:"serviceDetails"`
	ProposedServiceProductInfo *ProductInformationType163234S  `xml:"proposedServiceProductInfo"`
	ServiceAttributes          *AttributeType                  `xml:"serviceAttributes"`
	ServiceFareDetails         *FareQualifierDetailsType       `xml:"serviceFareDetails"`
	ServiceDescriptionInfo     []string      `xml:"serviceDescriptionInfo"`
}

// ServiceInfoType ...
type ServiceInfoType struct {
	ServiceTypeInfo                *SelectionDetailsType   `xml:"serviceTypeInfo"`
	ServiceInformationDetailsGroup *ServiceInfoDetailsType `xml:"serviceInformationDetailsGroup"`
}

// ServiceRequestRejectType ...
type ServiceRequestRejectType struct {
	ServiceRequestRejectErrorCode *ApplicationErrorInformationTypeI `xml:"serviceRequestRejectErrorCode"`
	ServiceRequestErrorFreeText   string         `xml:"serviceRequestErrorFreeText"`
}

// SpecialRequirementsDataDetailsType ...
type SpecialRequirementsDataDetailsType struct {
	SpecialRequirementData string  `xml:"specialRequirementData"`
	SeatCharacteristics    []string `xml:"seatCharacteristics"`
}

// SpecialRequirementsDetailsType ...
type SpecialRequirementsDetailsType struct {
	ServiceRequirementsInfo *SpecialRequirementsTypeDetailsType `xml:"serviceRequirementsInfo"`
}

// SpecialRequirementsDetailsType163236S ...
type SpecialRequirementsDetailsType163236S struct {
	//  XMLName                        xml.Name                                   `xml:"SpecialRequirementsDetailsType_163236S"`
	SpecialRequirementsDetails     *SpecialRequirementsTypeDetailsType231319C `xml:"specialRequirementsDetails"`
	SpecialRequirementsDataDetails []*SpecialRequirementsDataDetailsType      `xml:"specialRequirementsDataDetails"`
}

// SpecialRequirementsTypeDetailsType ...
type SpecialRequirementsTypeDetailsType struct {
	ServiceClassification   string    `xml:"serviceClassification"`
	ServiceMarketingCarrier string    `xml:"serviceMarketingCarrier"`
	ServiceGroup            string    `xml:"serviceGroup"`
	ServiceSubGroup         string    `xml:"serviceSubGroup"`
	ServiceFreeText         []string `xml:"serviceFreeText"`
}

// SpecialRequirementsTypeDetailsType231319C ...
type SpecialRequirementsTypeDetailsType231319C struct {
	//  XMLName                 xml.Name                      `xml:"SpecialRequirementsTypeDetailsType_231319C"`
	SpecialRequirementType  string `xml:"specialRequirementType"`
	NumberOfInstances       int    `xml:"numberOfInstances"`
	FirstGeographicalPoint  string `xml:"firstGeographicalPoint"`
	SecondGeographicalPoint string `xml:"secondGeographicalPoint"`
}

// SpecificDataInformationType ...
type SpecificDataInformationType struct {
	DataTypeInformation *DataTypeInformationType `xml:"dataTypeInformation"`
}

// SpecificTravellerDetailsTypeI ...
type SpecificTravellerDetailsTypeI struct {
	ReferenceNumber  string `xml:"referenceNumber"`
	MeasurementValue int     `xml:"measurementValue"`
	Surname          string `xml:"surname"`
	FirstName        string `xml:"firstName"`
}

// SpecificTravellerTypeI ...
type SpecificTravellerTypeI struct {
	TravellerDetails []*SpecificTravellerDetailsTypeI `xml:"travellerDetails"`
	string         string                        `xml:"Dummy.NET"`
}

// StatusDetailsType ...
type StatusDetailsType struct {
	Indicator string `xml:"indicator"`
}

// StatusType ...
type StatusType struct {
	StatusDetails []*StatusDetailsType `xml:"statusDetails"`
}

// SubItineraryInfoType ...
type SubItineraryInfoType struct {
	ProposedServicePortionRef *ActionDetailsType                   `xml:"proposedServicePortionRef"`
	TravelProductInfo         *TravelProductInformationType163210S `xml:"travelProductInfo"`
}

// TaxDetailsInfoType ...
type TaxDetailsInfoType struct {
	TaxDetailsListInfo      *TaxDetailsType163207S            `xml:"taxDetailsListInfo"`
	ElementaryTaxSubDetails []*MonetaryInformationType163167S `xml:"elementaryTaxSubDetails"`
}

// TaxDetailsType163207S ...
type TaxDetailsType163207S struct {
	//  XMLName    xml.Name          `xml:"TaxDetailsType_163207S"`
	TaxDetails []*TaxDetailsType `xml:"taxDetails"`
}

// TaxDetailsType ...
type TaxDetailsType struct {
	Rate         string  `xml:"rate"`
	CountryCode  string   `xml:"countryCode"`
	CurrencyCode string   `xml:"currencyCode"`
	Type         string   `xml:"type"`
	Indicator    []string `xml:"indicator"`
}

// TicketingInfoType ...
type TicketingInfoType struct {
	PricingTicketingDetails *PricingTicketingDetailsType `xml:"pricingTicketingDetails"`
}

// TravelProductInformationType ...
type TravelProductInformationType struct {
	FlightDate           *ProductDateTimeType231252C              `xml:"flightDate"`
	BoardPointDetails    *LocationType                            `xml:"boardPointDetails"`
	OffpointDetails      *LocationType                            `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationType               `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsType231256C `xml:"flightIdentification"`
	FlightTypeDetails    *ProductTypeDetailsType                  `xml:"flightTypeDetails"`
}

// TravelProductInformationType163210S ...
type TravelProductInformationType163210S struct {
	//  XMLName              xml.Name                          `xml:"TravelProductInformationType_163210S"`
	FlightDate           *ProductDateTimeType              `xml:"flightDate"`
	BoardPointDetails    *LocationType                     `xml:"boardPointDetails"`
	OffpointDetails      *LocationType                     `xml:"offpointDetails"`
	CompanyDetails       *CompanyIdentificationType231301C `xml:"companyDetails"`
	FlightIdentification *ProductIdentificationDetailsType `xml:"flightIdentification"`
	FlightTypeDetails    *ProductTypeDetailsType           `xml:"flightTypeDetails"`
	ItemNumber           int         `xml:"itemNumber"`
}

// TravellerInsuranceInformationType ...
type TravellerInsuranceInformationType struct {
	SegmentItemNumber  string `xml:"segmentItemNumber"`
	NumericReferenceId int      `xml:"numericReferenceId"`
	CabinDesignator    string         `xml:"cabinDesignator"`
}

// TravellerInsuranceInformationType163226S ...
type TravellerInsuranceInformationType163226S struct {
	//  XMLName           xml.Name                       `xml:"TravellerInsuranceInformationType_163226S"`
	SegmentItemNumber string `xml:"segmentItemNumber"`
}

// UserIdentificationType ...
type UserIdentificationType struct {
	OriginIdentification *OriginatorIdentificationDetailsTypeI `xml:"originIdentification"`
}

// NumericDecimalLength1To18 ...
type NumericDecimalLength1To18 float64

