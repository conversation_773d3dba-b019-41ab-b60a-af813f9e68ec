package security_sign_out

import "encoding/xml"

// ErrorSection ...
type ErrorSection struct {
	XMLName             xml.Name                         `xml:"errorSection"`
	ApplicationError    *ApplicationErrorInformationType `xml:"applicationError"`
	InteractiveFreeText *InteractiveFreeTextTypeI        `xml:"interactiveFreeText"`
}

// SecuritySignOutReply ...
type SecuritySignOutReply struct {
	XMLName       xml.Name                     `xml:"Security_SignOutReply"`
	ErrorSection  *ErrorSection                `xml:"errorSection"`
	ProcessStatus *ResponseAnalysisDetailsType `xml:"processStatus"`
}

// ApplicationErrorDetailType is Code identifying the agency responsible for a code list.
type ApplicationErrorDetailType struct {
	ErrorCode      string `xml:"errorCode"`
	ErrorCategory  string `xml:"errorCategory"`
	ErrorCodeOwner string `xml:"errorCodeOwner"`
}

// ApplicationErrorInformationType is Application error details.
type ApplicationErrorInformationType struct {
	ErrorDetails *ApplicationErrorDetailType `xml:"errorDetails"`
}

// FreeTextQualificationTypeI is Language
type FreeTextQualificationTypeI struct {
	TextSubjectQualifier string `xml:"textSubjectQualifier"`
	InformationType      string `xml:"informationType"`
	Language             string `xml:"language"`
}

// InteractiveFreeTextTypeI is Free Text
type InteractiveFreeTextTypeI struct {
	FreeTextQualification *FreeTextQualificationTypeI `xml:"freeTextQualification"`
	FreeText              []string                    `xml:"freeText"`
}

// ResponseAnalysisDetailsType is P must be specified when status of the process is OK.
type ResponseAnalysisDetailsType struct {
	StatusCode string `xml:"statusCode"`
}

// AlphaNumericStringLength1To70 is Format limitations: an..70
type AlphaNumericStringLength1To70 string

// AlphaStringLength1To6 is Format limitations: a..6
type AlphaStringLength1To6 string

// AlphaNumericStringLength1To5 is Format limitations: an..5
type AlphaNumericStringLength1To5 string

// AlphaNumericStringLength1To3 is Format limitations: an..3
type AlphaNumericStringLength1To3 string

// AlphaNumericStringLength1To4 is Format limitations: an..4
type AlphaNumericStringLength1To4 string
