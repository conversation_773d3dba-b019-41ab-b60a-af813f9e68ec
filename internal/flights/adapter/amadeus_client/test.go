package amadeus_client

import (
	"encoding/xml"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/air_retrieve_seat_map"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/fare_price_pnr_with_booking_class"
	pnr_reply "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/pnr_repply"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func TestMapETicket() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/amadeus_client/docs/pnr_reply.xml")

	pnrReply := &pnr_reply.PNRReply{}
	err := xml.Unmarshal(byteValue, pnrReply)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	_, res, emdTicket, err := getETicketsInfo(pnrReply)

	if err != nil {
		fmt.Println("Error convert", err)

		return err
	}

	err = utils.WriteStructToXMLFile(res, "./logs/amadeus_eTicket.xml")

	if err != nil {
		fmt.Println("Error write")

		return err
	}

	err = utils.WriteStructToXMLFile(emdTicket, "./logs/amadeus_emd.xml")

	if err != nil {
		fmt.Println("Error write")

		return err
	}

	return nil
}

func TestSeatMap() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/amadeus_client/docs/seat_map.xml")

	seatReply := &air_retrieve_seat_map.AirRetrieveSeatMapReply{}
	err := xml.Unmarshal(byteValue, seatReply)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	res, err := converts.ToDomainSegmentSeat(seatReply, &domain.ItinerarySegment{})

	if err != nil {
		fmt.Println("Error convert", err)

		return err
	}

	err = utils.WriteStructToXMLFile(res, "./logs/amadeus_seat_map.xml")

	if err != nil {
		fmt.Println("Error write")

		return err
	}

	return nil
}

func TestLastTKTDate() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/amadeus_client/docs/pnr_reply_opc.xml")

	pnrReply := &pnr_reply.PNRReply{}
	err := xml.Unmarshal(byteValue, pnrReply)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	_, opcData, _, err := converts.FromPNRToLastTicketDate(pnrReply, &fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply{
		FareList: []*fare_price_pnr_with_booking_class.FareList{},
	})

	if err != nil {
		fmt.Println("Error convert", err)

		return err
	}

	err = utils.WriteStructToXMLFile(opcData, "./logs/amadeus_opc.xml")

	if err != nil {
		fmt.Println("Error write")

		return err
	}

	return nil
}

func TestSSRLastTKTDate() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/amadeus_client/docs/pnr_reply_ssr.xml")

	pnrReply := &pnr_reply.PNRReply{}
	err := xml.Unmarshal(byteValue, pnrReply)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	ssr, err := converts.FromPNRToSSRLastTicketDate(pnrReply)
	if err != nil {
		fmt.Println("Error convert", err)

		return err
	}

	utils.WriteStructToXMLFile(ssr, "./logs/ama_ssr.xml")
	return nil
}
