      <PNR_Reply xmlns="http://xml.amadeus.com/PNRACC_21_1_1A">
         <pnrHeader>
            <reservationInfo>
               <reservation>
                  <companyId>1A</companyId>
                  <controlNumber>6Y7LQB</controlNumber>
                  <date>141223</date>
                  <time>1105</time>
               </reservation>
            </reservationInfo>
         </pnrHeader>
         <securityInformation>
            <responsibilityInformation>
               <typeOfPnrElement>RP</typeOfPnrElement>
               <agentId>WSSU</agentId>
               <officeId>SGNVM28CP</officeId>
               <iataCode>37301390</iataCode>
            </responsibilityInformation>
            <queueingInformation>
               <queueingOfficeId>SGNVM28CP</queueingOfficeId>
            </queueingInformation>
            <cityCode>SGN</cityCode>
            <secondRpInformation>
               <creationOfficeId>SGNVM28CP</creationOfficeId>
               <agentSignature>9999WS</agentSignature>
               <creationDate>141223</creationDate>
               <creatorIataCode>37301390</creatorIataCode>
               <creationTime>1105</creationTime>
            </secondRpInformation>
         </securityInformation>
         <freetextData>
            <freetextDetail>
               <subjectQualifier>3</subjectQualifier>
               <type>P12</type>
            </freetextDetail>
            <longFreetext>--- TST TSM RLR ---</longFreetext>
         </freetextData>
         <pnrHeaderTag>
            <statusInformation>
               <indicator>TST</indicator>
            </statusInformation>
            <statusInformation>
               <indicator>TSM</indicator>
            </statusInformation>
            <statusInformation>
               <indicator>RLR</indicator>
            </statusInformation>
         </pnrHeaderTag>
         <sbrPOSDetails>
            <sbrUserIdentificationOwn>
               <originIdentification>
                  <originatorId>37301390</originatorId>
                  <inHouseIdentification1>SGNVM28CP</inHouseIdentification1>
               </originIdentification>
               <originatorTypeCode>T</originatorTypeCode>
            </sbrUserIdentificationOwn>
            <sbrSystemDetails>
               <deliveringSystem>
                  <companyId>1A</companyId>
                  <locationId>SGN</locationId>
               </deliveringSystem>
            </sbrSystemDetails>
            <sbrPreferences>
               <userPreferences>
                  <codedCountry>VN</codedCountry>
               </userPreferences>
            </sbrPreferences>
         </sbrPOSDetails>
         <sbrCreationPosDetails>
            <sbrUserIdentificationOwn>
               <originIdentification>
                  <originatorId>37301390</originatorId>
                  <inHouseIdentification1>SGNVM28CP</inHouseIdentification1>
               </originIdentification>
               <originatorTypeCode>T</originatorTypeCode>
            </sbrUserIdentificationOwn>
            <sbrSystemDetails>
               <deliveringSystem>
                  <companyId>1A</companyId>
                  <locationId>SGN</locationId>
               </deliveringSystem>
            </sbrSystemDetails>
            <sbrPreferences>
               <userPreferences>
                  <codedCountry>VN</codedCountry>
               </userPreferences>
            </sbrPreferences>
         </sbrCreationPosDetails>
         <sbrUpdatorPosDetails>
            <sbrUserIdentificationOwn>
               <originIdentification>
                  <originatorId>37301390</originatorId>
                  <inHouseIdentification1>SGNVM28CP</inHouseIdentification1>
               </originIdentification>
               <originatorTypeCode>T</originatorTypeCode>
            </sbrUserIdentificationOwn>
            <sbrSystemDetails>
               <deliveringSystem>
                  <companyId>1A</companyId>
                  <locationId>SGN</locationId>
               </deliveringSystem>
            </sbrSystemDetails>
            <sbrPreferences>
               <userPreferences>
                  <codedCountry>VN</codedCountry>
               </userPreferences>
            </sbrPreferences>
         </sbrUpdatorPosDetails>
         <technicalData>
            <enveloppeNumberData>
               <sequenceDetails>
                  <number>5</number>
               </sequenceDetails>
            </enveloppeNumberData>
            <lastTransmittedEnvelopeNumber>
               <currentRecord>4</currentRecord>
            </lastTransmittedEnvelopeNumber>
            <purgeDateData>
               <dateTime>
                  <year>2024</year>
                  <month>1</month>
                  <day>15</day>
               </dateTime>
            </purgeDateData>
         </technicalData>
         <travellerInfo>
            <elementManagementPassenger>
               <reference>
                  <qualifier>PT</qualifier>
                  <number>2</number>
               </reference>
               <segmentName>NM</segmentName>
               <lineNumber>1</lineNumber>
            </elementManagementPassenger>
            <passengerData>
               <travellerInformation>
                  <traveller>
                     <surname>CHAU</surname>
                     <quantity>1</quantity>
                  </traveller>
                  <passenger>
                     <firstName>MINH TRUNG</firstName>
                     <type>ADT</type>
                  </passenger>
               </travellerInformation>
            </passengerData>
            <enhancedPassengerData>
               <enhancedTravellerInformation>
                  <travellerNameInfo>
                     <quantity>1</quantity>
                     <type>ADT</type>
                  </travellerNameInfo>
                  <otherPaxNamesDetails>
                     <nameType>UN</nameType>
                     <referenceName>Y</referenceName>
                     <displayedName>Y</displayedName>
                     <surname>CHAU</surname>
                     <givenName>MINH TRUNG</givenName>
                  </otherPaxNamesDetails>
               </enhancedTravellerInformation>
            </enhancedPassengerData>
         </travellerInfo>
         <travellerInfo>
            <elementManagementPassenger>
               <reference>
                  <qualifier>PT</qualifier>
                  <number>3</number>
               </reference>
               <segmentName>NM</segmentName>
               <lineNumber>2</lineNumber>
            </elementManagementPassenger>
            <passengerData>
               <travellerInformation>
                  <traveller>
                     <surname>NGUYEN</surname>
                     <quantity>1</quantity>
                  </traveller>
                  <passenger>
                     <firstName>VO KHOI</firstName>
                     <type>ADT</type>
                  </passenger>
               </travellerInformation>
            </passengerData>
            <enhancedPassengerData>
               <enhancedTravellerInformation>
                  <travellerNameInfo>
                     <quantity>1</quantity>
                     <type>ADT</type>
                  </travellerNameInfo>
                  <otherPaxNamesDetails>
                     <nameType>UN</nameType>
                     <referenceName>Y</referenceName>
                     <displayedName>Y</displayedName>
                     <surname>NGUYEN</surname>
                     <givenName>VO KHOI</givenName>
                  </otherPaxNamesDetails>
               </enhancedTravellerInformation>
            </enhancedPassengerData>
         </travellerInfo>
         <travellerInfo>
            <elementManagementPassenger>
               <reference>
                  <qualifier>PT</qualifier>
                  <number>4</number>
               </reference>
               <segmentName>NM</segmentName>
               <lineNumber>3</lineNumber>
            </elementManagementPassenger>
            <passengerData>
               <travellerInformation>
                  <traveller>
                     <surname>PHAM</surname>
                     <quantity>1</quantity>
                  </traveller>
                  <passenger>
                     <firstName>NGOC LINH</firstName>
                     <type>CHD</type>
                  </passenger>
               </travellerInformation>
               <dateOfBirth>
                  <dateAndTimeDetails>
                     <qualifier>706</qualifier>
                     <date>11092018</date>
                  </dateAndTimeDetails>
               </dateOfBirth>
            </passengerData>
            <enhancedPassengerData>
               <enhancedTravellerInformation>
                  <travellerNameInfo>
                     <quantity>1</quantity>
                     <type>CHD</type>
                  </travellerNameInfo>
                  <otherPaxNamesDetails>
                     <nameType>UN</nameType>
                     <referenceName>Y</referenceName>
                     <displayedName>Y</displayedName>
                     <surname>PHAM</surname>
                     <givenName>NGOC LINH</givenName>
                  </otherPaxNamesDetails>
               </enhancedTravellerInformation>
               <dateOfBirthInEnhancedPaxData>
                  <dateAndTimeDetails>
                     <qualifier>706</qualifier>
                     <date>11092018</date>
                  </dateAndTimeDetails>
               </dateOfBirthInEnhancedPaxData>
            </enhancedPassengerData>
         </travellerInfo>
         <originDestinationDetails>
            <originDestination/>
            <itineraryInfo>
               <elementManagementItinerary>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <segmentName>AIR</segmentName>
                  <lineNumber>4</lineNumber>
               </elementManagementItinerary>
               <travelProduct>
                  <product>
                     <depDate>201223</depDate>
                     <depTime>1925</depTime>
                     <arrDate>201223</arrDate>
                     <arrTime>2055</arrTime>
                  </product>
                  <boardpointDetail>
                     <cityCode>DOH</cityCode>
                  </boardpointDetail>
                  <offpointDetail>
                     <cityCode>KWI</cityCode>
                  </offpointDetail>
                  <companyDetail>
                     <identification>QR</identification>
                  </companyDetail>
                  <productDetails>
                     <identification>1082</identification>
                     <classOfService>N</classOfService>
                  </productDetails>
                  <typeDetail>
                     <detail>ET</detail>
                  </typeDetail>
               </travelProduct>
               <itineraryMessageAction>
                  <business>
                     <function>1</function>
                  </business>
               </itineraryMessageAction>
               <itineraryReservationInfo>
                  <reservation>
                     <companyId>QR</companyId>
                     <controlNumber>6Y7LQB</controlNumber>
                  </reservation>
               </itineraryReservationInfo>
               <relatedProduct>
                  <quantity>3</quantity>
                  <status>HK</status>
               </relatedProduct>
               <flightDetail>
                  <productDetails>
                     <equipment>359</equipment>
                     <numOfStops>0</numOfStops>
                     <duration>0130</duration>
                     <weekDay>3</weekDay>
                  </productDetails>
                  <arrivalStationInfo>
                     <terminal>1</terminal>
                  </arrivalStationInfo>
                  <mileageTimeDetails>
                     <flightLegMileage>351</flightLegMileage>
                     <unitQualifier>M</unitQualifier>
                  </mileageTimeDetails>
                  <facilities>
                     <entertainement>M</entertainement>
                     <entertainementDescription>M</entertainementDescription>
                  </facilities>
               </flightDetail>
               <cabinDetails>
                  <cabinDetails>
                     <classDesignator>M</classDesignator>
                  </cabinDetails>
               </cabinDetails>
               <selectionDetails>
                  <selection>
                     <option>P2</option>
                  </selection>
               </selectionDetails>
               <carbonDioxydeInfo>
                  <carbonDioxydeAmount>
                     <quantityDetails>
                        <qualifier>COE</qualifier>
                        <value>64.056685</value>
                        <unit>KPP</unit>
                     </quantityDetails>
                  </carbonDioxydeAmount>
                  <carbonDioxydeInfoSource>
                     <freeTextDetails>
                        <textSubjectQualifier>3</textSubjectQualifier>
                        <source>S</source>
                        <encoding>7</encoding>
                     </freeTextDetails>
                     <freeText>SOURCE:ICAO CARBON EMISSIONS CALCULATOR</freeText>
                  </carbonDioxydeInfoSource>
               </carbonDioxydeInfo>
               <distributionMethod>
                  <distributionMethodDetails>
                     <distriProductCode>E</distriProductCode>
                  </distributionMethodDetails>
               </distributionMethod>
               <legInfo>
                  <markerLegInfo/>
                  <legTravelProduct>
                     <flightDate>
                        <departureDate>201223</departureDate>
                        <departureTime>1925</departureTime>
                        <arrivalDate>201223</arrivalDate>
                        <arrivalTime>2055</arrivalTime>
                     </flightDate>
                     <boardPointDetails>
                        <trueLocationId>DOH</trueLocationId>
                     </boardPointDetails>
                     <offpointDetails>
                        <trueLocationId>KWI</trueLocationId>
                     </offpointDetails>
                  </legTravelProduct>
                  <interactiveFreeText>
                     <freeTextQualification>
                        <textSubjectQualifier>ACO</textSubjectQualifier>
                     </freeTextQualification>
                     <freeText>AIRCRAFT OWNER QATAR AIRWAYS</freeText>
                  </interactiveFreeText>
               </legInfo>
               <markerRailTour/>
            </itineraryInfo>
            <itineraryInfo>
               <elementManagementItinerary>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <segmentName>AIR</segmentName>
                  <lineNumber>5</lineNumber>
               </elementManagementItinerary>
               <travelProduct>
                  <product>
                     <depDate>110124</depDate>
                     <depTime>1050</depTime>
                     <arrDate>110124</arrDate>
                     <arrTime>1215</arrTime>
                  </product>
                  <boardpointDetail>
                     <cityCode>KWI</cityCode>
                  </boardpointDetail>
                  <offpointDetail>
                     <cityCode>DOH</cityCode>
                  </offpointDetail>
                  <companyDetail>
                     <identification>QR</identification>
                  </companyDetail>
                  <productDetails>
                     <identification>1085</identification>
                     <classOfService>N</classOfService>
                  </productDetails>
                  <typeDetail>
                     <detail>ET</detail>
                  </typeDetail>
               </travelProduct>
               <itineraryMessageAction>
                  <business>
                     <function>1</function>
                  </business>
               </itineraryMessageAction>
               <itineraryReservationInfo>
                  <reservation>
                     <companyId>QR</companyId>
                     <controlNumber>6Y7LQB</controlNumber>
                  </reservation>
               </itineraryReservationInfo>
               <relatedProduct>
                  <quantity>3</quantity>
                  <status>HK</status>
               </relatedProduct>
               <flightDetail>
                  <productDetails>
                     <equipment>320</equipment>
                     <numOfStops>0</numOfStops>
                     <duration>0125</duration>
                     <weekDay>4</weekDay>
                  </productDetails>
                  <departureInformation>
                     <departTerminal>1</departTerminal>
                  </departureInformation>
                  <mileageTimeDetails>
                     <flightLegMileage>351</flightLegMileage>
                     <unitQualifier>M</unitQualifier>
                  </mileageTimeDetails>
                  <facilities>
                     <entertainement>M</entertainement>
                     <entertainementDescription>M</entertainementDescription>
                  </facilities>
               </flightDetail>
               <cabinDetails>
                  <cabinDetails>
                     <classDesignator>M</classDesignator>
                  </cabinDetails>
               </cabinDetails>
               <selectionDetails>
                  <selection>
                     <option>P2</option>
                  </selection>
               </selectionDetails>
               <carbonDioxydeInfo>
                  <carbonDioxydeAmount>
                     <quantityDetails>
                        <qualifier>COE</qualifier>
                        <value>64.056685</value>
                        <unit>KPP</unit>
                     </quantityDetails>
                  </carbonDioxydeAmount>
                  <carbonDioxydeInfoSource>
                     <freeTextDetails>
                        <textSubjectQualifier>3</textSubjectQualifier>
                        <source>S</source>
                        <encoding>7</encoding>
                     </freeTextDetails>
                     <freeText>SOURCE:ICAO CARBON EMISSIONS CALCULATOR</freeText>
                  </carbonDioxydeInfoSource>
               </carbonDioxydeInfo>
               <distributionMethod>
                  <distributionMethodDetails>
                     <distriProductCode>E</distriProductCode>
                  </distributionMethodDetails>
               </distributionMethod>
               <legInfo>
                  <markerLegInfo/>
                  <legTravelProduct>
                     <flightDate>
                        <departureDate>110124</departureDate>
                        <departureTime>1050</departureTime>
                        <arrivalDate>110124</arrivalDate>
                        <arrivalTime>1215</arrivalTime>
                     </flightDate>
                     <boardPointDetails>
                        <trueLocationId>KWI</trueLocationId>
                     </boardPointDetails>
                     <offpointDetails>
                        <trueLocationId>DOH</trueLocationId>
                     </offpointDetails>
                  </legTravelProduct>
                  <interactiveFreeText>
                     <freeTextQualification>
                        <textSubjectQualifier>ACO</textSubjectQualifier>
                     </freeTextQualification>
                     <freeText>AIRCRAFT OWNER QATAR AIRWAYS</freeText>
                  </interactiveFreeText>
               </legInfo>
               <markerRailTour/>
            </itineraryInfo>
         </originDestinationDetails>
         <dataElementsMaster>
            <marker2/>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>8</number>
                  </reference>
                  <segmentName>AP</segmentName>
                  <lineNumber>6</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>7</type>
                  </freetextDetail>
                  <longFreetext>+84878703711</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>9</number>
                  </reference>
                  <segmentName>AP</segmentName>
                  <lineNumber>7</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>7</type>
                  </freetextDetail>
                  <longFreetext>+84878703700</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>10</number>
                  </reference>
                  <segmentName>AP</segmentName>
                  <lineNumber>8</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>7</type>
                  </freetextDetail>
                  <longFreetext>+84878703722</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>77</number>
                  </reference>
                  <segmentName>TK</segmentName>
                  <lineNumber>9</lineNumber>
               </elementManagementData>
               <ticketElement>
                  <passengerType>PAX</passengerType>
                  <ticket>
                     <indicator>OK</indicator>
                     <date>141223</date>
                     <officeId>SGNVM28CP</officeId>
                     <electronicTicketFlag>ET</electronicTicketFlag>
                     <airlineCode>QR</airlineCode>
                  </ticket>
               </ticketElement>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>78</number>
                  </reference>
                  <segmentName>TK</segmentName>
                  <lineNumber>10</lineNumber>
               </elementManagementData>
               <ticketElement>
                  <passengerType>PAX</passengerType>
                  <ticket>
                     <indicator>OK</indicator>
                     <date>141223</date>
                     <officeId>SGNVM28CP</officeId>
                     <electronicTicketFlag>ET</electronicTicketFlag>
                     <airlineCode>QR</airlineCode>
                  </ticket>
               </ticketElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>41</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>11</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>RQST</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <boardpoint>KWI</boardpoint>
                     <offpoint>DOH</offpoint>
                  </ssr>
                  <ssrb>
                     <data>22B</data>
                     <crossRef>2</crossRef>
                     <seatType>N</seatType>
                  </ssrb>
               </serviceRequest>
               <seatPaxInfo>
                  <seatPaxDetails>
                     <genericDetails>
                        <seatCharacteristic>N</seatCharacteristic>
                        <seatCharacteristic>CH</seatCharacteristic>
                        <seatCharacteristic>9</seatCharacteristic>
                     </genericDetails>
                  </seatPaxDetails>
                  <seatPaxIndicator>
                     <statusDetails>
                        <indicator>CGB</indicator>
                     </statusDetails>
                  </seatPaxIndicator>
                  <crossRef>
                     <reference>
                        <qualifier>PT</qualifier>
                        <number>2</number>
                     </reference>
                  </crossRef>
               </seatPaxInfo>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>44</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>12</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>RQST</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <boardpoint>KWI</boardpoint>
                     <offpoint>DOH</offpoint>
                  </ssr>
                  <ssrb>
                     <data>22C</data>
                     <crossRef>3</crossRef>
                     <seatType>N</seatType>
                  </ssrb>
               </serviceRequest>
               <seatPaxInfo>
                  <seatPaxDetails>
                     <genericDetails>
                        <seatCharacteristic>N</seatCharacteristic>
                        <seatCharacteristic>CH</seatCharacteristic>
                        <seatCharacteristic>A</seatCharacteristic>
                        <seatCharacteristic>H</seatCharacteristic>
                     </genericDetails>
                  </seatPaxDetails>
                  <seatPaxIndicator>
                     <statusDetails>
                        <indicator>CGB</indicator>
                     </statusDetails>
                  </seatPaxIndicator>
                  <crossRef>
                     <reference>
                        <qualifier>PT</qualifier>
                        <number>3</number>
                     </reference>
                  </crossRef>
               </seatPaxInfo>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>47</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>13</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>RQST</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <boardpoint>KWI</boardpoint>
                     <offpoint>DOH</offpoint>
                  </ssr>
                  <ssrb>
                     <data>22D</data>
                     <crossRef>4</crossRef>
                     <seatType>N</seatType>
                  </ssrb>
               </serviceRequest>
               <seatPaxInfo>
                  <seatPaxDetails>
                     <genericDetails>
                        <seatCharacteristic>N</seatCharacteristic>
                        <seatCharacteristic>CH</seatCharacteristic>
                        <seatCharacteristic>A</seatCharacteristic>
                        <seatCharacteristic>H</seatCharacteristic>
                     </genericDetails>
                  </seatPaxDetails>
                  <seatPaxIndicator>
                     <statusDetails>
                        <indicator>CGB</indicator>
                     </statusDetails>
                  </seatPaxIndicator>
                  <crossRef>
                     <reference>
                        <qualifier>PT</qualifier>
                        <number>4</number>
                     </reference>
                  </crossRef>
               </seatPaxInfo>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>7</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>14</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>CHLD</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>11SEP18</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>15</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>15</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>CTCM</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>84888333777</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>16</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>16</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>CTCM</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>84995006006</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>17</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>17</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>CTCM</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>84096200300</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>18</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>18</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>DOCS</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>P/VNM/C123456/VNM/10OCT82/M/25JAN24/CHAU/MINHTRUNG</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>19</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>19</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>DOCS</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>P/VNM/C111222/VNM/07SEP84/M/24JAN24/NGUYEN/VOKHOI</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>20</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>20</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>DOCS</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>P/VNM/C111777/VNM/11SEP21/M/26DEC24/PHAM/NGOCLINH</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>29</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>21</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>PDBG</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>TTL5KG.FOR TRAVEL AGENT USE ONLY</freeText>
                  </ssr>
               </serviceRequest>
               <reasonForIssuanceCode>
                  <specialCondition>C</specialCondition>
                  <otherSpecialCondition>0AA</otherSpecialCondition>
               </reasonForIssuanceCode>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>CGB</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>30</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>22</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>PDBG</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>TTL5KG.FOR TRAVEL AGENT USE ONLY</freeText>
                  </ssr>
               </serviceRequest>
               <reasonForIssuanceCode>
                  <specialCondition>C</specialCondition>
                  <otherSpecialCondition>0AA</otherSpecialCondition>
               </reasonForIssuanceCode>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>CGB</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>31</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>23</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>PDBG</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>TTL5KG.FOR TRAVEL AGENT USE ONLY</freeText>
                  </ssr>
               </serviceRequest>
               <reasonForIssuanceCode>
                  <specialCondition>C</specialCondition>
                  <otherSpecialCondition>0AA</otherSpecialCondition>
               </reasonForIssuanceCode>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>CGB</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>32</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>24</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>PDBG</type>
                     <status>HK</status>
                     <quantity>1</quantity>
                     <companyId>QR</companyId>
                     <freeText>TTL5KG.FOR TRAVEL AGENT USE ONLY</freeText>
                  </ssr>
               </serviceRequest>
               <reasonForIssuanceCode>
                  <specialCondition>C</specialCondition>
                  <otherSpecialCondition>0AA</otherSpecialCondition>
               </reasonForIssuanceCode>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>CGB</indicator>
                  </statusDetails>
               </elementsIndicators>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>64</number>
                  </reference>
                  <segmentName>SSR</segmentName>
                  <lineNumber>25</lineNumber>
               </elementManagementData>
               <serviceRequest>
                  <ssr>
                     <type>OTHS</type>
                     <status></status>
                     <companyId>1A</companyId>
                     <freeText>307447599537 - FARE RULE OVERRIDES TKT DEADLINE IF MORE RESTRICTIVE</freeText>
                  </ssr>
               </serviceRequest>
               <elementsIndicators>
                  <statusDetails>
                     <indicator>NON</indicator>
                  </statusDetails>
               </elementsIndicators>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>14</number>
                  </reference>
                  <segmentName>OP</segmentName>
                  <lineNumber>26</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P21</type>
                  </freetextDetail>
                  <longFreetext>SGNVM28CP/14DEC</longFreetext>
               </otherDataFreetext>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>79</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>27</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9797705181/ETQR/VND7801000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>80</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>28</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9797705182/ETQR/VND7801000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>81</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>29</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9797705183/ETQR/VND6637000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>82</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>30</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9509388895/DTQR/VND3660000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>29</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>31</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>83</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>31</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9509388896/DTQR/VND3660000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>30</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>32</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>84</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>32</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9509388897/DTQR/VND220000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>41</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>85</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>33</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9509388898/DTQR/VND220000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>44</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>86</number>
                  </reference>
                  <segmentName>FA</segmentName>
                  <lineNumber>34</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P06</type>
                  </freetextDetail>
                  <longFreetext>PAX 157-9509388899/DTQR/VND220000/14DEC23/SGNVM28CP/37301390</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>47</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>68</number>
                  </reference>
                  <segmentName>FB</segmentName>
                  <lineNumber>35</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P07</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000000 TTP/TTM/ET OK ETICKET/EMD NO PRINTERS DEFINED IN OFFICE PROFILE - PLEASE CALL HELP DESK</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>69</number>
                  </reference>
                  <segmentName>FB</segmentName>
                  <lineNumber>36</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P07</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000001 TTP/TTM/ET OK ETICKET/EMD NO PRINTERS DEFINED IN OFFICE PROFILE - PLEASE CALL HELP DESK</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>70</number>
                  </reference>
                  <segmentName>FB</segmentName>
                  <lineNumber>37</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P07</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000002 TTP/TTM/ET OK ETICKET/EMD NO PRINTERS DEFINED IN OFFICE PROFILE - PLEASE CALL HELP DESK</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>29</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>31</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>71</number>
                  </reference>
                  <segmentName>FB</segmentName>
                  <lineNumber>38</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P07</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000003 TTP/TTM/ET OK ETICKET/EMD NO PRINTERS DEFINED IN OFFICE PROFILE - PLEASE CALL HELP DESK</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>30</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>32</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>72</number>
                  </reference>
                  <segmentName>FB</segmentName>
                  <lineNumber>39</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P07</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000004 TTP/TTM/ET OK ETICKET/EMD NO PRINTERS DEFINED IN OFFICE PROFILE - PLEASE CALL HELP DESK</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>41</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>73</number>
                  </reference>
                  <segmentName>FB</segmentName>
                  <lineNumber>40</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P07</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000005 TTP/TTM/ET OK ETICKET/EMD NO PRINTERS DEFINED IN OFFICE PROFILE - PLEASE CALL HELP DESK</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>44</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>74</number>
                  </reference>
                  <segmentName>FB</segmentName>
                  <lineNumber>41</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P07</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000006 TTP/TTM/ET OK ETICKET/EMD NO PRINTERS DEFINED IN OFFICE PROFILE - PLEASE CALL HELP DESK</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>47</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>22</number>
                  </reference>
                  <segmentName>FE</segmentName>
                  <lineNumber>42</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>10</type>
                  </freetextDetail>
                  <longFreetext>PAX /C1-2 NON END/CHNG PENALTIES AS PER RULE</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>24</number>
                  </reference>
                  <segmentName>FE</segmentName>
                  <lineNumber>43</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>10</type>
                  </freetextDetail>
                  <longFreetext>PAX /C1-2 NON END/CHNG PENALTIES AS PER RULE</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>75</number>
                  </reference>
                  <segmentName>FG</segmentName>
                  <lineNumber>44</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P14</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000000 SGN1A0980</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>76</number>
                  </reference>
                  <segmentName>FG</segmentName>
                  <lineNumber>45</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P14</type>
                  </freetextDetail>
                  <longFreetext>PAX 0000000000 SGN1A0980</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>11</number>
                  </reference>
                  <segmentName>FM</segmentName>
                  <lineNumber>46</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>11</type>
                  </freetextDetail>
                  <longFreetext>PAX *M*0</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>12</number>
                  </reference>
                  <segmentName>FM</segmentName>
                  <lineNumber>47</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>11</type>
                  </freetextDetail>
                  <longFreetext>PAX *M*0</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>13</number>
                  </reference>
                  <segmentName>FP</segmentName>
                  <lineNumber>48</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>16</type>
                  </freetextDetail>
                  <longFreetext>CASH</longFreetext>
               </otherDataFreetext>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>23</number>
                  </reference>
                  <segmentName>FV</segmentName>
                  <lineNumber>49</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P18</type>
                  </freetextDetail>
                  <longFreetext>PAX QR</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>3</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
            <dataElementsIndiv>
               <elementManagementData>
                  <reference>
                     <qualifier>OT</qualifier>
                     <number>25</number>
                  </reference>
                  <segmentName>FV</segmentName>
                  <lineNumber>50</lineNumber>
               </elementManagementData>
               <otherDataFreetext>
                  <freetextDetail>
                     <subjectQualifier>3</subjectQualifier>
                     <type>P18</type>
                  </freetextDetail>
                  <longFreetext>PAX QR</longFreetext>
               </otherDataFreetext>
               <referenceForDataElement>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>1</number>
                  </reference>
                  <reference>
                     <qualifier>ST</qualifier>
                     <number>2</number>
                  </reference>
                  <reference>
                     <qualifier>PT</qualifier>
                     <number>4</number>
                  </reference>
               </referenceForDataElement>
            </dataElementsIndiv>
         </dataElementsMaster>
         <tstData>
            <tstGeneralInformation>
               <generalInformation>
                  <tstReferenceNumber>1</tstReferenceNumber>
                  <tstCreationDate>141223</tstCreationDate>
               </generalInformation>
            </tstGeneralInformation>
            <tstFreetext>
               <freetextDetail>
                  <subjectQualifier>3</subjectQualifier>
                  <type>41</type>
               </freetextDetail>
               <longFreetext>PAX</longFreetext>
            </tstFreetext>
            <tstFreetext>
               <freetextDetail>
                  <subjectQualifier>3</subjectQualifier>
                  <type>37</type>
               </freetextDetail>
               <longFreetext>DOH QR KWI Q5.00Q10.00 79.67QR DOH Q5.00 79.67NUC179.34END ROE3.64</longFreetext>
            </tstFreetext>
            <fareBasisInfo>
               <fareElement>
                  <primaryCode>NLQ</primaryCode>
                  <notValidBefore>201223</notValidBefore>
                  <notValidAfter>200424</notValidAfter>
                  <baggageAllowance>30K</baggageAllowance>
                  <fareBasis>AR1RE</fareBasis>
               </fareElement>
               <fareElement>
                  <primaryCode>NLQ</primaryCode>
                  <connection>O</connection>
                  <notValidBefore>201223</notValidBefore>
                  <notValidAfter>200424</notValidAfter>
                  <baggageAllowance>30K</baggageAllowance>
                  <fareBasis>AR1RE</fareBasis>
               </fareElement>
            </fareBasisInfo>
            <fareData>
               <issueIdentifier>F</issueIdentifier>
               <monetaryInfo>
                  <qualifier>F</qualifier>
                  <amount>660.00</amount>
                  <currencyCode>QAR</currencyCode>
               </monetaryInfo>
               <monetaryInfo>
                  <qualifier>E</qualifier>
                  <amount>4423000</amount>
                  <currencyCode>VND</currencyCode>
               </monetaryInfo>
               <monetaryInfo>
                  <qualifier>T</qualifier>
                  <amount>7801000</amount>
                  <currencyCode>VND</currencyCode>
               </monetaryInfo>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>1342000</taxAmount>
                  <taxCountryCode>YQ</taxCountryCode>
                  <taxNatureCode>AC</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>404000</taxAmount>
                  <taxCountryCode>YR</taxCountryCode>
                  <taxNatureCode>VB</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>403000</taxAmount>
                  <taxCountryCode>G4</taxCountryCode>
                  <taxNatureCode>AF</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>51000</taxAmount>
                  <taxCountryCode>PZ</taxCountryCode>
                  <taxNatureCode>SE</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>51000</taxAmount>
                  <taxCountryCode>PZ</taxCountryCode>
                  <taxNatureCode>AV</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>403000</taxAmount>
                  <taxCountryCode>QA</taxCountryCode>
                  <taxNatureCode>AP</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>68000</taxAmount>
                  <taxCountryCode>R9</taxCountryCode>
                  <taxNatureCode>SE</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>80000</taxAmount>
                  <taxCountryCode>GZ</taxCountryCode>
                  <taxNatureCode>SE</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>159000</taxAmount>
                  <taxCountryCode>KW</taxCountryCode>
                  <taxNatureCode>AE</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>397000</taxAmount>
                  <taxCountryCode>N4</taxCountryCode>
                  <taxNatureCode>CB</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>20000</taxAmount>
                  <taxCountryCode>YX</taxCountryCode>
                  <taxNatureCode>AP</taxNatureCode>
               </taxFields>
            </fareData>
            <segmentAssociation>
               <selection>
                  <option>N</option>
               </selection>
               <selection>
                  <option>I</option>
               </selection>
            </segmentAssociation>
            <referenceForTstData>
               <reference>
                  <qualifier>PT</qualifier>
                  <number>2</number>
               </reference>
               <reference>
                  <qualifier>PT</qualifier>
                  <number>3</number>
               </reference>
               <reference>
                  <qualifier>ST</qualifier>
                  <number>1</number>
               </reference>
               <reference>
                  <qualifier>ST</qualifier>
                  <number>2</number>
               </reference>
            </referenceForTstData>
         </tstData>
         <tstData>
            <tstGeneralInformation>
               <generalInformation>
                  <tstReferenceNumber>2</tstReferenceNumber>
                  <tstCreationDate>141223</tstCreationDate>
               </generalInformation>
            </tstGeneralInformation>
            <tstFreetext>
               <freetextDetail>
                  <subjectQualifier>3</subjectQualifier>
                  <type>41</type>
               </freetextDetail>
               <longFreetext>PAX</longFreetext>
            </tstFreetext>
            <tstFreetext>
               <freetextDetail>
                  <subjectQualifier>3</subjectQualifier>
                  <type>37</type>
               </freetextDetail>
               <longFreetext>DOH QR KWI Q5.00Q10.00 59.75QR DOH Q5.00 59.75NUC139.50END ROE3.64</longFreetext>
            </tstFreetext>
            <fareBasisInfo>
               <fareElement>
                  <primaryCode>NLQ</primaryCode>
                  <notValidBefore>201223</notValidBefore>
                  <notValidAfter>200424</notValidAfter>
                  <baggageAllowance>30K</baggageAllowance>
                  <fareBasis>AR1REC</fareBasis>
               </fareElement>
               <fareElement>
                  <primaryCode>NLQ</primaryCode>
                  <connection>O</connection>
                  <notValidBefore>201223</notValidBefore>
                  <notValidAfter>200424</notValidAfter>
                  <baggageAllowance>30K</baggageAllowance>
                  <fareBasis>AR1REC</fareBasis>
               </fareElement>
            </fareBasisInfo>
            <fareData>
               <issueIdentifier>F</issueIdentifier>
               <monetaryInfo>
                  <qualifier>F</qualifier>
                  <amount>510.00</amount>
                  <currencyCode>QAR</currencyCode>
               </monetaryInfo>
               <monetaryInfo>
                  <qualifier>E</qualifier>
                  <amount>3418000</amount>
                  <currencyCode>VND</currencyCode>
               </monetaryInfo>
               <monetaryInfo>
                  <qualifier>T</qualifier>
                  <amount>6637000</amount>
                  <currencyCode>VND</currencyCode>
               </monetaryInfo>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>1342000</taxAmount>
                  <taxCountryCode>YQ</taxCountryCode>
                  <taxNatureCode>AC</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>404000</taxAmount>
                  <taxCountryCode>YR</taxCountryCode>
                  <taxNatureCode>VB</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>403000</taxAmount>
                  <taxCountryCode>G4</taxCountryCode>
                  <taxNatureCode>AF</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>51000</taxAmount>
                  <taxCountryCode>PZ</taxCountryCode>
                  <taxNatureCode>SE</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>51000</taxAmount>
                  <taxCountryCode>PZ</taxCountryCode>
                  <taxNatureCode>AV</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>403000</taxAmount>
                  <taxCountryCode>QA</taxCountryCode>
                  <taxNatureCode>AP</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>68000</taxAmount>
                  <taxCountryCode>R9</taxCountryCode>
                  <taxNatureCode>SE</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>80000</taxAmount>
                  <taxCountryCode>GZ</taxCountryCode>
                  <taxNatureCode>SE</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>397000</taxAmount>
                  <taxCountryCode>N4</taxCountryCode>
                  <taxNatureCode>CB</taxNatureCode>
               </taxFields>
               <taxFields>
                  <taxIndicator>X</taxIndicator>
                  <taxCurrency>VND</taxCurrency>
                  <taxAmount>20000</taxAmount>
                  <taxCountryCode>YX</taxCountryCode>
                  <taxNatureCode>AP</taxNatureCode>
               </taxFields>
            </fareData>
            <segmentAssociation>
               <selection>
                  <option>N</option>
               </selection>
               <selection>
                  <option>I</option>
               </selection>
            </segmentAssociation>
            <referenceForTstData>
               <reference>
                  <qualifier>PT</qualifier>
                  <number>4</number>
               </reference>
               <reference>
                  <qualifier>ST</qualifier>
                  <number>1</number>
               </reference>
               <reference>
                  <qualifier>ST</qualifier>
                  <number>2</number>
               </reference>
            </referenceForTstData>
         </tstData>
         <pricingRecordGroup>
            <pricingRecordData/>
            <productPricingQuotationRecord>
               <pricingRecordId>
                  <referenceType>TST</referenceType>
                  <uniqueReference>1</uniqueReference>
               </pricingRecordId>
               <passengerTattoos>
                  <passengerReference>
                     <type>PA</type>
                     <value>2</value>
                  </passengerReference>
               </passengerTattoos>
               <passengerTattoos>
                  <passengerReference>
                     <type>PA</type>
                     <value>3</value>
                  </passengerReference>
               </passengerTattoos>
               <documentDetailsGroup>
                  <totalFare>
                     <monetaryDetails>
                        <typeQualifier>T</typeQualifier>
                        <amount>7801000</amount>
                        <currency>VND</currency>
                     </monetaryDetails>
                  </totalFare>
                  <issueIdentifier>
                     <priceTicketDetails>
                        <indicators>I</indicators>
                     </priceTicketDetails>
                     <priceTariffType>F</priceTariffType>
                  </issueIdentifier>
                  <manualIndicator>
                     <statusDetails>
                        <indicator>M</indicator>
                        <action>N</action>
                     </statusDetails>
                  </manualIndicator>
                  <officeInformation>
                     <originIdentification>
                        <inHouseIdentification1>SGNVM28CP</inHouseIdentification1>
                        <inHouseIdentification2>WS</inHouseIdentification2>
                     </originIdentification>
                  </officeInformation>
                  <creationDate>
                     <businessSemantic>CRD</businessSemantic>
                     <dateTime>
                        <year>2023</year>
                        <month>12</month>
                        <day>14</day>
                     </dateTime>
                  </creationDate>
                  <couponDetailsGroup>
                     <productId>
                        <referenceDetails>
                           <type>ST</type>
                           <value>1</value>
                        </referenceDetails>
                     </productId>
                  </couponDetailsGroup>
                  <couponDetailsGroup>
                     <productId>
                        <referenceDetails>
                           <type>ST</type>
                           <value>2</value>
                        </referenceDetails>
                     </productId>
                  </couponDetailsGroup>
                  <fareComponentDetailsGroup>
                     <fareComponentID>
                        <itemNumberDetails>
                           <number>1</number>
                           <type>FAR</type>
                        </itemNumberDetails>
                     </fareComponentID>
                     <marketFareComponent>
                        <boardPointDetails>
                           <trueLocationId>DOH</trueLocationId>
                        </boardPointDetails>
                        <offpointDetails>
                           <trueLocationId>KWI</trueLocationId>
                        </offpointDetails>
                     </marketFareComponent>
                     <monetaryInformation>
                        <monetaryDetails>
                           <typeQualifier>B</typeQualifier>
                           <amount>79.67</amount>
                           <currency>NUC</currency>
                        </monetaryDetails>
                     </monetaryInformation>
                     <componentClassInfo>
                        <fareBasisDetails>
                           <rateTariffClass>NLQAR1RE</rateTariffClass>
                        </fareBasisDetails>
                     </componentClassInfo>
                     <fareQualifiersDetail>
                        <discountDetails>
                           <fareQualifier>763</fareQualifier>
                        </discountDetails>
                     </fareQualifiersDetail>
                     <fareFamilyDetails>
                        <fareFamilyname>ECLASSIC</fareFamilyname>
                     </fareFamilyDetails>
                     <fareFamilyOwner>
                        <companyIdentification>
                           <otherCompany>QR</otherCompany>
                        </companyIdentification>
                     </fareFamilyOwner>
                     <couponDetailsGroup>
                        <productId>
                           <referenceDetails>
                              <type>ST</type>
                              <value>1</value>
                           </referenceDetails>
                        </productId>
                     </couponDetailsGroup>
                  </fareComponentDetailsGroup>
                  <fareComponentDetailsGroup>
                     <fareComponentID>
                        <itemNumberDetails>
                           <number>2</number>
                           <type>FAR</type>
                        </itemNumberDetails>
                     </fareComponentID>
                     <marketFareComponent>
                        <boardPointDetails>
                           <trueLocationId>KWI</trueLocationId>
                        </boardPointDetails>
                        <offpointDetails>
                           <trueLocationId>DOH</trueLocationId>
                        </offpointDetails>
                     </marketFareComponent>
                     <monetaryInformation>
                        <monetaryDetails>
                           <typeQualifier>B</typeQualifier>
                           <amount>79.67</amount>
                           <currency>NUC</currency>
                        </monetaryDetails>
                     </monetaryInformation>
                     <componentClassInfo>
                        <fareBasisDetails>
                           <rateTariffClass>NLQAR1RE</rateTariffClass>
                        </fareBasisDetails>
                     </componentClassInfo>
                     <fareQualifiersDetail>
                        <discountDetails>
                           <fareQualifier>763</fareQualifier>
                        </discountDetails>
                     </fareQualifiersDetail>
                     <fareFamilyDetails>
                        <fareFamilyname>ECLASSIC</fareFamilyname>
                     </fareFamilyDetails>
                     <fareFamilyOwner>
                        <companyIdentification>
                           <otherCompany>QR</otherCompany>
                        </companyIdentification>
                     </fareFamilyOwner>
                     <couponDetailsGroup>
                        <productId>
                           <referenceDetails>
                              <type>ST</type>
                              <value>2</value>
                           </referenceDetails>
                        </productId>
                     </couponDetailsGroup>
                  </fareComponentDetailsGroup>
               </documentDetailsGroup>
            </productPricingQuotationRecord>
            <productPricingQuotationRecord>
               <pricingRecordId>
                  <referenceType>TST</referenceType>
                  <uniqueReference>2</uniqueReference>
               </pricingRecordId>
               <passengerTattoos>
                  <passengerReference>
                     <type>PA</type>
                     <value>4</value>
                  </passengerReference>
               </passengerTattoos>
               <documentDetailsGroup>
                  <totalFare>
                     <monetaryDetails>
                        <typeQualifier>T</typeQualifier>
                        <amount>6637000</amount>
                        <currency>VND</currency>
                     </monetaryDetails>
                  </totalFare>
                  <issueIdentifier>
                     <priceTicketDetails>
                        <indicators>I</indicators>
                     </priceTicketDetails>
                     <priceTariffType>F</priceTariffType>
                  </issueIdentifier>
                  <manualIndicator>
                     <statusDetails>
                        <indicator>M</indicator>
                        <action>N</action>
                     </statusDetails>
                  </manualIndicator>
                  <officeInformation>
                     <originIdentification>
                        <inHouseIdentification1>SGNVM28CP</inHouseIdentification1>
                        <inHouseIdentification2>WS</inHouseIdentification2>
                     </originIdentification>
                  </officeInformation>
                  <creationDate>
                     <businessSemantic>CRD</businessSemantic>
                     <dateTime>
                        <year>2023</year>
                        <month>12</month>
                        <day>14</day>
                     </dateTime>
                  </creationDate>
                  <couponDetailsGroup>
                     <productId>
                        <referenceDetails>
                           <type>ST</type>
                           <value>1</value>
                        </referenceDetails>
                     </productId>
                  </couponDetailsGroup>
                  <couponDetailsGroup>
                     <productId>
                        <referenceDetails>
                           <type>ST</type>
                           <value>2</value>
                        </referenceDetails>
                     </productId>
                  </couponDetailsGroup>
                  <fareComponentDetailsGroup>
                     <fareComponentID>
                        <itemNumberDetails>
                           <number>1</number>
                           <type>FAR</type>
                        </itemNumberDetails>
                     </fareComponentID>
                     <marketFareComponent>
                        <boardPointDetails>
                           <trueLocationId>DOH</trueLocationId>
                        </boardPointDetails>
                        <offpointDetails>
                           <trueLocationId>KWI</trueLocationId>
                        </offpointDetails>
                     </marketFareComponent>
                     <monetaryInformation>
                        <monetaryDetails>
                           <typeQualifier>B</typeQualifier>
                           <amount>59.75</amount>
                           <currency>NUC</currency>
                        </monetaryDetails>
                     </monetaryInformation>
                     <componentClassInfo>
                        <fareBasisDetails>
                           <rateTariffClass>NLQAR1RECH</rateTariffClass>
                        </fareBasisDetails>
                     </componentClassInfo>
                     <fareQualifiersDetail>
                        <discountDetails>
                           <fareQualifier>763</fareQualifier>
                        </discountDetails>
                     </fareQualifiersDetail>
                     <fareFamilyDetails>
                        <fareFamilyname>ECLASSIC</fareFamilyname>
                     </fareFamilyDetails>
                     <fareFamilyOwner>
                        <companyIdentification>
                           <otherCompany>QR</otherCompany>
                        </companyIdentification>
                     </fareFamilyOwner>
                     <couponDetailsGroup>
                        <productId>
                           <referenceDetails>
                              <type>ST</type>
                              <value>1</value>
                           </referenceDetails>
                        </productId>
                     </couponDetailsGroup>
                  </fareComponentDetailsGroup>
                  <fareComponentDetailsGroup>
                     <fareComponentID>
                        <itemNumberDetails>
                           <number>2</number>
                           <type>FAR</type>
                        </itemNumberDetails>
                     </fareComponentID>
                     <marketFareComponent>
                        <boardPointDetails>
                           <trueLocationId>KWI</trueLocationId>
                        </boardPointDetails>
                        <offpointDetails>
                           <trueLocationId>DOH</trueLocationId>
                        </offpointDetails>
                     </marketFareComponent>
                     <monetaryInformation>
                        <monetaryDetails>
                           <typeQualifier>B</typeQualifier>
                           <amount>59.75</amount>
                           <currency>NUC</currency>
                        </monetaryDetails>
                     </monetaryInformation>
                     <componentClassInfo>
                        <fareBasisDetails>
                           <rateTariffClass>NLQAR1RECH</rateTariffClass>
                        </fareBasisDetails>
                     </componentClassInfo>
                     <fareQualifiersDetail>
                        <discountDetails>
                           <fareQualifier>763</fareQualifier>
                        </discountDetails>
                     </fareQualifiersDetail>
                     <fareFamilyDetails>
                        <fareFamilyname>ECLASSIC</fareFamilyname>
                     </fareFamilyDetails>
                     <fareFamilyOwner>
                        <companyIdentification>
                           <otherCompany>QR</otherCompany>
                        </companyIdentification>
                     </fareFamilyOwner>
                     <couponDetailsGroup>
                        <productId>
                           <referenceDetails>
                              <type>ST</type>
                              <value>2</value>
                           </referenceDetails>
                        </productId>
                     </couponDetailsGroup>
                  </fareComponentDetailsGroup>
               </documentDetailsGroup>
            </productPricingQuotationRecord>
         </pricingRecordGroup>
      </PNR_Reply>
