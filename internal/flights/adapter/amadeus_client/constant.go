package amadeus_client

import (
	"time"
)

const (
	successfulCode = 200
	timeoutContext = time.Second * 5
)

const (
	Method_POST                                         = "POST"
	RelativePath_SearchFlight                           = "/search-flight"
	RelativePath_FareInfomationBestPricing              = "/fare-information-best-pricing"
	RelativePath_AirSellFromRecommendation              = "/air-sell-from-recommendation"
	RelativePath_PNRAddMultiElements                    = "/pnr-add-multi-element"
	RelativePath_FareCheckRules                         = "/fare-check-rules"
	RelativePath_FarePricePnrWithBookingClass           = "/fare-price-pnr-with-booking-class"
	RelativePath_FarePricePnrWithBookingClassFBA        = "/fare-price-pnr-with-booking-class-fba"
	RelativePath_TicketCreateTSTFromPricing             = "/ticket-create-tst-from-pricing"
	RelativePath_RetrievePNR                            = "/pnr-retrieve"
	RelativePath_PNRCancel                              = "/pnr-cancel"
	RelativePath_IssueTicket                            = "/issue-ticket"
	RelativePath_QueuePlacePNR                          = "/queue-place-pnr"
	RelativePath_SecuritySignOut                        = "/sign-out"
	RelativePath_ServiceStandaloneCatalogue             = "/service-standalone-catalogue"
	RelativePath_AirRetrieveSeatMap                     = "/air-retrieve-seatmap"
	RelativePath_ServiceIntegratedPricing               = "/service-integrated-pricing"
	RelativePath_CreateTSMFromPricing                   = "/create-tsm-from-pricing"
	RelativePath_DocIssuanceIssueMiscellaneousDocuments = "/doc-issue-miscellaneous-documents"
	RelativePath_CreateFOP                              = "/fop_create_form_of_payment"
)

const (
	ErrMessNoFareFound                      = "NO FARE FOUND FOR REQUESTED ITINERARY"
	ErrTooManyRequestedSegments             = "Too many requested segments"
	ErrMessFlightNotConfirmed               = "UNABLE TO SATISFY, NEED CONFIRMED FLIGHT STATUS"
	ErrNoItinerary                          = "NO ITINERARY FOUND FOR REQUESTED SEGMENT"
	ErrMessFareNotAvailable                 = "NO FARE FOR BOOKING CODE-TRY OTHER PRICING OPTIONS"
	ErrMsgNoValidFare                       = "NO VALID FARE/RULE COMBINATIONS FOR PRICING"
	ErrMsgCommunicationsNotAvailable        = "COMMUNICATIONS NOT AVAILABLE"
	ErrMsgSeatMapNotAvailable               = "SEAT MAP NOT AVAILABLE"
	ErrMsgSeatMapNotAvailableZone           = "Seat map not available for requested zone, seat may be requested"
	ErrMsgSeatMapNotAvailableForThisCarrier = "SEAT MAP NOT AVAILABLE FOR THIS CARRIER"
	ErrMsgNameTooLong                       = "ITEM TOO LONG / NOT ENTERED"
	ErrMsgNameTooLong2                      = "Invalid length for data element"
	ErrReTicketTST                          = "NO FARES/RBD/CARRIER/PASSENGER TYPE"
)
