package request

type Segment struct {
	StartPoint       string `json:"start_point,omitempty"`
	EndPoint         string `json:"end_point,omitempty"`
	DepartDate       int64  `json:"depart_date,omitempty"`
	MarketingCompany string `json:"marketing_company,omitempty"`
	OperatingCompany string `json:"operating_company,omitempty"`
	FlightNumber     string `json:"flight_number,omitempty"`
	BookingClass     string `json:"booking_class,omitempty"`
	SegmentTattoo    int    `json:"segment_tattoo,omitempty"`
	GroupNumber      int    `json:"group_number,omitempty"`
	CompanyCode      string `json:"company_code,omitempty"`
	FareBasicCode    string `json:"fare_basis_code,omitempty"`
}

type Passengers struct {
	Adt int `json:"adt,omitempty"`
	Chd int `json:"chd,omitempty"`
	Inf int `json:"inf,omitempty"`
}
