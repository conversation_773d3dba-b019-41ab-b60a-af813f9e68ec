package request

type FOPCreditCard struct {
	Type           string `json:"type,omitempty"`
	Number         string `json:"number,omitempty"`
	ExpirationDate int64  `json:"expiration_date,omitempty"`
	CVCCode        int    `json:"cvc_code,omitempty"`
	Holder         string `json:"holder,omitempty"`
}

type Traveler struct {
	ID               int    `json:"id,omitempty"`
	Type             string `json:"type,omitempty"`
	LastName         string `json:"last_name,omitempty"`
	FirstName        string `json:"first_name,omitempty"`
	DOBForPassport   int64  `json:"dob_for_passport"`
	DOB              int64  `json:"dob"`
	Gender           string `json:"gender,omitempty"`
	Nationality      string `json:"nationality,omitempty"`
	IDIssuingCountry string `json:"id_issuing_country,omitempty"`
	PassportNumber   string `json:"passport_number,omitempty"`
	ExpirationDate   int64  `json:"expiration_date,omitempty"`
	CompanyCode      string `json:"company_code,omitempty"`
	ParentID         int    `json:"parent_id,omitempty"`
	ChidID           int    `json:"-"`
}

type Contact struct {
	Phone string `json:"phone,omitempty"`
	Email string `json:"email,omitempty"`
}

type PnrAddMultiElement struct {
	Session              *Session         `json:"session,omitempty"`
	ActionCodes          []int            `json:"action_codes,omitempty"`
	FOP                  *FOP             `json:"fop,omitempty"`
	TicketMode           string           `json:"ticket_mode,omitempty"`
	TicketExpirationDate int64            `json:"ticket_expiration_date,omitempty"`
	Travelers            []*Traveler      `json:"travelers,omitempty"`
	Contact              *Contact         `json:"contact,omitempty"`
	CommissionPercentage []*Commission    `json:"commission_percentage,omitempty"`
	SeatSelections       []*SeatSelection `json:"seat_selections,omitempty"`
}

type SeatSelection struct {
	PassengerTattoo int    `json:"passenger_tattoo,omitempty"`
	SegmentTattoo   int    `json:"segment_tattoo,omitempty"`
	SeatCode        string `json:"seat_code,omitempty"`
}

type FOP struct {
	Type       string         `json:"type,omitempty"`
	CreditCard *FOPCreditCard `json:"credit_card,omitempty"`
}

type Commission struct {
	Type  string  `json:"type"`
	Value float64 `json:"value"`
}
