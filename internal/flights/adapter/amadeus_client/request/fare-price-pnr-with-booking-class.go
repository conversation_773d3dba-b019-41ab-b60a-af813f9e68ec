package request

type FarePricePnrWithBookingClass struct {
	Session           *Session `json:"session,omitempty"`
	ValidatingCarrier string   `json:"validating_carrier,omitempty"`
}

type FareBasis struct {
	Code     string  `json:"code,omitempty"`
	Segments []int64 `json:"segments"`
}
type FarePricePnrWithBookingClassFBA struct {
	Session           *Session     `json:"session,omitempty"`
	ValidatingCarrier string       `json:"validating_carrier,omitempty"`
	Passengers        *Passengers  `json:"passengers,omitempty"`
	FareBasis         []*FareBasis `json:"fare_basis"`
}
