package request

type AirSeatMapSegment struct {
	StartPoint       string `json:"start_point,omitempty"`
	EndPoint         string `json:"end_point,omitempty"`
	DepartDate       int64  `json:"depart_date,omitempty"`
	ArrivalDate       int64  `json:"arrival_date,omitempty"`
	MarketingCompany string `json:"marketing_company,omitempty"`
	OperatingCompany string `json:"operating_company,omitempty"`
	FlightNumber     string `json:"flight_number,omitempty"`
	BookingClass     string `json:"booking_class,omitempty"`
	FareBasisCode    string `json:"fare_basis_code,omitempty"`
}
type AirRetrieveSeatMapRQ struct {
	Stateful bool               `json:"stateful"`
	Session  *Session           `json:"session,omitempty"`
	Traveler []*Traveler        `json:"travelers,omitempty"`
	Segment  *AirSeatMapSegment `json:"flight,omitempty"`
}
