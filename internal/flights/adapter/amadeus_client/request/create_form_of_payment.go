package request

type ElementAssociation struct {
	Type  string `json:"type"`
	Value int    `json:"value"`
}

type MopInfo struct {
	SeqNbr int    `json:"sequence_number"`
	Code   string `json:"fop_code"`
}

type CreateFormOfPaymentRQ struct {
	Session             *Session              `json:"session"`
	TransactionCode     string                `json:"transaction_code"`
	ElementAssociations []*ElementAssociation `json:"element_association"`
	MopInfo             *MopInfo              `json:"mop_info"`
}
