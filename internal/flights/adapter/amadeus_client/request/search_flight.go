package request

type SearchFlight struct {
	DCP               *DCP        `json:"dcp,omitempty"`
	Passengers        *Passengers `json:"passengers,omitempty"`
	Flights           []*Flight   `json:"flights,omitempty"`
	ExclusionAirlines []string    `json:"exclusion_airlines"`
}

type Flight struct {
	StartPoint string `json:"start_point,omitempty"`
	EndPoint   string `json:"end_point,omitempty"`
	DepartDate int64  `json:"depart_date,omitempty"`
}
