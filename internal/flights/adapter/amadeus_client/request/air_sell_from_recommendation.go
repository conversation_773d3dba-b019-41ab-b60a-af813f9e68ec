package request

type Session struct {
	ID       string `json:"id,omitempty"`
	Sequence int    `json:"sequence,omitempty"`
	Token    string `json:"token,omitempty"`
}

type Itinerary struct {
	StartPoint string     `json:"start_point,omitempty"`
	EndPoint   string     `json:"end_point,omitempty"`
	Segments   []*Segment `json:"segments,omitempty"`
}

type AirSellFromRecommendation struct {
	Session     *Session     `json:"session,omitempty"`
	Passengers  *Passengers  `json:"passengers,omitempty"`
	Itineraries []*Itinerary `json:"itineraries,omitempty"`
}
