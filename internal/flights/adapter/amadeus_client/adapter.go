package amadeus_client

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/fare_price_pnr_with_booking_class"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models/pnr_repply"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/request"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/telegram"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

const (
	ACTION_NO_PROCESSING       = 0
	ACTION_END_TRANSACT        = 10
	ACTION_END_TRANSACT_ADVICE = 12 // ETK

	TYPE_CASH         = "CA"
	TICKETMODE_CANCEL = "XL"
	TICKETMODE_OK     = "OK"

	OPTION_ETICKET    = "ET"
	OPTION_ETICKET_RT = "RT"

	E_TICKET_FLAG_SUCCESS = "ET"
)

type AmadeusAdapter interface {
	SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error)
	ConfirmFare(ctx context.Context, booking *domain.BookingSession, req *domain.PNR, isIssuing bool, tracingID string) (*domain.AmadeusCfFareRes, error)
	IssueTicket(ctx context.Context, recordLocator string, commRate *float64, hasINF bool, hasEMD bool, tsmValues []int, booking *domain.BookingSession, pnrReq *domain.PNR, retryTST bool, tracingID string) ([]*models.ReservationInfo, []*models.EticketInfo, []*models.EMDInfo, error, error)
	CancelBooking(ctx context.Context, recordLoc string) error
	FetchEticketInfo(ctx context.Context, recLoc string) ([]*models.ReservationInfo, []*models.EticketInfo, []*models.EMDInfo, error)
	GetBaggage(ctx context.Context, searchCached *domain.SearchFlightsCachedRecord, flightItinerary *domain.FlightItinerary, tracingID string) ([]*domain.BaggageOption, error)
	PlaceQueue(ctx context.Context, recLoc string) error
	GetSeatMapStateless(ctx context.Context, segment *domain.ItinerarySegment, tracingID string) (*domain.SeatSegment, error)
	RetrievePNRForTransferredBooking(ctx context.Context, recordLocator string) (*domain.PNRAmadeusTransferred, []*models.ReservationInfo, []*models.EticketInfo, error)
	VerifyBookingFlightData(ctx context.Context, bkFlight *domain.BookingSession) (bool, error)
	GetLastTktInPNR(ctx context.Context, booking *domain.BookingSession, recordLocator string) (int64, error)
	BookingAutoIssue(ctx context.Context, booking *domain.BookingSession, req *domain.PNR) ([]*models.ReservationInfo, []*models.EticketInfo, []*models.EMDInfo, *domain.TotalFareInfo, error, error)
	ConfirmFareV2(ctx context.Context, booking *domain.BookingSession, pnrReq *domain.PNR, tracingID string) (*domain.AmadeusCfFareRes, error)
}
type amadeusAdapter struct {
	Cfg            *config.Schema
	AmadesusClient AmadeusClient
	AirportRepo    repositories.AirportRepository
	TemplateRepo   repositories.AmaSSRTemplateRepository
}

func (a *amadeusAdapter) BookingAutoIssue(ctx context.Context, booking *domain.BookingSession, req *domain.PNR) ([]*models.ReservationInfo, []*models.EticketInfo, []*models.EMDInfo, *domain.TotalFareInfo, error, error) {
	hasINF := booking.SearchRequest.Passengers.INF > 0
	hasEMD := booking.HasEMD() && booking.FareDataCf != nil && (booking.FareDataCf.TotalSeatAmount > 0 || booking.FareDataCf.TotalBaggageAmount > 0)
	commRate := booking.CommissionRate

	fareRes, err := a.ConfirmFare(ctx, booking, req, true, booking.BookingCode)
	if err != nil {
		return nil, nil, nil, nil, nil, errors.Wrap(err, "ConfirmFare")
	}

	if fareRes.FareCf == nil {
		return nil, nil, nil, nil, nil, errors.Wrap(commonErrors.ErrSomethingOccurred, "FareCf is nil")
	}

	if fareRes.CommRate != nil {
		commRate = nil
	}

	booking.BookingRef = fareRes.RecordLocator
	booking.FareDataIss = fareRes.FareCf

	hasChanges, err := a.VerifyBookingFlightData(ctx, booking)
	if err != nil {
		return nil, nil, nil, fareRes.FareCf, nil, errors.Wrap(err, "VerifyBookingFlightData")
	}

	if hasChanges {
		log.Error("[IMPORTANT] Internal booking hasChanges after issued", log.Any("hasChanges", hasChanges), log.String("bkCode", booking.BookingCode))
	}

	res1, res2, res3, res4, res5 := a.IssueTicket(ctx, fareRes.RecordLocator, commRate, hasINF, hasEMD, fareRes.TSMValues, booking, req, false, booking.BookingCode)

	return res1, res2, res3, fareRes.FareCf, res4, res5
}

func (a *amadeusAdapter) VerifyBookingFlightData(ctx context.Context, bkFlight *domain.BookingSession) (bool, error) {
	pnr, sess, err := a.AmadesusClient.RetrievePNR(ctx, &request.RetrivePnr{
		RecordLocator: &bkFlight.BookingRef,
	}, bkFlight.BookingRef)
	if err != nil {
		return false, errors.Wrap(err, "AmadesusClient.RetrievePNR")
	}

	hasChanges, shouldReconfirm, err := converts.SyncBooking(pnr, bkFlight)
	if err != nil {
		return false, errors.Wrap(err, "converts.SyncBooking")
	}

	if hasChanges || shouldReconfirm {
		req := &request.PnrAddMultiElement{
			Session: &request.Session{
				ID:       sess.SessionID,
				Sequence: sess.SequenceNumber + 1,
				Token:    sess.SecurityToken,
			},
			ActionCodes: []int{ACTION_END_TRANSACT_ADVICE},
		}

		_, _, err := a.AmadesusClient.PNRAddMultiElements(ctx, req, bkFlight.BookingRef)
		if err != nil {
			return false, errors.Wrap(err, "AmadesusClient.PNRAddMultiElements")
		}
	}

	return hasChanges, nil
}

// PlaceQueue implements AmadesusAdapter.
func (a *amadeusAdapter) PlaceQueue(ctx context.Context, recLoc string) error {
	_, err := a.AmadesusClient.QueuePlacePNR(ctx, &request.QueuePlacePNR{
		RecordLocator: recLoc,
		Queue:         1,
	}, recLoc)

	if err != nil {
		return errors.Wrap(err, "QueuePlacePNR")
	}

	return nil
}

func NewAmadeusAdapter(cfg *config.Schema, requestRepo repositories.RequestRepository, amadeusISOCountryRepo repositories.AmadeusISOCountryRepository, redisRepo commonRedis.IRedis, airportRepo repositories.AirportRepository, templateRepo repositories.AmaSSRTemplateRepository) AmadeusAdapter {
	return &amadeusAdapter{
		AmadesusClient: NewAmadeusClient(cfg, requestRepo, amadeusISOCountryRepo, redisRepo),
		AirportRepo:    airportRepo,
		Cfg:            cfg,
		TemplateRepo:   templateRepo,
	}
}

func (a *amadeusAdapter) FetchEticketInfo(ctx context.Context, recLoc string) ([]*models.ReservationInfo, []*models.EticketInfo, []*models.EMDInfo, error) {
	retrieveRq := &request.RetrivePnr{
		RecordLocator: &recLoc,
	}

	resPNR, session, err := a.AmadesusClient.RetrievePNR(ctx, retrieveRq, recLoc)
	if err != nil {
		// Exception
		log.Error("RetrievePNR error", log.Any("error", err), log.Any("req", retrieveRq))
		return nil, nil, nil, nil
	}

	go func() {
		bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
		defer cancel()
		_, _ = a.AmadesusClient.SecuritySignOut(bCtx, &request.SignOutRQ{Session: converts.FromModelSessionToRequest(session)}, recLoc)
	}()

	reservationInfo, etickets, emdTicket, err := getETicketsInfo(resPNR)

	if err != nil {
		// Exception
		log.Error("getETicketsInfo error", log.Any("error", err), log.Any("resPNR", resPNR))
	}

	return reservationInfo, etickets, emdTicket, nil
}

func (a *amadeusAdapter) GetSeatMapStateless(ctx context.Context, segment *domain.ItinerarySegment, tracingID string) (*domain.SeatSegment, error) {
	if segment == nil {
		return nil, fmt.Errorf("GetSeatMapStateless: Segment nil")
	}

	airSeatReq := &request.AirRetrieveSeatMapRQ{
		Stateful: false,
		// Hard code pnr for get seat before booking
		Traveler: []*request.Traveler{{
			LastName:  "TRAN",
			FirstName: "TRI",
			Type:      "ADT",
		}},
		Segment: &request.AirSeatMapSegment{
			StartPoint:       segment.DepartPlace,
			EndPoint:         segment.ArrivalPlace,
			DepartDate:       segment.DepartDate,
			ArrivalDate:      segment.ArrivalDate,
			MarketingCompany: segment.CarrierMarketing,
			OperatingCompany: segment.CarrierOperator,
			FlightNumber:     segment.FlightNumber,
			BookingClass:     segment.BookingClass,
			FareBasisCode:    segment.FareBasis,
		},
	}

	res, err := a.AmadesusClient.AirRetrieveSeatMap(ctx, airSeatReq, tracingID)
	if err != nil {
		// SEAT MAP NOT AVAILABLE
		if errors.Is(err, domain.ErrSeatMapNotAvailable) {
			return nil, nil
		}

		log.Error("a.AmadesusClient.AirRetrieveSeatMap error", log.Any("error", err))
		return nil, commonErrors.ErrSomethingOccurred
	}

	seatSegment, err := converts.ToDomainSegmentSeat(res, segment)
	if err != nil {
		log.Error("a.AmadesusClient.AirRetrieveSeatMap error", log.Any("error", err))
	}

	return seatSegment, nil
}

func (a *amadeusAdapter) CancelBooking(ctx context.Context, recordLoc string) error {
	req := &request.PNRCancel{
		ActionCodes:     []int{ACTION_END_TRANSACT},
		CancelItinerary: true,
		RecordLocator:   recordLoc,
	}

	_, _, err := a.AmadesusClient.PNRCancel(ctx, req, recordLoc)
	if err != nil {
		return errors.Wrap(err, "AmadesusClient.PNRCancel")
	}

	return nil
}

func (a *amadeusAdapter) SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error) {
	if req == nil {
		return nil, nil
	}

	clientReq := &request.SearchFlight{
		Passengers: &request.Passengers{
			Adt: req.Passengers.ADT,
			Chd: req.Passengers.CHD,
			Inf: req.Passengers.INF,
		},
		Flights: converts.ToRequestFlights(req.Itineraries),
	}

	res, err := a.AmadesusClient.SearchFlight(ctx, clientReq, tracingID)
	if err != nil {
		return nil, err
	}

	resFlights, err := converts.ToDomainResponseFlights(res, req)
	if err != nil {
		return nil, err
	}

	return resFlights, nil
}

func buildStringSSRNotify(booking *domain.BookingSession) string {
	//<điểm đi 1>_<điểm đến 1>_<carrier 1><flight_number 1>_<điểm đi 2>_<điểm đến 2>_<carrier 2><flight_number 2>_…<điểm đi n>_<điểm đến n>_<carrier n><flight_number n>
	result := ""
	if booking != nil {
		for _, iti := range booking.Itineraries {
			if iti != nil {
				for _, seg := range iti.Segments {
					if seg != nil {
						result += fmt.Sprintf("%s-%s-%s%s-", seg.DepartPlace, seg.ArrivalPlace, seg.CarrierMarketing, seg.FlightNumber)
					}
				}
			}
		}
	}

	if len(result) > 0 {
		result = result[:len(result)-1]
	}
	return result
}

// ConfirmFareV2 checks if the flight is available and the lastest price amount
func (a *amadeusAdapter) ConfirmFareV2(ctx context.Context, booking *domain.BookingSession, pnrReq *domain.PNR, tracingID string) (*domain.AmadeusCfFareRes, error) {
	if pnrReq == nil || booking == nil {
		return nil, fmt.Errorf("GetSeatMapStateless: pnrReq nil or booking nil")
	}

	fareReq := &request.FareInformationBestPricing{
		Passengers: &request.Passengers{
			Adt: booking.PassengerInfo.ADT,
			Chd: booking.PassengerInfo.CHD,
			Inf: booking.PassengerInfo.INF,
		},
		Segments: converts.FromDomainItinerariesToSegments(booking.Itineraries),
	}

	_, sessionFareInfo, err := a.AmadesusClient.FareInformationBestPricing(ctx, fareReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "FareInfomationBestPricing")
	}

	airSellReq := &request.AirSellFromRecommendation{
		Session: converts.FromModelSessionToRequest(sessionFareInfo),
		Passengers: &request.Passengers{
			Adt: booking.PassengerInfo.ADT,
			Chd: booking.PassengerInfo.CHD,
			Inf: booking.PassengerInfo.INF,
		},
		Itineraries: converts.FromDomainItineraries(booking.Itineraries),
	}

	_, sessionAirSell, err := a.AmadesusClient.AirSellFromRecommendation(ctx, airSellReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "AirSellFromRecommendation")
	}

	go func() {
		bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
		defer cancel()
		_, _ = a.AmadesusClient.SecuritySignOut(bCtx, &request.SignOutRQ{Session: converts.FromModelSessionToRequest(sessionAirSell)}, tracingID)
	}()

	response := &domain.AmadeusCfFareRes{}

	return response, nil
}

func (a *amadeusAdapter) ConfirmFare(ctx context.Context, booking *domain.BookingSession, pnrReq *domain.PNR, isIssuing bool, tracingID string) (*domain.AmadeusCfFareRes, error) {
	var recordLocator string

	if pnrReq == nil || booking == nil {
		return nil, fmt.Errorf("GetSeatMapStateless: pnrReq nil or booking nil")
	}

	fareReq := &request.FareInformationBestPricing{
		Passengers: &request.Passengers{
			Adt: booking.PassengerInfo.ADT,
			Chd: booking.PassengerInfo.CHD,
			Inf: booking.PassengerInfo.INF,
		},
		Segments: converts.FromDomainItinerariesToSegments(booking.Itineraries),
	}

	_, sessionFareInfo, err := a.AmadesusClient.FareInformationBestPricing(ctx, fareReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "FareInfomationBestPricing")
	}

	airSellReq := &request.AirSellFromRecommendation{
		Session: converts.FromModelSessionToRequest(sessionFareInfo),
		Passengers: &request.Passengers{
			Adt: booking.PassengerInfo.ADT,
			Chd: booking.PassengerInfo.CHD,
			Inf: booking.PassengerInfo.INF,
		},
		Itineraries: converts.FromDomainItineraries(booking.Itineraries),
	}

	_, sessionAirSell, err := a.AmadesusClient.AirSellFromRecommendation(ctx, airSellReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "AirSellFromRecommendation")
	}

	ticketExpDate := booking.Itineraries[0].DepartDt.UTC().Add(-time.Hour).In(time.FixedZone("UTC+7", 7*60*60))

	travelers := converts.FromDomainTravelers(pnrReq.ListPax, booking.Itineraries[0].CarrierMarketing)
	addPRNReq := &request.PnrAddMultiElement{
		Session:     converts.FromModelSessionToRequest(sessionAirSell),
		ActionCodes: []int{ACTION_NO_PROCESSING},
		FOP: &request.FOP{
			Type: TYPE_CASH,
		},
		TicketMode:           TICKETMODE_CANCEL,
		TicketExpirationDate: ticketExpDate.UnixMilli() + time.Hour.Milliseconds()*7, // TODO: hack send to amadeus service time utc + 7
		Travelers:            travelers,
		Contact:              converts.FromDomainContact(pnrReq.ContactInfo, a.Cfg.BizitripEmail),
	}

	pnrRes, sessionAddMulti, err := a.AmadesusClient.PNRAddMultiElements(ctx, addPRNReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "PNRAddMultiElements")
	}

	paxAdtIndexToIDMap, paxParentIndexToInfIDMap, paxIndexToPaxTypeMap := converts.GetPaxMapFromPNR(pnrRes, pnrReq)
	farePriceReq := &request.FarePricePnrWithBookingClass{
		Session:           converts.FromModelSessionToRequest(sessionAddMulti),
		ValidatingCarrier: booking.Itineraries[0].CarrierMarketing,
	}

	pricingFare, sessionFarePrice, err := a.AmadesusClient.FarePricePnrWithBookingClass(ctx, farePriceReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "FarePricePnrWithBookingClass")
	}

	amadeusISOCountryCodeMap, err := a.AmadesusClient.GetAmadeusISOCountry()
	if err != nil {
		return nil, errors.Wrap(err, "a.AmadesusClient.GetAmadeusISOCountry")
	}

	res, err := converts.FromFarePricePNRWithBookingClassToTotalFareInfo(pricingFare, booking, paxAdtIndexToIDMap, paxParentIndexToInfIDMap, paxIndexToPaxTypeMap, amadeusISOCountryCodeMap)
	if err != nil {
		return nil, errors.Wrap(err, "FromFarePricePNRWithBookingClassToTotalFareInfo")
	}

	if isIssuing && booking.FareDataCf != nil && res.TotalFare != nil && res.TotalFare.BaseTotalFareAmount > booking.FareDataCf.BaseTotalFareAmount {
		go func() {
			bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
			defer cancel()
			_, _ = a.AmadesusClient.SecuritySignOut(bCtx, &request.SignOutRQ{Session: converts.FromModelSessionToRequest(sessionFarePrice)}, tracingID)
		}()

		return nil, domain.ErrTicketFareChanged
	}

	createTSTReq := &request.TicketCreateTSTFromPricing{
		Session:    converts.FromModelSessionToRequest(sessionFarePrice),
		TSTNumbers: res.TSTNumbers,
	}
	_, sessionTST, err := a.AmadesusClient.TicketCreateTSTFromPricing(ctx, createTSTReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "TicketCreateTSTFromPricing")
	}

	tsmValues := []int{}

	if booking.FareData != nil {
		seatSelections := converts.FromDomainSeatSelections(booking.Itineraries)
		if len(seatSelections) > 0 {
			pnrAddSeat := &request.PnrAddMultiElement{
				Session:        converts.FromModelSessionToRequest(sessionTST),
				ActionCodes:    []int{ACTION_NO_PROCESSING},
				SeatSelections: seatSelections,
			}

			_, _, err = a.AmadesusClient.PNRAddMultiElements(ctx, pnrAddSeat, tracingID)
			if err != nil {
				log.Error("PNRAddMultiElements err", log.Any("error", err), log.String("tracingID", tracingID))
				return nil, domain.ErrSeatNotAvailable
			}
		}

		if booking.FareData.TotalSeatAmount > 0 {
			// || booking.FareData.TotalBaggageAmount > 0 {
			_, err = a.AmadesusClient.ServiceIntegratedPricing(ctx, &request.ServiceIntegratedPricingRQ{
				Session: converts.FromModelSessionToRequest(sessionTST),
			}, tracingID)
			if err != nil {
				log.Error("ServiceIntegratedPricing err", log.Any("error", err), log.String("tracingID", tracingID))
				return nil, domain.ErrSeatNotAvailable
			}

			tsmRes, err := a.AmadesusClient.CreateTSMFromPricing(ctx, &request.CreateTSMFromPricingRQ{
				Session: converts.FromModelSessionToRequest(sessionTST),
			}, tracingID)
			if err != nil {
				log.Error("CreateTSMFromPricing err", log.Any("error", err), log.String("tracingID", tracingID))
				return nil, domain.ErrSeatNotAvailable
			}

			for _, tst := range tsmRes.TstList {
				if tst.TsmReference != nil && tst.TsmReference.ReferenceType == "TSM" {
					tsmValues = append(tsmValues, tst.TsmReference.UniqueReference)
				}
			}
		}
	}

	pnrEndReq := &request.PnrAddMultiElement{
		Session:     converts.FromModelSessionToRequest(sessionTST),
		ActionCodes: []int{ACTION_END_TRANSACT},
	}

	pnrEnd, _, err := a.AmadesusClient.PNRAddMultiElements(ctx, pnrEndReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "pnrEnd PNRAddMultiElements")
	}

	if len(pnrEnd.PnrHeader) == 0 || len(pnrEnd.PnrHeader[0].ReservationInfo.Reservation) == 0 || pnrEnd.PnrHeader[0].ReservationInfo.Reservation[0].ControlNumber == nil {
		err := fmt.Errorf("ACTION_END_TRANSACT not return record_locator %v", log.Any("pnrEnd", pnrEnd))
		return nil, errors.Wrap(err, "invalid pnr end")
	}

	recordLocator = *pnrEnd.PnrHeader[0].ReservationInfo.Reservation[0].ControlNumber

	// TODO Open when ready
	// https://www.notion.so/deeptechjsc/HUB-Issue-tr-c-h-n-LTD-nh-ng-failed-ba19b6a1539e4424b367631c7fc5ba78?d=787304c1b434424a837759a6a5186f3e

	pnrRetrieve, session, err := a.AmadesusClient.RetrievePNR(ctx, &request.RetrivePnr{
		RecordLocator: &recordLocator,
	}, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "RetrievePNR")
	}

	go func() {
		bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
		defer cancel()
		_, _ = a.AmadesusClient.SecuritySignOut(bCtx, &request.SignOutRQ{Session: converts.FromModelSessionToRequest(session)}, tracingID)
	}()

	fareListLastTKTDate, opcData, carrierCount, err := converts.FromPNRToLastTicketDate(pnrRetrieve, pricingFare)
	if err != nil {
		return nil, errors.Wrap(err, "FromPNRToLastTicketDate")
	}

	response := &domain.AmadeusCfFareRes{
		FareCf:          res.TotalFare,
		RecordLocator:   recordLocator,
		LastTktDate:     0,
		CommRate:        res.CommRate,
		TSMValues:       tsmValues,
		FareExpiredDate: fareListLastTKTDate,
	}

	if len(opcData) > 0 {
		airPortCodes := make([]string, 0, len(opcData))
		for _, item := range opcData {
			if len(item) == 2 {
				airPortCodes = append(airPortCodes, item[1])
			}
		}

		airportMap, err := a.AirportRepo.FindByCodes(ctx, airPortCodes)
		if err != nil {
			log.Error("[ConfirmFare] s.airportRepo.FindTzByCodes error", log.Any("error", err), log.Any("airportcodes", airPortCodes))
			return response, nil
		}

		opcTKT := []int64{}
		layout := "0201061504" // DDMMYYHHMM
		for _, item := range opcData {
			if len(item) == 2 {
				airport := airportMap[item[1]]
				if airport == nil {
					log.Error("[ConfirmFare] airport not found", log.String("airport", item[1]))
					return response, nil
				}

				loc, err := time.LoadLocation(airport.Timezone)
				if err != nil {
					log.Error("[ConfirmFare] Load location error", log.Any("err", err), log.String("timezone", airport.Timezone))
					return response, nil
				}

				t, err := time.ParseInLocation(layout, item[0], loc)
				if err != nil {
					log.Error("[ConfirmFare] error parsing time", log.Any("err", err), log.String("dateStr", item[0]))
					return response, nil
				}

				opcTKT = append(opcTKT, t.UnixMilli())
			}
		}

		if len(opcTKT) == 0 {
			log.Info("response opc empty", log.Any("OPC", opcData))
			return response, nil
		}

		minOPC := int64(opcTKT[0])
		for _, value := range opcTKT {
			if value != 0 && value < minOPC {
				minOPC = value
			}
		}

		lastTKT := minOPC
		if fareListLastTKTDate > 0 && minOPC > fareListLastTKTDate {
			lastTKT = fareListLastTKTDate
		}

		listCarrier := []string{}

		for _, iti := range booking.Itineraries {
			if iti != nil {
				for _, segment := range iti.Segments {
					if segment != nil {
						if isContain := helpers.Contains(listCarrier, segment.CarrierMarketing); !isContain {
							listCarrier = append(listCarrier, segment.CarrierMarketing)
						}
					}
				}
			}
		}

		if len(listCarrier) == carrierCount {
			return &domain.AmadeusCfFareRes{
				FareCf:            res.TotalFare,
				RecordLocator:     recordLocator,
				LastTktDate:       lastTKT,
				CommRate:          res.CommRate,
				TSMValues:         tsmValues,
				FareExpiredDate:   fareListLastTKTDate,
				TicketExpiredDate: minOPC,
			}, nil
		} else {
			response.TicketExpiredDate = minOPC
			response.EmptySSRData = true
		}
	}

	ssrFreeTexts, err := converts.FromPNRToSSRLastTicketDate(pnrRetrieve)
	if err != nil {
		return nil, errors.Wrap(err, "FromPNRToSSRLastTicketDate")
	}

	if len(ssrFreeTexts) > 0 {
		ssrData := []*converts.SSRResponse{}

		template, err := a.TemplateRepo.FindAll(ctx)
		if err != nil {
			return nil, errors.Wrap(err, "TemplateRepo.FindAll")
		}

		for _, freeText := range ssrFreeTexts {
			parseResult, err := converts.ParseSSRLastTicketDate(freeText, template)
			if err != nil {
				log.Error("[ParseSSRLastTicketDate] Err", log.Any("err", err))
				go func() {
					bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
					defer cancel()
					requestURL := telegram.BuildRequestURL(a.Cfg.TelegramURL, a.Cfg.TelegramSSRNotifyBotToken)
					message := fmt.Sprintf("Invalid SSR template: %s, Booking Info: %s", freeText, buildStringSSRNotify(booking))
					telegram.SendMessage(bCtx, requestURL, a.Cfg.TelegramSSrNotifyChatID, message)
				}()
				continue
			}

			ssrData = append(ssrData, parseResult)
		}

		if len(ssrData) == 0 {
			return response, nil
		}

		airPortCodes := make([]string, 0, len(ssrData))
		for _, item := range ssrData {
			if item.Tz != "" && item.Tz != "GMT" {
				airPortCodes = append(airPortCodes, item.Tz)
			}
		}

		airportMap, err := a.AirportRepo.FindByCodes(ctx, airPortCodes)
		if err != nil {
			log.Error("[ConfirmFare] s.airportRepo.FindTzByCodes error", log.Any("error", err), log.Any("airportcodes", airPortCodes))
			return response, nil
		}

		ssrTKT := []int64{}
		for _, item := range ssrData {
			loc, err := time.LoadLocation("UTC")
			if err != nil {
				log.Error("[ConfirmFare] Load location error", log.Any("err", err))
				return response, nil
			}

			if item.Tz != "" && item.Tz != "GMT" {
				airport := airportMap[item.Tz]
				if airport == nil {
					log.Error("[ConfirmFare] airport not found", log.String("airport", item.Tz))
					return response, nil
				}

				loc, err = time.LoadLocation(airport.Timezone)
				if err != nil {
					log.Error("[ConfirmFare] Load location error", log.Any("err", err), log.String("timezone", airport.Timezone))
					return response, nil
				}
			}

			t, err := time.ParseInLocation(item.Format, item.DateStr, loc)
			if err != nil {
				log.Error("[ConfirmFare] error parsing time", log.Any("err", err), log.String("dateStr", item.DateStr))
				return response, nil
			}

			t = addYearForSSR(t, loc)
			ssrTKT = append(ssrTKT, t.UnixMilli())
		}

		minSSR := int64(ssrTKT[0])
		for _, value := range ssrTKT {
			if value != 0 && value < minSSR {
				minSSR = value
			}
		}

		lastTKT := minSSR
		if fareListLastTKTDate > 0 && minSSR > fareListLastTKTDate {
			lastTKT = fareListLastTKTDate
		}

		if minSSR < response.TicketExpiredDate {
			response.LastTktDate = lastTKT
			response.TicketExpiredDate = minSSR
		} else {
			response.LastTktDate = response.TicketExpiredDate
		}
	} else {
		response.SkipDefaultLastTktDate = true
		response.EmptySSRData = true
	}

	return response, nil
}

func addYearForSSR(t time.Time, loc *time.Location) time.Time {
	if t.Year() == 0 {
		currentTime := time.Now().In(loc)
		currentYear := currentTime.Year()
		t = t.AddDate(currentYear, 0, 0)

		if t.Before(currentTime) {
			t = t.AddDate(currentYear+1, 0, 0)
		}
	}

	return t
}

func (a *amadeusAdapter) GetLastTktInPNR(ctx context.Context, booking *domain.BookingSession, recordLocator string) (int64, error) {
	pnrRetrieve, session, err := a.AmadesusClient.RetrievePNR(ctx, &request.RetrivePnr{
		RecordLocator: &recordLocator,
	}, recordLocator)
	if err != nil {
		return 0, errors.Wrap(err, "RetrievePNR")
	}

	go func() {
		bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
		defer cancel()
		_, _ = a.AmadesusClient.SecuritySignOut(bCtx, &request.SignOutRQ{Session: converts.FromModelSessionToRequest(session)}, recordLocator)
	}()

	_, opcData, carrierCount, err := converts.FromPNRToLastTicketDate(pnrRetrieve, &fare_price_pnr_with_booking_class.FarePricePNRWithBookingClassReply{
		FareList: []*fare_price_pnr_with_booking_class.FareList{}, //  skip check ticketFare
	})
	if err != nil {
		return 0, errors.Wrap(err, "FromPNRToLastTicketDate")
	}

	lastTKT := int64(0)
	if len(opcData) > 0 {
		airPortCodes := make([]string, 0, len(opcData))
		for _, item := range opcData {
			if len(item) == 2 {
				airPortCodes = append(airPortCodes, item[1])
			}
		}

		airportMap, err := a.AirportRepo.FindByCodes(ctx, airPortCodes)
		if err != nil {
			log.Error("[GetLastTktInPNR] s.airportRepo.FindTzByCodes error", log.Any("error", err), log.Any("airportcodes", airPortCodes))
			return 0, nil
		}

		opcTKT := []int64{}
		layout := "0201061504" // DDMMYYHHMM
		for _, item := range opcData {
			if len(item) == 2 {
				airport := airportMap[item[1]]
				if airport == nil {
					log.Error("[GetLastTktInPNR] airport not found", log.String("airport", item[1]))
					continue
				}

				loc, err := time.LoadLocation(airport.Timezone)
				if err != nil {
					log.Error("[GetLastTktInPNR] Load location error", log.Any("err", err), log.String("timezone", airport.Timezone))
					continue
				}

				t, err := time.ParseInLocation(layout, item[0], loc)
				if err != nil {
					log.Error("[GetLastTktInPNR] error parsing time", log.Any("err", err), log.String("dateStr", item[0]))
					continue
				}

				opcTKT = append(opcTKT, t.UnixMilli())
			}
		}

		if len(opcTKT) == 0 {
			log.Info("response opc empty", log.Any("final", lastTKT), log.Any("OPC", opcData))
			return lastTKT, nil
		}

		minOPC := int64(opcTKT[0])
		for _, value := range opcTKT {
			if value != 0 && value < minOPC {
				minOPC = value
			}
		}

		lastTKT = minOPC

		listCarrier := []string{}

		for _, iti := range booking.Itineraries {
			if iti != nil {
				for _, segment := range iti.Segments {
					if segment != nil {
						if isContain := helpers.Contains(listCarrier, segment.CarrierMarketing); !isContain {
							listCarrier = append(listCarrier, segment.CarrierMarketing)
						}
					}
				}
			}
		}

		if len(listCarrier) == carrierCount {
			log.Info("response opc", log.Any("final", lastTKT), log.Any("OPC", opcData))
			return lastTKT, nil
		}
	}

	ssrFreeTexts, err := converts.FromPNRToSSRLastTicketDate(pnrRetrieve)
	if err != nil {
		return lastTKT, errors.Wrap(err, "FromPNRToSSRLastTicketDate")
	}

	if len(ssrFreeTexts) > 0 {
		ssrData := []*converts.SSRResponse{}

		template, err := a.TemplateRepo.FindAll(ctx)
		if err != nil {
			return lastTKT, errors.Wrap(err, "TemplateRepo.FindAll")
		}

		for _, freeText := range ssrFreeTexts {
			parseResult, err := converts.ParseSSRLastTicketDate(freeText, template)
			if err != nil {
				// TODO send to telegram
				log.Error("[ParseSSRLastTicketDate] Err", log.Any("err", err))
				go func() {
					bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
					defer cancel()
					requestURL := telegram.BuildRequestURL(a.Cfg.TelegramURL, a.Cfg.TelegramSSRNotifyBotToken)
					message := fmt.Sprintf("Invalid SSR template: %s, Booking Info: %s", freeText, buildStringSSRNotify(booking))
					telegram.SendMessage(bCtx, requestURL, a.Cfg.TelegramSSrNotifyChatID, message)
				}()
				continue
			}

			ssrData = append(ssrData, parseResult)
		}

		if len(ssrData) == 0 {
			return lastTKT, errors.New("SSR Empty")
		}

		airPortCodes := make([]string, 0, len(ssrData))
		for _, item := range ssrData {
			if item.Tz != "" && item.Tz != "GMT" {
				airPortCodes = append(airPortCodes, item.Tz)
			}
		}

		airportMap, err := a.AirportRepo.FindByCodes(ctx, airPortCodes)
		if err != nil {
			log.Error("[GetLastTktInPNR] s.airportRepo.FindTzByCodes error", log.Any("error", err), log.Any("airportcodes", airPortCodes))
			return lastTKT, err
		}

		ssrTKT := []int64{}

		for _, item := range ssrData {
			loc, err := time.LoadLocation("UTC")
			if err != nil {
				log.Error("[ConfirmFare] Load location error", log.Any("err", err))
				return lastTKT, err
			}

			if item.Tz != "" && item.Tz != "GMT" {
				airport := airportMap[item.Tz]
				if airport == nil {
					log.Error("[ConfirmFare] airport not found", log.String("airport", item.Tz))
					return lastTKT, err
				}

				loc, err = time.LoadLocation(airport.Timezone)
				if err != nil {
					log.Error("[ConfirmFare] Load location error", log.Any("err", err), log.String("timezone", airport.Timezone))
					return lastTKT, err
				}
			}

			t, err := time.ParseInLocation(item.Format, item.DateStr, loc)
			if err != nil {
				log.Error("[ConfirmFare] error parsing time", log.Any("err", err), log.String("dateStr", item.DateStr))
				return lastTKT, err
			}
			t = addYearForSSR(t, loc)
			ssrTKT = append(ssrTKT, t.UnixMilli())
		}

		minSSR := int64(ssrTKT[0])
		for _, value := range ssrTKT {
			if value != 0 && value < minSSR {
				minSSR = value
			}
		}

		if (lastTKT != 0 && lastTKT > minSSR && minSSR != 0) || (minSSR != 0 && lastTKT == 0) {
			return minSSR, nil
		}
	}

	if lastTKT == 0 {
		go func() {
			bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
			defer cancel()
			requestURL := telegram.BuildRequestURL(a.Cfg.TelegramURL, a.Cfg.TelegramSSRNotifyBotToken)
			message := fmt.Sprintf("None last ticket date! Booking Info: %s", buildStringSSRNotify(booking))
			telegram.SendMessage(bCtx, requestURL, a.Cfg.TelegramSSrNotifyChatID, message)
		}()
	}

	log.Info("response ssr", log.Any("final", lastTKT), log.Any("OPC", opcData), log.Any("SSR", ssrFreeTexts))
	return lastTKT, nil
}

func (a *amadeusAdapter) GetBaggage(ctx context.Context, searchCached *domain.SearchFlightsCachedRecord, flightItinerary *domain.FlightItinerary, tracingID string) ([]*domain.BaggageOption, error) {
	serviceStandaloneCatalogueReq := &request.ServiceStandaloneCatalogue{
		Passengers: &request.Passengers{
			Adt: searchCached.Request.Passengers.ADT,
			Chd: searchCached.Request.Passengers.CHD,
			Inf: searchCached.Request.Passengers.INF,
		},
		Segments: converts.FromDomainItinerariesToSegments([]*domain.FlightItinerary{flightItinerary}),
	}

	serviceStandaloneCatalogueRes, err := a.AmadesusClient.ServiceStandaloneCatalogue(ctx, serviceStandaloneCatalogueReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "ServiceStandaloneCatalogue")
	}

	baggageOptions, err := converts.ToDomainBaggageOptions(serviceStandaloneCatalogueRes, flightItinerary.Index)
	if err != nil {
		return nil, err
	}

	return baggageOptions, nil
}

func (a *amadeusAdapter) IssueTicket(ctx context.Context, recordLocator string, commRate *float64, hasINF bool, hasEMD bool, tsmValues []int, booking *domain.BookingSession, pnrReq *domain.PNR, retryTST bool, tracingID string) ([]*models.ReservationInfo, []*models.EticketInfo, []*models.EMDInfo, error, error) {
	retrievePNR, retrivePNRSession, err := a.AmadesusClient.RetrievePNR(ctx, &request.RetrivePnr{
		RecordLocator: &recordLocator,
	}, tracingID)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "RetrievePNR")
	}

	if retryTST {
		if booking == nil {
			log.Error("[IssueTicket] booking nil")
			return nil, nil, nil, nil, commonErrors.ErrInternalServer
		}

		fareBasis := []*request.FareBasis{}
		segmentIdx := 0
		for _, iti := range booking.Itineraries {
			for _, segment := range iti.Segments {
				segmentIdx++
				isExist := false
				for _, item := range fareBasis {
					if item.Code == segment.FareBasis {
						isExist = true
						item.Segments = append(item.Segments, int64(segmentIdx))
						break
					}
				}

				if !isExist {
					fareBasis = append(fareBasis, &request.FareBasis{
						Code:     segment.FareBasis,
						Segments: []int64{int64(segmentIdx)},
					})
				}
			}
		}

		fbaReq := &request.FarePricePnrWithBookingClassFBA{
			Session:           converts.FromModelSessionToRequest(retrivePNRSession),
			ValidatingCarrier: booking.Itineraries[0].CarrierMarketing,
			Passengers: &request.Passengers{
				Adt: booking.PassengerInfo.ADT,
				Chd: booking.PassengerInfo.CHD,
				Inf: booking.PassengerInfo.INF,
			},
			FareBasis: fareBasis,
		}

		pricingFare, sessionFarePrice, err := a.AmadesusClient.FarePricePnrWithBookingClassFBA(ctx, fbaReq, tracingID)
		if err != nil {
			return nil, nil, nil, nil, errors.Wrap(err, "FarePricePnrWithBookingClassFBA")
		}

		paxAdtIndexToIDMap, paxParentIndexToInfIDMap, paxIndexToPaxTypeMap := converts.GetPaxMapFromPNR(retrievePNR, pnrReq)

		amadeusISOCountryCodeMap, err := a.AmadesusClient.GetAmadeusISOCountry()
		if err != nil {
			return nil, nil, nil, nil, errors.Wrap(err, "[FarePricePnrWithBookingClassFBA] a.AmadesusClient.GetAmadeusISOCountry")
		}

		res, err := converts.FromFarePricePNRWithBookingClassToTotalFareInfo(pricingFare, booking, paxAdtIndexToIDMap, paxParentIndexToInfIDMap, paxIndexToPaxTypeMap, amadeusISOCountryCodeMap)
		if err != nil {
			return nil, nil, nil, nil, errors.Wrap(err, "[FarePricePnrWithBookingClassFBA] FromFarePricePNRWithBookingClassToTotalFareInfo")
		}

		//compare ticket price only
		if booking.FareDataCf.BaseTotalFareAmount != res.TotalFare.BaseTotalFareAmount {
			log.Error("[FarePricePnrWithBookingClassFBA] Fare changed", log.Any("Old", booking.FareDataCf.BaseTotalFareAmount), log.Any("New", res.TotalFare.BaseTotalFareAmount))
			return nil, nil, nil, nil, domain.ErrTicketFareChanged
		}

		createTSTReq := &request.TicketCreateTSTFromPricing{
			Session:    converts.FromModelSessionToRequest(sessionFarePrice),
			TSTNumbers: res.TSTNumbers,
		}
		_, sessionTST, err := a.AmadesusClient.TicketCreateTSTFromPricing(ctx, createTSTReq, tracingID)
		if err != nil {
			return nil, nil, nil, nil, errors.Wrap(err, "[FarePricePnrWithBookingClassFBA] TicketCreateTSTFromPricing")
		}

		// override for next step
		retrivePNRSession = sessionTST
	}

	pnrCancelReq := &request.PNRCancel{
		Session:     converts.FromModelSessionToRequest(retrivePNRSession),
		ActionCodes: []int{ACTION_NO_PROCESSING},
		Elements:    []int{getTicketModeElementNumber(retrievePNR.DataElementsMaster)},
	}

	_, pnrCancelSession, err := a.AmadesusClient.PNRCancel(ctx, pnrCancelReq, tracingID)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "PNRCancel")
	}

	session := converts.FromModelSessionToRequest(pnrCancelSession)

	addTKOKReq := &request.PnrAddMultiElement{
		Session:     session,
		ActionCodes: []int{ACTION_NO_PROCESSING},
		TicketMode:  TICKETMODE_OK,
	}

	if commRate != nil {
		comms := []*request.Commission{
			{
				Type:  string(enum.PaxTypeAdult),
				Value: *commRate,
			},
		}

		if hasINF {
			comms = append(comms, &request.Commission{
				Type:  string(enum.PaxTypeInfant),
				Value: 0,
			})
		}

		addTKOKReq.CommissionPercentage = comms
	}

	_, addTKOKSession, err := a.AmadesusClient.PNRAddMultiElements(ctx, addTKOKReq, tracingID)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "PNRAddMultiElements")
	}

	issueTicketReq := &request.IssueTicket{
		Session: converts.FromModelSessionToRequest(addTKOKSession),
		Options: []string{OPTION_ETICKET_RT},
	}

	_, issueSession, err := a.AmadesusClient.IssueTicket(ctx, issueTicketReq, tracingID)
	if err != nil {
		return nil, nil, nil, nil, errors.Wrap(err, "IssueTicket")
	}

	var errEMD error
	if hasEMD && len(tsmValues) > 0 {
		elementAssociations := []*request.ElementAssociation{}

		for _, value := range tsmValues {
			elementAssociations = append(elementAssociations, &request.ElementAssociation{
				Type:  "TSM",
				Value: value,
			})
		}

		_, fopSession, err := a.AmadesusClient.CreateFOP(ctx, &request.CreateFormOfPaymentRQ{
			Session:             converts.FromModelSessionToRequest(issueSession),
			TransactionCode:     "DEF",
			ElementAssociations: elementAssociations,
			MopInfo: &request.MopInfo{
				SeqNbr: 1,
				Code:   "CASH",
			},
		}, tracingID)
		if err != nil {
			log.Error("issueEMD CreateFOP error", log.Any("error", err), log.String("tracingID", tracingID))
			errEMD = domain.ErrIssueEMDFailed
		} else {
			addCommitReq := &request.PnrAddMultiElement{
				Session:     converts.FromModelSessionToRequest(fopSession),
				ActionCodes: []int{converts.ACTION_COMMIT},
			}

			_, addCommitSession, err := a.AmadesusClient.PNRAddMultiElements(ctx, addCommitReq, tracingID)
			if err != nil {
				log.Error("issueEMD addCommt error", log.Any("error", err), log.String("tracingID", tracingID))
				errEMD = domain.ErrIssueEMDFailed
			} else {
				_, err = a.AmadesusClient.DocIssuanceIssueMiscellaneousDocuments(ctx, &request.DocIssuanceIssueMiscellaneousDocumentsRQ{
					Session: converts.FromModelSessionToRequest(addCommitSession),
				}, recordLocator)
				if err != nil {
					log.Error("issueEMD DocIssuanceIssueMiscellaneousDocuments error", log.Any("error", err), log.String("recordLocator", recordLocator))
					errEMD = domain.ErrIssueEMDFailed
				}
			}
		}
	}

	// Wait 3s before retrieve PNR
	time.Sleep(time.Second * 3)

	retrieveRq := &request.RetrivePnr{
		Session:       session,
		RecordLocator: &recordLocator,
	}

	resPNR, sessionRetrieve, err := a.AmadesusClient.RetrievePNR(ctx, retrieveRq, tracingID)
	if err != nil {
		// Exception
		log.Error("RetrievePNR error", log.Any("error", err), log.Any("req", retrieveRq))
		return nil, nil, nil, nil, nil
	}

	go func() {
		bCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
		defer cancel()
		_, _ = a.AmadesusClient.SecuritySignOut(bCtx, &request.SignOutRQ{Session: converts.FromModelSessionToRequest(sessionRetrieve)}, tracingID)
	}()

	reservationInfo, etickets, emdInfo, err := getETicketsInfo(resPNR)
	if err != nil {
		// Exception
		log.Error("getETicketsInfo error", log.Any("error", err), log.Any("resPNR", resPNR))
		return nil, nil, nil, nil, nil
	}

	return reservationInfo, etickets, emdInfo, errEMD, nil
}

func (a *amadeusAdapter) RetrievePNRForTransferredBooking(ctx context.Context, recordLocator string) (*domain.PNRAmadeusTransferred, []*models.ReservationInfo, []*models.EticketInfo, error) {
	pnrReply, _, err := a.AmadesusClient.RetrievePNR(ctx, &request.RetrivePnr{
		RecordLocator: &recordLocator,
	}, recordLocator)
	if err != nil {
		return nil, nil, nil, errors.Wrap(err, "RetrievePNR")
	}

	if pnrReply == nil {
		return nil, nil, nil, fmt.Errorf("pnrReply nil")
	}

	if pnrReply.OriginDestinationDetails == nil {
		return &domain.PNRAmadeusTransferred{
			Status:     enum.BookingStatusCancelled,
			TotalPrice: 0,
			TourCode:   0,
		}, nil, nil, nil
	}

	var (
		bkStatus      enum.BookingStatus
		totalPrice    float64
		tourCode      float64
		hasTicketFlag bool
	)

	if pnrReply.DataElementsMaster != nil && len(pnrReply.DataElementsMaster.DataElementsIndiv) > 0 {
		for _, dataElement := range pnrReply.DataElementsMaster.DataElementsIndiv {
			if dataElement.TicketElement != nil &&
				dataElement.TicketElement.Ticket != nil &&
				dataElement.TicketElement.Ticket.ElectronicTicketFlag != nil {
				hasTicketFlag = true
				if *dataElement.TicketElement.Ticket.ElectronicTicketFlag == E_TICKET_FLAG_SUCCESS {
					bkStatus = enum.BookingStatusTicketed
				} else {
					bkStatus = enum.BookingStatusOK
				}

				break
			}
		}
	}

	if pnrReply.PricingRecordGroup != nil ||
		len(pnrReply.PricingRecordGroup.ProductPricingQuotationRecord) > 0 {
		for _, pricingQuotationRecord := range pnrReply.PricingRecordGroup.ProductPricingQuotationRecord {
			if pricingQuotationRecord.DocumentDetailsGroup != nil &&
				pricingQuotationRecord.DocumentDetailsGroup.TotalFare != nil &&
				pricingQuotationRecord.DocumentDetailsGroup.TotalFare.MonetaryDetails != nil {
				totalPrice, err = strconv.ParseFloat(*pricingQuotationRecord.DocumentDetailsGroup.TotalFare.MonetaryDetails.Amount, 64)
				if err != nil {
					return nil, nil, nil, errors.Wrap(err, "strconv.ParseFloat totalPrice")
				}
			}
		}
	}

	if !hasTicketFlag {
		return &domain.PNRAmadeusTransferred{
			Status:     enum.BookingStatusOK,
			TotalPrice: 0,
			TourCode:   0,
		}, nil, nil, nil
	}

	if bkStatus == enum.BookingStatusTicketed {
		reservationInfo, etickets, _, err := getETicketsInfo(pnrReply)
		if err != nil {
			// Exception
			log.Error("getETicketsInfo error", log.Any("error", err))
			return nil, nil, nil, nil
		}

		return &domain.PNRAmadeusTransferred{
			Status:     bkStatus,
			TotalPrice: totalPrice,
			TourCode:   tourCode,
		}, reservationInfo, etickets, nil
	}

	return &domain.PNRAmadeusTransferred{
		Status:     bkStatus,
		TotalPrice: totalPrice,
		TourCode:   tourCode,
	}, nil, nil, nil
}

// Good luck :)
func getETicketsInfo(resPNR *pnr_repply.PNRReply) ([]*models.ReservationInfo, []*models.EticketInfo, []*models.EMDInfo, error) {
	out := []*models.EticketInfo{}
	emd := []*models.EMDInfo{}

	if len(resPNR.TravellerInfo) == 0 || resPNR.OriginDestinationDetails == nil {
		return nil, nil, nil, domain.ErrInvalidValue
	}

	// Map segment ref number and segment info
	stMap := map[string]*struct {
		StartPoint      string
		ArrivalPoint    string
		StartDate       int64
		ReservationInfo string
	}{}

	// Map pax ref number and pax info
	ptMap := map[string]*struct {
		Surname   string
		GivenName string
		Gender    commonEnum.GenderType
	}{}

	infMap := map[string]*struct {
		Surname   string
		GivenName string
		Gender    commonEnum.GenderType
	}{}

	// Begin pax info loop
	for _, traveller := range resPNR.TravellerInfo {
		if traveller.ElementManagementPassenger == nil || traveller.ElementManagementPassenger.Reference == nil || traveller.ElementManagementPassenger.Reference.Number == nil ||
			traveller.ElementManagementPassenger.Reference.Qualifier != nil && *traveller.ElementManagementPassenger.Reference.Qualifier != "PT" ||
			len(traveller.EnhancedPassengerData) == 0 {
			return nil, nil, nil, nil
		}

		iNum := *traveller.ElementManagementPassenger.Reference.Number
		num := strconv.Itoa(iNum)

		for _, traveler := range traveller.EnhancedPassengerData {
			if traveler.EnhancedTravellerInformation == nil || len(traveler.EnhancedTravellerInformation.OtherPaxNamesDetails) == 0 ||
				resPNR.OriginDestinationDetails == nil {
				return nil, nil, nil, nil
			}

			if traveler.EnhancedTravellerInformation.TravellerNameInfo != nil && traveler.EnhancedTravellerInformation.TravellerNameInfo.Type == "INF" {
				infMap[num] = &struct {
					Surname   string
					GivenName string
					Gender    commonEnum.GenderType
				}{
					Surname:   traveler.EnhancedTravellerInformation.OtherPaxNamesDetails[0].Surname,
					GivenName: traveler.EnhancedTravellerInformation.OtherPaxNamesDetails[0].GivenName,
				}

				continue
			}
			ptMap[num] = &struct {
				Surname   string
				GivenName string
				Gender    commonEnum.GenderType
			}{
				Surname:   traveler.EnhancedTravellerInformation.OtherPaxNamesDetails[0].Surname,
				GivenName: traveler.EnhancedTravellerInformation.OtherPaxNamesDetails[0].GivenName,
			}
		}
	}
	// End pax info loop

	// Begin iti info loop
	for _, itiInfo := range resPNR.OriginDestinationDetails.ItineraryInfo {
		if itiInfo == nil || itiInfo.ElementManagementItinerary == nil || itiInfo.ElementManagementItinerary.Reference == nil ||
			itiInfo.ElementManagementItinerary.Reference.Number == nil || itiInfo.ElementManagementItinerary.Reference.Qualifier == nil ||
			*itiInfo.ElementManagementItinerary.Reference.Qualifier != "ST" || itiInfo.TravelProduct == nil {
			continue
		}

		iNum := *itiInfo.ElementManagementItinerary.Reference.Number
		num := strconv.Itoa(iNum)
		travelProduct := itiInfo.TravelProduct

		startP := travelProduct.BoardpointDetail.CityCode
		endP := travelProduct.OffpointDetail.CityCode

		startD, _, err := getProductDate(travelProduct.Product)
		if err != nil {
			return nil, nil, nil, errors.Wrap(err, "getProductDate")
		}

		if startP == nil || endP == nil || *startP == "" || *endP == "" {
			return nil, nil, nil, nil
		}

		reserCode := ""

		if itiInfo.ItineraryReservationInfo != nil && itiInfo.ItineraryReservationInfo.Reservation != nil && itiInfo.ItineraryReservationInfo.Reservation.ControlNumber != nil {
			reserCode = *itiInfo.ItineraryReservationInfo.Reservation.ControlNumber
		}

		stMap[num] = &struct {
			StartPoint      string
			ArrivalPoint    string
			StartDate       int64
			ReservationInfo string
		}{
			StartPoint:      *startP,
			ArrivalPoint:    *endP,
			StartDate:       startD,
			ReservationInfo: reserCode,
		}
	}
	// End iti info loop

	// Begin data element in div loop
	for _, ele := range resPNR.DataElementsMaster.DataElementsIndiv {

		if ele.ReferenceForDataElement == nil || ele.ElementManagementData == nil || ele.ElementManagementData.SegmentName == nil ||
			(*ele.ElementManagementData.SegmentName != "FA" && *ele.ElementManagementData.SegmentName != "SSR") {
			continue
		}

		// Handle element SSR DOCS
		if *ele.ElementManagementData.SegmentName == "SSR" && ele.ServiceRequest != nil && ele.ServiceRequest.Ssr != nil &&
			len(ele.ServiceRequest.Ssr.FreeText) > 0 && ele.ServiceRequest.Ssr.FreeText[0] != nil && ele.ServiceRequest.Ssr.Type != nil &&
			*ele.ServiceRequest.Ssr.Type == "DOCS" {

			passportText := *ele.ServiceRequest.Ssr.FreeText[0]
			tempArr := strings.Split(passportText, "/")

			if len(tempArr) < 6 {
				continue
			}

			rawGender := tempArr[5]
			gender := commonEnum.GenderTypeNone

			if rawGender == "M" {
				gender = commonEnum.GenderTypeMale
			} else {
				gender = commonEnum.GenderTypeFeMale
			}

			for _, ref := range ele.ReferenceForDataElement.Reference {
				if ref.Number != nil && ref.Qualifier != nil && *ref.Qualifier == "PT" {
					num := *ref.Number

					if ptMap[num] != nil {
						ptMap[num].Gender = gender
					}
				}
			}
			// Always exit on DOCS element
			continue
		}

		if len(ele.OtherDataFreetext) == 0 || ele.OtherDataFreetext[0].LongFreetext == nil {
			continue
		}

		// Handle data element FB
		freeText := *ele.OtherDataFreetext[0].LongFreetext

		parts := strings.Split(freeText, " ")

		if len(parts) < 2 {
			return nil, nil, nil, errors.New("len parts < 2")
		}

		ticketNumber := strings.Split(parts[1], "/")
		if len(ticketNumber) < 2 {
			return nil, nil, nil, errors.New("ticketNumber empty")
		}

		infPrefix := parts[0]

		sts := []string{}
		pts := []string{}
		ots := []string{}

		for _, ref := range ele.ReferenceForDataElement.Reference {
			if ref.Qualifier == nil || ref.Number == nil {
				continue
			}

			num := *ref.Number
			if *ref.Qualifier == "ST" {
				sts = append(sts, num)
			}

			if *ref.Qualifier == "PT" {
				pts = append(pts, num)
			}

			if *ref.Qualifier == "OT" {
				ots = append(ots, num)
			}
		}

		if ticketNumber[1][:2] == "ET" {
			for _, pt := range pts {
				pti := ptMap[pt]
				if infPrefix == "INF" {
					pti = infMap[pt]
				}

				if pti == nil {
					return nil, nil, nil, errors.New("pti nil")
				}

				for _, st := range sts {
					sti := stMap[st]

					if sti == nil {
						return nil, nil, nil, errors.New("sti nil")
					}

					temp := &models.EticketInfo{
						Number:       ticketNumber[0],
						Surname:      pti.Surname,
						GivenName:    pti.GivenName,
						Gender:       pti.Gender,
						StartPoint:   sti.StartPoint,
						ArrivalPoint: sti.ArrivalPoint,
						StartDate:    sti.StartDate,
					}

					out = append(out, temp)
				}
			}
		}

		if ticketNumber[1][:2] == "DT" {
			for _, ot := range ots {
				var otDetail *pnr_repply.DataElementsIndiv
				for _, ele := range resPNR.DataElementsMaster.DataElementsIndiv {
					if ele.ElementManagementData != nil &&
						ele.ElementManagementData.Reference != nil &&
						ele.ElementManagementData.Reference.Number != nil &&
						fmt.Sprintf("%d", *ele.ElementManagementData.Reference.Number) == ot {
						otDetail = ele
						break
					}
				}

				if otDetail == nil || otDetail.ServiceRequest == nil || otDetail.ServiceRequest.Ssr == nil {
					break
				}

				if len(otDetail.ServiceRequest.Ssrb) == 0 ||
					otDetail.ServiceRequest.Ssrb[0] == nil ||
					otDetail.ServiceRequest.Ssrb[0].Data == nil ||
					*otDetail.ServiceRequest.Ssrb[0].Data == "" {
					break
				}

				emd = append(emd, &models.EMDInfo{
					EMDNumber:    ticketNumber[0],
					StartPoint:   *otDetail.ServiceRequest.Ssr.Boardpoint,
					ArrivalPoint: *otDetail.ServiceRequest.Ssr.Offpoint,
					SeatCode:     *otDetail.ServiceRequest.Ssrb[0].Data,
				})
			}
		}
	}

	// End element in div loop

	reserInfos := []*models.ReservationInfo{}

	for _, sti := range stMap {
		reserInfos = append(reserInfos, &models.ReservationInfo{
			StartPoint:      sti.StartPoint,
			ArrivalPoint:    sti.ArrivalPoint,
			StartDate:       sti.StartDate,
			ReservationCode: sti.ReservationInfo,
		})
	}

	return reserInfos, out, emd, nil
}

func getProductDate(in *pnr_repply.ProductDateTimeTypeI270055C) (int64, int64, error) {
	const dateFmt = "020106 1504"

	if in == nil || in.ArrDate == nil || in.ArrTime == nil || in.DepDate == nil || in.DepTime == nil {
		return 0, 0, nil
	}

	rawStartDate := *in.DepDate + " " + *in.DepTime
	rawArrDate := *in.ArrDate + " " + *in.ArrTime

	startDate, err := time.Parse(dateFmt, rawStartDate)
	if err != nil {
		return 0, 0, errors.Wrap(err, "parse start date")
	}

	arrDate, err := time.Parse(dateFmt, rawArrDate)
	if err != nil {
		return 0, 0, errors.Wrap(err, "parse arrival date")
	}

	return startDate.UnixMilli(), arrDate.UnixMilli(), nil
}

func getTicketModeElementNumber(dataElement *pnr_repply.DataElementsMaster) int {
	var ticketModeNumber int

	for _, element := range dataElement.DataElementsIndiv {
		if *element.ElementManagementData.SegmentName == "TK" {
			ticketModeNumber = *element.ElementManagementData.Reference.Number
			break
		}
	}

	return ticketModeNumber
}
