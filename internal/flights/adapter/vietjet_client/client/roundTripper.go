package client

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type (
	ContextKeyAction  struct{}
	ContextKeyTracing struct{}
)

type roundTripper struct {
	proxied     http.RoundTripper
	requestRepo repositories.RequestRepository
	provider    enum.FlightProvider
}

func (rt *roundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	var duration int64
	beginAt := time.Now()
	var reqBody []byte

	if req.Body != nil {
		var err error
		reqBody, err = io.ReadAll(req.Body)
		if err != nil {
			return nil, err
		}
		req.Body = io.NopCloser(bytes.NewBuffer(reqBody))
	}

	response, err := rt.proxied.RoundTrip(req)
	duration = time.Since(beginAt).Milliseconds()
	if err != nil {
		return nil, err
	}
	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	headerClone := getHeaders(req)

	go func(headerClone map[string]string) {
		path := req.URL.String()
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)
		defer cancel()
		requestID := uuid.NewString()

		if rt.requestRepo == nil {
			return
		}
		var action, tracingID string
		action, _ = req.Context().Value(ContextKeyAction{}).(string)
		tracingID, _ = req.Context().Value(ContextKeyTracing{}).(string)
		doErr := err

		if err := rt.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     req.Method,
			Body:       string(reqBody),
			Headers:    headerClone,
			Response:   resBody,
			StatusCode: response.StatusCode,
			Duration:   duration,
			Action:     action,
			TracingID:  tracingID,
			Provider:   rt.provider,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("vietjet roundTripper requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.Any("req", req.Body),
			)
		}
	}(headerClone)

	response.Body = io.NopCloser(bytes.NewBuffer(resBody))
	return response, nil
}

func getHeaders(req *http.Request) map[string]string {
	headers := make(map[string]string)
	for name, values := range req.Header {
		headers[name] = values[0]
	}
	return headers
}
