// Code generated by go-swagger; DO NOT EDIT.

package reservations

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"context"
	"net/http"
	"time"

	"github.com/go-openapi/errors"
	"github.com/go-openapi/runtime"
	cr "github.com/go-openapi/runtime/client"
	"github.com/go-openapi/strfmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client/models"
)

// NewAPIReservationsByKeyPaymentTransactionsPostParams creates a new APIReservationsByKeyPaymentTransactionsPostParams object,
// with the default timeout for this client.
//
// Default values are not hydrated, since defaults are normally applied by the API server side.
//
// To enforce default values in parameter, use SetDefaults or WithDefaults.
func NewAPIReservationsByKeyPaymentTransactionsPostParams() *APIReservationsByKeyPaymentTransactionsPostParams {
	return &APIReservationsByKeyPaymentTransactionsPostParams{
		timeout: cr.DefaultTimeout,
	}
}

// NewAPIReservationsByKeyPaymentTransactionsPostParamsWithTimeout creates a new APIReservationsByKeyPaymentTransactionsPostParams object
// with the ability to set a timeout on a request.
func NewAPIReservationsByKeyPaymentTransactionsPostParamsWithTimeout(timeout time.Duration) *APIReservationsByKeyPaymentTransactionsPostParams {
	return &APIReservationsByKeyPaymentTransactionsPostParams{
		timeout: timeout,
	}
}

// NewAPIReservationsByKeyPaymentTransactionsPostParamsWithContext creates a new APIReservationsByKeyPaymentTransactionsPostParams object
// with the ability to set a context for a request.
func NewAPIReservationsByKeyPaymentTransactionsPostParamsWithContext(ctx context.Context) *APIReservationsByKeyPaymentTransactionsPostParams {
	return &APIReservationsByKeyPaymentTransactionsPostParams{
		Context: ctx,
	}
}

// NewAPIReservationsByKeyPaymentTransactionsPostParamsWithHTTPClient creates a new APIReservationsByKeyPaymentTransactionsPostParams object
// with the ability to set a custom HTTPClient for a request.
func NewAPIReservationsByKeyPaymentTransactionsPostParamsWithHTTPClient(client *http.Client) *APIReservationsByKeyPaymentTransactionsPostParams {
	return &APIReservationsByKeyPaymentTransactionsPostParams{
		HTTPClient: client,
	}
}

/*
APIReservationsByKeyPaymentTransactionsPostParams contains all the parameters to send to the API endpoint

	for the Api reservations by key payment transactions post operation.

	Typically these are written to a http.Request.
*/
type APIReservationsByKeyPaymentTransactionsPostParams struct {

	/* Key.

	   The access key for a reservation.
	*/
	Key string

	// Model.
	Model *models.ReservationPaymentTransactionModel

	timeout    time.Duration
	Context    context.Context
	HTTPClient *http.Client
}

// WithDefaults hydrates default values in the Api reservations by key payment transactions post params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *APIReservationsByKeyPaymentTransactionsPostParams) WithDefaults() *APIReservationsByKeyPaymentTransactionsPostParams {
	o.SetDefaults()
	return o
}

// SetDefaults hydrates default values in the Api reservations by key payment transactions post params (not the query body).
//
// All values with no default are reset to their zero value.
func (o *APIReservationsByKeyPaymentTransactionsPostParams) SetDefaults() {
	// no default values defined for this parameter
}

// WithTimeout adds the timeout to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) WithTimeout(timeout time.Duration) *APIReservationsByKeyPaymentTransactionsPostParams {
	o.SetTimeout(timeout)
	return o
}

// SetTimeout adds the timeout to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) SetTimeout(timeout time.Duration) {
	o.timeout = timeout
}

// WithContext adds the context to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) WithContext(ctx context.Context) *APIReservationsByKeyPaymentTransactionsPostParams {
	o.SetContext(ctx)
	return o
}

// SetContext adds the context to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) SetContext(ctx context.Context) {
	o.Context = ctx
}

// WithHTTPClient adds the HTTPClient to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) WithHTTPClient(client *http.Client) *APIReservationsByKeyPaymentTransactionsPostParams {
	o.SetHTTPClient(client)
	return o
}

// SetHTTPClient adds the HTTPClient to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) SetHTTPClient(client *http.Client) {
	o.HTTPClient = client
}

// WithKey adds the key to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) WithKey(key string) *APIReservationsByKeyPaymentTransactionsPostParams {
	o.SetKey(key)
	return o
}

// SetKey adds the key to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) SetKey(key string) {
	o.Key = key
}

// WithModel adds the model to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) WithModel(model *models.ReservationPaymentTransactionModel) *APIReservationsByKeyPaymentTransactionsPostParams {
	o.SetModel(model)
	return o
}

// SetModel adds the model to the Api reservations by key payment transactions post params
func (o *APIReservationsByKeyPaymentTransactionsPostParams) SetModel(model *models.ReservationPaymentTransactionModel) {
	o.Model = model
}

// WriteToRequest writes these params to a swagger request
func (o *APIReservationsByKeyPaymentTransactionsPostParams) WriteToRequest(r runtime.ClientRequest, reg strfmt.Registry) error {

	if err := r.SetTimeout(o.timeout); err != nil {
		return err
	}
	var res []error

	// path param key
	if err := r.SetPathParam("key", o.Key); err != nil {
		return err
	}
	if o.Model != nil {
		if err := r.SetBodyParam(o.Model); err != nil {
			return err
		}
	}

	if len(res) > 0 {
		return errors.CompositeValidationError(res...)
	}
	return nil
}
