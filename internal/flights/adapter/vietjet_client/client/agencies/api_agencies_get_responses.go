// Code generated by go-swagger; DO NOT EDIT.

package agencies

// This file was generated by the swagger tool.
// Editing this file might prove futile when you re-run the swagger generate command

import (
	"fmt"
	"io"

	"github.com/go-openapi/runtime"
	"github.com/go-openapi/strfmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client/models"
)

// APIAgenciesGetReader is a Reader for the APIAgenciesGet structure.
type APIAgenciesGetReader struct {
	formats strfmt.Registry
}

// ReadResponse reads a server response into the received o.
func (o *APIAgenciesGetReader) ReadResponse(response runtime.ClientResponse, consumer runtime.Consumer) (interface{}, error) {
	switch response.Code() {
	case 200, 201:
		result := NewAPIAgenciesGetOK()
		if err := result.readResponse(response, consumer, o.formats); err != nil {
			return nil, err
		}
		return result, nil
	default:
		return nil, runtime.NewAPIError("[GET /api/Agencies] ApiAgenciesGet", response, response.Code())
	}
}

// NewAPIAgenciesGetOK creates a APIAgenciesGetOK with default headers values
func NewAPIAgenciesGetOK() *APIAgenciesGetOK {
	return &APIAgenciesGetOK{}
}

/*
APIAgenciesGetOK describes a response with status code 200, with default header values.

Success
*/
type APIAgenciesGetOK struct {
	Payload []*models.AgencyModel
}

// IsSuccess returns true when this api agencies get o k response has a 2xx status code
func (o *APIAgenciesGetOK) IsSuccess() bool {
	return true
}

// IsRedirect returns true when this api agencies get o k response has a 3xx status code
func (o *APIAgenciesGetOK) IsRedirect() bool {
	return false
}

// IsClientError returns true when this api agencies get o k response has a 4xx status code
func (o *APIAgenciesGetOK) IsClientError() bool {
	return false
}

// IsServerError returns true when this api agencies get o k response has a 5xx status code
func (o *APIAgenciesGetOK) IsServerError() bool {
	return false
}

// IsCode returns true when this api agencies get o k response a status code equal to that given
func (o *APIAgenciesGetOK) IsCode(code int) bool {
	return code == 200
}

// Code gets the status code for the api agencies get o k response
func (o *APIAgenciesGetOK) Code() int {
	return 200
}

func (o *APIAgenciesGetOK) Error() string {
	return fmt.Sprintf("[GET /api/Agencies][%d] apiAgenciesGetOK  %+v", 200, o.Payload)
}

func (o *APIAgenciesGetOK) String() string {
	return fmt.Sprintf("[GET /api/Agencies][%d] apiAgenciesGetOK  %+v", 200, o.Payload)
}

func (o *APIAgenciesGetOK) GetPayload() []*models.AgencyModel {
	return o.Payload
}

func (o *APIAgenciesGetOK) readResponse(response runtime.ClientResponse, consumer runtime.Consumer, formats strfmt.Registry) error {

	// response payload
	if err := consumer.Consume(response.Body(), &o.Payload); err != nil && err != io.EOF {
		return err
	}

	return nil
}
