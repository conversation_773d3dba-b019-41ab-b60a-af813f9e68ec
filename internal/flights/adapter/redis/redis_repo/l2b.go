package redisrepo

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

const (
	l2bTrackingPrefix = "l2b"
)

type L2bRepository interface {
	Track(ctx context.Context, officeID string, target enum.L2bAPI, ts int64) error
	RangeAll(ctx context.Context, target enum.L2bAPI) ([]*domain.L2bTrackingItem, int, error)
	RemRange(ctx context.Context, length int64, target enum.L2bAPI) error
}

type l2bRepository struct {
	db redis.IRedis
}

func NewL2bRepository(db redis.IRedis) L2bRepository {
	return &l2bRepository{db}
}

func (r *l2bRepository) Track(ctx context.Context, officeID string, target enum.L2bAPI, ts int64) error {
	cmdRs := r.db.CMD().LPush(ctx, r.genKey(target), r.genVal(officeID, ts))
	return cmdRs.Err()
}

func (r *l2bRepository) genVal(officeID string, ts int64) string {
	return fmt.Sprintf("%s:%d", officeID, ts)
}

func (r *l2bRepository) genKey(target enum.L2bAPI) string {
	return fmt.Sprintf("%s:%s", l2bTrackingPrefix, enum.L2bAPIName[target])
}

func (r *l2bRepository) RemRange(ctx context.Context, length int64, target enum.L2bAPI) error {
	key := r.genKey(target)

	cmdRs := r.db.CMD().LTrim(ctx, key, int64(length), -1)
	return cmdRs.Err()
}

func (r *l2bRepository) RangeAll(ctx context.Context, target enum.L2bAPI) ([]*domain.L2bTrackingItem, int, error) {
	out := []*domain.L2bTrackingItem{}
	key := r.genKey(target)

	cmdRs := r.db.CMD().LRange(ctx, key, 0, -1)
	if cmdRs.Err() != nil {
		return nil, 0, cmdRs.Err()
	}

	for _, item := range cmdRs.Val() {
		strArr := strings.Split(item, ":")

		if len(strArr) != 2 {
			continue
		}

		officeID := strArr[0]
		strTs := strArr[1]

		ts, err := strconv.ParseInt(strTs, 10, 0)
		if err != nil {
			return nil, 0, errors.Wrap(err, "ParseInt")
		}

		tst := time.UnixMilli(ts).In(time.FixedZone("UTC+7", 7*60*60))

		out = append(out, &domain.L2bTrackingItem{
			RequestedAt: ts,
			OfficeID:    officeID,
			Month:       int(tst.Month()),
			Year:        tst.Year(),
			API:         target,
		})
	}

	return out, len(cmdRs.Val()), nil
}
