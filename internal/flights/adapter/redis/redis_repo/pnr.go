package redisrepo

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/redis"
)

const (
	PNRUpdateAcquireLockPrefixKey = "pnr_update_acquire_lock_%s"
	AcquireLockTTL                = 3 * time.Second
)

var ErrLockAlreadyHeld = errors.New("LOCK_ALREADY_HELD")

type PNRRepository interface {
	AcquireLock(id string) error
	ReleaseLock(id string) error
}

type pnrRepository struct {
	db redis.IRedis
}

func NewPNRRedisRepository(db redis.IRedis) PNRRepository {
	return &pnrRepository{
		db: db,
	}
}

func (r *pnrRepository) AcquireLock(id string) error {
	locked, err := r.db.AcquireLock(generatePNRLockUpdateKey(id), id, AcquireLockTTL)
	if err != nil {
		return errors.Wrap(err, "r.db.AcquireLock")
	}

	if !locked {
		return ErrLockAlreadyHeld
	}

	return nil
}

func (r *pnrRepository) ReleaseLock(id string) error {
	key := generatePNRLockUpdateKey(id)

	if err := r.db.Remove(key); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func generatePNRLockUpdateKey(id string) string {
	return fmt.Sprintf(PNRUpdateAcquireLockPrefixKey, id)
}
