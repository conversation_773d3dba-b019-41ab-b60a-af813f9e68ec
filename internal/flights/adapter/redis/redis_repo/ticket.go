package redisrepo

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/redis"
)

const (
	issueTicketLockPrefix = "lock_issue_ticket_%s"
	issueTicketLockTTL    = time.Minute
)

type TicketRepository interface {
	AcquireIssueTicketLock(bookingID string) error
	ReleaseIssueTicketLock(bookingID string) error
}

type ticketRepository struct {
	db redis.IRedis
}

func NewTicketRedisRepository(db redis.IRedis) TicketRepository {
	return &ticketRepository{
		db: db,
	}
}

func (r *ticketRepository) AcquireIssueTicketLock(id string) error {
	locked, err := r.db.AcquireLock(r.generateIssueTicketLockUpdateKey(id), "", issueTicketLockTTL)
	if err != nil {
		return errors.Wrap(err, "r.db.AcquireLock")
	}

	if !locked {
		return ErrLockAlreadyHeld
	}

	return nil
}

func (r *ticketRepository) ReleaseIssueTicketLock(bookingID string) error {
	key := r.generateIssueTicketLockUpdateKey(bookingID)

	if err := r.db.Remove(key); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *ticketRepository) generateIssueTicketLockUpdateKey(bookingID string) string {
	return fmt.Sprintf(issueTicketLockPrefix, bookingID)
}
