package redisrepo

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	goRedis "github.com/redis/go-redis/v9"
	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

const (
	getTokenLockPrefix = "lock_get_token_%s_%s"
	getTokenPrefix     = "get_token_%s_%s"

	getTokenLockTTL = 5 * time.Second
)

type TokenRepository interface {
	GetToken(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix string) (string, error)
	SetToken(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix, token string, ttlDuration time.Duration) error
	LockGetToken(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix string) (bool, error)
	ReleaseGetTokeLock(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix string) error
	WaitForLockToRelease(flightProvider enum.FlightProvider, accountPrefix string, sleeptime time.Duration, attemps int) error
}

type tokenRepository struct {
	db commonRedis.IRedis
}

func NewTokenRedisRepository(db commonRedis.IRedis) TokenRepository {
	return &tokenRepository{
		db: db,
	}
}

func (r *tokenRepository) GetToken(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix string) (string, error) {
	key := r.generateGetTokenKey(flightProvider, accountPrefix)

	rdVal := r.db.CMD().Get(ctx, key)
	err := rdVal.Err()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return "", nil
		}

		return "", errors.Wrap(err, "CMD().Get")
	}

	return rdVal.Val(), nil
}

func (r *tokenRepository) SetToken(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix, token string, ttlDuration time.Duration) error {
	key := r.generateGetTokenKey(flightProvider, accountPrefix)

	err := r.db.CMD().Set(ctx, key, token, ttlDuration).Err()
	if err != nil {
		return errors.Wrap(err, "r.db.CMD().Set")
	}

	return nil
}

func (r *tokenRepository) LockGetToken(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix string) (bool, error) {
	key := r.generateGetTokenLockKey(flightProvider, accountPrefix)

	locked, err := r.db.CMD().SetNX(ctx, key, accountPrefix, getTokenLockTTL).Result()
	if err != nil {
		return false, errors.Wrap(err, "r.db.SetNX")
	}

	return locked, nil
}

func (r *tokenRepository) ReleaseGetTokeLock(ctx context.Context, flightProvider enum.FlightProvider, accountPrefix string) error {
	key := r.generateGetTokenLockKey(flightProvider, accountPrefix)

	if err := r.db.CMD().Del(ctx, key); err != nil {
		return errors.Wrap(err.Err(), " r.db.Remove(key)")
	}

	return nil
}

func (r *tokenRepository) WaitForLockToRelease(flightProvider enum.FlightProvider, accountPrefix string, sleeptime time.Duration, attemps int) error {
	key := r.generateGetTokenLockKey(flightProvider, accountPrefix)

	for i := 0; i < attemps; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)

		val := r.db.CMD().Get(ctx, key)
		err := val.Err()
		cancel()
		if err != nil {
			if errors.Is(err, goRedis.Nil) {
				return nil
			}

			return err
		}

		time.Sleep(sleeptime)
	}

	return fmt.Errorf("Lock for key %s not released after %d attempts", key, attemps)
}

func (r *tokenRepository) generateGetTokenLockKey(flightProvider enum.FlightProvider, accountPrefix string) string {
	return fmt.Sprintf(getTokenLockPrefix, enum.FlightProviderName[flightProvider], accountPrefix)
}

func (r *tokenRepository) generateGetTokenKey(flightProvider enum.FlightProvider, accountPrefix string) string {
	return fmt.Sprintf(getTokenPrefix, enum.FlightProviderName[flightProvider], accountPrefix)
}
