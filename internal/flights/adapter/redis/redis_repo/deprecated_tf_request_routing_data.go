//* CONSIDER TO REMOVE *//

package redisrepo

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type TFRoutingRepositories interface {
	// ReleaseRoutingIDLock(routingID string) error
	AccquireRoutingIDLock(routingID string) (bool, error)
	IsLocking(ctx context.Context, routingID string) (bool, error)
	SetCacheFlightDetailsForBooking(ctx context.Context, tfRequestRoutingData *domain.TFRequestRoutingData) error
	GetCacheFlightDetailsForBooking(ctx context.Context, routingID string) (*domain.TFRequestRoutingData, error)
	DelCacheFlightDetailsForBooking(ctx context.Context, routingID string) error
}

type tfRoutingRepositories struct {
	redisClient redis.IRedis
}

const (
	TFRoutingLockKey = "lock_tf_routing_id_%s"
	TFRoutingLockTTL = time.Minute * 3

	CacheFlightDetailsForBookingKey = "flight_details_for_booking_with_routing_id_%s"
)

func NewTFRoutingRepositories(redisClient redis.IRedis) TFRoutingRepositories {
	return &tfRoutingRepositories{redisClient}
}

func (r *tfRoutingRepositories) IsLocking(ctx context.Context, routingID string) (bool, error) {
	key := r.genKey(routingID)

	val := r.redisClient.CMD().Get(ctx, key)
	err := val.Err()
	if err != nil {
		if errors.Is(err, goRedis.Nil) {
			return false, nil
		}

		return false, err
	}

	return true, nil
}

func (*tfRoutingRepositories) genKey(key string) string {
	return fmt.Sprintf(TFRoutingLockKey, key)
}

func (r *tfRoutingRepositories) AccquireRoutingIDLock(routingID string) (bool, error) {
	if routingID == "" {
		return false, nil
	}

	key := r.genKey(routingID)

	lock, err := r.redisClient.AcquireLock(key, "", TFRoutingLockTTL)
	if err != nil {
		return false, errors.Wrap(err, "AcquireLock")
	}

	return lock, nil
}

func (r *tfRoutingRepositories) SetCacheFlightDetailsForBooking(ctx context.Context, tfRequestRoutingData *domain.TFRequestRoutingData) error {
	if tfRequestRoutingData == nil {
		return fmt.Errorf("tfRequestRoutingData nil")
	}

	key := r.genCacheFlightDetailsForBookingKey(tfRequestRoutingData.RoutingID)
	byteDate, err := json.Marshal(tfRequestRoutingData)
	if err != nil {
		return errors.Wrap(err, "json.Marshal(tfRequestRoutingData)")
	}
	_, err = r.redisClient.CMD().SetEx(ctx, key, byteDate, constants.SessionExpireTime).Result()
	if err != nil {
		return errors.Wrap(err, "CMD().SetEx")
	}

	return nil
}

func (r *tfRoutingRepositories) GetCacheFlightDetailsForBooking(ctx context.Context, routingID string) (*domain.TFRequestRoutingData, error) {
	key := r.genCacheFlightDetailsForBookingKey(routingID)

	byteValue, err := r.redisClient.CMD().Get(ctx, key).Bytes()
	if err != nil {
		if errors.Is(err, goRedis.Nil) {
			return nil, nil
		}
		return nil, errors.Wrap(err, "CMD().Get")
	}
	var res *domain.TFRequestRoutingData
	err = json.Unmarshal(byteValue, &res)
	if err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal(byteValue, res)")
	}

	return res, nil
}

func (r *tfRoutingRepositories) DelCacheFlightDetailsForBooking(ctx context.Context, routingID string) error {
	key := r.genCacheFlightDetailsForBookingKey(routingID)

	err := r.redisClient.CMD().Del(ctx, key).Err()
	if err != nil {
		if errors.Is(err, goRedis.Nil) {
			return nil
		}
		return errors.Wrap(err, "CMD().Del")
	}

	return nil
}

func (*tfRoutingRepositories) genCacheFlightDetailsForBookingKey(routingID string) string {
	return fmt.Sprintf(CacheFlightDetailsForBookingKey, routingID)
}

// func (r *tfRoutingRepositories) ReleaseRoutingIDLock(routingID string) error {
// 	fullKey := r.genKey(routingID)

// 	if err := r.redisClient.Remove(fullKey); err != nil {
// 		return errors.Wrap(err, " r.db.Remove(key)")
// 	}

// 	return nil
// }
