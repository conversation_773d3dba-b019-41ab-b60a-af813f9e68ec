package redisrepo

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/log"
)

const (
	ancillaryLockKey = "lock_ancillary_%s_%s"
	ancillaryLockTTL = time.Second * 60
)

type AncillaryRepository interface {
	WaitForLockToRelease(key string, sleep time.Duration, attempts int) error
	AcquireLock(key string) (bool, error)
	ReleaseLock(hash string) error
}

type ancillaryRepository struct {
	redis redis.IRedis
}

func NewAncillaryRepository(redis redis.IRedis) AncillaryRepository {
	return &ancillaryRepository{redis}
}

func (*ancillaryRepository) genKey(key string) string {
	return fmt.Sprintf(ancillaryLockKey, key)
}

func (r *ancillaryRepository) ReleaseLock(hash string) error {
	fullKey := r.genKey(hash)

	if err := r.redis.Remove(fullKey); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *ancillaryRepository) AcquireLock(hash string) (bool, error) {
	key := r.genKey(hash)

	lock, err := r.redis.AcquireLock(key, "", ancillaryLockTTL)
	if err != nil {
		return false, errors.Wrap(err, "AcquireLock")
	}

	return lock, nil
}

func (r *ancillaryRepository) WaitForLockToRelease(hash string, sleep time.Duration, attempts int) error {
	key := r.genKey(hash)

	for i := 0; i < attempts; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		val := r.redis.CMD().Get(ctx, key)
		err := val.Err()

		if err != nil {
			if errors.Is(err, goRedis.Nil) {
				return nil
			}

			log.Error("WaitForLockToRelease r.redis.CMD.Get error", log.Any("error", err), log.Int("attemps", i), log.String("key", key))
		}

		time.Sleep(sleep)
	}

	return nil
}
