package redisrepo

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

const (
	suppliersRoutePrefix = "suppliers_by_route_%s"
)

type SupplierRepository interface {
	SetSuppliersByRoute(ctx context.Context, route string, suppliers map[enum.FlightProvider][]string) error
	GetSuppliersByRoute(ctx context.Context, route string) (map[enum.FlightProvider][]string, error)
}

type supplierRepository struct {
	cfg *config.Schema
	db  commonRedis.IRedis
}

func NewSupplierRepository(cfg *config.Schema, db commonRedis.IRedis) SupplierRepository {
	return &supplierRepository{
		cfg: cfg,
		db:  db,
	}
}

func (r *supplierRepository) SetSuppliersByRoute(ctx context.Context, route string, suppliers map[enum.FlightProvider][]string) error {
	dataByte, err := json.Marshal(suppliers)
	if err != nil {
		return errors.Wrap(err, "json.Marshal")
	}
	key := r.generateKeyByRoute(route)

	//  ttl from now to 1h
	loc, err := time.LoadLocation(r.cfg.TravelFusionTimeZone)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("time.LoadLocation %s", r.cfg.TravelFusionTimeZone))
	}
	now := time.Now().In(loc)
	midnight := now.Truncate(24 * time.Hour).Add(25 * time.Hour)

	err = r.db.CMD().Set(ctx, key, dataByte, midnight.Sub(now)).Err()
	if err != nil {
		return errors.Wrap(err, "r.db.CMD().Set")
	}

	return nil
}

func (r *supplierRepository) GetSuppliersByRoute(ctx context.Context, route string) (map[enum.FlightProvider][]string, error) {
	key := r.generateKeyByRoute(route)

	data, err := r.db.CMD().Get(ctx, key).Bytes()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}

		return nil, errors.Wrap(err, "r.db.CMD().Get")
	}

	var suppliers map[enum.FlightProvider][]string
	err = json.Unmarshal(data, &suppliers)
	if err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	return suppliers, nil
}

func (r *supplierRepository) generateKeyByRoute(route string) string {
	return fmt.Sprintf(suppliersRoutePrefix, route)
}
