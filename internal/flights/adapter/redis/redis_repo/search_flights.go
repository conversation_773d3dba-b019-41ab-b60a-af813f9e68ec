package redisrepo

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

const (
	searchFlightsLockKey   = "lock_search_flights_request_%s"
	cachingLockKey         = "lock_search_flights_result_caching_%s"
	fareCheckClientLockKey = "lock_fare_check_client_%s"
	lockTTL                = time.Second * 70
)

type SearchFlightsRepository interface {
	WaitForLockToRelease(key string, sleeptime time.Duration, attemps int) error
	AccquireLock(key string) (bool, error)
	ReleaseLock(hash string) error
	// Caching lock
	AccquireCachingLock(searchKey string) (bool, error)
	ReleaseCachingLock(searchKey string) error
	WaitCachingLockRelease(searchKey string) error

	// FareCheck Client lock
	AccquireFareCheckClientLock(flightID string) (bool, error)
	GetFareCheckClientLock(flightID string) (bool, error)
}

type searchFlightsRepository struct {
	redis redis.IRedis
}

func NewSearchFlightsRepository(redis redis.IRedis) SearchFlightsRepository {
	return &searchFlightsRepository{redis}
}

func (r *searchFlightsRepository) genKeyAccquireFareCheckClientLock(key string) string {
	return fmt.Sprintf(fareCheckClientLockKey, key)
}

func (r *searchFlightsRepository) GetFareCheckClientLock(flightID string) (bool, error) {
	key := r.genKeyAccquireFareCheckClientLock(flightID)
	value := ""

	err := r.redis.Get(key, &value)
	if err != nil {
		if errors.Is(err, goRedis.Nil) {
			return false, nil
		}

		return false, errors.Wrap(err, "AcquireLock")
	}

	return true, nil
}

func (r *searchFlightsRepository) AccquireFareCheckClientLock(flightID string) (bool, error) {
	key := r.genKeyAccquireFareCheckClientLock(flightID)

	lock, err := r.redis.AcquireLock(key, "", constants.CachedSearchResultTimeout)
	if err != nil {
		return false, errors.Wrap(err, "AcquireLock")
	}

	return lock, nil
}

func (r *searchFlightsRepository) AccquireCachingLock(searchKey string) (bool, error) {
	key := r.genKeyForCachingLock(searchKey)

	lock, err := r.redis.AcquireLock(key, "", time.Minute*10)
	if err != nil {
		return false, errors.Wrap(err, "AcquireLock")
	}

	return lock, nil
}

func (r *searchFlightsRepository) ReleaseCachingLock(searchKey string) error {
	fullKey := r.genKeyForCachingLock(searchKey)

	if err := r.redis.Remove(fullKey); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *searchFlightsRepository) WaitCachingLockRelease(searchKey string) error {
	sleepTime := time.Millisecond * 500
	attemps := int(constants.CachedSearchResultTimeout / sleepTime)

	key := r.genKeyForCachingLock(searchKey)

	for i := 0; i < attemps; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		val := r.redis.CMD().Get(ctx, key)
		err := val.Err()

		if err != nil {
			if errors.Is(err, goRedis.Nil) {
				return nil
			}

			log.Error("WaitForLockToRelease r.redis.CMD.Get error", log.Any("error", err), log.Int("attemps", i), log.String("key", key))
		}

		time.Sleep(sleepTime)
	}

	return nil
}

func (*searchFlightsRepository) genKey(key string) string {
	return fmt.Sprintf(searchFlightsLockKey, key)
}

func (*searchFlightsRepository) genKeyForCachingLock(key string) string {
	return fmt.Sprintf(cachingLockKey, key)
}

func (r *searchFlightsRepository) ReleaseLock(hash string) error {
	fullKey := r.genKey(hash)

	time.Sleep(200 * time.Millisecond)
	if err := r.redis.Remove(fullKey); err != nil {
		return errors.Wrap(err, " r.db.Remove(key)")
	}

	return nil
}

func (r *searchFlightsRepository) AccquireLock(hash string) (bool, error) {
	key := r.genKey(hash)

	lock, err := r.redis.AcquireLock(key, "", lockTTL)
	if err != nil {
		return false, errors.Wrap(err, "AcquireLock")
	}

	return lock, nil
}

func (r *searchFlightsRepository) WaitForLockToRelease(hash string, sleeptime time.Duration, attemps int) error {
	key := r.genKey(hash)

	for i := 0; i < attemps; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		val := r.redis.CMD().Get(ctx, key)
		err := val.Err()

		if err != nil {
			if errors.Is(err, goRedis.Nil) {
				return nil
			}

			log.Error("WaitForLockToRelease r.redis.CMD.Get error", log.Any("error", err), log.Int("attemps", i), log.String("key", key))
		}

		time.Sleep(sleeptime)
	}

	return nil
}
