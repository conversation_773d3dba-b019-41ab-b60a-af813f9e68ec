package repositories

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const listFlightColName = "list_flight"

const (
	listFlightIndexRecordID = "__record_id__"
	listFlightIndexFlightID = "__flight_id__"
)

type ListFlightRepository interface {
	InsertMany(ctx context.Context, reqs []*domain.ResponseFlight) error
	FindRecIDs(ctx context.Context, recIDs []string) ([]*domain.ResponseFlight, error)
	FindByFlightID(ctx context.Context, flightID string) (*domain.ResponseFlight, error)
	UpdateOneByFlightID(ctx context.Context, fligthID string, req *domain.ResponseFlight) error
	SetSoldOut(ctx context.Context, flightID string, value bool) error
	FindByFlightIds(ctx context.Context, flightIds []string, hideBag bool) ([]*domain.ResponseFlight, error)
}

type listFlightRepository struct {
	db mongodb.DB
}

func NewListFlightRepository(db mongodb.DB) ListFlightRepository {
	return &listFlightRepository{db}
}

func (r *listFlightRepository) SetSoldOut(ctx context.Context, flightID string, value bool) error {
	filter := bson.M{
		"flight_id": flightID,
	}

	update := bson.M{
		"sold_out": value,
	}

	return r.db.UpdateOne(ctx, listFlightColName, filter, update, &options.UpdateOptions{
		Hint: listFlightIndexFlightID,
	})
}

func (r *listFlightRepository) UpdateOneByFlightID(ctx context.Context, fligthID string, req *domain.ResponseFlight) error {
	m, err := converts.FromDomainResponseFlight(req)
	if err != nil {
		return errors.Wrap(err, "converts FromDomainResponseFlight")
	}

	m.UpdatedAt = time.Now().UnixMilli()

	filter := bson.M{
		"flight_id": req.FlightID,
	}

	return r.db.UpdateOne(ctx, listFlightColName, filter, m, &options.UpdateOptions{
		Hint: listFlightIndexFlightID,
	})
}

func (r *listFlightRepository) InsertMany(ctx context.Context, reqs []*domain.ResponseFlight) error {
	request := make([]mongo.WriteModel, 0, len(reqs))
	for _, req := range reqs {
		m, err := converts.FromDomainResponseFlight(req)
		if err != nil {
			return errors.Wrap(err, "converts.FromDomainListFlightCachedRecord")
		}

		m.BeforeCreate()

		request = append(request, mongo.NewInsertOneModel().SetDocument(m))
	}

	if len(request) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, listFlightColName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

// Filter: soldOut: false
func (r *listFlightRepository) FindRecIDs(ctx context.Context, recIDs []string) ([]*domain.ResponseFlight, error) {
	objRecIDS := []primitive.ObjectID{}

	for _, recID := range recIDs {
		temp, err := primitive.ObjectIDFromHex(recID)
		if err != nil {
			return nil, errors.Wrap(err, "temp ObjectIDFromHex [value]: "+recID)
		}

		objRecIDS = append(objRecIDS, temp)
	}

	m := []*models.ResponseFlight{}

	filter := bson.M{
		"record_id": bson.M{
			"$in": objRecIDS,
		},
		"sold_out": false,
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(listFlightIndexRecordID),
	}

	err := r.db.Find(ctx, listFlightColName, &m, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "r.db.Find")
	}

	res, err := converts.ToDomainResponseFlights(m)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainResponseFlights")
	}

	return res, nil
}

func (r *listFlightRepository) FindByFlightID(ctx context.Context, flightID string) (*domain.ResponseFlight, error) {
	result := &models.ResponseFlight{}

	filter := bson.M{
		"flight_id": flightID,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(listFlightIndexFlightID),
	}

	err := r.db.FindOne(ctx, listFlightColName, result, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "db.FindOne")
	}

	out, err := converts.ToDomainResponseFlight(result)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainResponseFlight")
	}

	return out, nil
}

func (r *listFlightRepository) FindByFlightIds(ctx context.Context, flightIds []string, hideBag bool) ([]*domain.ResponseFlight, error) {
	result := []*models.ResponseFlight{}

	filter := bson.M{
		"flight_id": bson.M{
			"$in": flightIds,
		},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(listFlightIndexFlightID),
	}

	err := r.db.Find(ctx, listFlightColName, &result, otps...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	out, err := converts.ToDomainResponseFlights(result)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainResponseFlights")
	}

	return out, nil
}
