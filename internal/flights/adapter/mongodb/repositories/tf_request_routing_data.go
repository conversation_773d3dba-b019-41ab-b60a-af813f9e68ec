package repositories

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	tfRequestRoutingDataColName       = "tf_request_routing_data"
	tfRequestRoutingIndexItineraryKey = "__itinerary_key__"
	tfRequestRoutingIndexRoutingID    = "__routing_id__"
	tfRequestRoutingIndexExpiredAt    = "__expired_at__"
)

type TFRoutingRepository interface {
	FindByItiKeys(ctx context.Context, requestKey string, itiKeys []string) ([]*domain.TFRequestRoutingData, error)
	DeleteByRoutingID(ctx context.Context, requestKey string, routingID string) error
	UpsertMany(ctx context.Context, items []*domain.TFRequestRoutingData) error
	DeleteExpiredRouting(ctx context.Context) error
}

type tfRoutingRepository struct {
	db mongodb.DB
}

func NewTFRoutingRepository(db mongodb.DB) TFRoutingRepository {
	return &tfRoutingRepository{db}
}

func (r *tfRoutingRepository) UpsertMany(ctx context.Context, items []*domain.TFRequestRoutingData) error {
	requests := make([]mongo.WriteModel, 0, len(items))

	for _, item := range items {
		m := converts.FromDomainTFRequestRoutingData(item)

		m.BeforeUpdate()

		filter := bson.M{
			"itinerary_key": m.ItineraryKey,
			"request_key":   m.RequestKey,
		}

		update := bson.M{
			"$set": m,
		}

		requests = append(requests, mongo.NewUpdateOneModel().SetUpdate(update).SetFilter(filter).SetUpsert(true).SetHint(tfRequestRoutingIndexItineraryKey))
	}

	if len(requests) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, tfRequestRoutingDataColName, requests)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (r *tfRoutingRepository) DeleteExpiredRouting(ctx context.Context) error {
	return r.db.Delete(ctx, tfRequestRoutingDataColName, bson.M{"expired_at": bson.M{"$lt": time.Now().UnixMilli()}}, &options.DeleteOptions{
		Hint: tfRequestRoutingIndexExpiredAt,
	})
}

func (r *tfRoutingRepository) DeleteByRoutingID(ctx context.Context, requestKey string, routingID string) error {
	return r.db.Delete(ctx, tfRequestRoutingDataColName, bson.M{"routing_id": routingID, "request_key": requestKey}, &options.DeleteOptions{
		Hint: tfRequestRoutingIndexRoutingID,
	})
}

func (r *tfRoutingRepository) FindByItiKeys(ctx context.Context, requestKey string, itiKeys []string) ([]*domain.TFRequestRoutingData, error) {
	result := []*models.TFRequestRoutingData{}

	filter := bson.M{
		"request_key": requestKey,
		"itinerary_key": bson.M{
			"$in": itiKeys,
		},
		"expired_at": bson.M{"$gt": time.Now().UnixMilli()},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(tfRequestRoutingIndexItineraryKey),
	}

	if err := r.db.Find(ctx, tfRequestRoutingDataColName, &result, otps...); err != nil {
		return nil, err
	}

	return converts.ToDomainTFRequestRoutingDatas(result), nil
}
