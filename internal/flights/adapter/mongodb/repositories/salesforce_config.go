package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const salesforceConfigColName = "salesforce_configs"

type SalesforceConfigRepository interface {
	InsertOne(ctx context.Context, req *domain.SalesforceConfig) error
	FindByOfficeID(ctx context.Context, officeID string) (*domain.SalesforceConfig, error)
}

type salesforceConfigRepository struct {
	db mongodb.DB
}

func NewSalesforceConfigRepository(db mongodb.DB) SalesforceConfigRepository {
	return &salesforceConfigRepository{db}
}

func (r *salesforceConfigRepository) InsertOne(ctx context.Context, req *domain.SalesforceConfig) error {
	m := converts.FromDomainSalesforceConfig(req)
	m.BeforeCreate()

	return r.db.InsertOne(ctx, salesforceConfigColName, &m, &options.InsertOneOptions{})
}

func (r *salesforceConfigRepository) FindByOfficeID(ctx context.Context, officeID string) (*domain.SalesforceConfig, error) {
	var res *models.SalesforceConfig
	filter := bson.M{
		"office_id": officeID,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.IndexDefault),
	}

	err := r.db.FindOne(ctx,
		salesforceConfigColName,
		&res,
		options...,
	)

	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	return converts.ToDomainSalesforceConfig(res), nil
}
