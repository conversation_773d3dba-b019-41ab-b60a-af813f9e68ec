package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	commissionColName        = "commissions"
	comissionIndexKeyAirline = "__dcp_key__airline__"
)

type CommissionRepository interface {
	FindByAirlines(ctx context.Context, dcpKey string, airlines []string) ([]*domain.Commission, error)
}

type commissionRepository struct {
	db mongodb.DB
}

func NewCommissionRepository(db mongodb.DB) CommissionRepository {
	return &commissionRepository{db}
}

func (r *commissionRepository) FindByAirlines(ctx context.Context, dcpKey string, airlines []string) ([]*domain.Commission, error) {
	result := []*models.Commission{}

	filter := bson.M{
		"dcp_key": dcpKey,
		"airline": bson.M{
			"$in": airlines,
		},
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(comissionIndexKeyAirline),
	}

	if err := r.db.Find(ctx, commissionColName, &result, opts...); err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	return converts.ToDomainComissions(result), nil
}
