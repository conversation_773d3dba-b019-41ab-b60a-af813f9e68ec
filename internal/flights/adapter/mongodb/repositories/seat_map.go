package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	seatMapCollectionName string = "seat_maps"
)

type SeatMapRepository interface {
	UpsertMany(ctx context.Context, seatSegments []*domain.SeatSegment) error
	FindSeatMapByKey(ctx context.Context, key string, expiredAtGt int64) (*domain.SeatSegment, error)
	FindSeatMapByKeyLasted(ctx context.Context, key string) (*domain.SeatSegment, error)
	ClearSeatMapByKeys(ctx context.Context, keys []string) error
}

type seatMapRepository struct {
	db mongodb.DB
}

func NewSeatMapRepository(db mongodb.DB) SeatMapRepository {
	return &seatMapRepository{
		db: db,
	}
}

func (r *seatMapRepository) UpsertMany(ctx context.Context, seatSegments []*domain.SeatSegment) error {
	request := make([]mongo.WriteModel, 0, len(seatSegments))
	for _, req := range seatSegments {
		m := converts.FromDomainSeatSegment(req)
		m.BeforeUpdate()

		filter := bson.M{
			"key": m.Key,
		}

		update := bson.M{
			"$set": m,
		}

		request = append(request, mongo.NewUpdateOneModel().SetUpdate(update).SetFilter(filter).SetUpsert(true).SetHint(models.SeatMapIndexKey))
	}

	if len(request) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, seatMapCollectionName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (r *seatMapRepository) FindSeatMapByKey(ctx context.Context, key string, expiredAtGt int64) (*domain.SeatSegment, error) {
	result := &models.SeatSegment{}

	filter := bson.M{
		"key": key,
		"expired_at": bson.M{
			"$gt": expiredAtGt,
		},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.SeatMapIndexKey),
	}

	err := r.db.FindOne(ctx, seatMapCollectionName, &result, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainSeatSegment(result), nil
}

func (r *seatMapRepository) FindSeatMapByKeyLasted(ctx context.Context, key string) (*domain.SeatSegment, error) {
	result := &models.SeatSegment{}

	filter := bson.M{
		"key": key,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.SeatMapIndexKey),
		mongodb.WithSorter(bson.M{"created_at": -1}),
	}
	err := r.db.FindOne(ctx, seatMapCollectionName, &result, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainSeatSegment(result), nil
}

func (r *seatMapRepository) ClearSeatMapByKeys(ctx context.Context, keys []string) error {
	filter := bson.M{
		"key": bson.M{
			"$in": keys,
		},
	}

	update := bson.M{
		"expired_at": 0,
	}
	return r.db.UpdateMany(ctx,
		seatMapCollectionName,
		filter,
		update,
		&options.UpdateOptions{Hint: models.SeatMapIndexKey},
	)
}
