package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

const bookingColname = "booking"
const (
	bookingIndexID           = "_id_"
	bookingIndexSessionID    = "__session_id__"
	bookingIndexBookingCode  = "__booking_code__"
	bookingIndexItiKeyStatus = "__ities_key__status__"
	bookingIndexTicketStatus = "__ticket_status__"
)

type BookingRepository interface {
	InsertOne(ctx context.Context, req *domain.BookingSession) error
	UpdateFareDataCf(ctx context.Context, sessionID string, req *domain.TotalFareInfo, originalFare *domain.TotalFareInfo) error
	UpdateFareDataCfWithRaw(ctx context.Context, sessionID string, fareDataCf *domain.TotalFareInfo, fareDataCfRaw *domain.TotalFareInfo, originalFare *domain.TotalFareInfo) error
	UpdateFareDataCfRaw(ctx context.Context, sessionID string, fareDataCfRaw *domain.TotalFareInfo) error
	UpdateBooking(ctx context.Context, sessionID string, req *domain.UpdateBookingRepoRequest) error
	UpdateTicketAndReservationCodes(ctx context.Context, officeID, bookingCode, userID string, itineraries []*domain.FlightItinerary, ticketStatus enum.TicketStatus) error
	UpdateTicketStatus(ctx context.Context, officeID, bookingCode, userID string, ticketStatus enum.TicketStatus) error
	FindBookingBySessionID(ctx context.Context, sessionID string) (*domain.BookingSession, error)
	FindBookingByID(ctx context.Context, id string) (*domain.BookingSession, error)
	FindOneByBookingCode(ctx context.Context, officeID string, bookingCode string) (*domain.BookingSession, error)
	UpdateOne(ctx context.Context, id string, req *domain.BookingSession) error
	FindOneByBookingCodeV2(ctx context.Context, code string) (*domain.BookingSession, error)
	FindActiveBookings(ctx context.Context, itiKey []string) ([]*domain.BookingSession, error)
	ListBooking(ctx context.Context, pagi *commonDomain.Pagination, ticketStatus enum.TicketStatus, orderKey string, orderVal int) ([]*domain.BookingSession, error)
	ListExpiredBooking(ctx context.Context) ([]*domain.BookingSession, error)
	ListManualIssuingBookings(ctx context.Context, ticketStatuses []enum.TicketStatus, pagi *commonDomain.Pagination, bookingCode, pnrCode *string) ([]*domain.BookingSession, error)
	GetBookingReport(ctx context.Context, queryFilter *domain.GetReportBookingRequest) ([]*domain.BookingSession, error)
	ListUpcomingBookings(ctx context.Context, req *domain.ListUpcomingBookingsReq) ([]*domain.BookingSession, error)
	ListBookingFilter(ctx context.Context, filter *domain.ListBookingFilter) (*domain.ListBookingResult, error)
}

type bookingRepository struct {
	db mongodb.DB
}

func NewBookingRepository(db mongodb.DB) BookingRepository {
	return &bookingRepository{db}
}

func (r *bookingRepository) ListUpcomingBookings(ctx context.Context, req *domain.ListUpcomingBookingsReq) ([]*domain.BookingSession, error) {
	now := time.Now()

	var upperLimit time.Time

	results := []*models.BookingSession{}

	itiFilter := bson.M{
		"$gt": now,
	}

	filter := bson.M{
		"ticket_status":                 enum.TicketStatusName[enum.TicketStatusOK],
		"itineraries.0.depart_date_utc": itiFilter,
	}

	if req.MaximumDays > 0 {
		upperLimit = time.Now().Add(time.Hour * 24 * time.Duration(req.MaximumDays))

		itiFilter["$lt"] = upperLimit
	}

	if req.Text != "" {
		filter["$or"] = bson.A{
			bson.M{
				"itineraries.reservation_code": bson.M{"$regex": req.Text, "$options": "i"},
			},
			bson.M{
				"booking_code": bson.M{"$regex": req.Text, "$options": "i"},
			},
		}
	}

	if req.NotiStatus == enum.NotificationStatusSent {
		filter["notified"] = true
	}

	if req.NotiStatus == enum.NotificationStatusUnsent {
		filter["notified"] = bson.M{"$ne": true}
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithSorter(bson.M{
			"itineraries.0.depart_date_utc": 1,
		}),
		mongodb.WithHint(bookingIndexTicketStatus),
		mongodb.WithPaging(req.Pagi),
	}

	if err := r.db.Find(ctx, bookingColname, &results, otps...); err != nil {
		return nil, errors.Wrap(err, "r.db.Find")
	}

	out, err := converts.ToDomainBookingSessions(results)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSessions")
	}

	return out, nil
}

func (r *bookingRepository) ListExpiredBooking(ctx context.Context) ([]*domain.BookingSession, error) {
	result := []*models.BookingSession{}

	filter := bson.D{
		{Key: "is_transferred", Value: bson.M{
			"$ne": true,
		}},
		{Key: "$or",
			Value: bson.A{
				bson.D{
					{Key: "status", Value: enum.BookingStatusConfirmed},
				},
				bson.D{
					{Key: "last_ticketing_date", Value: bson.D{{Key: "$lt", Value: time.Now().UnixMilli()}}},
					{Key: "status", Value: enum.BookingStatusOK},
				},
			},
		},
	}

	opts := []mongodb.Option{
		mongodb.WithPaging(&commonDomain.Pagination{
			PageLimit:   30,
			PageCurrent: 1,
		}),
		mongodb.WithHint(bookingIndexTicketStatus),
		mongodb.WithFilter(filter),
	}

	err := r.db.Find(ctx, bookingColname, &result, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	cResult, err := converts.ToDomainBookingSessions(result)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSessions")
	}

	return cResult, nil
}

func (r *bookingRepository) ListBooking(ctx context.Context, pagi *commonDomain.Pagination, ticketStatus enum.TicketStatus, orderKey string, orderVal int) ([]*domain.BookingSession, error) {
	result := []*models.BookingSession{}

	opts := []mongodb.Option{
		mongodb.WithPaging(pagi),
		mongodb.WithHint(bookingIndexTicketStatus),
		mongodb.WithFilter(bson.M{
			"ticket_status": ticketStatus,
		}),
	}

	if orderKey != "" {
		order := bson.M{orderKey: orderVal}

		opts = append(opts, mongodb.WithSorter(order))
	}

	err := r.db.Find(ctx, bookingColname, &result, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	cResult, err := converts.ToDomainBookingSessions(result)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSessions")
	}

	return cResult, nil
}

func (r *bookingRepository) FindActiveBookings(ctx context.Context, itiIDs []string) ([]*domain.BookingSession, error) {
	rs := []*models.BookingSession{}

	filter := bson.D{
		{Key: "itineraries.id", Value: bson.M{"$in": itiIDs}},
		{Key: "$or", Value: bson.A{
			bson.D{
				{Key: "status", Value: enum.BookingStatusConfirmed},
				{Key: "flight_provider", Value: bson.D{{Key: "$in", Value: []string{enum.FlightProviderName[enum.FlightProviderAmadeus], enum.FlightProviderName[enum.FlightProviderTravelFusion]}}}},
			},
			bson.D{
				{Key: "status", Value: enum.BookingStatusOK},
			},
		}},
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(bookingIndexItiKeyStatus),
	}

	err := r.db.Find(ctx, bookingColname, &rs, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "r.db.Find")
	}

	out, err := converts.ToDomainBookingSessions(rs)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainBookingSessions")
	}

	return out, nil
}

func (r *bookingRepository) UpdateOne(ctx context.Context, id string, req *domain.BookingSession) error {
	m, err := converts.FromDomainBookingSession(req)
	if err != nil {
		return errors.Wrap(err, "FromDomainBookingSession")
	}

	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.Wrap(err, "ObjectIDFromHex id")
	}

	filter := bson.M{
		"_id": objID,
	}

	return r.db.UpdateOne(ctx, bookingColname, filter, m, &options.UpdateOptions{
		Hint: bookingIndexID,
	})
}

func (r *bookingRepository) UpdateBooking(ctx context.Context, sessionID string, req *domain.UpdateBookingRepoRequest) error {
	now := time.Now().UnixMilli()

	fitler := bson.M{
		"session_id": sessionID,
	}

	update := bson.M{
		"updated_at": now,
	}

	if req.Status != enum.BookingStatusEmpty {
		update["status"] = req.Status
	}

	if req.Confirmed != nil {
		update["confirmed"] = *req.Confirmed
	}

	if req.LastTicketingDate != 0 {
		update["last_ticketing_date"] = req.LastTicketingDate
	}

	if req.TicketStatus != "" {
		update["ticket_status"] = req.TicketStatus
	}

	if req.BookingRef != "" {
		update["booking_ref"] = req.BookingRef
	}

	if req.OrderNumRef != "" {
		update["order_num_ref"] = req.OrderNumRef
	}

	if req.BookingCode != "" {
		update["booking_code"] = req.BookingCode
	}

	if req.OrderID != "" {
		update["order_id"] = req.OrderID
	}

	if req.ExpectedPrice != nil {
		update["expected_price"] = &models.ExpectedPrice{
			Amount:   req.ExpectedPrice.Amount,
			Currency: req.ExpectedPrice.Currency,
		}
	}

	if req.CommissionRate != nil {
		update["commission_rate"] = *req.CommissionRate
	}

	if req.FareExpiredDate != nil {
		update["fare_expired_date"] = *req.FareExpiredDate
	}

	if req.TicketExpiredDate != nil {
		update["ticket_expired_date"] = *req.TicketExpiredDate
	}

	if req.IsTransferred != nil {
		update["is_transferred"] = *req.IsTransferred
	}

	if req.AirlineSystem != "" {
		update["airline_system"] = req.AirlineSystem
	}

	if req.ReservationCode != nil {
		update["itineraries.$[].reservation_code"] = *req.ReservationCode
	}

	if req.InternalBooking != nil {
		update["internal_booking"] = *req.InternalBooking
	}

	if req.IsVJ24h != nil {
		update["is_vj_24"] = *req.IsVJ24h
	}

	return r.db.UpdateOne(ctx, bookingColname, fitler, update, &options.UpdateOptions{
		Hint: bookingIndexSessionID,
	})
}

func (r *bookingRepository) UpdateFareDataCf(ctx context.Context, sessionID string, req *domain.TotalFareInfo, originalFare *domain.TotalFareInfo) error {
	now := time.Now().UnixMilli()
	m := converts.FromDomainTotalFareInfo(req)

	m.ConfirmedAt = now

	fitler := bson.M{
		"session_id": sessionID,
	}

	update := bson.M{
		"fare_data_cf": m,
		"updated_at":   now,
		"status":       enum.BookingStatusConfirmed,
	}

	if originalFare != nil {
		update["origin_fare_data"] = converts.FromDomainTotalFareInfo(originalFare)
	}

	return r.db.UpdateOne(ctx, bookingColname, fitler, update, &options.UpdateOptions{
		Hint: bookingIndexSessionID,
	})
}

// UpdateFareDataCfWithRaw updates both FareDataCf and FareDataCfRaw in single transaction
// This is the RECOMMENDED method for confirm fare workflow to ensure data consistency
//
// Parameters:
// - fareDataCf: Processed fare data after pricing calculation (required)
// - fareDataCfRaw: Raw fare data from provider before pricing (optional, can be nil)
// - originalFare: Original fare data for audit trail (optional, can be nil)
//
// Usage example:
//
//	err := repo.UpdateFareDataCfWithRaw(ctx, sessionID, processedFare, rawFare, originalFare)
//
// Best practices:
// - Use this method in confirm fare workflow to persist both raw and processed data
// - fareDataCfRaw contains original provider response before markup/discount
// - fareDataCf contains final fare after pricing service calculation
// - Both fields are updated atomically to maintain data consistency
func (r *bookingRepository) UpdateFareDataCfWithRaw(ctx context.Context, sessionID string, fareDataCf *domain.TotalFareInfo, fareDataCfRaw *domain.TotalFareInfo, originalFare *domain.TotalFareInfo) error {
	now := time.Now().UnixMilli()

	// Convert FareDataCf
	fareDataCfModel := converts.FromDomainTotalFareInfo(fareDataCf)
	fareDataCfModel.ConfirmedAt = now

	filter := bson.M{
		"session_id": sessionID,
	}

	update := bson.M{
		"fare_data_cf": fareDataCfModel,
		"updated_at":   now,
		"status":       enum.BookingStatusConfirmed,
	}

	// Add FareDataCfRaw if provided
	if fareDataCfRaw != nil {
		fareDataCfRawModel := converts.FromDomainTotalFareInfo(fareDataCfRaw)
		update["fare_data_cf_raw"] = fareDataCfRawModel
	}

	// Add OriginFareData if provided
	if originalFare != nil {
		update["origin_fare_data"] = converts.FromDomainTotalFareInfo(originalFare)
	}

	return r.db.UpdateOne(ctx, bookingColname, filter, update, &options.UpdateOptions{
		Hint: bookingIndexSessionID,
	})
}

// UpdateFareDataCfRaw updates only FareDataCfRaw field
func (r *bookingRepository) UpdateFareDataCfRaw(ctx context.Context, sessionID string, fareDataCfRaw *domain.TotalFareInfo) error {
	if fareDataCfRaw == nil {
		return fmt.Errorf("fareDataCfRaw cannot be nil")
	}

	now := time.Now().UnixMilli()
	fareDataCfRawModel := converts.FromDomainTotalFareInfo(fareDataCfRaw)

	filter := bson.M{
		"session_id": sessionID,
	}

	update := bson.M{
		"fare_data_cf_raw": fareDataCfRawModel,
		"updated_at":       now,
	}

	return r.db.UpdateOne(ctx, bookingColname, filter, update, &options.UpdateOptions{
		Hint: bookingIndexSessionID,
	})
}

func (r *bookingRepository) InsertOne(ctx context.Context, req *domain.BookingSession) error {
	m, err := converts.FromDomainBookingSession(req)
	if err != nil {
		return errors.Wrap(err, "FromDomainBookingSession")
	}

	m.BeforeCreate()

	return r.db.InsertOne(ctx, bookingColname, m, &options.InsertOneOptions{})
}

func (r *bookingRepository) FindBookingBySessionID(ctx context.Context, sessionID string) (*domain.BookingSession, error) {
	var res *models.BookingSession

	filter := bson.M{
		"session_id": sessionID,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(bookingIndexSessionID),
	}

	err := r.db.FindOneWithRetry(ctx,
		constants.MongoRetryCount,
		constants.MongoRetryDelay,
		bookingColname,
		&res,
		options...,
	)

	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response, err := converts.ToDomainBookingSession(res)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSession")
	}

	return response, nil
}

func (r *bookingRepository) FindBookingByID(ctx context.Context, id string) (*domain.BookingSession, error) {
	var res *models.BookingSession

	// Convert string ID to MongoDB ObjectID
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.Wrap(err, "ObjectIDFromHex id")
	}

	filter := bson.M{
		"_id": objID,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(bookingIndexID),
	}

	err = r.db.FindOne(ctx,
		bookingColname,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response, err := converts.ToDomainBookingSession(res)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSession")
	}

	return response, nil
}

func (r *bookingRepository) FindOneByBookingCode(ctx context.Context, officeID string, bookingCode string) (*domain.BookingSession, error) {
	var res *models.BookingSession

	filter := bson.M{
		"booking_code": bookingCode,
	}

	if officeID != "" {
		filter["office_id"] = officeID
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(bookingIndexBookingCode),
	}

	err := r.db.FindOne(ctx,
		bookingColname,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response, err := converts.ToDomainBookingSession(res)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSession")
	}

	return response, nil
}

func (r *bookingRepository) FindOneByBookingCodeV2(ctx context.Context, bookingCode string) (*domain.BookingSession, error) {
	var res *models.BookingSession

	filter := bson.M{
		"booking_code": bookingCode,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(bookingIndexBookingCode),
	}

	err := r.db.FindOne(ctx,
		bookingColname,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response, err := converts.ToDomainBookingSession(res)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSession")
	}

	return response, nil
}

func (r *bookingRepository) ListManualIssuingBookings(ctx context.Context, ticketStatuses []enum.TicketStatus, pagi *commonDomain.Pagination, bookingCode, pnrCode *string) ([]*domain.BookingSession, error) {
	result := []*models.BookingSession{}

	filter := bson.M{
		"ticket_status": bson.M{
			"$in": ticketStatuses,
		},
		"manual_issuing": true,
	}

	if bookingCode != nil {
		filter["booking_code"] = *bookingCode
	}

	if pnrCode != nil {
		filter["booking_ref"] = *pnrCode
	}

	opts := []mongodb.Option{
		mongodb.WithPaging(pagi),
		mongodb.WithHint(bookingIndexTicketStatus),
		mongodb.WithFilter(filter),
	}

	err := r.db.Find(ctx, bookingColname, &result, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	cResult, err := converts.ToDomainBookingSessions(result)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSessions")
	}

	return cResult, nil
}

func (r *bookingRepository) UpdateTicketAndReservationCodes(ctx context.Context, officeID, bookingCode, userID string, itineraries []*domain.FlightItinerary, ticketStatus enum.TicketStatus) error {
	now := time.Now().UnixMilli()
	mItineraries, err := converts.FromDomainFlightItineraries(itineraries)
	if err != nil {
		return errors.Wrap(err, "FromDomainFlightItineraries")
	}

	filter := bson.M{
		"booking_code": bookingCode,
		"office_id":    officeID,
	}

	update := bson.M{
		"itineraries":   mItineraries,
		"ticket_status": ticketStatus,
		"updated_at":    now,
		"updated_by":    userID,
	}

	return r.db.UpdateOne(ctx, bookingColname, filter, update, &options.UpdateOptions{
		Hint: bookingIndexBookingCode,
	})
}

func (r *bookingRepository) UpdateTicketStatus(ctx context.Context, officeID, bookingCode, userID string, ticketStatus enum.TicketStatus) error {
	now := time.Now().UnixMilli()

	filter := bson.M{
		"booking_code": bookingCode,
		"office_id":    officeID,
	}

	update := bson.M{
		"ticket_status": ticketStatus,
		"updated_at":    now,
		"updated_by":    userID,
	}

	return r.db.UpdateOne(ctx, bookingColname, filter, update, &options.UpdateOptions{
		Hint: bookingIndexSessionID,
	})
}

func (r *bookingRepository) GetBookingReport(ctx context.Context, queryFilter *domain.GetReportBookingRequest) ([]*domain.BookingSession, error) {
	result := []*models.BookingSession{}

	filter := bson.M{}

	if queryFilter != nil {
		if queryFilter.BookingCode != "" {
			filter["booking_code"] = queryFilter.BookingCode
		}

		if len(queryFilter.Statuses) > 0 {
			filter["status"] = bson.M{"$in": queryFilter.Statuses}
		}

		filter["created_at"] = bson.M{
			"$gte": queryFilter.FromDate,
			"$lte": queryFilter.ToDate,
		}

	}

	opts := []mongodb.Option{
		mongodb.WithHint(bookingIndexID),
		mongodb.WithFilter(filter),
	}

	err := r.db.Find(ctx, bookingColname, &result, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	cResult, err := converts.ToDomainBookingSessions(result)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSessions")
	}

	return cResult, nil
}

// ListBookingFilter performs booking search with basic filtering
func (r *bookingRepository) ListBookingFilter(ctx context.Context, filter *domain.ListBookingFilter) (*domain.ListBookingResult, error) {
	// Build MongoDB filter
	mongoFilter := r.buildSimpleFilter(filter)

	// Build sort options (default: created_at desc)
	sortOptions := bson.M{"created_at": -1}

	// Execute query with pagination
	results := []*models.BookingSession{}
	opts := []mongodb.Option{
		mongodb.WithFilter(mongoFilter),
		mongodb.WithSorter(sortOptions),
		mongodb.WithPaging(filter.Pagination),
		mongodb.WithHint(bookingIndexID),
	}

	err := r.db.Find(ctx, bookingColname, &results, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	// Convert to domain
	bookings, err := converts.ToDomainBookingSessions(results)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainBookingSessions")
	}

	result := &domain.ListBookingResult{
		Bookings:   bookings,
		Pagination: filter.Pagination,
	}

	return result, nil
}

// buildSimpleFilter builds MongoDB filter from filter criteria
func (r *bookingRepository) buildSimpleFilter(filter *domain.ListBookingFilter) bson.M {
	mongoFilter := bson.M{}

	if filter.OfficeID != "" {
		mongoFilter["office_id"] = filter.OfficeID
	}

	if len(filter.ManageOfficeIDs) > 0 {
		mongoFilter["office_id"] = bson.M{"$in": filter.ManageOfficeIDs}
	}

	// Booking code filter (case-insensitive partial match)
	if filter.BookingCode != "" {
		mongoFilter["booking_code"] = bson.M{"$regex": filter.BookingCode, "$options": "i"}
	}

	// Date range filter (created_at)
	if filter.FromDate != nil || filter.ToDate != nil {
		dateFilter := bson.M{}
		if filter.FromDate != nil {
			dateFilter["$gte"] = filter.FromDate
		}
		if filter.ToDate != nil {
			dateFilter["$lte"] = filter.ToDate
		}
		mongoFilter["created_at"] = dateFilter
	}

	// Status filters
	if len(filter.StatusesList) > 0 {
		statusNames := make([]string, len(filter.StatusesList))
		for i, status := range filter.StatusesList {
			statusNames[i] = string(status)
		}
		mongoFilter["status"] = bson.M{"$in": statusNames}
	}

	return mongoFilter
}
