package repositories

import (
	"context"
	"errors"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	airportCollectionName string = "airports"
)

type AirportRepository interface {
	FindAirPort(ctx context.Context, code string) (*domain.Airport, error)
	FindByCodes(ctx context.Context, codes []string) (map[string]*domain.Airport, error)
}

type airportRepository struct {
	db mongodb.DB
}

func NewAirportRepository(db mongodb.DB) AirportRepository {
	return &airportRepository{
		db: db,
	}
}

func (r *airportRepository) FindByCodes(ctx context.Context, codes []string) (map[string]*domain.Airport, error) {
	result := []*models.AirPorts{}

	filter := bson.M{
		"code": bson.M{
			"$in": codes,
		},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.AirPortsIndexIATACode),
	}

	err := r.db.Find(ctx, airportCollectionName, &result, otps...)
	if err != nil {
		return nil, err
	}

	output := map[string]*domain.Airport{}

	for _, item := range result {
		output[item.Code] = &domain.Airport{
			Code:     item.Code,
			Timezone: item.Timezone,
			Name:     item.Name,
			City:     item.City,
			Country:  item.Country,
		}
	}

	return output, nil
}

func (r *airportRepository) FindAirPort(ctx context.Context, code string) (*domain.Airport, error) {
	var airport *models.AirPorts
	filter := bson.M{
		"code": code,
	}

	opts := []mongodb.Option{
		mongodb.WithHint(models.AirPortsIndexIATACode),
		mongodb.WithFilter(filter),
	}

	err := r.db.FindOne(ctx,
		airportCollectionName, &airport, opts...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return &domain.Airport{
		Code:     airport.Code,
		Timezone: airport.Timezone,
	}, nil
}
