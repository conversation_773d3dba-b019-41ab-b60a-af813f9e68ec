package repositories

import (
	"context"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

const (
	amadeusISOCountryCollectionName string = "amadeus_iso_countries"
)

type AmadeusISOCountryRepository interface {
	Find(ctx context.Context) ([]*domain.AmadeusISOCountry, error)
}

type amadeusISOCountryRepository struct {
	db mongodb.DB
}

func NewAmadeusISOCountryRepository(db mongodb.DB) AmadeusISOCountryRepository {
	return &amadeusISOCountryRepository{
		db: db,
	}
}

func (r *amadeusISOCountryRepository) Find(ctx context.Context) ([]*domain.AmadeusISOCountry, error) {
	result := []*models.AmadeusISOCountry{}

	otps := []mongodb.Option{
		mongodb.WithHint(models.IndexDefault),
	}

	err := r.db.Find(ctx, amadeusISOCountryCollectionName, &result, otps...)
	if err != nil {
		return nil, err
	}

	return converts.ToDomainAmadeusISOCountries(result), err
}
