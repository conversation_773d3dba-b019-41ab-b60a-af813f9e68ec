package repositories

import (
	"context"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	aircraftLayoutCollectionName string = "aircraft_layouts"
)

type AircraftLayoutRepository interface {
	FindAircraftLayouts(ctx context.Context, airlineCode string) (map[string]*domain.AircraftLayout, error)
}

type aircraftLayoutRepository struct {
	db mongodb.DB
}

func NewAircraftLayoutRepository(db mongodb.DB) AircraftLayoutRepository {
	return &aircraftLayoutRepository{
		db: db,
	}
}

func (r *aircraftLayoutRepository) FindAircraftLayouts(ctx context.Context, airlineCode string) (map[string]*domain.AircraftLayout, error) {
	result := []*models.AircraftLayout{}

	filter := bson.M{
		"airline_code": airlineCode,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.AircraftLayoutAirlineCodeIndex),
	}

	err := r.db.Find(ctx, aircraftLayoutCollectionName, &result, otps...)
	if err != nil {
		return nil, err
	}

	return converts.ToDomainMapAircraftLayouts(result), nil
}
