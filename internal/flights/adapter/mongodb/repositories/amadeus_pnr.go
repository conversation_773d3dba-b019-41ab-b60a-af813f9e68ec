package repositories

import (
	"context"
	"errors"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const amadeusPNRColName = "amadeus_pnr"

const (
	amadeusPNRSessionID = "__session_id__"
)

type AmadeusPNRRepository interface {
	FindOne(ctx context.Context, sessionID string) (*domain.AmadeusPNR, error)
	Update(ctx context.Context, sessionID string, req *domain.AmadeusPNR) error
	InsertOne(ctx context.Context, req *domain.AmadeusPNR) error
}

type amadeusPNRRepository struct {
	db mongodb.DB
}

func NewAmadeusPNRRepository(db mongodb.DB) AmadeusPNRRepository {
	return &amadeusPNRRepository{db}
}

func (r *amadeusPNRRepository) InsertOne(ctx context.Context, req *domain.AmadeusPNR) error {
	m := converts.FromDomainAmadeusPNR(req)

	m.BeforeCreate()

	return r.db.InsertOne(ctx, amadeusPNRColName, m, &options.InsertOneOptions{})
}

func (r *amadeusPNRRepository) FindOne(ctx context.Context, sessionID string) (*domain.AmadeusPNR, error) {
	m := &models.AmadeusPNR{}

	filter := bson.M{
		"session_id": sessionID,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(amadeusPNRSessionID),
	}

	err := r.db.FindOne(ctx, amadeusPNRColName, m, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainAmadeusPNR(m), nil
}

func (r *amadeusPNRRepository) Update(ctx context.Context, sessionID string, req *domain.AmadeusPNR) error {
	m := converts.FromDomainAmadeusPNR(req)

	m.BeforeUpdate()

	filter := bson.M{
		"session_id": sessionID,
	}

	return r.db.UpdateOne(ctx, amadeusPNRColName, filter, m, &options.UpdateOptions{
		Hint: amadeusPNRSessionID,
	})
}
