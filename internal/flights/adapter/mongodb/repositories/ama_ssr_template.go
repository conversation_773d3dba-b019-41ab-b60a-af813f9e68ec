package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const amaSSRTemplateColName = "amadeus_ssr_template"

type AmaSSRTemplateRepository interface {
	FindAll(ctx context.Context) ([]*domain.SSRTemplate, error)
	Update(ctx context.Context, sessionID string, req *domain.SSRTemplate) error
	InsertOne(ctx context.Context, req *domain.SSRTemplate) error
}

type amaSSRTemplateRepository struct {
	db mongodb.DB
}

func NewAmaSSRTemplateRepository(db mongodb.DB) AmaSSRTemplateRepository {
	return &amaSSRTemplateRepository{db}
}

func (r *amaSSRTemplateRepository) InsertOne(ctx context.Context, req *domain.SSRTemplate) error {
	m := converts.FromDomainSSRTemplate(req)

	m.BeforeCreate()

	return r.db.InsertOne(ctx, amaSSRTemplateColName, m, &options.InsertOneOptions{})
}

func (r *amaSSRTemplateRepository) FindAll(ctx context.Context) ([]*domain.SSRTemplate, error) {
	m := []*models.SSRTemplate{}
	filter := bson.M{
		"deleted_at": bson.M{
			"$exists": false,
		},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.SSRTemplateIndexDefault),
	}

	err := r.db.Find(ctx, amaSSRTemplateColName, &m, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainSSRTemplates(m), nil
}

func (r *amaSSRTemplateRepository) Update(ctx context.Context, ID string, req *domain.SSRTemplate) error {
	m := converts.FromDomainSSRTemplate(req)

	m.BeforeUpdate()

	oID, err := primitive.ObjectIDFromHex(ID)
	if err != nil {
		return errors.Wrap(err, "id ObjectIDFromHex")
	}

	filter := bson.M{
		"_id": oID,
	}

	return r.db.UpdateOne(ctx, amaSSRTemplateColName, filter, m, &options.UpdateOptions{
		Hint: models.SSRTemplateIndexDefault,
	})
}
