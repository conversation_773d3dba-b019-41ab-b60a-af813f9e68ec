package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const pnrColName = "pnr"

const (
	pnrIndexSessionID = "__session_id__"
)

type PNRRepository interface {
	FindOne(ctx context.Context, sessionID string) (*domain.PNR, error)
	Update(ctx context.Context, sessionID string, req *domain.PNR) error
	InsertOne(ctx context.Context, req *domain.PNR) error
	FindMany(ctx context.Context, sessions []string) ([]*domain.PNR, error)
}

type pnrRepository struct {
	db mongodb.DB
}

func NewPNRRepository(db mongodb.DB) PNRRepository {
	return &pnrRepository{db}
}

func (r *pnrRepository) FindMany(ctx context.Context, sessions []string) ([]*domain.PNR, error) {
	rs := []*models.PNR{}

	filter := bson.M{
		"session_id": bson.M{
			"$in": sessions,
		},
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(pnrIndexSessionID),
	}

	err := r.db.Find(ctx, pnrColName, &rs, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "r.db.Find")
	}

	return converts.ToDomainPNRs(rs), nil
}

func (r *pnrRepository) InsertOne(ctx context.Context, req *domain.PNR) error {
	m := converts.FromDomainPNR(req)
	m.BeforeCreate()

	return r.db.InsertOne(ctx, pnrColName, m, &options.InsertOneOptions{})
}

func (r *pnrRepository) FindOne(ctx context.Context, sessionID string) (*domain.PNR, error) {
	m := &models.PNR{}

	filter := bson.M{
		"session_id": sessionID,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(pnrIndexSessionID),
	}

	err := r.db.FindOne(ctx, pnrColName, m, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainPNR(m), nil
}

func (r *pnrRepository) Update(ctx context.Context, sessionID string, req *domain.PNR) error {

	m := converts.FromDomainPNR(req)

	m.BeforeUpdate()

	filter := bson.M{
		"session_id": sessionID,
	}

	return r.db.UpdateOne(ctx, pnrColName, filter, m, &options.UpdateOptions{
		Hint: pnrIndexSessionID,
	})
}
