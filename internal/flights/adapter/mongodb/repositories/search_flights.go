package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const searchFlightsColName = "search_flights"

const (
	searchFlightsIndexHashKey = "__hash_key__"
	searchFlightsIndexKey     = "__key__"
)

type SearchFlightsRepository interface {
	// UpsertMany(ctx context.Context, reqs []*domain.SearchFlightsCachedRecord) error
	InsertMany(ctx context.Context, reqs []*domain.SearchFlightsCachedRecord) error
	FindByKey(ctx context.Context, key string, provider enum.FlightProvider) ([]*domain.SearchFlightsCachedRecord, error)
	FindByHashKey(ctx context.Context, hashKey string, rawProviders []enum.FlightProvider, version enum.SearchVersion, expiredAtGt int64) ([]*domain.SearchFlightsCachedRecord, error)
	UpdateExpiredAt(ctx context.Context, id string, expiredAt int64) error
}

type searchFlightsRepository struct {
	db mongodb.DB
}

func NewSearchFlightsRepository(db mongodb.DB) SearchFlightsRepository {
	return &searchFlightsRepository{db}
}

func (r *searchFlightsRepository) UpdateExpiredAt(ctx context.Context, id string, expiredAt int64) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	filter := bson.M{"_id": objID}
	update := bson.M{
		"expired_at": expiredAt,
	}

	otp := &options.UpdateOptions{
		Hint: models.IndexDefault,
	}

	return r.db.UpdateOne(ctx, searchFlightsColName, filter, update, otp)
}

func (r *searchFlightsRepository) UpsertMany(ctx context.Context, reqs []*domain.SearchFlightsCachedRecord) error {
	request := make([]mongo.WriteModel, 0, len(reqs))

	for _, req := range reqs {
		m, err := converts.FromDomainSearchFlightsCachedRecord(req)
		if err != nil {
			return errors.Wrap(err, "converts.FromDomainSearchFlightsCachedRecord")
		}

		m.BeforeUpdate()

		filter := bson.M{
			"key":      m.Key,
			"provider": m.Provider,
		}

		update := bson.M{
			"$set": m,
		}

		request = append(request, mongo.NewUpdateOneModel().SetUpdate(update).SetFilter(filter).SetUpsert(true))
	}

	if len(request) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, searchFlightsColName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (r *searchFlightsRepository) InsertMany(ctx context.Context, reqs []*domain.SearchFlightsCachedRecord) error {
	request := make([]mongo.WriteModel, 0, len(reqs))
	for _, req := range reqs {
		m, err := converts.FromDomainSearchFlightsCachedRecord(req)
		if err != nil {
			return errors.Wrap(err, "converts.FromDomainSearchFlightsCachedRecord")
		}

		m.BeforeCreate()

		request = append(request, mongo.NewInsertOneModel().SetDocument(m))
	}

	if len(request) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, searchFlightsColName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (r *searchFlightsRepository) FindByKey(ctx context.Context, key string, provider enum.FlightProvider) ([]*domain.SearchFlightsCachedRecord, error) {
	m := []*models.SearchFlights{}

	filter := bson.M{
		"key":      key,
		"provider": enum.FlightProviderName[provider],
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchFlightsIndexKey),
	}

	err := r.db.Find(ctx, searchFlightsColName, &m, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "r.db.Find")
	}

	res, err := converts.ToDomainSearchFlightsCachedRecords(m)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainSearchFlightsCachedRecords")
	}

	return res, nil
}

func (r *searchFlightsRepository) FindByHashKey(ctx context.Context, hashKey string, rawProviders []enum.FlightProvider, version enum.SearchVersion, expiredAtGt int64) ([]*domain.SearchFlightsCachedRecord, error) {
	result := []*models.SearchFlights{}

	filter := bson.M{
		"hash_key": hashKey,
		"expired_at": bson.M{
			"$gt": expiredAtGt,
		},
	}

	sort := bson.D{
		{Key: "index", Value: 1},
	}

	if len(rawProviders) > 0 {
		providers := make([]string, 0, len(rawProviders))

		for _, item := range rawProviders {
			providers = append(providers, enum.FlightProviderName[item])
		}

		filter["provider"] = bson.M{
			"$in": providers,
		}
	}

	if version != enum.SearchVersionNone {
		filter["search_version"] = version
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchFlightsIndexHashKey),
		mongodb.WithSorter(sort),
	}

	err := r.db.Find(ctx, searchFlightsColName, &result, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "db.Find")
	}

	out, err := converts.ToDomainSearchFlightsCachedRecords(result)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainSearchFlightsCachedRecords")
	}

	return out, nil
}
