package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	l2bColName              = "l2b"
	l2bIndexOfficeMonthYear = "__office_id__month__year__"
)

type L2bRepository interface {
	InsertMany(ctx context.Context, items []*domain.L2bTrackingItem) error
	GetL2bMTDInfo(ctx context.Context, req *domain.GetL2bMTDInfoRepoReq) (*domain.L2bMTDInfo, error)
}

type l2bRepository struct {
	db mongodb.DB
}

func NewL2bRepository(db mongodb.DB) L2bRepository {
	return &l2bRepository{db}
}

func (r *l2bRepository) GetL2bMTDInfo(ctx context.Context, req *domain.GetL2bMTDInfoRepoReq) (*domain.L2bMTDInfo, error) {

	otps := []*options.AggregateOptions{
		{
			Hint: l2bIndexOfficeMonthYear,
		},
	}

	pipeline := mongo.Pipeline{
		{
			{
				Key: "$facet",
				Value: bson.D{
					{Key: "total_request", Value: bson.A{
						bson.M{"$match": bson.M{
							"$and": bson.A{
								bson.M{"office_id": req.OfficeID},
								bson.M{"month": req.Month},
								bson.M{"year": req.Year},
							},
						}},
						bson.M{
							"$count": "count",
						},
					}},
					{Key: "total_issued", Value: bson.A{
						bson.M{"$match": bson.M{
							"$and": bson.A{
								bson.M{"office_id": req.OfficeID},
								bson.M{"month": req.Month},
								bson.M{"year": req.Year},
								bson.M{"api_name": enum.L2bAPIName[enum.L2bAPIIssueTicket]},
							},
						}},
						bson.M{
							"$count": "count",
						},
					}},
				},
			},
		},
		{
			{
				Key: "$unwind",
				Value: bson.M{
					"path":                       "$total_request",
					"preserveNullAndEmptyArrays": true,
				},
			},
		},
		{
			{
				Key: "$unwind",
				Value: bson.M{
					"path":                       "$total_issued",
					"preserveNullAndEmptyArrays": true,
				},
			},
		},
		{
			{
				Key: "$addFields",
				Value: bson.M{
					"total_request": "$total_request.count",
					"total_issued":  "$total_issued.count",
				},
			},
		},
	}

	cur, err := r.db.Aggregate(ctx, l2bColName, pipeline, otps...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Aggregate")
	}
	defer cur.Close(ctx)

	rs := &models.L2bMTDInfo{}

	ok := cur.Next(ctx)
	if !ok {
		return converts.ToDomainL2bMTDInfo(rs), nil
	}

	if err := cur.Decode(&rs); err != nil {
		return nil, err
	}

	return converts.ToDomainL2bMTDInfo(rs), nil
}

func (r *l2bRepository) InsertMany(ctx context.Context, items []*domain.L2bTrackingItem) error {
	request := make([]mongo.WriteModel, 0, len(items))
	for _, item := range items {
		m := converts.FromDomainL2bTrackingItem(item)

		request = append(request, mongo.NewInsertOneModel().SetDocument(m))
	}

	err := r.db.BulkWriteRaw(ctx, l2bColName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}
