package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const supplierRoutesCol = "supplier_routes"
const (
	SupplierRoutesDefaultIndex          = "_id_"
	SupplierAirportRoutesIndex          = "__airport_routes__"
	SupplierRoutesProviderSupplierIndex = "__provider_supplier__"
)

type SupplierRouteRepository interface {
	UpsertMany(ctx context.Context, reqs []*domain.SupplierRoute) error
	FindByRoute(ctx context.Context, route string) ([]*domain.SupplierRoute, error)
	UpdateSoftDeleteByProvider(ctx context.Context, provider enum.FlightProvider) error
	DeleteByProvider(ctx context.Context, provider enum.FlightProvider) error
}

type supplierRouteRepository struct {
	db mongodb.DB
}

func NewSupplierRouteRepository(db mongodb.DB) SupplierRouteRepository {
	return &supplierRouteRepository{db}
}

func (tf *supplierRouteRepository) UpsertMany(ctx context.Context, reqs []*domain.SupplierRoute) error {
	request := make([]mongo.WriteModel, 0, len(reqs))

	for _, req := range reqs {
		m := converts.FromDomainSupplierRoute(req)
		m.SoftDelete = false
		m.BeforeUpdate()

		filter := bson.M{
			"provider": m.Provider,
			"supplier": m.Supplier,
		}

		update := bson.M{
			"$set": m,
		}

		request = append(request, mongo.NewUpdateOneModel().SetUpdate(update).SetFilter(filter).SetHint(SupplierRoutesProviderSupplierIndex).SetUpsert(true))
	}

	if len(request) == 0 {
		return nil
	}

	err := tf.db.BulkWriteRaw(ctx, supplierRoutesCol, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (tf *supplierRouteRepository) UpdateSoftDeleteByProvider(ctx context.Context, provider enum.FlightProvider) error {
	filter := bson.M{
		"provider": provider,
	}

	update := bson.M{
		"soft_delete": true,
	}

	err := tf.db.UpdateMany(ctx,
		supplierRoutesCol,
		filter,
		update,
		&options.UpdateOptions{Hint: SupplierAirportRoutesIndex},
	)

	if err != nil {
		return errors.Wrap(err, "UpdateSoftDeleteByProvider")
	}

	return nil
}

func (tf *supplierRouteRepository) DeleteByProvider(ctx context.Context, provider enum.FlightProvider) error {
	filter := bson.M{
		"provider":    provider,
		"soft_delete": true,
	}

	err := tf.db.Delete(
		ctx,
		supplierRoutesCol,
		filter,
		&options.DeleteOptions{Hint: SupplierRoutesDefaultIndex},
	)

	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}

		return errors.Wrap(err, "DeleteByProvider")
	}

	return nil
}

func (tf *supplierRouteRepository) FindByRoute(ctx context.Context, route string) ([]*domain.SupplierRoute, error) {
	var res []*models.SupplierRoute
	filter := bson.M{
		"airport_routes": bson.M{"$in": []string{route}},
		"soft_delete":    false,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(SupplierAirportRoutesIndex),
	}

	err := tf.db.Find(ctx,
		supplierRoutesCol,
		&res,
		options...,
	)

	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	return converts.ToDomainSupplierRoutes(res), nil
}
