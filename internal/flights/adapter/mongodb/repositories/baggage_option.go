package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const (
	baggageOptionCollectionName string = "baggage_option"
)

type BaggageOptionRepository interface {
	UpsertMany(ctx context.Context, items []*domain.BaggageOptionCache) error
	FindByItineraryId(ctx context.Context, flightID, itiID string, expiredAtGt int64) (*domain.BaggageOptionCache, error)
}

type baggageOptionRepository struct {
	db mongodb.DB
}

func NewBaggageOptionRepository(db mongodb.DB) BaggageOptionRepository {
	return &baggageOptionRepository{db}
}

func (r *baggageOptionRepository) UpsertMany(ctx context.Context, items []*domain.BaggageOptionCache) error {
	request := make([]mongo.WriteModel, 0, len(items))
	for _, baggageOptionCache := range items {
		m := converts.FromDomainBaggageOptionCache(baggageOptionCache)
		m.BeforeUpdate()

		filter := bson.M{
			"itinerary_id": m.ItineraryID,
			"flight_id":    m.FlightID,
		}

		update := bson.M{
			"$set": m,
		}

		request = append(request, mongo.NewUpdateOneModel().SetUpdate(update).SetFilter(filter).SetUpsert(true).SetHint(models.BaggageOptionItineraryIDIndexKey))
	}

	if len(request) == 0 {
		return nil
	}

	err := r.db.BulkWriteRaw(ctx, baggageOptionCollectionName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}

func (r *baggageOptionRepository) FindByItineraryId(ctx context.Context, flightID, itiID string, expiredAtGt int64) (*domain.BaggageOptionCache, error) {
	var result *models.BaggageOptionCache

	filter := bson.M{
		"itinerary_id": itiID,
		"flight_id":    flightID,
		"expired_at": bson.M{
			"$gt": expiredAtGt,
		},
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.BaggageOptionItineraryIDIndexKey),
	}

	err := r.db.FindOne(ctx, baggageOptionCollectionName, &result, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainBaggageOptionCache(result), nil
}
