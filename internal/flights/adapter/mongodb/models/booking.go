package models

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

const (
	BookingIndexSessionID = "__session_id__"
)

type BookingSession struct {
	Base                `bson:",inline"`
	SessionID           string                `bson:"session_id"`
	OrderID             string                `bson:"order_id"`
	Itineraries         []*FlightItinerary    `bson:"itineraries"`
	FareDataRaw         *TotalFareInfo        `bson:"fare_data_raw,omitempty"`
	FareData            *TotalFareInfo        `bson:"fare_data"`
	FareDataCfRaw       *TotalFareInfo        `bson:"fare_data_cf_raw,omitempty"`
	FareDataCf          *TotalFareInfo        `bson:"fare_data_cf"`
	FareDataIss         *TotalFareInfo        `bson:"fare_data_iss"`
	PassengerInfo       *PaxRequest           `bson:"passenger_info"`
	Provider            string                `bson:"flight_provider"`
	OfficeID            string                `bson:"office_id"`
	FlightType          enum.FlightType       `bson:"flight_type,omitempty"`
	BookingCode         string                `bson:"booking_code,omitempty"`
	BookingRef          string                `bson:"booking_ref,omitempty"`
	ReservationCode     string                `bson:"reservation_code,omitempty"`
	LastTicketingDate   int64                 `bson:"last_ticketing_date"`
	FareExpiredDate     int64                 `bson:"fare_expired_date"`
	TicketExpiredDate   int64                 `bson:"ticket_expired_date"`
	Status              enum.BookingStatus    `bson:"status"`
	SearchRequest       *SearchFlightsRequest `bson:"search_request"`
	SearchKey           string                `bson:"search_key"`
	FlightID            string                `bson:"flight_id"`
	LastTransactionID   string                `bson:"last_transaction_id,omitempty"`
	TicketStatus        enum.TicketStatus     `bson:"ticket_status,omitempty"`
	EMDStatus           enum.EMDStatus        `bson:"emd_status,omitempty"`
	PendingDeadline     int64                 `bson:"pending_deadline,omitempty"`
	OriginFareData      *TotalFareInfo        `bson:"origin_fare_data,omitempty"`
	ExpectedPrice       *ExpectedPrice        `bson:"expected_price,omitempty"`
	EndUserIPAddress    string                `bson:"end_user_ip"`
	EndUserBrowserAgent string                `bson:"end_user_browser_agent"`
	EndUserCountryCode  string                `bson:"end_user_country_code"`
	CommissionRate      *float64              `bson:"commission_rate"`
	AirlineSystem       string                `bson:"airline_system,omitempty"`
	VAT                 bool                  `bson:"vat"`
	IsTransferred       bool                  `bson:"is_transferred"`
	Confirmed           bool                  `bson:"confirmed"`
	Notified            bool                  `bson:"notified"`
	ManualIssuing       bool                  `bson:"manual_issuing"`
	OrderNumRef         string                `bson:"order_num_ref,omitempty"`
	InternalBooking     bool                  `bson:"internal_booking"`
	IsVJ24h             bool                  `bson:"is_vj_24"`
	Metadata            []*Metadata           `bson:"metadata,omitempty"`
}

type TotalFareInfo struct {
	TotalPaxFares       []*ItineraryPaxFare `bson:"total_pax_fares,omitempty"`
	BaseTotalFareAmount float64             `bson:"base_total_fare_amount"`
	TotalFareAmount     float64             `bson:"total_fare_amount"`
	TotalFareBasic      float64             `bson:"total_fare_basic"`
	TotalTaxAmount      float64             `bson:"total_tax_amount"`
	TotalSeatAmount     float64             `bson:"total_seat_amount"`
	TotalBaggageAmount  float64             `bson:"total_baggage_amount"`
	Currency            string              `bson:"currency"`
	ConfirmedAt         int64               `bson:"confirmed_at,omitempty"`
	PaxFareInfos        []*PaxFareInfo      `bson:"pax_fare_infos,omitempty"`
	ExchangeRate        float64             `bson:"exchange_rate,omitempty"`
	PricingTracking     *PricingTracking    `bson:"pricing_tracking,omitempty"`
}

// PricingTracking struct for MongoDB - matches domain.PricingTracking
type PricingTracking struct {
	ItineraryPricingResults []*ItineraryPricingResult `bson:"itinerary_pricing_results,omitempty"`
	PassengerResults        []*PassengerPricingResult `bson:"passenger_results,omitempty"`
	OriginalTotalFareAmount float64                   `bson:"original_total_fare_amount"`
	FinalTotalFareAmount    float64                   `bson:"final_total_fare_amount"`
	OriginalTotalFareBasic  float64                   `bson:"original_total_fare_basic"`
	FinalTotalFareBasic     float64                   `bson:"final_total_fare_basic"`
	TotalHiddenFeeAmount    float64                   `bson:"total_hidden_fee_amount"`
	TotalDiscountAmount     float64                   `bson:"total_discount_amount"`
}

type ItineraryPricingResult struct {
	ItineraryID      string                 `bson:"itinerary_id"`
	CarrierMarketing string                 `bson:"carrier_marketing"`
	DepartCountry    string                 `bson:"depart_country"`
	ArrivalCountry   string                 `bson:"arrival_country"`
	BookingClass     string                 `bson:"booking_class"`
	CabinClass       string                 `bson:"cabin_class"`
	Route            string                 `bson:"route"`
	HiddenFeeConfig  *FlightHiddenFeeConfig `bson:"hidden_fee_config,omitempty"`
	DiscountConfig   *FlightDiscountConfig  `bson:"discount_config,omitempty"`
}

// PassengerPricingResult maps to pricePb.PassengerPricingResult
type PassengerPricingResult struct {
	PassengerType      enum.PaxType `bson:"passenger_type"`
	Quantity           uint32       `bson:"quantity"`
	OriginalFareAmount float64      `bson:"original_fare_amount"`
	OriginalFareBasic  float64      `bson:"original_fare_basic"`
	TaxAmount          float64      `bson:"tax_amount"`
	FinalFareAmount    float64      `bson:"final_fare_amount"`
	FinalFareBasic     float64      `bson:"final_fare_basic"`
	HiddenFeeAmount    float64      `bson:"hidden_fee_amount"`
	DiscountAmount     float64      `bson:"discount_amount"`
}

type FlightHiddenFeeConfig struct {
	ID           string  `bson:"id"`
	Type         string  `bson:"type"`
	Amount       float64 `bson:"amount"`
	VAT          float64 `bson:"vat"`
	AirlineCode  string  `bson:"airline_code"`
	Route        string  `bson:"route"`
	BookingClass string  `bson:"booking_class"`
	Percent      float64 `bson:"percent"`
	Provider     string  `bson:"provider"`
}

// FlightDiscountConfig maps to pricePb.FlightDiscountConfig
type FlightDiscountConfig struct {
	ID           string  `bson:"id"`
	Type         string  `bson:"type"`
	Amount       float64 `bson:"amount"`
	VAT          float64 `bson:"vat"`
	AirlineCode  string  `bson:"airline_code"`
	Route        string  `bson:"route"`
	BookingClass string  `bson:"booking_class"`
	Percent      float64 `bson:"percent"`
	Provider     string  `bson:"provider"`
}

type ExpectedPrice struct {
	Amount   float64 `bson:"amount"`
	Currency string  `bson:"currency"`
}

type PaxFareInfo struct {
	PaxID              int                     `bson:"pax_id"`
	PaxType            enum.PaxType            `bson:"pax_type"`
	PaxFares           []*PaxFareReport        `bson:"pax_fares"`
	Seat               []*PassengerSeatInfo    `bson:"seats"`
	Baggages           []*PassengerBaggageInfo `bson:"baggages"`
	TotalSeatAmount    float64                 `bson:"total_seat_amount"`
	TotalBaggageAmount float64                 `bson:"total_baggage_amount"`
	TotalPrice         float64                 `bson:"total_price"`
	Currency           string                  `bson:"currency"`
}

type PaxFareReport struct {
	ItineraryIDs []string      `bson:"itinerary_ids"`
	Route        string        `bson:"route"`
	FareAmount   float64       `bson:"fare_amount"`
	FareBasic    float64       `bson:"fare_basic"`
	TaxAmount    float64       `bson:"tax_amount"`
	Currency     string        `bson:"currency"`
	DetailFares  []*DetailFare `bson:"detail_fares"`
}

type DetailFare struct {
	Type                 string       `bson:"type"`
	TypeDescription      string       `bson:"type_description"`
	SurchargeCode        string       `bson:"surcharge_code"`
	SurchargeDescription string       `bson:"surcharge_description"`
	BaseAmount           float64      `bson:"base_amount"`
	DiscountAmount       float64      `bson:"discount_amount"`
	TaxAmount            float64      `bson:"tax_amount"`
	TaxDetails           []*TaxDetail `bson:"tax_details"`
	TotalAmount          float64      `bson:"total_amount"`
	Currency             string       `bson:"currency"`
}
type TaxDetail struct {
	Name   string  `bson:"name"`
	Amount float64 `bson:"amount"`
}
