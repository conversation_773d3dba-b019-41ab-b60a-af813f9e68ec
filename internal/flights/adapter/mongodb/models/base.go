package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Base struct {
	ID        primitive.ObjectID `bson:"_id,omitempty"`
	CreatedAt int64              `bson:"created_at,omitempty"`
	UpdatedAt int64              `bson:"updated_at,omitempty"`
	DeletedAt int64              `bson:"deleted_at,omitempty"`
	CreatedBy string             `bson:"created_by,omitempty"`
	UpdatedBy string             `bson:"updated_by,omitempty"`
	DeletedBy string             `bson:"deleted_by,omitempty"`
}

func (b *Base) BeforeCreate() {

	if b.ID.IsZero() {
		b.ID = primitive.NewObjectID()
	}
	b.CreatedAt = time.Now().UnixMilli()
	b.UpdatedAt = time.Now().UnixMilli()
}

func (b *Base) BeforeUpdate() {
	b.UpdatedAt = time.Now().UnixMilli()
}
