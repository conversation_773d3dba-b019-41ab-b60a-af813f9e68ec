package models

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

// Index
const (
	BaggageOptionItineraryIDIndexKey = "__itinerary_id__expired_at__"
)

// Free Baggage
type BaggageInfo struct {
	Name          string       `bson:"name,omitempty"`
	Code          string       `bson:"code,omitempty"`
	Price         float64      `bson:"price,omitempty"`
	Currency      string       `bson:"currency,omitempty"`
	IsHandBaggage bool         `bson:"is_hand_baggage,omitempty"`
	Quantity      int64        `bson:"quantity,omitempty"`
	PaxType       enum.PaxType `bson:"pax_type"`
}

// BaggageOptons
type BaggageOptionCache struct {
	Base           `bson:",inline"`
	FlightID       string           `bson:"flight_id"`
	ItineraryID    string           `bson:"itinerary_id"`
	BaggageOptions []*BaggageOption `bson:"baggage_options"`
	ExpiredAt      int64            `bson:"expired_at"`
}

type BaggageOptionDetail struct {
	Type      enum.BaggageType `bson:"type"`
	SubCode   string           `bson:"sub_code,omitempty"`
	Quantity  int              `bson:"quantity"`
	Weight    int              `bson:"weight,omitempty"`
	MaxWeight int              `bson:"max_weight,omitempty"`
	Unit      string           `bson:"unit,omitempty"`
	Dimension string           `bson:"dimension,omitempty"`
}

type BaggageOption struct {
	OptionID           string                 `bson:"option_id"`
	OfferData          *OfferData             `bson:"offer_data"`
	ItineraryIndex     int                    `bson:"itinerary_index"`
	SegmentIndex       []int                  `bson:"segment_index"`
	BaggageInfo        []*BaggageOptionDetail `bson:"baggage_info"`
	TotalWeight        int                    `bson:"total_weight"`
	Unit               string                 `bson:"unit"`
	TotalBaggageCharge *BaggageCharge         `bson:"total_baggage_charge"`
}

type BaggageCharge struct {
	BaseAmount  float64 `bson:"base_amount"`
	TaxAmount   float64 `bson:"tax_amount"`
	TotalAmount float64 `bson:"total_amount"`
	Currency    string  `bson:"currency"`
}

type OfferData struct {
	TFOfferData  *TFOfferData      `bson:"tf_offer_data,omitempty"`
	VNAOfferData *VNAOfferData     `bson:"vna_offer_data,omitempty"`
	AMAOfferData *AmadeusOfferData `bson:"ama_offer_data,omitempty"`
	HNHOfferData *HNHOfferData     `bson:"hnh_offer_data,omitempty"`
}

type VNAOfferData struct {
	OfferID           string `bson:"offer_id,omitempty"`
	SubCode           string `bson:"sub_code,omitempty"`
	SSRCode           string `bson:"ssr_code,omitempty"`
	OwningCarrierCode string `bson:"owning_carrier_code,omitempty"`
	Vendor            string `bson:"vendor,omitempty"`
	CommercialName    string `bson:"commercial_name,omitempty"`
	Group             string `bson:"group,omitempty"`
	RFICode           string `bson:"rfi_code,omitempty"`
}

type TFOfferData struct {
	ParameterName  string `bson:"parameter_name,omitempty"`
	ParameterValue string `bson:"parameter_value,omitempty"`
}

type AmadeusOfferData struct {
	RFICode        string                     `bson:"rfi_code,omitempty"`
	RFISCode       string                     `bson:"rfis_code,omitempty"`
	ProviderCode   string                     `bson:"provider_code,omitempty"`
	CustomerRefIDs []int                      `bson:"customer_ref_ids,omitempty"`
	Parameters     []*AmadeusBaggageParameter `bson:"parameters,omitempty"`
}

type HNHOfferData struct {
	Airline    string  `bson:"airline,omitempty"`
	Value      string  `bson:"value,omitempty"`
	Code       string  `bson:"code,omitempty"`
	Currency   string  `bson:"currency,omitempty"`
	Route      string  `bson:"route,omitempty"`
	StartPoint string  `bson:"start_point,omitempty"`
	EndPoint   string  `bson:"end_point,omitempty"`
	Leg        int32   `bson:"leg,omitempty"`
	Price      float64 `bson:"price,omitempty"`
}

type AmadeusBaggageParameter struct {
	Name  string `bson:"name,omitempty"`
	Value string `bson:"value,omitempty"`
}
