package models

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

type PNR struct {
	Base        `bson:",inline"`
	SessionID   string     `bson:"session_id"`
	ListPax     []*PaxInfo `bson:"list_pax"`
	ContactInfo *Contact   `bson:"contact_info"`
}

type PaxInfo struct {
	Type        enum.PaxType          `bson:"type"`
	ID          int                   `bson:"id"`
	Surname     string                `bson:"surname"`
	GivenName   string                `bson:"given_name"`
	DOB         *int64                `bson:"dob,omitempty"`
	Age         int                   `bson:"age"`
	Passport    *PaxPassport          `bson:"passport,omitempty"`
	PhoneCode   string                `bson:"phone_code"`
	Phone       string                `bson:"phone"`
	Email       string                `bson:"email"`
	Gender      commonEnum.GenderType `bson:"gender"`
	Nationality string                `bson:"nationality"`
}

type PaxPassport struct {
	Number         string `bson:"number"`
	ExpiryDate     int64  `bson:"expiry_date"`
	IssuingCountry string `bson:"issuing_country"`
}

type Contact struct {
	Surname   string                `bson:"surname"`
	GivenName string                `bson:"given_name"`
	PhoneCode string                `bson:"phone_code"`
	Phone     string                `bson:"phone"`
	Email     string                `bson:"email"`
	Gender    commonEnum.GenderType `bson:"gender"`
}
