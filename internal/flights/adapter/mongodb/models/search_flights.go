package models

import (
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SearchFlights struct {
	Base       `bson:",inline"`
	Index      int                   `bson:"index"`
	Key        string                `bson:"key"`
	HashKey    string                `bson:"hash_key"`
	Provider   string                `bson:"provider"`
	ExpiredAt  int64                 `bson:"expired_at"`
	Request    *SearchFlightsRequest `bson:"request"`
	FlightType enum.FlightType       `bson:"flight_type"`
	Version    enum.SearchVersion    `bson:"search_version"`
}

type SearchFlightsRequest struct {
	Itineraries []*ItineraryRequest `bson:"itineraries"`
	Passengers  PaxRequest          `bson:"passengers"`
}

type PaxRequest struct {
	ADT int `bson:"adt"`
	CHD int `bson:"chd"`
	INF int `bson:"inf"`
}

type ItineraryRequest struct {
	DepartPlace  string `bson:"depart_place"`
	DepartDate   int64  `bson:"depart_date"`
	ArrivalPlace string `bson:"arrival_place"`
}

type ResponseFlight struct {
	Base                  `bson:",inline"`
	RecordID              primitive.ObjectID    `bson:"record_id"`
	FlightID              string                `bson:"flight_id"`
	Index                 int                   `bson:"index"`
	Itineraries           []*FlightItinerary    `bson:"itineraries"`
	TotalPaxFares         []*ItineraryPaxFare   `bson:"total_pax_fares,omitempty"`
	TotalFareAmount       float64               `bson:"total_fare_amount"`
	BaseTotalFareAmount   float64               `bson:"base_total_fare_amount"`
	TotalFareBasic        float64               `bson:"total_fare_basic"`
	TotalTaxAmount        float64               `bson:"total_tax_amount"`
	Currency              string                `bson:"currency"`
	AirlineSystem         string                `bson:"airline_system,omitempty"`
	ExpiredAt             int64                 `bson:"expired_at"`
	TotalFareInfoSnapshot *TotalFareInfo        `bson:"total_fare_info_snapshot,omitempty"`
	Metadata              []*Metadata           `bson:"metadata,omitempty"`
	SoldOut               bool                  `bson:"sold_out"`
	VAT                   bool                  `bson:"vat"`
	MiniRules             []*MiniRule           `bson:"mini_rules"`
	Leg                   int                   `bson:"leg,omitempty"`
	OptionType            enum.FlightOptionType `bson:"option_type"`
	GroupID               string                `bson:"group_id,omitempty"`
}

type FlightItinerary struct {
	ID                 string              `bson:"id"`
	Index              int                 `bson:"index"`
	Key                string              `bson:"key"`
	ProviderBookingKey string              `bson:"provider_booking_key,omitempty"`
	StopNumber         int                 `bson:"stop_number"`
	CabinClass         string              `bson:"cabin_class"`
	BookingClass       string              `bson:"booking_class"`
	CabinClassCode     string              `bson:"cabin_code,omitempty"`
	FareBasis          string              `bson:"fare_basis,omitempty"`
	Availability       int                 `bson:"availability,omitempty"`
	DepartPlace        string              `bson:"depart_place"`
	DepartDate         int64               `bson:"depart_date"`
	DepartDateUTC      time.Time           `bson:"depart_date_utc"` // MongoDB datetime query filter
	DepartDt           string              `bson:"depart_dt,omitempty"`
	ArrivalDt          string              `bson:"arrival_dt,omitempty"`
	ArrivalPlace       string              `bson:"arrival_place"`
	ArrivalDate        int64               `bson:"arrival_date"`
	CarrierMarketing   string              `bson:"carrier_marketing"`
	CarrierOperator    string              `bson:"carrier_operator"`
	FlightNumber       string              `bson:"flight_number"`
	FlightDuration     int                 `bson:"flight_duration"`
	FareAmount         float64             `bson:"fare_amount,omitempty"`
	FareBasic          float64             `bson:"fare_basic,omitempty"`
	TaxAmount          float64             `bson:"tax_amount,omitempty"`
	Currency           string              `bson:"currency,omitempty"`
	FreeBaggage        []*BaggageInfo      `bson:"free_baggage,omitempty"`
	PaxFares           []*ItineraryPaxFare `bson:"pax_fares,omitempty"`
	Segments           []*ItinerarySegment `bson:"segments"`
	ArrivalCountry     string              `bson:"arrival_country"`
	DepartCountry      string              `bson:"depart_country"`
	PaxInfo            []*ItineraryPax     `bson:"pax_info,omitempty"`
	ReservationCode    string              `bson:"reservation_code,omitempty"`
	FareType           enum.FareType       `bson:"fare_type,omitempty"`
	FlightID           string              `bson:"flight_id,omitempty"`
}

type PassengerBaggageInfo struct {
	EMDNumber   string         `bson:"emd_number,omitempty"`
	BaggageInfo *BaggageOption `bson:"baggage_info,omitempty"`
}

type ItineraryPax struct {
	PaxID        int                     `bson:"pax_id"`
	TicketNumber string                  `bson:"ticket_number,omitempty"`
	Seats        []*PassengerSeatInfo    `bson:"seat,omitempty"`
	Baggages     []*PassengerBaggageInfo `bson:"baggages,omitempty"`
}

type ItineraryPaxFare struct {
	PaxType    enum.PaxType `bson:"pax_type"`
	FareAmount float64      `bson:"fare_amount"`
	FareBasic  float64      `bson:"fare_basic"`
	TaxAmount  float64      `bson:"tax_amount"`
	Currency   string       `bson:"currency"`
}

type ItinerarySegment struct {
	ItineraryID      string `bson:"itinerary_id"`
	Key              string `bson:"key"`
	Index            int    `bson:"index"`
	FareBasis        string `bson:"fare_basis,omitempty"`
	DepartPlace      string `bson:"depart_place"`
	DepartDate       int64  `bson:"depart_date"`
	DepartDt         string `bson:"depart_dt,omitempty"`
	ArrivalDt        string `bson:"arrival_dt,omitempty"`
	ArrivalPlace     string `bson:"arrival_place"`
	ArrivalDate      int64  `bson:"arrival_date"`
	CarrierMarketing string `bson:"carrier_marketing"`
	CarrierOperator  string `bson:"carrier_operator"`
	FlightNumber     string `bson:"flight_number"`
	Aircraft         string `bson:"aircraft,omitempty"`
	FlightDuration   int    `bson:"flight_duration"`
	BookingClass     string `bson:"booking_class"`
	CabinClassCode   string `bson:"cabin_class_code,omitempty"`
	DepartTerminal   string `bson:"depart_terminal,omitempty"`
	ArrivalTerminal  string `bson:"arrival_terminal,omitempty"`
	Availability     int    `bson:"availability,omitempty"`
	CabinClass       string `bson:"cabin_class,omitempty"`
}

type PassengerSeatInfo struct {
	EMDNumber    string        `bson:"emd_number,omitempty"`
	SegmentIndex int           `bson:"segment_index"`
	RowNumber    string        `json:"row_number"`
	SeatFacility *SeatFacility `json:"seat_facility"`
}
