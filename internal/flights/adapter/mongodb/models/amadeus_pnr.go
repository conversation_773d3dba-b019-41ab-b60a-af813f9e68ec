package models

type AmadeusPNR struct {
	Base                   `bson:",inline"`
	SessionID              string         `bson:"session_id"`
	RecordLocator          string         `bson:"record_locator"`
	LastTktDate            int64          `bson:"last_tkt_date"`
	SkipDefaultLastTktDate bool           `bson:"skip_default_last_tkt_date"`
	ExpectedPrice          *ExpectedPrice `bson:"expected_price,omitempty"`
	CommRate               *float64       `bson:"comm_rate"`
	TSMValues              []int          `bson:"tsm_values"`
	FareExpiredDate        int64          `bson:"fare_expired_date"`
	TicketExpiredDate      int64          `bson:"ticket_expired_date"`
}
