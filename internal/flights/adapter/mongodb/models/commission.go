package models

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type Commission struct {
	DCPKey         string         `bson:"dcp_key"`
	Airline        string         `bson:"airline,omitempty"`
	DepartCountry  string         `bson:"depart_country,omitempty"`
	ArrivalCountry string         `bson:"arrival_country,omitempty"`
	DepartDtFrom   int64          `bson:"depart_dt_from,omitempty"`
	DepartDtTo     int64          `bson:"depart_dt_to,omitempty"`
	BookingClass   string         `bson:"booking_class,omitempty"`
	DepartPlace    string         `bson:"depart_place,omitempty"`
	ArrivalPlace   string         `bson:"arrival_place,omitempty"`
	FareType       *enum.FareType `bson:"fare_type,omitempty"`
	Rate           float64        `bson:"rate,omitempty"`
}
