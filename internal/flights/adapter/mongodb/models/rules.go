package models

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type MnrDetail struct {
	MiniRules []*MiniRule `bson:"mini_rules"`
}

type MiniRule struct {
	PaxType      enum.PaxType       `bson:"pax_type"`
	OldSegments  []int              `bson:"segments,omitempty"`
	Segments     []*MiniRuleSegment `bson:"new_segments"`
	PenaltyRules []*PenaltyRule     `bson:"penalty_rules"`
	PenaltyText  string             `bson:"penalty_text"`
}

type MiniRuleSegment struct {
	ItineraryIndex int `bson:"itinerary_index"`
	SegmentIndex   int `bson:"segment_index"`
}

type PenaltyRule struct {
	PenaltyType    enum.PenaltyType `bson:"penalty_type"`
	IsPermitted    enum.Permitted   `bson:"is_permitted"`
	Situation      enum.Situation   `bson:"situation"`
	Amount         *float64         `bson:"amount"`
	Percent        *float64         `bson:"percent"`
	BaseType       enum.BaseType    `bson:"base_type"`
	Currency       string           `bson:"currency"`
	NoShowTime     string           `bson:"no_show_time"`
	NoShowTimeUnit string           `bson:"no_show_time_unit"`
}

type CategDescrType struct {
	DescriptionInfo  *CategoryDescriptionType `bson:"description_info"`
	ProcessIndicator *string                  `bson:"process_indicator"`
}

type CategoryDescriptionType struct {
	Number *string `bson:"number"`
	Code   *string `bson:"code"`
}

type MonetaryInformationType struct {
	MonetaryDetails      []*MonetaryInformationDetailsType `bson:"monetary_details"`
	OtherMonetaryDetails *MonetaryInformationDetailsType   `bson:"other_monetary_details"`
}

type MonetaryInformationDetailsType struct {
	TypeQualifier string `bson:"type"`
	Amount        string `bson:"amount"`
}

type ItemNumberType struct {
	ItemNumberDetails *ItemNumberIdentificationType `bson:"-"`
}

type ItemNumberIdentificationType struct {
	Number string `bson:"number"`
}

type StatusType struct {
	StatusInformation []*StatusDetailsType `bson:"status_information"`
}

type StatusDetailsType struct {
	Indicator string `bson:"indicator"`
	Action    string `bson:"action"`
}
