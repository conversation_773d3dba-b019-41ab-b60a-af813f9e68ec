package models

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

const (
	SeatMapIndexKey = "__key__expired_at__"
)

type ColumnHeader struct {
	Title   string `bson:"title"`
	IsAisle bool   `bson:"is_aisle"`
}
type SeatOption struct {
	CabinClass string          `bson:"cabin_class"`
	Rows       []*SeatRow      `bson:"rows"`
	Columns    []*ColumnHeader `bson:"column_headers"`
}

type SeatRow struct {
	RowNumber  string          `bson:"row_number"`
	Facilities []*SeatFacility `bson:"facilities"`
}

type SeatFacility struct {
	Type         enum.SeatType   `bson:"type"`
	SeatCode     string          `bson:"seat_code"`
	Availability enum.SeatStatus `bson:"availability"`
	SeatCharge   *SeatCharge     `bson:"seat_charge"`
	Property     *SeatProperty   `bson:"property"`
	SelectionKey string          `bson:"selection_key,omitempty"`
}

type SeatCharge struct {
	BaseAmount  float64 `bson:"base_amount"`
	TaxAmount   float64 `bson:"tax_amount"`
	TotalAmount float64 `bson:"total_amount"`
	Currency    string  `bson:"currency"`
}

type SeatProperty struct {
	Aisle                 bool `bson:"aisle"`
	Window                bool `bson:"window"`
	EmergencyExit         bool `bson:"emergency_exit"`
	OverWing              bool `bson:"over_wing"`
	Bassinet              bool `bson:"bassinet"`
	NotAllowedForInfant   bool `bson:"not_allowed_for_infant"`
	NotSuitableForInfant  bool `bson:"not_suitable_for_infant"`
	NotSuitableForChild   bool `bson:"not_suitable_for_child"`
	HandicappedFacilities bool `bson:"handicapped_facilities"`
	ExtraLegroom          bool `bson:"extra_leg_room"`
}

type SeatSegment struct {
	Base         `bson:",inline"`
	Key          string        `bson:"key"`
	ItineraryID  string        `bson:"itinerary_id"`
	SegmentIndex int           `bson:"segment_index"`
	SeatOptions  []*SeatOption `bson:"seat_option"`
	ExpiredAt    int64         `bson:"expired_at"`
}
