package models

type L2bTrackingItem struct {
	RequestedAt int64  `bson:"requested_at"`
	OfficeID    string `bson:"office_id"`
	Month       int    `bson:"month"`
	Year        int    `bson:"year"`
	APIName     string `bson:"api_name"`
}

type L2bMTDInfo struct {
	MTD          string  `bson:"mtd"`
	OfficeID     string  `bson:"office_id"`
	TotalRequest int64   `bson:"total_request"`
	TotalIssued  int64   `bson:"total_issued"`
	L2bRate      float64 `bson:"l2b_rate"`
}
