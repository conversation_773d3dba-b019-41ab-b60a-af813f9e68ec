package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainAmadeusPNR(in *domain.AmadeusPNR) *models.AmadeusPNR {
	if in == nil {
		return nil
	}

	var ep *models.ExpectedPrice

	if in.ExpectedPrice != nil {
		ep = &models.ExpectedPrice{
			Amount:   in.ExpectedPrice.Amount,
			Currency: in.ExpectedPrice.Currency,
		}
	}

	return &models.AmadeusPNR{
		SessionID:              in.SessionID,
		RecordLocator:          in.RecordLocator,
		LastTktDate:            in.LastTktDate,
		ExpectedPrice:          ep,
		SkipDefaultLastTktDate: in.SkipDefaultLastTktDate,
		CommRate:               in.CommRate,
		TSMValues:              in.TSMValues,
		FareExpiredDate:        in.FareExpiredDate,
		TicketExpiredDate:      in.TicketExpiredDate,
	}
}

func ToDomainAmadeusPNR(in *models.AmadeusPNR) *domain.AmadeusPNR {
	if in == nil {
		return nil
	}

	var ep *domain.ExpectedPrice
	if in.ExpectedPrice != nil {
		ep = &domain.ExpectedPrice{
			Amount:   in.ExpectedPrice.Amount,
			Currency: in.ExpectedPrice.Currency,
		}
	}

	return &domain.AmadeusPNR{
		SessionID:              in.SessionID,
		Created_at:             in.CreatedAt,
		RecordLocator:          in.RecordLocator,
		LastTktDate:            in.LastTktDate,
		SkipDefaultLastTktDate: in.SkipDefaultLastTktDate,
		ExpectedPrice:          ep,
		CommRate:               in.CommRate,
		TSMValues:              in.TSMValues,
		FareExpiredDate:        in.FareExpiredDate,
		TicketExpiredDate:      in.TicketExpiredDate,
	}
}
