package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToDomainBaggageOptionCache(info *models.BaggageOptionCache) *domain.BaggageOptionCache {
	if info == nil {
		return nil
	}

	res := &domain.BaggageOptionCache{
		FlightID:       info.FlightID,
		ItineraryID:    info.ItineraryID,
		BaggageOptions: toDomainBaggageOptions(info.BaggageOptions),
		ExpiredAt:      info.ExpiredAt,
	}

	return res
}

func toDomainBaggageOptions(items []*models.BaggageOption) []*domain.BaggageOption {
	res := make([]*domain.BaggageOption, 0, len(items))
	for _, baggageOption := range items {
		res = append(res, toDomainBaggageOption(baggageOption))
	}

	return res
}

func toDomainBaggageOption(info *models.BaggageOption) *domain.BaggageOption {
	if info == nil {
		return nil
	}

	return &domain.BaggageOption{
		OptionID:           info.OptionID,
		OfferData:          toDomainOfferData(info.OfferData),
		ItineraryIndex:     info.ItineraryIndex,
		SegmentIndex:       info.SegmentIndex,
		BaggageInfo:        toDomainBaggageOptionDetails(info.BaggageInfo),
		TotalWeight:        info.TotalWeight,
		Unit:               info.Unit,
		TotalBaggageCharge: toDomainBaggageCharge(info.TotalBaggageCharge),
	}
}

func toDomainOfferData(info *models.OfferData) *domain.OfferData {
	if info == nil {
		return nil
	}

	return &domain.OfferData{
		TFOfferData:  toDomainTFOfferData(info.TFOfferData),
		VNAOfferData: toDomainVNAOfferData(info.VNAOfferData),
		AMAOfferData: toDomainAMAOfferData(info.AMAOfferData),
		HNHOfferData: toDomainHNHOfferData(info.HNHOfferData),
	}
}
func toDomainTFOfferData(info *models.TFOfferData) *domain.TFOfferData {
	if info == nil {
		return nil
	}

	return &domain.TFOfferData{
		ParameterName:  info.ParameterName,
		ParameterValue: info.ParameterValue,
	}
}

func toDomainHNHOfferData(info *models.HNHOfferData) *domain.HNHOfferData {
	if info == nil {
		return nil
	}

	return &domain.HNHOfferData{
		Airline:    info.Airline,
		Value:      info.Value,
		Code:       info.Code,
		Currency:   info.Currency,
		Route:      info.Route,
		StartPoint: info.StartPoint,
		EndPoint:   info.EndPoint,
		Leg:        info.Leg,
		Price:      info.Price,
	}
}

func toDomainVNAOfferData(info *models.VNAOfferData) *domain.VNAOfferData {
	if info == nil {
		return nil
	}

	return &domain.VNAOfferData{
		OfferID:           info.OfferID,
		SubCode:           info.SubCode,
		SSRCode:           info.SSRCode,
		OwningCarrierCode: info.OwningCarrierCode,
		Vendor:            info.Vendor,
		CommercialName:    info.CommercialName,
		Group:             info.Group,
		RFICode:           info.RFICode,
	}
}

func toDomainAMAOfferData(info *models.AmadeusOfferData) *domain.AmadeusOfferData {
	if info == nil {
		return nil
	}

	return &domain.AmadeusOfferData{
		RFICode:        info.RFICode,
		RFISCode:       info.RFISCode,
		ProviderCode:   info.ProviderCode,
		CustomerRefIDs: info.CustomerRefIDs,
		Parameters:     toDomainAmadeusBaggageParameters(info.Parameters),
	}
}

func toDomainAmadeusBaggageParameter(info *models.AmadeusBaggageParameter) *domain.AmadeusBaggageParameter {
	if info == nil {
		return nil
	}

	return &domain.AmadeusBaggageParameter{
		Name:  info.Name,
		Value: info.Value,
	}
}

func toDomainAmadeusBaggageParameters(items []*models.AmadeusBaggageParameter) []*domain.AmadeusBaggageParameter {
	if len(items) == 0 {
		return nil
	}

	res := make([]*domain.AmadeusBaggageParameter, 0, len(items))
	for _, item := range items {
		res = append(res, toDomainAmadeusBaggageParameter(item))
	}

	return res
}

func toDomainBaggageOptionDetails(items []*models.BaggageOptionDetail) []*domain.BaggageOptionDetail {
	if len(items) == 0 {
		return nil
	}

	res := make([]*domain.BaggageOptionDetail, 0, len(items))
	for _, baggageOptionDetail := range items {
		res = append(res, toDomainBaggageOptionDetail(baggageOptionDetail))
	}

	return res
}

func toDomainBaggageOptionDetail(info *models.BaggageOptionDetail) *domain.BaggageOptionDetail {
	if info == nil {
		return nil
	}

	return &domain.BaggageOptionDetail{
		Type:      info.Type,
		SubCode:   info.SubCode,
		Quantity:  info.Quantity,
		Weight:    info.Weight,
		MaxWeight: info.MaxWeight,
		Unit:      info.Unit,
		Dimension: info.Dimension,
	}
}

func toDomainBaggageCharge(info *models.BaggageCharge) *domain.BaggageCharge {
	if info == nil {
		return nil
	}

	return &domain.BaggageCharge{
		BaseAmount:  info.BaseAmount,
		TaxAmount:   info.TaxAmount,
		TotalAmount: info.TotalAmount,
		Currency:    info.Currency,
	}
}
