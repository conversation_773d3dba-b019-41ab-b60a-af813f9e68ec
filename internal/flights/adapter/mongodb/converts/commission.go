package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToDomainComission(in *models.Commission) *domain.Commission {
	if in == nil {
		return nil
	}

	return &domain.Commission{
		Airline:        in.Airline,
		DepartCountry:  in.DepartCountry,
		ArrivalCountry: in.ArrivalCountry,
		DepartDtFrom:   in.DepartDtFrom,
		DepartDtTo:     in.DepartDtTo,
		BookingClass:   in.BookingClass,
		DepartPlace:    in.DepartPlace,
		ArrivalPlace:   in.ArrivalPlace,
		FareType:       in.FareType,
		Rate:           in.Rate,
	}
}

func ToDomainComissions(ins []*models.Commission) []*domain.Commission {
	out := make([]*domain.Commission, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainComission(item))
	}

	return out
}
