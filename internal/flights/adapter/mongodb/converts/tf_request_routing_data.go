package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainTFRequestRoutingData(in *domain.TFRequestRoutingData) *models.TFRequestRoutingData {
	if in == nil {
		return nil
	}

	return &models.TFRequestRoutingData{
		RoutingID:    in.RoutingID,
		OutwardID:    in.OutwardID,
		ReturnID:     in.ReturnID,
		ItineraryKey: in.ItineraryKey,
		ExpiredAt:    in.ExpiredAt,
		RequestKey:   in.RequestKey,
	}
}

func FromDomainTFRequestRoutingDatas(ins []*domain.TFRequestRoutingData) []*models.TFRequestRoutingData {
	out := make([]*models.TFRequestRoutingData, 0, len(ins))

	for _, item := range ins {
		out = append(out, FromDomainTFRequestRoutingData(item))
	}

	return out
}

func ToDomainTFRequestRoutingData(in *models.TFRequestRoutingData) *domain.TFRequestRoutingData {
	if in == nil {
		return nil
	}

	return &domain.TFRequestRoutingData{
		RoutingID:    in.RoutingID,
		OutwardID:    in.OutwardID,
		ReturnID:     in.ReturnID,
		ItineraryKey: in.ItineraryKey,
		ExpiredAt:    in.ExpiredAt,
		RequestKey:   in.RequestKey,
	}
}

func ToDomainTFRequestRoutingDatas(ins []*models.TFRequestRoutingData) []*domain.TFRequestRoutingData {
	out := make([]*domain.TFRequestRoutingData, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainTFRequestRoutingData(item))
	}

	return out
}
