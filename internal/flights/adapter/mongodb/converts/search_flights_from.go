package converts

import (
	"time"

	"github.com/pkg/errors"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func fromDomainItineraryRequest(in *domain.ItineraryRequest) *models.ItineraryRequest {
	if in == nil {
		return nil
	}

	return &models.ItineraryRequest{
		DepartPlace:  in.DepartPlace,
		DepartDate:   in.DepartDate,
		ArrivalPlace: in.ArrivalPlace,
	}
}

func fromDomainItineraryRequests(ins []*domain.ItineraryRequest) []*models.ItineraryRequest {
	out := make([]*models.ItineraryRequest, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainItineraryRequest(item))
	}

	return out
}

func fromDomainSearchFlightRequest(in *domain.SearchFlightsRequest) *models.SearchFlightsRequest {
	if in == nil {
		return nil
	}

	return &models.SearchFlightsRequest{
		Itineraries: fromDomainItineraryRequests(in.Itineraries),
		Passengers: models.PaxRequest{
			ADT: in.Passengers.ADT,
			CHD: in.Passengers.CHD,
			INF: in.Passengers.INF,
		},
	}
}

func FromDomainSearchFlightsCachedRecord(in *domain.SearchFlightsCachedRecord) (*models.SearchFlights, error) {
	if in == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	objID, err := primitive.ObjectIDFromHex(in.ID)
	if err != nil {
		return nil, errors.Wrap(err, "id ObjectIDFromHex")
	}

	return &models.SearchFlights{
		Base: models.Base{
			ID: objID,
		},
		Index:      in.Index,
		Key:        in.Key,
		HashKey:    in.HashKey,
		Provider:   enum.FlightProviderName[in.Provider],
		ExpiredAt:  in.ExpiredAt,
		Request:    fromDomainSearchFlightRequest(in.Request),
		FlightType: in.FlightType,
		Version:    in.Version,
	}, nil
}

func FromDomainResponseFlights(ins []*domain.ResponseFlight) ([]*models.ResponseFlight, error) {
	out := make([]*models.ResponseFlight, 0, len(ins))

	for _, item := range ins {
		temp, err := FromDomainResponseFlight(item)
		if err != nil {
			return nil, err
		}

		out = append(out, temp)
	}

	return out, nil
}

func FromDomainResponseFlight(in *domain.ResponseFlight) (*models.ResponseFlight, error) {
	if in == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	ities, err := FromDomainFlightItineraries(in.Itineraries)
	if err != nil {
		return nil, err
	}

	recID, err := primitive.ObjectIDFromHex(in.RecordID)
	if err != nil {
		return nil, errors.Wrap(err, "recID ObjectIDFromHex")
	}

	out := &models.ResponseFlight{
		FlightID:            in.FlightID,
		Index:               in.Index,
		Itineraries:         ities,
		TotalPaxFares:       fromDomainPaxFares(in.TotalPaxFares),
		TotalFareAmount:     in.TotalFareAmount,
		BaseTotalFareAmount: in.BaseTotalFareAmount,
		TotalFareBasic:      in.TotalFareBasic,
		TotalTaxAmount:      in.TotalTaxAmount,
		Currency:            in.Currency,
		AirlineSystem:       in.AirlineSystem,
		VAT:                 in.VAT,
		RecordID:            recID,
		ExpiredAt:           in.ExpiredAt,
		Metadata:            FromDomainMetaData(in.Metadata),
		SoldOut:             in.SoldOut,
		MiniRules:           fromDomainMiniRules(in.MiniRules),
		Leg:                 in.Leg,
		OptionType:          in.OptionType,
		GroupID:             in.GroupID,
	}

	if in.TotalFareInfoSnapshot != nil {
		out.TotalFareInfoSnapshot = &models.TotalFareInfo{
			TotalPaxFares:       fromDomainPaxFares(in.TotalFareInfoSnapshot.TotalPaxFares),
			BaseTotalFareAmount: in.TotalFareInfoSnapshot.BaseTotalFareAmount,
			TotalFareAmount:     in.TotalFareInfoSnapshot.TotalFareAmount,
			TotalFareBasic:      in.TotalFareInfoSnapshot.TotalFareBasic,
			TotalTaxAmount:      in.TotalFareInfoSnapshot.TotalTaxAmount,
			Currency:            in.Currency,
		}
	}

	return out, nil
}

func FromDomainFlightItineraries(ins []*domain.FlightItinerary) ([]*models.FlightItinerary, error) {
	out := make([]*models.FlightItinerary, 0, len(ins))

	for _, item := range ins {
		temp, err := fromDomainFlightItinerary(item)
		if err != nil {
			return nil, err
		}

		out = append(out, temp)
	}

	return out, nil
}

func fromDomainItineraryPaxList(ins []*domain.ItineraryPax) []*models.ItineraryPax {
	out := make([]*models.ItineraryPax, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainItineraryPax(item))
	}

	return out
}

func fromDomainItineraryPax(in *domain.ItineraryPax) *models.ItineraryPax {
	if in == nil {
		return nil
	}

	return &models.ItineraryPax{
		PaxID:        in.PaxID,
		TicketNumber: in.TicketNumber,
		Seats:        FromDomainPassengerSeatInfos(in.Seats),
		Baggages:     FromDomainPassengerBaggageInfos(in.Baggages),
	}
}

func FromDomainPassengerBaggageInfos(info []*domain.PassengerBaggageInfo) []*models.PassengerBaggageInfo {
	if len(info) == 0 {
		return nil
	}

	res := make([]*models.PassengerBaggageInfo, len(info))
	for index, passengerBaggageInfo := range info {
		res[index] = fromDomainPassengerBaggageInfo(passengerBaggageInfo)
	}

	return res
}

func fromDomainPassengerBaggageInfo(info *domain.PassengerBaggageInfo) *models.PassengerBaggageInfo {
	if info == nil {
		return nil
	}

	return &models.PassengerBaggageInfo{
		EMDNumber:   info.EMDNumber,
		BaggageInfo: fromDomainBaggageOption(info.BaggageInfo),
	}
}

func fromDomainBaggageInfos(ins []*domain.BaggageInfo) []*models.BaggageInfo {
	out := make([]*models.BaggageInfo, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainBaggageInfo(item))
	}

	return out
}

func fromDomainBaggageInfo(in *domain.BaggageInfo) *models.BaggageInfo {
	if in == nil {
		return nil
	}

	return &models.BaggageInfo{
		Name:          in.Name,
		Code:          in.Code,
		Price:         in.Price,
		Currency:      in.Currency,
		IsHandBaggage: in.IsHandBaggage,
		Quantity:      in.Quantity,
		PaxType:       in.PaxType,
	}
}

func fromDomainFlightItinerary(in *domain.FlightItinerary) (*models.FlightItinerary, error) {
	if in == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	strDepartDt := in.DepartDt.Format(time.RFC3339)
	strArrivalDt := in.ArrivalDt.Format(time.RFC3339)

	return &models.FlightItinerary{
		ID:                 in.ID,
		Key:                in.Key,
		Index:              in.Index,
		ProviderBookingKey: in.ProviderBookingKey,
		StopNumber:         in.StopNumber,
		CabinClass:         in.CabinClass,
		BookingClass:       in.BookingClass,
		FareBasis:          in.FareBasis,
		Availability:       in.Availability,
		DepartPlace:        in.DepartPlace,
		DepartDate:         in.DepartDate,
		ArrivalPlace:       in.ArrivalPlace,
		ArrivalDate:        in.ArrivalDate,
		CarrierMarketing:   in.CarrierMarketing,
		CarrierOperator:    in.CarrierOperator,
		FlightNumber:       in.FlightNumber,
		FlightDuration:     in.FlightDuration,
		FareAmount:         in.FareAmount,
		FareBasic:          in.FareBasic,
		TaxAmount:          in.TaxAmount,
		Currency:           in.Currency,
		FreeBaggage:        fromDomainBaggageInfos(in.FreeBaggage),
		PaxFares:           fromDomainPaxFares(in.PaxFares),
		Segments:           fromDomainSegments(in.Segments),
		PaxInfo:            fromDomainItineraryPaxList(in.PaxInfo),
		DepartDt:           strDepartDt,
		ArrivalDt:          strArrivalDt,
		DepartDateUTC:      in.DepartDt,
		ReservationCode:    in.ReservationCode,
		ArrivalCountry:     in.ArrivalCountry,
		DepartCountry:      in.DepartCountry,
		FareType:           in.FareType,
		CabinClassCode:     in.CabinClassCode,
		FlightID:           in.FlightID,
	}, nil
}

func fromDomainSegments(ins []*domain.ItinerarySegment) []*models.ItinerarySegment {
	out := make([]*models.ItinerarySegment, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainSegment(item))
	}

	return out
}

func fromDomainSegment(in *domain.ItinerarySegment) *models.ItinerarySegment {
	if in == nil {
		return nil
	}

	return &models.ItinerarySegment{
		ItineraryID:      in.ItineraryID,
		Key:              in.Key,
		Index:            in.Index,
		DepartPlace:      in.DepartPlace,
		DepartDate:       in.DepartDate,
		DepartDt:         in.DepartDt,
		ArrivalDt:        in.ArrivalDt,
		ArrivalPlace:     in.ArrivalPlace,
		ArrivalDate:      in.ArrivalDate,
		CarrierMarketing: in.CarrierMarketing,
		CarrierOperator:  in.CarrierOperator,
		FlightNumber:     in.FlightNumber,
		Aircraft:         in.Aircraft,
		FlightDuration:   in.FlightDuration,
		BookingClass:     in.BookingClass,
		ArrivalTerminal:  in.ArrivalTerminal,
		DepartTerminal:   in.DepartTerminal,
		CabinClassCode:   in.CabinClassCode,
		FareBasis:        in.FareBasis,
		Availability:     in.Availability,
		CabinClass:       in.CabbinClass,
	}
}

func fromDomainPaxFares(ins []*domain.ItineraryPaxFare) []*models.ItineraryPaxFare {
	if len(ins) == 0 {
		return nil
	}

	out := make([]*models.ItineraryPaxFare, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainPaxFare(item))
	}

	return out
}

func fromDomainPaxFare(in *domain.ItineraryPaxFare) *models.ItineraryPaxFare {
	if in == nil {
		return nil
	}

	return &models.ItineraryPaxFare{
		PaxType:    in.PaxType,
		FareAmount: in.FareAmount,
		FareBasic:  in.FareBasic,
		TaxAmount:  in.TaxAmount,
		Currency:   in.Currency,
	}
}

func FromDomainPassengerSeatInfos(info []*domain.PassengerSeatInfo) []*models.PassengerSeatInfo {
	if len(info) == 0 {
		return nil
	}

	res := make([]*models.PassengerSeatInfo, len(info))
	for index, passengerSeatInfo := range info {
		res[index] = fromDomainPassengerSeatInfo(passengerSeatInfo)
	}

	return res
}

func fromDomainPassengerSeatInfo(info *domain.PassengerSeatInfo) *models.PassengerSeatInfo {
	if info == nil {
		return nil
	}

	return &models.PassengerSeatInfo{
		EMDNumber:    info.EMDNumber,
		SegmentIndex: info.SegmentIndex,
		RowNumber:    info.RowNumber,
		SeatFacility: FromDomainSeatFacility(info.SeatFacility),
	}
}

func fromDomainMiniRules(miniRules []*domain.MiniRule) []*models.MiniRule {
	if len(miniRules) == 0 {
		return nil
	}

	res := make([]*models.MiniRule, 0, len(miniRules))
	for _, miniRule := range miniRules {
		if miniRule == nil {
			continue
		}

		res = append(res, &models.MiniRule{
			PaxType:      miniRule.PaxType,
			Segments:     fromDomainMiniRuleSegments(miniRule.Segments),
			PenaltyRules: fromDomainPenaltyRules(miniRule.PenaltyRules),
			PenaltyText:  miniRule.PenaltyText,
		})
	}

	return res
}

func fromDomainMiniRuleSegments(ins []*domain.MiniRuleSegment) []*models.MiniRuleSegment {
	out := make([]*models.MiniRuleSegment, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainMiniRuleSegment(item))
	}

	return out
}

func fromDomainMiniRuleSegment(in *domain.MiniRuleSegment) *models.MiniRuleSegment {
	if in == nil {
		return nil
	}

	return &models.MiniRuleSegment{
		ItineraryIndex: in.ItineraryIndex,
		SegmentIndex:   in.SegmentIndex,
	}
}
func fromDomainPenaltyRules(penaltyRules []*domain.PenaltyRule) []*models.PenaltyRule {
	if len(penaltyRules) == 0 {
		return nil
	}

	res := make([]*models.PenaltyRule, 0, len(penaltyRules))

	for _, penaltyRule := range penaltyRules {
		if penaltyRule == nil {
			continue
		}

		res = append(res, &models.PenaltyRule{
			PenaltyType:    penaltyRule.PenaltyType,
			IsPermitted:    penaltyRule.IsPermitted,
			Situation:      penaltyRule.Situation,
			Amount:         penaltyRule.Amount,
			Percent:        penaltyRule.Percent,
			BaseType:       penaltyRule.BaseType,
			Currency:       penaltyRule.Currency,
			NoShowTime:     penaltyRule.NoShowTime,
			NoShowTimeUnit: penaltyRule.NoShowTimeUnit,
		})
	}

	return res
}

func fromDomainCatInfo(catInfo *domain.CategDescrType) *models.CategDescrType {
	if catInfo == nil {
		return nil
	}

	return &models.CategDescrType{
		DescriptionInfo: &models.CategoryDescriptionType{
			Number: catInfo.DescriptionInfo.Number,
		},
	}
}

func fromDomainMonInfo(monInfo *domain.MonetaryInformationType) *models.MonetaryInformationType {
	return &models.MonetaryInformationType{
		MonetaryDetails:      fromDomainMonetaryInformationDetailsType(monInfo.MonetaryDetails),
		OtherMonetaryDetails: fromDomainOtherMonetaryDetails(monInfo.OtherMonetaryDetails),
	}
}

func fromDomainMonetaryInformationDetailsType(monDetails []*domain.MonetaryInformationDetailsType) []*models.MonetaryInformationDetailsType {
	if len(monDetails) == 0 {
		return nil
	}

	res := make([]*models.MonetaryInformationDetailsType, len(monDetails))
	for index, monDetail := range monDetails {
		res[index] = &models.MonetaryInformationDetailsType{
			TypeQualifier: monDetail.TypeQualifier,
			Amount:        monDetail.Amount,
		}
	}

	return res
}

func fromDomainOtherMonetaryDetails(monDetail *domain.MonetaryInformationDetailsType) *models.MonetaryInformationDetailsType {
	if monDetail == nil {
		return nil
	}

	return &models.MonetaryInformationDetailsType{
		TypeQualifier: monDetail.TypeQualifier,
		Amount:        monDetail.Amount,
	}
}
func fromDomainStatusType(statusInfo *domain.StatusType) *models.StatusType {
	if statusInfo == nil {
		return nil
	}

	return &models.StatusType{
		StatusInformation: fromDomainStatusDetailsType(statusInfo.StatusInformation),
	}
}

func fromDomainStatusDetailsType(statusDetailsType []*domain.StatusDetailsType) []*models.StatusDetailsType {
	if len(statusDetailsType) == 0 {
		return nil
	}

	res := make([]*models.StatusDetailsType, len(statusDetailsType))
	for index, statusDetailType := range statusDetailsType {
		res[index] = &models.StatusDetailsType{
			Indicator: statusDetailType.Indicator,
			Action:    statusDetailType.Action,
		}
	}

	return res
}
