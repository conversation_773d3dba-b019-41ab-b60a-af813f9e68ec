package converts

import (
	"time"

	"github.com/pkg/errors"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func toDomainItineraryRequest(in *models.ItineraryRequest) *domain.ItineraryRequest {
	if in == nil {
		return nil
	}

	return &domain.ItineraryRequest{
		DepartPlace:  in.DepartPlace,
		DepartDate:   in.DepartDate,
		ArrivalPlace: in.ArrivalPlace,
	}
}

func toDomainItineraryRequests(ins []*models.ItineraryRequest) []*domain.ItineraryRequest {
	out := make([]*domain.ItineraryRequest, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainItineraryRequest(item))
	}

	return out
}

func toDomainSearchFlightRequest(in *models.SearchFlightsRequest) *domain.SearchFlightsRequest {
	if in == nil {
		return nil
	}

	return &domain.SearchFlightsRequest{
		Itineraries: toDomainItineraryRequests(in.Itineraries),
		Passengers: domain.PaxRequest{
			ADT: in.Passengers.ADT,
			CHD: in.Passengers.CHD,
			INF: in.Passengers.INF,
		},
	}
}

func ToDomainSearchFlightsCachedRecords(ins []*models.SearchFlights) ([]*domain.SearchFlightsCachedRecord, error) {
	out := make([]*domain.SearchFlightsCachedRecord, 0, len(ins))

	for _, item := range ins {
		temp, err := ToDomainSearchFlightsCachedRecord(item)
		if err != nil {
			return nil, err
		}

		out = append(out, temp)
	}
	return out, nil
}

func ToDomainSearchFlightsCachedRecord(in *models.SearchFlights) (*domain.SearchFlightsCachedRecord, error) {
	if in == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	return &domain.SearchFlightsCachedRecord{
		ID:         in.ID.Hex(),
		Key:        in.Key,
		HashKey:    in.HashKey,
		Provider:   enum.FlightProviderValue[in.Provider],
		ExpiredAt:  in.ExpiredAt,
		Request:    toDomainSearchFlightRequest(in.Request),
		FlightType: in.FlightType,
		Version:    in.Version,
	}, nil
}

func toDomainBaggageInfos(ins []*models.BaggageInfo) []*domain.BaggageInfo {
	out := make([]*domain.BaggageInfo, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainBaggageInfo(item))
	}

	return out
}

func toDomainBaggageInfo(in *models.BaggageInfo) *domain.BaggageInfo {
	if in == nil {
		return nil
	}

	return &domain.BaggageInfo{
		Name:          in.Name,
		Code:          in.Code,
		Price:         in.Price,
		Currency:      in.Currency,
		IsHandBaggage: in.IsHandBaggage,
		Quantity:      in.Quantity,
		PaxType:       in.PaxType,
	}
}

func ToDomainResponseFlights(ins []*models.ResponseFlight) ([]*domain.ResponseFlight, error) {
	out := make([]*domain.ResponseFlight, 0, len(ins))

	for _, item := range ins {
		temp, err := ToDomainResponseFlight(item)
		if err != nil {
			return nil, err
		}

		out = append(out, temp)
	}

	return out, nil
}

func ToDomainResponseFlight(in *models.ResponseFlight) (*domain.ResponseFlight, error) {
	if in == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	ities, err := toDomainFlightItineraries(in.Itineraries)
	if err != nil {
		return nil, err
	}

	return &domain.ResponseFlight{
		FlightID:    in.FlightID,
		Index:       in.Index,
		Itineraries: ities,
		SearchTotalFareInfo: domain.SearchTotalFareInfo{
			TotalPaxFares:       toDomainPaxFares(in.TotalPaxFares),
			BaseTotalFareAmount: in.BaseTotalFareAmount,
			TotalFareAmount:     in.TotalFareAmount,
			TotalFareBasic:      in.TotalFareBasic,
			TotalTaxAmount:      in.TotalTaxAmount,
			Currency:            in.Currency,
		},
		AirlineSystem:         in.AirlineSystem,
		RecordID:              in.RecordID.Hex(),
		Metadata:              ToDomainMetaData(in.Metadata),
		ExpiredAt:             in.ExpiredAt,
		TotalFareInfoSnapshot: toDomainSearchTotalFareInfo(in.TotalFareInfoSnapshot),
		SoldOut:               in.SoldOut,
		VAT:                   in.VAT,
		MiniRules:             toDomainMiniRules(in.MiniRules),
		Leg:                   in.Leg,
		OptionType:            in.OptionType,
		GroupID:               in.GroupID,
	}, nil
}

func toDomainFlightItineraries(ins []*models.FlightItinerary) ([]*domain.FlightItinerary, error) {
	out := make([]*domain.FlightItinerary, 0, len(ins))

	for _, item := range ins {
		temp, err := toDomainFlightItinerary(item)
		if err != nil {
			return nil, err
		}

		out = append(out, temp)
	}

	return out, nil
}

func toDomainFlightItinerary(in *models.FlightItinerary) (*domain.FlightItinerary, error) {
	if in == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	departDt, err := time.Parse(time.RFC3339, in.DepartDt)
	if err != nil {
		// Legacy time backwards compatibility
		departDt, err = time.Parse(time.DateTime, in.DepartDt)
		if err != nil {
			return nil, errors.Wrap(err, "time.Parse(time.DateTime, in.DepartDt)")
		}
	}

	arrivalDt, err := time.Parse(time.RFC3339, in.ArrivalDt)
	if err != nil {
		// Legacy time backwards compatibility
		arrivalDt, err = time.Parse(time.DateTime, in.ArrivalDt)
		if err != nil {
			return nil, errors.Wrap(err, "time.Parse(time.DateTime, in.ArrivalDt)")
		}
	}

	freeBag := toDomainBaggageInfos(in.FreeBaggage)

	return &domain.FlightItinerary{
		ID:                 in.ID,
		Index:              in.Index,
		ProviderBookingKey: in.ProviderBookingKey,
		StopNumber:         in.StopNumber,
		CabinClass:         in.CabinClass,
		BookingClass:       in.BookingClass,
		FareBasis:          in.FareBasis,
		Availability:       in.Availability,
		DepartPlace:        in.DepartPlace,
		DepartDate:         in.DepartDate,
		ArrivalPlace:       in.ArrivalPlace,
		ArrivalDate:        in.ArrivalDate,
		CarrierMarketing:   in.CarrierMarketing,
		CarrierOperator:    in.CarrierOperator,
		FlightNumber:       in.FlightNumber,
		FlightDuration:     in.FlightDuration,
		FareAmount:         in.FareAmount,
		FareBasic:          in.FareBasic,
		TaxAmount:          in.TaxAmount,
		Currency:           in.Currency,
		PaxFares:           toDomainPaxFares(in.PaxFares),
		Segments:           toDomainSegments(in.Segments),
		FreeBaggage:        freeBag,
		PaxInfo:            toDomainItineraryPaxList(in.PaxInfo),
		DepartDt:           departDt,
		ArrivalDt:          arrivalDt,
		ReservationCode:    in.ReservationCode,
		DepartCountry:      in.DepartCountry,
		ArrivalCountry:     in.ArrivalCountry,
		FareType:           in.FareType,
		CabinClassCode:     in.CabinClassCode,
		FlightID:           in.FlightID,
	}, nil
}

func toDomainItineraryPaxList(ins []*models.ItineraryPax) []*domain.ItineraryPax {
	out := make([]*domain.ItineraryPax, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainItineraryPax(item))
	}

	return out
}

func toDomainItineraryPax(in *models.ItineraryPax) *domain.ItineraryPax {
	if in == nil {
		return nil
	}

	return &domain.ItineraryPax{
		PaxID:        in.PaxID,
		TicketNumber: in.TicketNumber,
		Seats:        ToDomainPassengerSeatInfos(in.Seats),
		Baggages:     ToDomainPassengerBaggageInfos(in.Baggages),
	}
}

func toDomainSegments(ins []*models.ItinerarySegment) []*domain.ItinerarySegment {
	out := make([]*domain.ItinerarySegment, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainSegment(item))
	}

	return out
}

func toDomainSegment(in *models.ItinerarySegment) *domain.ItinerarySegment {
	if in == nil {
		return nil
	}

	return &domain.ItinerarySegment{
		ItineraryID:      in.ItineraryID,
		Key:              in.Key,
		Index:            in.Index,
		DepartPlace:      in.DepartPlace,
		DepartDate:       in.DepartDate,
		ArrivalPlace:     in.ArrivalPlace,
		ArrivalDate:      in.ArrivalDate,
		ArrivalDt:        in.ArrivalDt,
		DepartDt:         in.DepartDt,
		CarrierMarketing: in.CarrierMarketing,
		CarrierOperator:  in.CarrierOperator,
		FlightNumber:     in.FlightNumber,
		Aircraft:         in.Aircraft,
		FlightDuration:   in.FlightDuration,
		BookingClass:     in.BookingClass,
		ArrivalTerminal:  in.ArrivalTerminal,
		DepartTerminal:   in.DepartTerminal,
		CabinClassCode:   in.CabinClassCode,
		FareBasis:        in.FareBasis,
		Availability:     in.Availability,
		CabbinClass:      in.CabinClass,
	}
}

func toDomainPaxFares(ins []*models.ItineraryPaxFare) []*domain.ItineraryPaxFare {
	if len(ins) == 0 {
		return nil
	}

	out := make([]*domain.ItineraryPaxFare, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainPaxFare(item))
	}

	return out
}

func toDomainPaxFare(in *models.ItineraryPaxFare) *domain.ItineraryPaxFare {
	if in == nil {
		return nil
	}

	return &domain.ItineraryPaxFare{
		PaxType:    in.PaxType,
		FareAmount: in.FareAmount,
		FareBasic:  in.FareBasic,
		TaxAmount:  in.TaxAmount,
		Currency:   in.Currency,
	}
}

func ToDomainPassengerSeatInfos(info []*models.PassengerSeatInfo) []*domain.PassengerSeatInfo {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.PassengerSeatInfo, len(info))
	for index, passengerSeatInfo := range info {
		res[index] = toDomainPassengerSeatInfo(passengerSeatInfo)
	}

	return res
}

func toDomainPassengerSeatInfo(info *models.PassengerSeatInfo) *domain.PassengerSeatInfo {
	if info == nil {
		return nil
	}

	return &domain.PassengerSeatInfo{
		EMDNumber:    info.EMDNumber,
		SegmentIndex: info.SegmentIndex,
		RowNumber:    info.RowNumber,
		SeatFacility: ToDomainSeatFacility(info.SeatFacility),
	}
}

func toDomainMiniRules(miniRules []*models.MiniRule) []*domain.MiniRule {
	if len(miniRules) == 0 {
		return nil
	}

	res := make([]*domain.MiniRule, 0, len(miniRules))
	for _, miniRule := range miniRules {
		if miniRule == nil {
			continue
		}

		res = append(res, &domain.MiniRule{
			PaxType:      miniRule.PaxType,
			Segments:     toDomainMiniRuleSegments(miniRule.Segments),
			PenaltyRules: toDomainPenaltyRules(miniRule.PenaltyRules),
			PenaltyText:  miniRule.PenaltyText,
		})
	}

	return res
}

func toDomainMiniRuleSegments(ins []*models.MiniRuleSegment) []*domain.MiniRuleSegment {
	out := make([]*domain.MiniRuleSegment, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainMiniRuleSegment(item))
	}

	return out
}

func toDomainMiniRuleSegment(in *models.MiniRuleSegment) *domain.MiniRuleSegment {
	if in == nil {
		return nil
	}

	return &domain.MiniRuleSegment{
		ItineraryIndex: in.ItineraryIndex,
		SegmentIndex:   in.SegmentIndex,
	}
}

func toDomainPenaltyRules(penaltyRules []*models.PenaltyRule) []*domain.PenaltyRule {
	if len(penaltyRules) == 0 {
		return nil
	}

	res := make([]*domain.PenaltyRule, 0, len(penaltyRules))
	for _, penaltyRule := range penaltyRules {
		if penaltyRule == nil {
			continue
		}

		res = append(res, &domain.PenaltyRule{
			PenaltyType:    penaltyRule.PenaltyType,
			IsPermitted:    penaltyRule.IsPermitted,
			Situation:      penaltyRule.Situation,
			Amount:         penaltyRule.Amount,
			Percent:        penaltyRule.Percent,
			BaseType:       penaltyRule.BaseType,
			Currency:       penaltyRule.Currency,
			NoShowTime:     penaltyRule.NoShowTime,
			NoShowTimeUnit: penaltyRule.NoShowTimeUnit,
		})
	}

	return res
}

func toDomainCatInfo(catInfo *models.CategDescrType) *domain.CategDescrType {
	return &domain.CategDescrType{
		DescriptionInfo: &domain.CategoryDescriptionType{
			Number: catInfo.DescriptionInfo.Number,
		},
	}
}

func toDomainMonInfo(monInfo *models.MonetaryInformationType) *domain.MonetaryInformationType {
	return &domain.MonetaryInformationType{
		MonetaryDetails:      toDomainMonetaryInformationDetailsType(monInfo.MonetaryDetails),
		OtherMonetaryDetails: toDomainOtherMonetaryDetails(monInfo.OtherMonetaryDetails),
	}
}

func toDomainMonetaryInformationDetailsType(monDetails []*models.MonetaryInformationDetailsType) []*domain.MonetaryInformationDetailsType {
	if len(monDetails) == 0 {
		return nil
	}

	res := make([]*domain.MonetaryInformationDetailsType, len(monDetails))
	for index, monDetail := range monDetails {
		res[index] = &domain.MonetaryInformationDetailsType{
			TypeQualifier: monDetail.TypeQualifier,
			Amount:        monDetail.Amount,
		}
	}

	return res
}

func toDomainOtherMonetaryDetails(monDetail *models.MonetaryInformationDetailsType) *domain.MonetaryInformationDetailsType {
	if monDetail == nil {
		return nil
	}

	return &domain.MonetaryInformationDetailsType{
		TypeQualifier: monDetail.TypeQualifier,
		Amount:        monDetail.Amount,
	}
}
func toDomainStatusType(statusInfo *models.StatusType) *domain.StatusType {
	if statusInfo == nil {
		return nil
	}

	return &domain.StatusType{
		StatusInformation: toDomainStatusDetailsType(statusInfo.StatusInformation),
	}
}

func toDomainStatusDetailsType(statusDetailsType []*models.StatusDetailsType) []*domain.StatusDetailsType {
	if len(statusDetailsType) == 0 {
		return nil
	}

	res := make([]*domain.StatusDetailsType, len(statusDetailsType))
	for index, statusDetailType := range statusDetailsType {
		res[index] = &domain.StatusDetailsType{
			Indicator: statusDetailType.Indicator,
			Action:    statusDetailType.Action,
		}
	}

	return res
}

func ToDomainPassengerBaggageInfos(info []*models.PassengerBaggageInfo) []*domain.PassengerBaggageInfo {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.PassengerBaggageInfo, len(info))
	for index, passengerBaggageInfo := range info {
		res[index] = toDomainPassengerBaggageInfo(passengerBaggageInfo)
	}

	return res
}

func toDomainPassengerBaggageInfo(info *models.PassengerBaggageInfo) *domain.PassengerBaggageInfo {
	if info == nil {
		return nil
	}

	return &domain.PassengerBaggageInfo{
		EMDNumber:   info.EMDNumber,
		BaggageInfo: toDomainBaggageOption(info.BaggageInfo),
	}
}
