package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainMetaData(ins []*domain.Metadata) []*models.Metadata {
	out := make([]*models.Metadata, 0, len(ins))

	for _, item := range ins {
		out = append(out, &models.Metadata{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	return out
}

func ToDomainMetaData(ins []*models.Metadata) []*domain.Metadata {
	out := make([]*domain.Metadata, 0, len(ins))

	for _, item := range ins {
		out = append(out, &domain.Metadata{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	return out
}
