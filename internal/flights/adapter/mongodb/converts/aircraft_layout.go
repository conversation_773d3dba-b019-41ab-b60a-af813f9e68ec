package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func fromDomainAircraftLayout(in *domain.AircraftLayout) *models.AircraftLayout {
	if in == nil {
		return nil
	}

	return &models.AircraftLayout{
		AirlineCode:  in.AirlineCode,
		Aircraft:     in.Aircraft,
		AisleColumns: in.AisleColumns,
	}
}

func FromDomainAircraftLayouts(items []*domain.AircraftLayout) []*models.AircraftLayout {
	if len(items) == 0 {
		return nil
	}
	res := make([]*models.AircraftLayout, len(items))
	for index, aircraftLayout := range items {
		res[index] = fromDomainAircraftLayout(aircraftLayout)
	}

	return res
}

func toDomainAircraftLayout(in *models.AircraftLayout) *domain.AircraftLayout {
	if in == nil {
		return nil
	}

	return &domain.AircraftLayout{
		AirlineCode:  in.AirlineCode,
		Aircraft:     in.Aircraft,
		AisleColumns: in.AisleColumns,
	}
}

func ToDomainMapAircraftLayouts(items []*models.AircraftLayout) map[string]*domain.AircraftLayout {
	if len(items) == 0 {
		return nil
	}
	res := map[string]*domain.AircraftLayout{}
	for _, aircraftLayout := range items {
		res[aircraftLayout.Aircraft] = toDomainAircraftLayout(aircraftLayout)
	}

	return res
}
