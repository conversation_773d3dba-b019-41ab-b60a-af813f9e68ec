package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainBaggageOptionCache(info *domain.BaggageOptionCache) *models.BaggageOptionCache {
	if info == nil {
		return nil
	}

	res := &models.BaggageOptionCache{
		Base:           models.Base{},
		FlightID:       info.FlightID,
		ItineraryID:    info.ItineraryID,
		BaggageOptions: fromDomainBaggageOptions(info.BaggageOptions),
		ExpiredAt:      info.ExpiredAt,
	}

	return res
}

func fromDomainBaggageOptions(items []*domain.BaggageOption) []*models.BaggageOption {
	if len(items) == 0 {
		return nil
	}

	res := make([]*models.BaggageOption, 0, len(items))
	for _, baggageOption := range items {
		res = append(res, fromDomainBaggageOption(baggageOption))
	}

	return res
}

func fromDomainBaggageOption(info *domain.BaggageOption) *models.BaggageOption {
	if info == nil {
		return nil
	}

	return &models.BaggageOption{
		OptionID:           info.OptionID,
		OfferData:          fromDomainOfferData(info.OfferData),
		ItineraryIndex:     info.ItineraryIndex,
		SegmentIndex:       info.SegmentIndex,
		BaggageInfo:        fromDomainBaggageOptionDetails(info.BaggageInfo),
		TotalWeight:        info.TotalWeight,
		Unit:               info.Unit,
		TotalBaggageCharge: fromDomainBaggageCharge(info.TotalBaggageCharge),
	}
}

func fromDomainOfferData(info *domain.OfferData) *models.OfferData {
	if info == nil {
		return nil
	}

	return &models.OfferData{
		TFOfferData:  fromDomainTFOfferData(info.TFOfferData),
		VNAOfferData: fromDomainVNAOfferData(info.VNAOfferData),
		AMAOfferData: fromDomainAMAOfferData(info.AMAOfferData),
		HNHOfferData: fromDomainHNHOfferData(info.HNHOfferData),
	}
}

func fromDomainTFOfferData(info *domain.TFOfferData) *models.TFOfferData {
	if info == nil {
		return nil
	}

	return &models.TFOfferData{
		ParameterName:  info.ParameterName,
		ParameterValue: info.ParameterValue,
	}
}

func fromDomainHNHOfferData(info *domain.HNHOfferData) *models.HNHOfferData {
	if info == nil {
		return nil
	}

	return &models.HNHOfferData{
		Airline:    info.Airline,
		Value:      info.Value,
		Code:       info.Code,
		Currency:   info.Currency,
		Route:      info.Route,
		StartPoint: info.StartPoint,
		EndPoint:   info.EndPoint,
		Leg:        info.Leg,
		Price:      info.Price,
	}
}

func fromDomainVNAOfferData(info *domain.VNAOfferData) *models.VNAOfferData {
	if info == nil {
		return nil
	}

	return &models.VNAOfferData{
		OfferID:           info.OfferID,
		SubCode:           info.SubCode,
		SSRCode:           info.SSRCode,
		OwningCarrierCode: info.OwningCarrierCode,
		Vendor:            info.Vendor,
		CommercialName:    info.CommercialName,
		Group:             info.Group,
		RFICode:           info.RFICode,
	}
}

func fromDomainAMAOfferData(info *domain.AmadeusOfferData) *models.AmadeusOfferData {
	if info == nil {
		return nil
	}

	return &models.AmadeusOfferData{
		RFICode:        info.RFICode,
		RFISCode:       info.RFISCode,
		ProviderCode:   info.ProviderCode,
		CustomerRefIDs: info.CustomerRefIDs,
		Parameters:     fromDomainAmadeusBaggageParameters(info.Parameters),
	}
}

func fromDomainAmadeusBaggageParameter(info *domain.AmadeusBaggageParameter) *models.AmadeusBaggageParameter {
	if info == nil {
		return nil
	}

	return &models.AmadeusBaggageParameter{
		Name:  info.Name,
		Value: info.Value,
	}
}

func fromDomainAmadeusBaggageParameters(items []*domain.AmadeusBaggageParameter) []*models.AmadeusBaggageParameter {
	if len(items) == 0 {
		return nil
	}

	res := make([]*models.AmadeusBaggageParameter, 0, len(items))
	for _, item := range items {
		res = append(res, fromDomainAmadeusBaggageParameter(item))
	}

	return res
}

func fromDomainBaggageOptionDetails(items []*domain.BaggageOptionDetail) []*models.BaggageOptionDetail {
	if len(items) == 0 {
		return nil
	}

	res := make([]*models.BaggageOptionDetail, 0, len(items))
	for _, baggageOptionDetail := range items {
		res = append(res, fromDomainBaggageOptionDetail(baggageOptionDetail))
	}

	return res
}

func fromDomainBaggageOptionDetail(info *domain.BaggageOptionDetail) *models.BaggageOptionDetail {
	if info == nil {
		return nil
	}

	return &models.BaggageOptionDetail{
		Type:      info.Type,
		SubCode:   info.SubCode,
		Quantity:  info.Quantity,
		Weight:    info.Weight,
		MaxWeight: info.MaxWeight,
		Unit:      info.Unit,
		Dimension: info.Dimension,
	}
}

func fromDomainBaggageCharge(info *domain.BaggageCharge) *models.BaggageCharge {
	if info == nil {
		return nil
	}

	return &models.BaggageCharge{
		BaseAmount:  info.BaseAmount,
		TaxAmount:   info.TaxAmount,
		TotalAmount: info.TotalAmount,
		Currency:    info.Currency,
	}
}
