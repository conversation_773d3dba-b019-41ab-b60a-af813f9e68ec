package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func FromDomainBase(domainBase domain.Base) models.Base {
	var id primitive.ObjectID
	if domainBase.ID != "" {
		id, _ = primitive.ObjectIDFromHex(domainBase.ID)
	}

	return models.Base{
		ID:        id,
		CreatedAt: domainBase.CreatedAt,
		UpdatedAt: domainBase.UpdatedAt,
		DeletedAt: domainBase.DeletedAt,
		CreatedBy: domainBase.CreatedBy,
		UpdatedBy: domainBase.UpdatedBy,
		DeletedBy: domainBase.DeletedBy,
	}
}

func ToDomainBase(modelsBase models.Base) domain.Base {
	return domain.Base{
		ID:        modelsBase.ID.Hex(),
		CreatedAt: modelsBase.CreatedAt,
		UpdatedAt: modelsBase.UpdatedAt,
		DeletedAt: modelsBase.DeletedAt,
		CreatedBy: modelsBase.CreatedBy,
		UpdatedBy: modelsBase.UpdatedBy,
		DeletedBy: modelsBase.DeletedBy,
	}
}
