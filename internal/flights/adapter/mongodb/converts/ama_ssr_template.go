package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainSSRTemplate(in *domain.SSRTemplate) *models.SSRTemplate {
	if in == nil {
		return nil
	}

	return &models.SSRTemplate{
		Regex:         in.Regex,
		DateFormat:    in.DateFormat,
		TimezoneCheck: in.TimezoneCheck,
	}
}

func FromDomainSSRTemplates(items []*domain.SSRTemplate) []*models.SSRTemplate {
	if len(items) == 0 {
		return nil
	}
	res := make([]*models.SSRTemplate, len(items))
	for index, aircraftLayout := range items {
		res[index] = FromDomainSSRTemplate(aircraftLayout)
	}

	return res
}

func ToDomainSSRTemplate(in *models.SSRTemplate) *domain.SSRTemplate {
	if in == nil {
		return nil
	}

	return &domain.SSRTemplate{
		ID:            in.ID.Hex(),
		Regex:         in.Regex,
		DateFormat:    in.DateFormat,
		TimezoneCheck: in.TimezoneCheck,
	}
}

func ToDomainSSRTemplates(items []*models.SSRTemplate) []*domain.SSRTemplate {
	if len(items) == 0 {
		return nil
	}
	res := make([]*domain.SSRTemplate, 0, len(items))
	for _, aircraftLayout := range items {
		res = append(res, ToDomainSSRTemplate(aircraftLayout))
	}

	return res
}
