package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainFlightSession(in *domain.FlightSession) *models.FlightSession {
	if in == nil {
		return nil
	}

	return &models.FlightSession{
		SessionID: in.SessionID,
		OfficeID:  in.OfficeID,
		ExpiredAt: in.ExpiredAt,
	}
}

func ToDomainFlightSessions(ins []*models.FlightSession) []*domain.FlightSession {
	out := make([]*domain.FlightSession, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainFlightSession(item))
	}

	return out
}

func ToDomainFlightSession(in *models.FlightSession) *domain.FlightSession {
	if in == nil {
		return nil
	}

	return &domain.FlightSession{
		SessionID: in.SessionID,
		OfficeID:  in.OfficeID,
		ExpiredAt: in.ExpiredAt,
	}
}
