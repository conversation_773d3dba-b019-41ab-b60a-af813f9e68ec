package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainSupplierRoute(info *domain.SupplierRoute) *models.SupplierRoute {
	if info == nil {
		return nil
	}

	return &models.SupplierRoute{
		Provider:      info.Provider,
		Supplier:      info.Supplier,
		AirportRoutes: info.AirportRoutes,
	}
}

func ToDomainSupplierRoutes(info []*models.SupplierRoute) []*domain.SupplierRoute {
	if len(info) == 0 {
		return nil
	}
	result := make([]*domain.SupplierRoute, 0, len(info))

	for _, router := range info {
		result = append(result, ToDomainSupplierRoute(router))
	}

	return result
}

func ToDomainSupplierRoute(info *models.SupplierRoute) *domain.SupplierRoute {
	if info == nil {
		return nil
	}

	return &domain.SupplierRoute{
		Provider:      info.Provider,
		Supplier:      info.Supplier,
		AirportRoutes: info.AirportRoutes,
	}
}
