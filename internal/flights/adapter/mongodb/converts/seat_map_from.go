package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainSeatSegment(info *domain.SeatSegment) *models.SeatSegment {
	if info == nil {
		return nil
	}

	res := &models.SeatSegment{
		Key:          info.Key,
		ItineraryID:  info.ItineraryID,
		SegmentIndex: info.SegmentIndex,
		SeatOptions:  FromDomainSeatOptions(info.SeatOptions),
		ExpiredAt:    info.ExpiredAt,
	}

	return res
}

func FromDomainSeatOptions(items []*domain.SeatOption) []*models.SeatOption {
	if len(items) == 0 {
		return nil
	}

	res := make([]*models.SeatOption, 0, len(items))
	for _, seatOption := range items {
		res = append(res, fromDomainSeatOption(seatOption))
	}

	return res
}

func fromDomainSeatOption(info *domain.SeatOption) *models.SeatOption {
	if info == nil {
		return nil
	}

	return &models.SeatOption{
		CabinClass: info.CabinClass,
		Rows:       FromDomainSeatRows(info.Rows),
		Columns:    FromDomainColumnHeaders(info.Columns),
	}
}

func FromDomainColumnHeaders(items []*domain.ColumnHeader) []*models.ColumnHeader {
	if len(items) == 0 {
		return nil
	}

	res := make([]*models.ColumnHeader, 0, len(items))
	for _, columnHeader := range items {
		res = append(res, fromDomainColumnHeader(columnHeader))
	}
	return res
}

func fromDomainColumnHeader(info *domain.ColumnHeader) *models.ColumnHeader {
	if info == nil {
		return nil
	}

	return &models.ColumnHeader{
		Title:   info.Title,
		IsAisle: info.IsAisle,
	}
}

func fromDomainSeatRow(info *domain.SeatRow) *models.SeatRow {
	if info == nil {
		return nil
	}

	return &models.SeatRow{
		RowNumber:  info.RowNumber,
		Facilities: FromDomainSeatFacilities(info.Facilities),
	}
}

func FromDomainSeatRows(items []*domain.SeatRow) []*models.SeatRow {
	if len(items) == 0 {
		return nil
	}
	res := make([]*models.SeatRow, 0, len(items))

	for _, seatRow := range items {
		res = append(res, fromDomainSeatRow(seatRow))
	}

	return res
}

func FromDomainSeatFacility(info *domain.SeatFacility) *models.SeatFacility {
	if info == nil {
		return nil
	}

	res := &models.SeatFacility{
		Type:         info.Type,
		SeatCode:     info.SeatCode,
		Availability: info.Availability,
		SeatCharge:   &models.SeatCharge{},
		Property:     &models.SeatProperty{},
		SelectionKey: info.SelectionKey,
	}

	if info.SeatCharge != nil {
		res.SeatCharge = &models.SeatCharge{
			BaseAmount:  info.SeatCharge.BaseAmount,
			TaxAmount:   info.SeatCharge.TaxAmount,
			TotalAmount: info.SeatCharge.TotalAmount,
			Currency:    info.SeatCharge.Currency,
		}
	}

	if info.Property != nil {
		res.Property = &models.SeatProperty{
			Aisle:                 info.Property.Aisle,
			Window:                info.Property.Window,
			EmergencyExit:         info.Property.EmergencyExit,
			OverWing:              info.Property.OverWing,
			Bassinet:              info.Property.Bassinet,
			NotAllowedForInfant:   info.Property.NotAllowedForInfant,
			NotSuitableForInfant:  info.Property.NotSuitableForInfant,
			NotSuitableForChild:   info.Property.NotSuitableForChild,
			HandicappedFacilities: info.Property.HandicappedFacilities,
			ExtraLegroom:          info.Property.ExtraLegroom,
		}
	}

	return res
}

func FromDomainSeatFacilities(items []*domain.SeatFacility) []*models.SeatFacility {
	if len(items) == 0 {
		return nil
	}

	res := make([]*models.SeatFacility, 0, len(items))
	for _, facility := range items {
		res = append(res, FromDomainSeatFacility(facility))
	}

	return res
}
