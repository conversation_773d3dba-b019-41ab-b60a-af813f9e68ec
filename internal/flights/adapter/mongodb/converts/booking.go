package converts

import (
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func FromDomainBookingSession(in *domain.BookingSession) (*models.BookingSession, error) {
	if in == nil {
		return nil, errors.ErrInvalidInput
	}

	ities, err := FromDomainFlightItineraries(in.Itineraries)
	if err != nil {
		return nil, err
	}

	var fareCf, originFare, fareIss *models.TotalFareInfo

	if in.OriginFareData != nil {
		originFare = FromDomainTotalFareInfo(in.OriginFareData)
	}

	if in.FareDataCf != nil {
		fareCf = FromDomainTotalFareInfo(in.FareDataCf)
	}

	if in.FareDataIss != nil {
		fareIss = FromDomainTotalFareInfo(in.FareDataIss)
	}

	var ep *models.ExpectedPrice
	if in.ExpectedPrice != nil {
		ep = &models.ExpectedPrice{
			Amount:   in.ExpectedPrice.Amount,
			Currency: in.ExpectedPrice.Currency,
		}
	}

	return &models.BookingSession{
		Status:            in.Status,
		LastTicketingDate: in.LastTicketingDate,
		OfficeID:          in.OfficeID,
		FlightType:        in.FlightType,
		BookingRef:        in.BookingRef,
		BookingCode:       in.BookingCode,
		SessionID:         in.SessionID,
		Provider:          enum.FlightProviderName[in.Provider],
		Itineraries:       ities,
		FareData:          FromDomainTotalFareInfo(in.FareData),
		FareDataRaw:       FromDomainTotalFareInfo(in.FareDataRaw),
		FareDataCfRaw:     FromDomainTotalFareInfo(in.FareDataCfRaw),
		FareDataCf:        fareCf,
		FareDataIss:       fareIss,
		PassengerInfo: &models.PaxRequest{
			ADT: in.PassengerInfo.ADT,
			CHD: in.PassengerInfo.CHD,
			INF: in.PassengerInfo.INF,
		},
		SearchRequest:       fromDomainSearchFlightRequest(in.SearchRequest),
		SearchKey:           in.SearchKey,
		FlightID:            in.FlightID,
		OrderID:             in.OrderID,
		LastTransactionID:   in.LastTransactionID,
		TicketStatus:        in.TicketStatus,
		EMDStatus:           in.EMDStatus,
		PendingDeadline:     in.PendingDeadline,
		OriginFareData:      originFare,
		ExpectedPrice:       ep,
		EndUserIPAddress:    in.EndUserIPAddress,
		EndUserBrowserAgent: in.EndUserBrowserAgent,
		EndUserCountryCode:  in.EndUserCountryCode,
		CommissionRate:      in.CommissionRate,
		AirlineSystem:       in.AirlineSystem,
		VAT:                 in.VAT,
		IsTransferred:       in.IsTransferred,
		Confirmed:           in.Confirmed,
		FareExpiredDate:     in.FareExpiredDate,
		TicketExpiredDate:   in.TicketExpiredDate,
		Notified:            in.Notified,
		ManualIssuing:       in.ManualIssuing,
		OrderNumRef:         in.OrderNumRef,
		InternalBooking:     in.InternalBooking,
		IsVJ24h:             in.IsVJ24h,
		Metadata:            FromDomainMetaData(in.Metadata),
	}, nil
}

func FromDomainTotalFareInfo(in *domain.TotalFareInfo) *models.TotalFareInfo {
	if in == nil {
		return nil
	}

	return &models.TotalFareInfo{
		TotalPaxFares:       fromDomainPaxFares(in.TotalPaxFares),
		BaseTotalFareAmount: in.BaseTotalFareAmount,
		TotalFareBasic:      in.TotalFareBasic,
		TotalTaxAmount:      in.TotalTaxAmount,
		TotalSeatAmount:     in.TotalSeatAmount,
		TotalFareAmount:     in.TotalFareAmount,
		TotalBaggageAmount:  in.TotalBaggageAmount,
		Currency:            in.Currency,
		PaxFareInfos:        fromDomainPaxFareInfos(in.PaxFareInfos),
		ExchangeRate:        in.ExchangeRate,
		PricingTracking:     fromDomainTrackingPricing(in.PricingTracking),
	}
}

func fromDomainItineraryPricingResult(in *domain.ItineraryPricingResult) *models.ItineraryPricingResult {
	if in == nil {
		return nil
	}

	return &models.ItineraryPricingResult{
		ItineraryID:      in.ItineraryID,
		CarrierMarketing: in.CarrierMarketing,
		DepartCountry:    in.DepartCountry,
		ArrivalCountry:   in.ArrivalCountry,
		BookingClass:     in.BookingClass,
		CabinClass:       in.CabinClass,
		Route:            in.Route,
		HiddenFeeConfig:  fromDomainAppliedFlightHiddenFee(in.HiddenFeeConfig),
		DiscountConfig:   fromDomainAppliedFlightDiscount(in.DiscountConfig),
	}
}

func fromDomainAppliedFlightHiddenFee(in *domain.FlightHiddenFeeConfig) *models.FlightHiddenFeeConfig {
	if in == nil {
		return nil
	}

	return &models.FlightHiddenFeeConfig{
		ID:           in.ID,
		Type:         in.Type,
		Amount:       in.Amount,
		VAT:          in.VAT,
		AirlineCode:  in.AirlineCode,
		Route:        in.Route,
		BookingClass: in.BookingClass,
		Percent:      in.Percent,
		Provider:     in.Provider,
	}
}

func fromDomainAppliedFlightDiscount(in *domain.FlightDiscountConfig) *models.FlightDiscountConfig {
	if in == nil {
		return nil
	}

	return &models.FlightDiscountConfig{
		ID:           in.ID,
		Type:         in.Type,
		Amount:       in.Amount,
		VAT:          in.VAT,
		AirlineCode:  in.AirlineCode,
		Route:        in.Route,
		BookingClass: in.BookingClass,
		Percent:      in.Percent,
		Provider:     in.Provider,
	}
}

func toDomainSearchTotalFareInfo(in *models.TotalFareInfo) *domain.SearchTotalFareInfo {
	if in == nil {
		return nil
	}

	return &domain.SearchTotalFareInfo{
		TotalPaxFares:       toDomainPaxFares(in.TotalPaxFares),
		BaseTotalFareAmount: in.BaseTotalFareAmount,
		TotalFareBasic:      in.TotalFareBasic,
		TotalTaxAmount:      in.TotalTaxAmount,
		TotalFareAmount:     in.TotalFareAmount,
		Currency:            in.Currency,
	}
}

func toDomainTotalFareInfo(in *models.TotalFareInfo) *domain.TotalFareInfo {
	if in == nil {
		return nil
	}

	return &domain.TotalFareInfo{
		TotalPaxFares:       toDomainPaxFares(in.TotalPaxFares),
		BaseTotalFareAmount: in.BaseTotalFareAmount,
		TotalFareBasic:      in.TotalFareBasic,
		TotalTaxAmount:      in.TotalTaxAmount,
		TotalSeatAmount:     in.TotalSeatAmount,
		TotalFareAmount:     in.TotalFareAmount,
		TotalBaggageAmount:  in.TotalBaggageAmount,
		Currency:            in.Currency,
		PaxFareInfos:        toDomainPaxFareInfos(in.PaxFareInfos),
		ExchangeRate:        in.ExchangeRate,
		PricingTracking:     toDomainTrackingPricingFromEmbedded(in.PricingTracking),
	}
}

func toDomainItineraryPricingResults(ins []*models.ItineraryPricingResult) []*domain.ItineraryPricingResult {
	out := make([]*domain.ItineraryPricingResult, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainItineraryPricingResult(item))
	}

	return out
}

func toDomainItineraryPricingResult(in *models.ItineraryPricingResult) *domain.ItineraryPricingResult {
	if in == nil {
		return nil
	}

	return &domain.ItineraryPricingResult{
		ItineraryID:      in.ItineraryID,
		CarrierMarketing: in.CarrierMarketing,
		DepartCountry:    in.DepartCountry,
		ArrivalCountry:   in.ArrivalCountry,
		BookingClass:     in.BookingClass,
		CabinClass:       in.CabinClass,
		Route:            in.Route,
		HiddenFeeConfig:  toDomainAppliedFlightHiddenFee(in.HiddenFeeConfig),
		DiscountConfig:   toDomainAppliedFlightDiscount(in.DiscountConfig),
	}
}

func toDomainAppliedFlightHiddenFee(in *models.FlightHiddenFeeConfig) *domain.FlightHiddenFeeConfig {
	if in == nil {
		return nil
	}

	return &domain.FlightHiddenFeeConfig{
		ID:           in.ID,
		Type:         in.Type,
		Amount:       in.Amount,
		VAT:          in.VAT,
		AirlineCode:  in.AirlineCode,
		Route:        in.Route,
		BookingClass: in.BookingClass,
		Percent:      in.Percent,
		Provider:     in.Provider,
	}
}

func toDomainAppliedFlightDiscount(in *models.FlightDiscountConfig) *domain.FlightDiscountConfig {
	if in == nil {
		return nil
	}

	return &domain.FlightDiscountConfig{
		ID:           in.ID,
		Type:         in.Type,
		Amount:       in.Amount,
		VAT:          in.VAT,
		AirlineCode:  in.AirlineCode,
		Route:        in.Route,
		BookingClass: in.BookingClass,
		Percent:      in.Percent,
		Provider:     in.Provider,
	}
}

func ToDomainBookingSessions(ins []*models.BookingSession) ([]*domain.BookingSession, error) {
	out := make([]*domain.BookingSession, 0, len(ins))

	for _, item := range ins {
		temp, err := ToDomainBookingSession(item)
		if err != nil {
			return nil, err
		}

		out = append(out, temp)
	}

	return out, nil
}

func ToDomainBookingSession(in *models.BookingSession) (*domain.BookingSession, error) {
	if in == nil {
		return nil, errors.ErrInvalidInput
	}

	ities, err := toDomainFlightItineraries(in.Itineraries)
	if err != nil {
		return nil, err
	}

	var fareCf, fareIss, originFare *domain.TotalFareInfo

	if in.FareDataCf != nil {
		fareCf = toDomainTotalFareInfo(in.FareDataCf)
	}

	if in.FareDataIss != nil {
		fareIss = toDomainTotalFareInfo(in.FareDataIss)
	}

	if in.OriginFareData != nil {
		originFare = toDomainTotalFareInfo(in.OriginFareData)
	}

	var ep *domain.ExpectedPrice
	if in.ExpectedPrice != nil {
		ep = &domain.ExpectedPrice{
			Amount:   in.ExpectedPrice.Amount,
			Currency: in.ExpectedPrice.Currency,
		}
	}

	return &domain.BookingSession{
		Status:            in.Status,
		LastTicketingDate: in.LastTicketingDate,
		ID:                in.ID.Hex(),
		OfficeID:          in.OfficeID,
		FlightType:        in.FlightType,
		SessionID:         in.SessionID,
		Itineraries:       ities,
		BookingRef:        in.BookingRef,
		BookingCode:       in.BookingCode,
		Provider:          enum.FlightProviderValue[in.Provider],
		FareData:          toDomainTotalFareInfo(in.FareData),
		FareDataRaw:       toDomainTotalFareInfo(in.FareDataRaw),
		FareDataCfRaw:     toDomainTotalFareInfo(in.FareDataCfRaw),
		FareDataCf:        fareCf,
		FareDataIss:       fareIss,
		PassengerInfo: &domain.PaxRequest{
			ADT: in.PassengerInfo.ADT,
			CHD: in.PassengerInfo.CHD,
			INF: in.PassengerInfo.INF,
		},
		SearchRequest:       toDomainSearchFlightRequest(in.SearchRequest),
		SearchKey:           in.SearchKey,
		FlightID:            in.FlightID,
		OrderID:             in.OrderID,
		LastTransactionID:   in.LastTransactionID,
		TicketStatus:        in.TicketStatus,
		EMDStatus:           in.EMDStatus,
		PendingDeadline:     in.PendingDeadline,
		OriginFareData:      originFare,
		ExpectedPrice:       ep,
		EndUserIPAddress:    in.EndUserIPAddress,
		EndUserBrowserAgent: in.EndUserBrowserAgent,
		EndUserCountryCode:  in.EndUserCountryCode,
		CommissionRate:      in.CommissionRate,
		AirlineSystem:       in.AirlineSystem,
		CreatedAt:           in.CreatedAt,
		VAT:                 in.VAT,
		IsTransferred:       in.IsTransferred,
		Confirmed:           in.Confirmed,
		FareExpiredDate:     in.FareExpiredDate,
		TicketExpiredDate:   in.TicketExpiredDate,
		Notified:            in.Notified,
		ManualIssuing:       in.ManualIssuing,
		OrderNumRef:         in.OrderNumRef,
		InternalBooking:     in.InternalBooking,
		IsVJ24h:             in.IsVJ24h,
		Metadata:            ToDomainMetaData(in.Metadata),
	}, nil
}

func toDomainPaxFareInfos(ins []*models.PaxFareInfo) []*domain.PaxFareInfo {
	out := make([]*domain.PaxFareInfo, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainPaxFareInfo(item))
	}

	return out
}

func toDomainPaxFareInfo(in *models.PaxFareInfo) *domain.PaxFareInfo {
	if in == nil {
		return nil
	}

	return &domain.PaxFareInfo{
		PaxID:              in.PaxID,
		PaxType:            in.PaxType,
		TotalSeatAmount:    in.TotalSeatAmount,
		TotalBaggageAmount: in.TotalBaggageAmount,
		TotalPrice:         in.TotalPrice,
		Currency:           in.Currency,
		Seat:               ToDomainPassengerSeatInfos(in.Seat),
		Baggages:           ToDomainPassengerBaggageInfos(in.Baggages),
		PaxFares:           toDomainPaxFareReports(in.PaxFares),
	}
}

func toDomainPaxFareReports(ins []*models.PaxFareReport) []*domain.PaxFareReport {
	out := make([]*domain.PaxFareReport, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainPaxFareReport(item))
	}

	return out
}

func toDomainPaxFareReport(in *models.PaxFareReport) *domain.PaxFareReport {
	if in == nil {
		return nil
	}

	return &domain.PaxFareReport{
		ItineraryIDs: in.ItineraryIDs,
		Route:        in.Route,
		TaxAmount:    in.TaxAmount,
		Currency:     in.Currency,
		FareAmount:   in.FareAmount,
		FareBasic:    in.FareBasic,
		DetailFares:  toDomainDetailFares(in.DetailFares),
	}
}

func toDomainDetailFares(ins []*models.DetailFare) []*domain.DetailFareReport {
	out := make([]*domain.DetailFareReport, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainDetailFare(item))
	}

	return out
}

func toDomainDetailFare(in *models.DetailFare) *domain.DetailFareReport {
	if in == nil {
		return nil
	}

	return &domain.DetailFareReport{
		Type:                 in.Type,
		TypeDescription:      in.TypeDescription,
		SurchargeCode:        in.SurchargeCode,
		SurchargeDescription: in.SurchargeDescription,
		BaseAmount:           in.BaseAmount,
		DiscountAmount:       in.DiscountAmount,
		TaxAmount:            in.TaxAmount,
		TaxDetails:           toDomainTaxDetails(in.TaxDetails),
		TotalAmount:          in.TotalAmount,
		Currency:             in.Currency,
	}
}

func toDomainTaxDetails(ins []*models.TaxDetail) []*domain.TaxDetail {
	out := make([]*domain.TaxDetail, 0, len(ins))

	for _, item := range ins {
		if item != nil {
			out = append(out, &domain.TaxDetail{
				Name:   item.Name,
				Amount: item.Amount,
			})
		}
	}

	return out
}

func fromDomainPaxFareInfos(ins []*domain.PaxFareInfo) []*models.PaxFareInfo {
	out := make([]*models.PaxFareInfo, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainPaxFareInfo(item))
	}

	return out
}

func fromDomainPaxFareInfo(in *domain.PaxFareInfo) *models.PaxFareInfo {
	if in == nil {
		return nil
	}

	return &models.PaxFareInfo{
		PaxID:              in.PaxID,
		PaxType:            in.PaxType,
		TotalSeatAmount:    in.TotalSeatAmount,
		TotalBaggageAmount: in.TotalBaggageAmount,
		TotalPrice:         in.TotalPrice,
		Currency:           in.Currency,
		Seat:               FromDomainPassengerSeatInfos(in.Seat),
		Baggages:           FromDomainPassengerBaggageInfos(in.Baggages),
		PaxFares:           fromDomainPaxFareReports(in.PaxFares),
	}
}

func fromDomainPaxFareReports(ins []*domain.PaxFareReport) []*models.PaxFareReport {
	out := make([]*models.PaxFareReport, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainPaxFareReport(item))
	}

	return out
}

func fromDomainPaxFareReport(in *domain.PaxFareReport) *models.PaxFareReport {
	if in == nil {
		return nil
	}

	return &models.PaxFareReport{
		ItineraryIDs: in.ItineraryIDs,
		Route:        in.Route,
		TaxAmount:    in.TaxAmount,
		Currency:     in.Currency,
		FareAmount:   in.FareAmount,
		FareBasic:    in.FareBasic,
		DetailFares:  fromDomainDetailFares(in.DetailFares),
	}
}

func fromDomainDetailFares(ins []*domain.DetailFareReport) []*models.DetailFare {
	out := make([]*models.DetailFare, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainDetailFare(item))
	}

	return out
}

func fromDomainDetailFare(in *domain.DetailFareReport) *models.DetailFare {
	if in == nil {
		return nil
	}

	return &models.DetailFare{
		Type:                 in.Type,
		TypeDescription:      in.TypeDescription,
		SurchargeCode:        in.SurchargeCode,
		SurchargeDescription: in.SurchargeDescription,
		BaseAmount:           in.BaseAmount,
		DiscountAmount:       in.DiscountAmount,
		TaxAmount:            in.TaxAmount,
		TaxDetails:           fromDomainTaxDetails(in.TaxDetails),
		TotalAmount:          in.TotalAmount,
		Currency:             in.Currency,
	}
}

func fromDomainTaxDetails(ins []*domain.TaxDetail) []*models.TaxDetail {
	out := make([]*models.TaxDetail, 0, len(ins))

	for _, item := range ins {
		if item != nil {
			out = append(out, &models.TaxDetail{
				Name:   item.Name,
				Amount: item.Amount,
			})
		}
	}

	return out
}

// fromDomainItineraryPricingResults converts []*domain.ItineraryPricingResult to []*models.ItineraryPricingResult
func fromDomainItineraryPricingResults(ins []*domain.ItineraryPricingResult) []*models.ItineraryPricingResult {
	if ins == nil {
		return nil
	}

	out := make([]*models.ItineraryPricingResult, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainItineraryPricingResult(item))
	}

	return out
}

// fromDomainPassengerResults converts []*domain.PassengerPricingResult to []*models.PassengerPricingResult
func fromDomainPassengerResults(ins []*domain.PassengerPricingResult) []*models.PassengerPricingResult {
	out := make([]*models.PassengerPricingResult, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainPassengerResult(item))
	}

	return out
}

// fromDomainPassengerResult converts *domain.PassengerPricingResult to *models.PassengerPricingResult
func fromDomainPassengerResult(in *domain.PassengerPricingResult) *models.PassengerPricingResult {
	if in == nil {
		return nil
	}

	return &models.PassengerPricingResult{
		PassengerType:      in.PassengerType,
		Quantity:           in.Quantity,
		OriginalFareAmount: in.OriginalFareAmount,
		OriginalFareBasic:  in.OriginalFareBasic,
		TaxAmount:          in.TaxAmount,
		FinalFareAmount:    in.FinalFareAmount,
		FinalFareBasic:     in.FinalFareBasic,
		HiddenFeeAmount:    in.HiddenFeeAmount,
		DiscountAmount:     in.DiscountAmount,
	}
}

// ===== TRACKING PRICING HELPER FUNCTIONS =====

// fromDomainTrackingPricing converts domain.TrackingPricing to *models.TrackingPricing
func fromDomainTrackingPricing(tracking *domain.PricingTracking) *models.PricingTracking {
	if tracking == nil {
		return nil
	}

	return &models.PricingTracking{
		ItineraryPricingResults: fromDomainItineraryPricingResults(tracking.ItineraryPricingResults),
		PassengerResults:        fromDomainPassengerResults(tracking.PassengerResults),
		OriginalTotalFareAmount: tracking.OriginalTotalFareAmount,
		FinalTotalFareAmount:    tracking.FinalTotalFareAmount,
		OriginalTotalFareBasic:  tracking.OriginalTotalFareBasic,
		FinalTotalFareBasic:     tracking.FinalTotalFareBasic,
		TotalHiddenFeeAmount:    tracking.TotalHiddenFeeAmount,
		TotalDiscountAmount:     tracking.TotalDiscountAmount,
	}
}

// toDomainTrackingPricingFromEmbedded converts models.TrackingPricing to domain.TrackingPricing
func toDomainTrackingPricingFromEmbedded(tracking *models.PricingTracking) *domain.PricingTracking {
	if tracking == nil {
		return nil
	}

	// Return nil if no tracking data
	if len(tracking.ItineraryPricingResults) == 0 && len(tracking.PassengerResults) == 0 &&
		tracking.TotalHiddenFeeAmount == 0 && tracking.TotalDiscountAmount == 0 {
		return nil
	}

	return &domain.PricingTracking{
		ItineraryPricingResults: toDomainItineraryPricingResults(tracking.ItineraryPricingResults),
		PassengerResults:        toDomainPassengerResults(tracking.PassengerResults),
		OriginalTotalFareAmount: tracking.OriginalTotalFareAmount,
		FinalTotalFareAmount:    tracking.FinalTotalFareAmount,
		OriginalTotalFareBasic:  tracking.OriginalTotalFareBasic,
		FinalTotalFareBasic:     tracking.FinalTotalFareBasic,
		TotalHiddenFeeAmount:    tracking.TotalHiddenFeeAmount,
		TotalDiscountAmount:     tracking.TotalDiscountAmount,
	}
}

// toDomainPassengerResults converts []*models.PassengerPricingResult to interface{}
func toDomainPassengerResults(ins []*models.PassengerPricingResult) []*domain.PassengerPricingResult {
	if ins == nil {
		return nil
	}

	out := make([]*domain.PassengerPricingResult, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainPassengerResult(item))
	}

	return out
}

// toDomainPassengerResult converts *models.PassengerPricingResult to *domain.PassengerPricingResult
func toDomainPassengerResult(in *models.PassengerPricingResult) *domain.PassengerPricingResult {
	if in == nil {
		return nil
	}

	return &domain.PassengerPricingResult{
		PassengerType:      in.PassengerType,
		Quantity:           in.Quantity,
		OriginalFareAmount: in.OriginalFareAmount,
		OriginalFareBasic:  in.OriginalFareBasic,
		TaxAmount:          in.TaxAmount,
		FinalFareAmount:    in.FinalFareAmount,
		FinalFareBasic:     in.FinalFareBasic,
		HiddenFeeAmount:    in.HiddenFeeAmount,
		DiscountAmount:     in.DiscountAmount,
	}
}
