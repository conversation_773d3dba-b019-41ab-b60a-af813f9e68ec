package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainPNR(in *domain.PNR) *models.PNR {
	if in == nil {
		return nil
	}

	return &models.PNR{
		SessionID:   in.SessionID,
		ListPax:     fromDomainPaxInfos(in.ListPax),
		ContactInfo: fromDomainContact(in.ContactInfo),
	}
}

func ToDomainPNRs(ins []*models.PNR) []*domain.PNR {
	out := make([]*domain.PNR, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainPNR(item))
	}

	return out
}

func ToDomainPNR(in *models.PNR) *domain.PNR {
	if in == nil {
		return nil
	}

	return &domain.PNR{
		SessionID:   in.SessionID,
		ListPax:     toDomainPaxInfos(in.ListPax),
		ContactInfo: toDomainContact(in.ContactInfo),
	}
}

func fromDomainContact(in *domain.Contact) *models.Contact {
	if in == nil {
		return nil
	}

	return &models.Contact{
		Surname:   in.Surname,
		GivenName: in.GivenName,
		PhoneCode: in.PhoneCode,
		Phone:     in.Phone,
		Email:     in.Email,
		Gender:    in.Gender,
	}
}

func fromDomainPaxInfos(ins []*domain.PaxInfo) []*models.PaxInfo {
	out := make([]*models.PaxInfo, 0, len(ins))

	for _, item := range ins {
		out = append(out, fromDomainPaxInfo(item))
	}

	return out
}

func fromDomainPaxInfo(in *domain.PaxInfo) *models.PaxInfo {
	if in == nil {
		return nil
	}

	paxInfo := &models.PaxInfo{
		Type:        in.Type,
		ID:          in.ID,
		Surname:     in.Surname,
		GivenName:   in.GivenName,
		DOB:         in.DOB,
		Age:         in.Age,
		PhoneCode:   in.PhoneCode,
		Nationality: in.Nationality,
		Phone:       in.Phone,
		Email:       in.Email,
		Gender:      in.Gender,
	}

	if in.Passport != nil {
		paxInfo.Passport = &models.PaxPassport{
			Number:         in.Passport.Number,
			ExpiryDate:     in.Passport.ExpiryDate,
			IssuingCountry: in.Passport.IssuingCountry,
		}
	}

	return paxInfo
}

func toDomainContact(in *models.Contact) *domain.Contact {
	if in == nil {
		return nil
	}

	return &domain.Contact{
		Surname:   in.Surname,
		GivenName: in.GivenName,
		PhoneCode: in.PhoneCode,
		Phone:     in.Phone,
		Email:     in.Email,
		Gender:    in.Gender,
	}
}

func toDomainPaxInfos(ins []*models.PaxInfo) []*domain.PaxInfo {
	out := make([]*domain.PaxInfo, 0, len(ins))

	for _, item := range ins {
		out = append(out, toDomainPaxInfo(item))
	}

	return out
}

func toDomainPaxInfo(in *models.PaxInfo) *domain.PaxInfo {
	if in == nil {
		return nil
	}

	paxInfo := &domain.PaxInfo{
		Type:        in.Type,
		ID:          in.ID,
		Surname:     in.Surname,
		GivenName:   in.GivenName,
		DOB:         in.DOB,
		Age:         in.Age,
		PhoneCode:   in.PhoneCode,
		Nationality: in.Nationality,
		Phone:       in.Phone,
		Email:       in.Email,
		Gender:      in.Gender,
	}

	if in.Passport != nil {
		paxInfo.Passport = &domain.PaxPassport{
			Number:         in.Passport.Number,
			ExpiryDate:     in.Passport.ExpiryDate,
			IssuingCountry: in.Passport.IssuingCountry,
		}
	}

	return paxInfo
}
