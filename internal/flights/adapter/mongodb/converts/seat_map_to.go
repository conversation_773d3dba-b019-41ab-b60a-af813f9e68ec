package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToDomainSeatSegments(items []*models.SeatSegment) []*domain.SeatSegment {
	if len(items) == 0 {
		return nil
	}

	res := make([]*domain.SeatSegment, 0, len(items))
	for _, seatSegment := range items {
		res = append(res, ToDomainSeatSegment(seatSegment))
	}

	return res
}

func ToDomainSeatSegment(info *models.SeatSegment) *domain.SeatSegment {
	if info == nil {
		return nil
	}
	res := &domain.SeatSegment{
		SegmentIndex: info.SegmentIndex,
		Key:          info.Key,
		ItineraryID:  info.ItineraryID,
		SeatOptions:  toDomainSeatOptions(info.SeatOptions),
		ExpiredAt:    info.ExpiredAt,
	}

	return res
}

func toDomainSeatOptions(items []*models.SeatOption) []*domain.SeatOption {
	res := make([]*domain.SeatOption, 0, len(items))
	for _, seatOption := range items {
		res = append(res, toDomainSeatOption(seatOption))
	}

	return res
}

func toDomainSeatOption(info *models.SeatOption) *domain.SeatOption {
	if info == nil {
		return nil
	}

	return &domain.SeatOption{
		CabinClass: info.CabinClass,
		Rows:       toDomainSeatRows(info.Rows),
		Columns:    toDomainColumnHeaders(info.Columns),
	}
}

func toDomainColumnHeaders(items []*models.ColumnHeader) []*domain.ColumnHeader {
	if len(items) == 0 {
		return nil
	}

	res := make([]*domain.ColumnHeader, 0, len(items))
	for _, columnHeader := range items {
		res = append(res, toDomainColumnHeader(columnHeader))
	}

	return res
}

func toDomainColumnHeader(info *models.ColumnHeader) *domain.ColumnHeader {
	if info == nil {
		return nil
	}

	return &domain.ColumnHeader{
		Title:   info.Title,
		IsAisle: info.IsAisle,
	}
}

func toDomainSeatRow(info *models.SeatRow) *domain.SeatRow {
	if info == nil {
		return nil
	}

	return &domain.SeatRow{
		RowNumber:  info.RowNumber,
		Facilities: toDomainSeatFacilities(info.Facilities),
	}
}

func toDomainSeatRows(items []*models.SeatRow) []*domain.SeatRow {
	if len(items) == 0 {
		return nil
	}
	res := make([]*domain.SeatRow, 0, len(items))

	for _, seatRow := range items {
		res = append(res, toDomainSeatRow(seatRow))
	}

	return res
}

func ToDomainSeatFacility(info *models.SeatFacility) *domain.SeatFacility {
	if info == nil {
		return nil
	}

	res := &domain.SeatFacility{
		Type:         info.Type,
		SeatCode:     info.SeatCode,
		Availability: info.Availability,
		SelectionKey: info.SelectionKey,
	}

	if info.SeatCharge != nil {
		res.SeatCharge = &domain.SeatCharge{
			BaseAmount:  info.SeatCharge.BaseAmount,
			TaxAmount:   info.SeatCharge.TaxAmount,
			TotalAmount: info.SeatCharge.TotalAmount,
			Currency:    info.SeatCharge.Currency,
		}
	}

	if info.Property != nil {
		res.Property = &domain.SeatProperty{
			Aisle:                 info.Property.Aisle,
			Window:                info.Property.Window,
			EmergencyExit:         info.Property.EmergencyExit,
			OverWing:              info.Property.OverWing,
			Bassinet:              info.Property.Bassinet,
			NotAllowedForInfant:   info.Property.NotAllowedForInfant,
			NotSuitableForInfant:  info.Property.NotSuitableForInfant,
			NotSuitableForChild:   info.Property.NotSuitableForChild,
			HandicappedFacilities: info.Property.HandicappedFacilities,
			ExtraLegroom:          info.Property.ExtraLegroom,
		}
	}

	return res
}

func toDomainSeatFacilities(items []*models.SeatFacility) []*domain.SeatFacility {
	if len(items) == 0 {
		return nil
	}

	res := make([]*domain.SeatFacility, 0, len(items))
	for _, facility := range items {
		res = append(res, ToDomainSeatFacility(facility))
	}

	return res
}
