package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainSalesforceConfig(in *domain.SalesforceConfig) *models.SalesforceConfig {
	if in == nil {
		return nil
	}

	return &models.SalesforceConfig{
		OfficeID:     in.OfficeID,
		EnableReport: in.EnableReport,
	}
}

func ToDomainSalesforceConfig(in *models.SalesforceConfig) *domain.SalesforceConfig {
	if in == nil {
		return nil
	}

	return &domain.SalesforceConfig{
		OfficeID:     in.OfficeID,
		EnableReport: in.EnableReport,
	}
}
