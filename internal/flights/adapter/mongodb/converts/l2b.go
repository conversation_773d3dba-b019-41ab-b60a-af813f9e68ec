package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func ToDomainL2bTrackingItem(in *models.L2bTrackingItem) *domain.L2bTrackingItem {
	if in == nil {
		return nil
	}

	return &domain.L2bTrackingItem{
		RequestedAt: in.RequestedAt,
		OfficeID:    in.OfficeID,
		Month:       in.Month,
		Year:        in.Year,
		API:         enum.L2bAPIValue[in.APIName],
	}
}

func ToDomainL2bTrackingItems(ins []*models.L2bTrackingItem) []*domain.L2bTrackingItem {
	out := make([]*domain.L2bTrackingItem, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainL2bTrackingItem(item))
	}

	return out
}

func FromDomainL2bTrackingItem(in *domain.L2bTrackingItem) *models.L2bTrackingItem {
	if in == nil {
		return nil
	}

	return &models.L2bTrackingItem{
		RequestedAt: in.RequestedAt,
		OfficeID:    in.OfficeID,
		Month:       in.Month,
		Year:        in.Year,
		APIName:     enum.L2bAPIName[in.API],
	}
}

func FromDomainL2bTrackingItems(ins []*domain.L2bTrackingItem) []*models.L2bTrackingItem {
	out := make([]*models.L2bTrackingItem, 0, len(ins))

	for _, item := range ins {
		out = append(out, FromDomainL2bTrackingItem(item))
	}

	return out
}

func ToDomainL2bMTDInfo(in *models.L2bMTDInfo) *domain.L2bMTDInfo {
	if in == nil {
		return nil
	}

	return &domain.L2bMTDInfo{
		MTD:          in.MTD,
		OfficeID:     in.OfficeID,
		TotalRequest: in.TotalRequest,
		TotalIssued:  in.TotalIssued,
		L2bRate:      in.L2bRate,
	}
}
