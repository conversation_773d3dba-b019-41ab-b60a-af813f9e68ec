package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToDomainAmadeusISOCountry(in *models.AmadeusISOCountry) *domain.AmadeusISOCountry {
	if in == nil {
		return nil
	}

	return &domain.AmadeusISOCountry{
		Code:        in.Code,
		PaidTo:      in.PaidTo,
		Description: in.Description,
	}
}

func ToDomainAmadeusISOCountries(in []*models.AmadeusISOCountry) []*domain.AmadeusISOCountry {
	if len(in) == 0 {
		return nil
	}

	res := make([]*domain.AmadeusISOCountry, 0, len(in))
	for _, item := range in {
		res = append(res, ToDomainAmadeusISOCountry(item))
	}
	return res
}
