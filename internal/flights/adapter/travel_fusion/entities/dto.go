package entities

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/enum"
)

type ProcessDetailsReq struct {
	RoutingID string
	OutwardID string
	ReturnID  string
}

type Traveller struct {
	Type              enum.PaxType
	GenderType        enum.GenderType
	FirstName         string
	LastName          string
	Nationality       string
	DOB               int64
	Age               int
	Passport          *Passport
	LuggageOptionsRef []*LuggageOptionsRef
	SeatOptions       string
}

type LuggageOptionsRef struct {
	RefNumber     string
	LuggageOption enum.LuggageOption
}
type Passport struct {
	PassportNumber         string
	PassportExpiryDate     int64
	PassportCountryOfIssue string
}
type ContactInfo struct {
	FirstName   string
	LastName    string
	Email       string
	PhoneCode   string
	PhoneNumber string
	Title       string
}

type Address struct {
	Company        string
	Flat           string
	BuildingName   string
	BuildingNumber string
	Street         string
	Locality       string
	City           string
	Province       string
	Postcode       string
	CountryCode    string
}

type CreditCard struct {
	Company      string
	OwnerName    string
	Number       string
	SecurityCode string
	ExpiryDate   int64
	StartDate    int64
	CardType     string
	IssueNumber  string
}

type BillDetail struct {
	*Traveller
	*Address
	*CreditCard
}

type ProcessTermsReq struct {
	RoutingID           string
	OutwardID           string
	ReturnID            string
	Travelers           []*Traveller
	ContactInfo         *ContactInfo
	Address             *Address
	BillDetail          *BillDetail
	EndUserIPAddress    string
	EndUserBrowserAgent string
	EndUserCountryCode  string
}

type FakeBooking struct {
	EnableFakeBooking                bool
	FakeBookingSimulatedDelaySeconds int
	FakeBookingStatus                enum.BookingStatus
	EnableFakeCardVerification       bool
}

type StartBookingReq struct {
	BookingCode string
	FakeBooking FakeBooking
	Amount      float64
	Currency    string
}

type CheckBooingReq struct {
	BookingCode string
}

type GetBookingDetailsReq struct {
	BookingCode string
}
