package schema

// CommonPrice ...
type CommonPrice struct {
	Type               string                       `xml:"Type"`
	Amount             float64                      `xml:"Amount"`
	Currency           *CommonTLA                   `xml:"Currency"`
	BillingAmount      float64                      `xml:"BillingAmount"`
	BillingCurrency    *CommonTLA                   `xml:"BillingCurrency"`
	AmountWithoutTax   float64                      `xml:"AmountWithoutTax"`
	PriceIncludesTax   bool                         `xml:"PriceIncludesTax"`
	TaxIsEstimated     bool                         `xml:"TaxIsEstimated"`
	TaxItemList        *PriceTaxItemListType        `xml:"TaxItemList"`
	PassengerPriceList *PricePassengerPriceListType `xml:"PassengerPriceList"`
	Age                *CommonAge                   `xml:"Age"`
}

// MinimumCardCharge ...
type PriceMinimumCardCharge struct {
	Amount   float64    `xml:"Amount"`
	Currency *CommonTLA `xml:"Currency"`
}

// EnhancedPriceWithLuggage ...
type EnhancedPriceWithLuggage struct {
	Amount            float64                 `xml:"Amount"`
	Currency          *CommonTLA              `xml:"Currency"`
	MinimumCardCharge *PriceMinimumCardCharge `xml:"MinimumCardCharge"`
}

// PriceTypeType ...
type PriceTypeType string

// PriceTaxItemListType ...
type PriceTaxItemListType struct {
	TaxItem []*TaxItemType `xml:"TaxItem"`
}

// TaxItemType ...
type TaxItemType struct {
	Type     string     `xml:"Type"`
	Code     string     `xml:"Code"`
	Name     string     `xml:"Name"`
	Amount   float64    `xml:"Amount"`
	Currency *CommonTLA `xml:"Currency"`
}

// PricePassengerPriceListType ...
type PricePassengerPriceListType struct {
	PassengerPrice []*CommonPrice `xml:"PassengerPrice"`
}

// CommonSimplePrice ...
type CommonSimplePrice struct {
	Amount   float64    `xml:"Amount"`
	Currency *CommonTLA `xml:"Currency"`
}
