package requests

// StartRoutingPrePackagedType ...
type StartRoutingPrePackagedType struct {
	MillisAttr         int                      `xml:"millis,attr,omitempty"`
	EcodeAttr          string                   `xml:"ecode,attr,omitempty"`
	EtextAttr          string                   `xml:"etext,attr,omitempty"`
	EdetailAttr        string                   `xml:"edetail,attr,omitempty"`
	EdateAttr          string                   `xml:"edate,attr,omitempty"`
	HandlerSelect      string                   `xml:"HandlerSelect"`
	XmlLoginId         string                   `xml:"XmlLoginId"`
	LoginId            string                   `xml:"LoginId"`
	Origin             *StartRoutingLocation    `xml:"Origin"`
	Destination        *StartRoutingLocation    `xml:"Destination"`
	NumberOfAdults     string                   `xml:"NumberOfAdults"`
	DepartureDates     *StartRoutingDatesType   `xml:"DepartureDates"`
	DepartureDateRange int                      `xml:"DepartureDateRange"`
	Duration           *PrePackagedDurationType `xml:"Duration"`
	SupplierList       *CommonSupplierList      `xml:"SupplierList"`
	Timeout            int                      `xml:"Timeout"`
	RequestOrdinalId   int                      `xml:"RequestOrdinalId"`
	FromPrefetcher     bool                     `xml:"FromPrefetcher"`
}

// StartRoutingPrePackagedNumberOfAdultsType ...
type StartRoutingPrePackagedNumberOfAdultsType string

// PrePackagedDurationType ...
type PrePackagedDurationType struct {
	Type     string `xml:"Type"`
	Quantity int    `xml:"Quantity"`
}

// PrePackagedDurationTypeType ...
type PrePackagedDurationTypeType string
