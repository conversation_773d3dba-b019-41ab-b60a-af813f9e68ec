package requests

// GeneralRequestCommandListType ...
type CommandList struct {
	Login                            *LoginType                            `xml:"Login,omitempty"`
	StartRouting                     *StartRoutingType                     `xml:"StartRouting,omitempty"`
	StartManageBookingPlane          *StartManageBookingPlaneType          `xml:"StartManageBookingPlane,omitempty"`
	CheckRouting                     *CheckRoutingType                     `xml:"CheckRouting,omitempty"`
	ProcessDetails                   *ProcessDetailsType                   `xml:"ProcessDetails,omitempty"`
	ProcessTerms                     *ProcessTermsType                     `xml:"ProcessTerms,omitempty"`
	StartBooking                     *StartBookingType                     `xml:"StartBooking,omitempty"`
	CheckBooking                     *CheckBookingType                     `xml:"CheckBooking,omitempty"`
	GetBookingDetails                *GetBookingDetailsType                `xml:"GetBookingDetails,omitempty"`
	ResolveLocation                  *ResolveLocationType                  `xml:"ResolveLocation,omitempty"`
	GetCurrencies                    *GetCurrenciesType                    `xml:"GetCurrencies,omitempty"`
	GetCountries                     *GetCountriesType                     `xml:"GetCountries,omitempty"`
	GetPrePackagedSupplierSummary    *GetPrePackagedSupplierSummaryType    `xml:"GetPrePackagedSupplierSummary,omitempty"`
	GetHotelDetails                  *GetHotelDetailsType                  `xml:"GetHotelDetails,omitempty"`
	GetMultipleHotelDetails          *GetMultipleHotelDetailsType          `xml:"GetMultipleHotelDetails,omitempty"`
	GetStats                         *GetStatsType                         `xml:"GetStats,omitempty"`
	GetErrorCodes                    *GetErrorCodesType                    `xml:"GetErrorCodes,omitempty"`
	StartRoutingHotel                *StartRoutingHotelType                `xml:"StartRoutingHotel,omitempty"`
	CheckRoutingHotel                *CheckRoutingHotelType                `xml:"CheckRoutingHotel,omitempty"`
	ProcessDetailsHotel              *ProcessDetailsHotelType              `xml:"ProcessDetailsHotel,omitempty"`
	StartDetailsHotel                *StartDetailsHotelType                `xml:"StartDetailsHotel,omitempty"`
	CheckDetailsHotel                *CheckDetailsHotelType                `xml:"CheckDetailsHotel,omitempty"`
	StartTermsHotel                  *StartTermsHotelType                  `xml:"StartTermsHotel,omitempty"`
	CheckTermsHotel                  *CheckTermsHotelType                  `xml:"CheckTermsHotel,omitempty"`
	StartBookingHotel                *StartBookingHotelType                `xml:"StartBookingHotel,omitempty"`
	CheckBookingHotel                *CheckBookingHotelType                `xml:"CheckBookingHotel,omitempty"`
	StartRoutingCar                  *StartRoutingCarType                  `xml:"StartRoutingCar,omitempty"`
	CheckRoutingCar                  *CheckRoutingCarType                  `xml:"CheckRoutingCar,omitempty"`
	ProcessDetailsCar                *ProcessDetailsCarType                `xml:"ProcessDetailsCar,omitempty"`
	StartRoutingPrePackaged          *StartRoutingPrePackagedType          `xml:"StartRoutingPrePackaged,omitempty"`
	StartRoutingPrePackagedTF        *StartRoutingPrePackagedType          `xml:"StartRoutingPrePackagedTF,omitempty"`
	CheckRoutingPrePackaged          *CheckRoutingPrePackagedType          `xml:"CheckRoutingPrePackaged,omitempty"`
	CheckRoutingPrePackagedTF        *CheckRoutingPrePackagedType          `xml:"CheckRoutingPrePackagedTF,omitempty"`
	LogPrePackagedReferral           *LogPrePackagedReferralType           `xml:"LogPrePackagedReferral,omitempty"`
	ProcessCardVerification          *ProcessCardVerificationType          `xml:"ProcessCardVerification,omitempty"`
	CalculateExtraServiceCharge      *CalculateExtraServiceChargeType      `xml:"CalculateExtraServiceCharge,omitempty"`
	GetSupportedCardTypeList         *GetSupportedCardTypeList             `xml:"GetSupportedCardTypeList,omitempty"`
	StoreValueInVault                *StoreValueInVaultRequestType         `xml:"StoreValueInVault,omitempty"`
	DeleteValueInVault               *DeleteValueInVaultRequestType        `xml:"DeleteValueInVault,omitempty"`
	GetSupplierRoutes                *GetSupplierRoutesType                `xml:"GetSupplierRoutes,omitempty"`
	GetExtraServiceCharge            *GetExtraServiceChargeType            `xml:"GetExtraServiceCharge,omitempty"`
	ListSupplierRoutes               *ListSupplierRoutesType               `xml:"ListSupplierRoutes,omitempty"`
	ListTrainStations                *ListTrainStationsType                `xml:"ListTrainStations,omitempty"`
	GetSuppliersTravellerInfo        *GetSuppliersTravellerInfoType        `xml:"GetSuppliersTravellerInfo,omitempty"`
	GetBranchSupplierList            *GetBranchSupplierListType            `xml:"GetBranchSupplierList,omitempty"`
	GetSuppliersRoutingParameterList *GetSuppliersRoutingParameterListType `xml:"GetSuppliersRoutingParameterList,omitempty"`
}
