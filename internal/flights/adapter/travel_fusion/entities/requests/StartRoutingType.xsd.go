package requests

// NumberOfBags ...
type NumberOfBags struct {
	EcodeAttr string `xml:"ecode,attr,omitempty"`
	EtextAttr string `xml:"etext,attr,omitempty"`
	*CommonPositiveInteger
}

// MinimumCardChargeType ...
type MinimumCardChargeType struct {
	EcodeAttr string `xml:"ecode,attr,omitempty"`
	EtextAttr string `xml:"etext,attr,omitempty"`
	Value     string `xml:",chardata"`
}

// MinimumCardChargeTypeList ...
type MinimumCardChargeTypeList struct {
	MinimumCardChargeType []*MinimumCardChargeType `xml:"MinimumCardChargeType"`
}

// StartRoutingType ...
type StartRoutingType struct {
	MillisAttr            int                                 `xml:"millis,attr,omitempty"`
	HandlerSelect         string                              `xml:"HandlerSelect,omitempty"`
	XmlLoginId            string                              `xml:"XmlLoginId"`
	LoginId               string                              `xml:"LoginId"`
	Mode                  string                              `xml:"Mode"`
	ProductType           string                              `xml:"ProductType,omitempty"`
	Origin                *StartRoutingLocation               `xml:"Origin"`
	Destination           *StartRoutingLocation               `xml:"Destination"`
	OutwardDates          *StartRoutingDatesType              `xml:"OutwardDates"`
	ReturnDates           *StartRoutingDatesType              `xml:"ReturnDates"`
	AdditionalTripList    *StartRoutingAdditionalTripListType `xml:"AdditionalTripList"`
	MaxChanges            int                                 `xml:"MaxChanges,omitempty"`
	MaxHops               int                                 `xml:"MaxHops,omitempty"`
	BookingChange         *BookingChangeType                  `xml:"BookingChange,omitempty"`
	SupplierList          *CommonSupplierList                 `xml:"SupplierList,omitempty"`
	SupplierListType      *CommonTypeOfList                   `xml:"SupplierListType,omitempty"`
	RealOperatorFilter    *OperatorFilterType                 `xml:"RealOperatorFilter,omitempty"`
	VendingOperatorFilter *OperatorFilterType                 `xml:"VendingOperatorFilter,omitempty"`
	Timeout               int                                 `xml:"Timeout"`
	// TravelClass               *TfClassEnumerationType             `xml:"TravelClass"`
	TravellerList             *StartRoutingTravellerList `xml:"TravellerList"`
	NumberOfBags              *NumberOfBags              `xml:"NumberOfBags,omitempty"`
	ShowCardCharges           bool                       `xml:"ShowCardCharges,omitempty"`
	ShowLuggageCharges        bool                       `xml:"ShowLuggageCharges,omitempty"`
	ShowCheckInCharges        bool                       `xml:"ShowCheckInCharges,omitempty"`
	ShowSpeedyBoardingCharges bool                       `xml:"ShowSpeedyBoardingCharges,omitempty"`
	MinimumCardChargeTypeList *MinimumCardChargeTypeList `xml:"MinimumCardChargeTypeList,omitempty"`
	IncrementalResults        bool                       `xml:"IncrementalResults"`
	BookingProfile            *BookingProfileType        `xml:"BookingProfile,omitempty"`
	PerformSearch             bool                       `xml:"PerformSearch,omitempty"`
}

// UseAirportIntelligenceType ...
type UseAirportIntelligenceType struct {
	Enabled     bool `xml:"Enabled"`
	FirstCheck  int  `xml:"FirstCheck"`
	SecondCheck int  `xml:"SecondCheck"`
}

// BookingChangeType ...
type BookingChangeType struct {
	TFBookingReference  string                          `xml:"TFBookingReference"`
	AuthOption          *AuthOptionType                 `xml:"AuthOption"`
	ChangeAttributeList *BookingChangeAttributeListType `xml:"ChangeAttributeList"`
}

// BookingChangeAttributeListType ...
type BookingChangeAttributeListType struct {
	ChangeOutwardDate bool `xml:"ChangeOutwardDate"`
	ChangeReturnDate  bool `xml:"ChangeReturnDate"`
	ChangeOrigin      bool `xml:"ChangeOrigin"`
	ChangeDestination bool `xml:"ChangeDestination"`
	ChangeBaggage     bool `xml:"ChangeBaggage"`
	ChangeInsurance   bool `xml:"ChangeInsurance"`
	ChangeSeat        bool `xml:"ChangeSeat"`
	Cancel            bool `xml:"Cancel"`
}

// StartRoutingLocation ...
type StartRoutingLocation struct {
	Descriptor             string                              `xml:"Descriptor"`
	Type                   string                              `xml:"Type"`
	ResolutionTypeList     *StartRoutingResolutionTypeListType `xml:"ResolutionTypeList"`
	Radius                 int                                 `xml:"Radius"`
	Text                   string                              `xml:"Text"`
	UseAirportIntelligence *UseAirportIntelligenceType         `xml:"UseAirportIntelligence"`
}

// StartRoutingLocationTypeType ...
type StartRoutingLocationTypeType string

// StartRoutingResolutionTypeListType ...
type StartRoutingResolutionTypeListType struct {
	ResolutionType []string `xml:"ResolutionType"`
}

// StartRoutingDatesType ...
type StartRoutingDatesType struct {
	DateOfSearch     string          `xml:"DateOfSearch"`
	DepartDateFilter *DateFilterType `xml:"DepartDateFilter"`
	ArriveDateFilter *DateFilterType `xml:"ArriveDateFilter"`
}

// DateFilterType ...
type DateFilterType struct {
	DiscardBefore string `xml:"DiscardBefore"`
	DiscardAfter  string `xml:"DiscardAfter"`
}

// OperatorFilterType ...
type OperatorFilterType struct {
	Type         string                        `xml:"Type"`
	AllowPartial bool                          `xml:"AllowPartial"`
	OperatorList *StartRoutingOperatorListType `xml:"OperatorList"`
}

// OperatorFilterTypeType ...
type OperatorFilterTypeType string

// StartRoutingOperatorListType ...
type StartRoutingOperatorListType struct {
	Operator []*CommonOperatorCode `xml:"Operator"`
}

// StartRoutingTravellerList ...
type StartRoutingTravellerList struct {
	Traveller []*StartRoutingTraveller `xml:"Traveller"`
}

// StartRoutingTraveller ...
type StartRoutingTraveller struct {
	Age int `xml:"Age"`
}

// StartRoutingAdditionalTripType ...
type StartRoutingAdditionalTripType struct {
	Origin       *StartRoutingLocation  `xml:"Origin"`
	Destination  *StartRoutingLocation  `xml:"Destination"`
	OutwardDates *StartRoutingDatesType `xml:"OutwardDates"`
}

// StartRoutingAdditionalTripListType ...
type StartRoutingAdditionalTripListType struct {
	AdditionalTrip []*StartRoutingAdditionalTripType `xml:"AdditionalTrip"`
}
