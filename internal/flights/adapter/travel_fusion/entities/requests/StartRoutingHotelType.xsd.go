package requests

// StartRoutingHotelType ...
type StartRoutingHotelType struct {
	MillisAttr            int                        `xml:"millis,attr,omitempty"`
	XmlLoginId            string                     `xml:"XmlLoginId"`
	LoginId               string                     `xml:"LoginId"`
	Destination           *StartRoutingLocation      `xml:"Destination"`
	CheckinDates          *StartRoutingDatesType     `xml:"CheckinDates"`
	CheckoutDates         *StartRoutingDatesType     `xml:"CheckoutDates"`
	SupplierList          *CommonSupplierList        `xml:"SupplierList"`
	Timeout               int                        `xml:"Timeout"`
	AccommodationTypeList *AccommodationTypeListType `xml:"AccommodationTypeList"`
	RoomList              *HotelRequestRoomListType  `xml:"RoomList"`
	IncludeHotelDetails   string                     `xml:"IncludeHotelDetails"`
	IncrementalResults    bool                       `xml:"IncrementalResults"`
	BookingProfile        *BookingProfileType        `xml:"BookingProfile"`
	PerformSearch         bool                       `xml:"PerformSearch"`
}

// IncludeHotelDetailsFalseEnumeration ...
type IncludeHotelDetailsFalseEnumeration string

// AccommodationTypesEnumeration ...
type AccommodationTypesEnumeration string

// AccommodationTypeListType ...
type AccommodationTypeListType struct {
	AccommodationType []string `xml:"AccommodationType"`
}

// HotelRequestRoomListType ...
type HotelRequestRoomListType struct {
	Room []*HotelRequestRoomType `xml:"Room"`
}

// HotelRequestRoomType ...
type HotelRequestRoomType struct {
	Type          string                     `xml:"Type"`
	TravellerList *StartRoutingTravellerList `xml:"TravellerList"`
}

// RoomTypesEnumeration ...
type RoomTypesEnumeration string
