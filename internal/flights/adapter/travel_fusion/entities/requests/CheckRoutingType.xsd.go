package requests

// CheckRoutingType ...
type CheckRoutingType struct {
	MillisAttr int                       `xml:"millis,attr,omitempty"`
	XmlLoginId string                    `xml:"XmlLoginId"`
	LoginId    string                    `xml:"LoginId"`
	RoutingId  string                    `xml:"RoutingId"`
	Filter     *CheckRoutingFilterType   `xml:"Filter"`
	SortList   *CheckRoutingSortListType `xml:"SortList"`
}

// CheckRoutingFilterType ...
type CheckRoutingFilterType struct {
	FirstResult                int                               `xml:"FirstResult"`
	NumberOfResults            int                               `xml:"NumberOfResults"`
	SupplierList               *CommonSupplierList               `xml:"SupplierList"`
	OriginAirportCodeList      *CommonOriginAirportCodeList      `xml:"OriginAirportCodeList"`
	DestinationAirportCodeList *CommonDestinationAirportCodeList `xml:"DestinationAirportCodeList"`
}

// CheckRoutingSortOrderType ...
type CheckRoutingSortOrderType string

// CheckRoutingSortType ...
type CheckRoutingSortType struct {
	Order string `xml:"Order"`
	Type  string `xml:"Type"`
}

// CheckRoutingSortListType ...
type CheckRoutingSortListType struct {
	Sort []*CheckRoutingSortType `xml:"Sort"`
}

// CheckRoutingSortTypeType ...
type CheckRoutingSortTypeType string
