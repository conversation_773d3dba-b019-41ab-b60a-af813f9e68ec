package requests

// ProcessDetailsType ...
type ProcessDetailsType struct {
	MillisAttr            int                 `xml:"millis,attr,omitempty"`
	XmlLoginId            string              `xml:"XmlLoginId"`
	LoginId               string              `xml:"LoginId"`
	RoutingId             string              `xml:"RoutingId,omitempty"`
	OutwardId             string              `xml:"OutwardId,omitempty"`
	ReturnId              string              `xml:"ReturnId,omitempty"`
	BookingProfile        *BookingProfileType `xml:"BookingProfile,omitempty"`
	HandoffParametersOnly bool                `xml:"HandoffParametersOnly,omitempty"`
}
