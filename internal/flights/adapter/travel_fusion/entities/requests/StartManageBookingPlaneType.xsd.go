package requests

// StartManageBookingPlaneType ...
type StartManageBookingPlaneType struct {
	MillisAttr         int                       `xml:"millis,attr,omitempty"`
	HandlerSelect      string                    `xml:"HandlerSelect"`
	XmlLoginId         string                    `xml:"XmlLoginId"`
	LoginId            string                    `xml:"LoginId"`
	Mode               *CommonMode               `xml:"Mode"`
	Supplier           *CommonSupplierIdentifier `xml:"Supplier"`
	TFBookingReference string                    `xml:"TFBookingReference"`
	AuthOption         *AuthOptionType           `xml:"AuthOption"`
	Timeout            int                       `xml:"Timeout"`
	BookingProfile     *BookingProfileType       `xml:"BookingProfile"`
}

// AuthOptionItem ...
type AuthOptionItem struct {
	Name  string `xml:"Name"`
	Value string `xml:"Value"`
}

// AuthOptionItemList ...
type AuthOptionItemList struct {
	AuthOptionItem []*AuthOptionItem `xml:"AuthOptionItem"`
}

// AuthOptionType ...
type AuthOptionType struct {
	AuthOptionItemList *AuthOptionItemList `xml:"AuthOptionItemList"`
}
