package requests

// GetBookingDetailsType ...
type GetBookingDetailsType struct {
	MillisAttr                   int                       `xml:"millis,attr,omitempty"`
	XmlLoginId                   string                    `xml:"XmlLoginId"`
	LoginId                      string                    `xml:"LoginId"`
	Supplier                     *CommonSupplierIdentifier `xml:"Supplier"`
	SourceRef                    string                    `xml:"SourceRef"`
	AuthenticateByProfile        string                    `xml:"AuthenticateByProfile"`
	TFBookingReference           string                    `xml:"TFBookingReference"`
	SupplierReference            string                    `xml:"SupplierReference"`
	AuthOption                   *AuthOptionType           `xml:"AuthOption"`
	IncludeBookingProfileHistory string                    `xml:"IncludeBookingProfileHistory"`
	BookingProfile               *BookingProfileType       `xml:"BookingProfile"`
}

// GetCountriesType ...
type GetCountriesType struct {
	MillisAttr int    `xml:"millis,attr,omitempty"`
	XmlLoginId string `xml:"XmlLoginId"`
	LoginId    string `xml:"LoginId"`
}

// GetCurrenciesType ...
type GetCurrenciesType struct {
	MillisAttr int    `xml:"millis,attr,omitempty"`
	XmlLoginId string `xml:"XmlLoginId"`
	LoginId    string `xml:"LoginId"`
	Code       string `xml:"Code"`
}

// GetErrorCodesType ...
type GetErrorCodesType struct {
	MillisAttr int    `xml:"millis,attr,omitempty"`
	XmlLoginId string `xml:"XmlLoginId"`
	LoginId    string `xml:"LoginId"`
}

// LoginType ...
type LoginType struct {
	MillisAttr int    `xml:"millis,attr,omitempty"`
	Username   string `xml:"Username"`
	Password   string `xml:"Password"`
}

// GetPrePackagedSupplierSummaryType ...
type GetPrePackagedSupplierSummaryType struct {
	MillisAttr int    `xml:"millis,attr,omitempty"`
	XmlLoginId string `xml:"XmlLoginId"`
	LoginId    string `xml:"LoginId"`
	Supplier   string `xml:"Supplier"`
}

// GetHotelDetailsType ...
type GetHotelDetailsType struct {
	EcodeAttr           string              `xml:"ecode,attr,omitempty"`
	EtextAttr           string              `xml:"etext,attr,omitempty"`
	EdetailAttr         string              `xml:"edetail,attr,omitempty"`
	EdateAttr           string              `xml:"edate,attr,omitempty"`
	XmlLoginId          string              `xml:"XmlLoginId"`
	LoginId             string              `xml:"LoginId"`
	HotelId             string              `xml:"HotelId"`
	Language            string              `xml:"Language"`
	SupplierList        *CommonSupplierList `xml:"SupplierList"`
	IncludeHotelDetails bool                `xml:"IncludeHotelDetails"`
}

// GetMultipleHotelDetailsType ...
type GetMultipleHotelDetailsType struct {
	MillisAttr          int                 `xml:"millis,attr,omitempty"`
	XmlLoginId          string              `xml:"XmlLoginId"`
	LoginId             string              `xml:"LoginId"`
	HotelIdList         *HotelIdListType    `xml:"HotelIdList"`
	Language            string              `xml:"Language"`
	SupplierList        *CommonSupplierList `xml:"SupplierList"`
	IncludeHotelDetails bool                `xml:"IncludeHotelDetails"`
}

// HotelIdListType ...
type HotelIdListType struct {
	HotelId []*CommonId `xml:"HotelId"`
}

// LogPrePackagedReferralType ...
type LogPrePackagedReferralType struct {
	MillisAttr int    `xml:"millis,attr,omitempty"`
	XmlLoginId string `xml:"XmlLoginId"`
	LoginId    string `xml:"LoginId"`
	Supplier   string `xml:"Supplier"`
}

// ProcessCardVerificationType ...
type ProcessCardVerificationType struct {
	MillisAttr         int    `xml:"millis,attr,omitempty"`
	XmlLoginId         string `xml:"XmlLoginId"`
	LoginId            string `xml:"LoginId"`
	Command            string `xml:"Command"`
	TFBookingReference string `xml:"TFBookingReference"`
	ReturnUrl          string `xml:"ReturnUrl"`
}
