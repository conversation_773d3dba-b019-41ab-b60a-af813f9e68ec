package requests

// StartRoutingCarType ...
type StartRoutingCarType struct {
	MillisAttr         int                        `xml:"millis,attr,omitempty"`
	XmlLoginId         string                     `xml:"XmlLoginId"`
	LoginId            string                     `xml:"LoginId"`
	Pickup             *StartRoutingLocation      `xml:"Pickup"`
	Setdown            *StartRoutingLocation      `xml:"Setdown"`
	PickupDates        *StartRoutingDatesType     `xml:"PickupDates"`
	SetdownDates       *StartRoutingDatesType     `xml:"SetdownDates"`
	SupplierList       *CommonSupplierList        `xml:"SupplierList"`
	Timeout            int                        `xml:"Timeout"`
	TravellerList      *StartRoutingTravellerList `xml:"TravellerList"`
	IncrementalResults bool                       `xml:"IncrementalResults"`
	BookingProfile     *BookingProfileType        `xml:"BookingProfile"`
	PerformSearch      bool                       `xml:"PerformSearch"`
}
