package requests

// ProcessTermsType ...
type ProcessTermsType struct {
	MillisAttr     int                 `xml:"millis,attr,omitempty"`
	XmlLoginId     string              `xml:"XmlLoginId"`
	LoginId        string              `xml:"LoginId"`
	RoutingId      string              `xml:"RoutingId"`
	OutwardId      string              `xml:"OutwardId"`
	ReturnId       string              `xml:"ReturnId,omitempty"`
	BookingProfile *BookingProfileType `xml:"BookingProfile"`
}
