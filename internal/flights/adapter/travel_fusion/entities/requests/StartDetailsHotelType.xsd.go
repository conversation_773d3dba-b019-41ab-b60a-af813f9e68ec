package requests

// StartDetailsHotelType ...
type StartDetailsHotelType struct {
	MillisAttr            int                       `xml:"millis,attr,omitempty"`
	XmlLoginId            string                    `xml:"XmlLoginId"`
	LoginId               string                    `xml:"LoginId"`
	RoutingId             string                    `xml:"RoutingId"`
	OptionId              string                    `xml:"OptionId"`
	RoomExtraList         *RoomExtraListDetailsType `xml:"RoomExtraList"`
	HandoffParametersOnly bool                      `xml:"HandoffParametersOnly"`
}

// RoomExtraListDetailsType ...
type RoomExtraListDetailsType struct {
	RoomExtra []*RoomExtra `xml:"RoomExtra"`
}

// RoomExtra ...
type RoomExtra struct {
	RoomIdAttr int         `xml:"roomId,attr,omitempty"`
	ExtraId    []*CommonId `xml:"ExtraId"`
}
