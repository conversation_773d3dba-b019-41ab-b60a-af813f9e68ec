package requests

// GetStatsType ...
type GetStatsType struct {
	MillisAttr                    int                             `xml:"millis,attr,omitempty"`
	HandlerSelect                 string                          `xml:"HandlerSelect"`
	XmlLoginId                    string                          `xml:"XmlLoginId"`
	LoginId                       string                          `xml:"LoginId"`
	StatsType                     string                          `xml:"StatsType"`
	StartDate                     string                          `xml:"StartDate"`
	EndDate                       string                          `xml:"EndDate"`
	DateInterval                  int                             `xml:"DateInterval"`
	EndUserName                   string                          `xml:"EndUserName"`
	SupplierSearchesParameters    *SupplierSearchesParametersType `xml:"SupplierSearchesParameters"`
	ReferralsParameters           *ReferralsParametersType        `xml:"ReferralsParameters"`
	CommandSummaryParameters      *CommandSummaryParametersType   `xml:"CommandSummaryParameters"`
	UserSearchesParameters        *UserSearchesParametersType     `xml:"UserSearchesParameters"`
	Password                      string                          `xml:"Password"`
	MinimumNumberOfItemsPerPeriod int                             `xml:"MinimumNumberOfItemsPerPeriod"`
}

// StatsTypeEnumeration ...
type StatsTypeEnumeration string

// ReferralsParametersType ...
type ReferralsParametersType struct {
	SupplierName *CommonSupplierIdentifier `xml:"SupplierName"`
}

// CommandSummaryParametersType ...
type CommandSummaryParametersType struct {
	CommandName string `xml:"CommandName"`
}

// SupplierSearchesParametersType ...
type SupplierSearchesParametersType struct {
	ShowSupplierTotalsOnly bool `xml:"ShowSupplierTotalsOnly"`
}

// UserSearchesParametersType ...
type UserSearchesParametersType struct {
	Mode string `xml:"Mode"`
	Type string `xml:"Type"`
}
