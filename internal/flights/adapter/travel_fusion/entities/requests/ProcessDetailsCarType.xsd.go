package requests

// ProcessDetailsCarType ...
type ProcessDetailsCarType struct {
	MillisAttr            int       `xml:"millis,attr,omitempty"`
	XmlLoginId            string    `xml:"XmlLoginId"`
	LoginId               string    `xml:"LoginId"`
	RoutingId             *CommonId `xml:"RoutingId"`
	ResultId              *CommonId `xml:"ResultId"`
	PickupId              *CommonId `xml:"PickupId"`
	SetdownId             *CommonId `xml:"SetdownId"`
	HandoffParametersOnly bool      `xml:"HandoffParametersOnly"`
}
