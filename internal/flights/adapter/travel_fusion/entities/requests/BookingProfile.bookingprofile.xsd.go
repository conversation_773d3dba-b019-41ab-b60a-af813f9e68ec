package requests

// BookingProfileType ...
type BookingProfileType struct {
	CustomSupplierParameterList *CommonCustomSupplierParameterList `xml:"CustomSupplierParameterList,omitempty"`
	TravellerList               *BookingProfileTravellerList       `xml:"TravellerList"`
	ContactDetails              *BookingProfileContactDetailsType  `xml:"ContactDetails"`
	BillingDetails              *BookingProfileBillingDetailsType  `xml:"BillingDetails"`
}

// BookingProfileTravellerList ...
type BookingProfileTravellerList struct {
	Traveller []*BookingProfileTravellerType `xml:"Traveller"`
}

// BookingProfileTravellerType ...
type BookingProfileTravellerType struct {
	IdAttr                      int                                `xml:"id,attr,omitempty"`
	Age                         int                                `xml:"Age"`
	Name                        *CommonName                        `xml:"Name"`
	CustomSupplierParameterList *CommonCustomSupplierParameterList `xml:"CustomSupplierParameterList"`
	SplitTravellerId            string                             `xml:"SplitTravellerId,omitempty"`
}

// BookingProfileAddressType ...
type BookingProfileAddressType struct {
	Company        string `xml:"Company,omitempty"`
	Flat           string `xml:"Flat,omitempty"`
	BuildingName   string `xml:"BuildingName,omitempty"`
	BuildingNumber string `xml:"BuildingNumber,omitempty"`
	Street         string `xml:"Street,omitempty"`
	Locality       string `xml:"Locality,omitempty"`
	City           string `xml:"City,omitempty"`
	Province       string `xml:"Province,omitempty"`
	Postcode       string `xml:"Postcode,omitempty"`
	CountryCode    string `xml:"CountryCode,omitempty"`
}

// BookingProfileBillingDetailsType ...
type BookingProfileBillingDetailsType struct {
	Name       *CommonName                   `xml:"Name"`
	Address    *BookingProfileAddressType    `xml:"Address"`
	CreditCard *BookingProfileCreditCardType `xml:"CreditCard"`
}

// BookingProfileContactDetailsType ...
type BookingProfileContactDetailsType struct {
	EcodeAttr   string                     `xml:"ecode,attr,omitempty"`
	EtextAttr   string                     `xml:"etext,attr,omitempty"`
	EdetailAttr string                     `xml:"edetail,attr,omitempty"`
	EdateAttr   string                     `xml:"edate,attr,omitempty"`
	Name        *CommonName                `xml:"Name"`
	Address     *BookingProfileAddressType `xml:"Address,omitempty"`
	HomePhone   *CommonPhone               `xml:"HomePhone,omitempty"`
	WorkPhone   *CommonPhone               `xml:"WorkPhone,omitempty"`
	MobilePhone *CommonPhone               `xml:"MobilePhone,omitempty"`
	Fax         *CommonPhone               `xml:"Fax,omitempty"`
	Email       string                     `xml:"Email,omitempty"`
}

// BookingProfileCreditCardType ...
type BookingProfileCreditCardType struct {
	Company      string      `xml:"Company"`
	NameOnCard   *CommonName `xml:"NameOnCard"`
	Number       string      `xml:"Number"`
	SecurityCode string      `xml:"SecurityCode"`
	ExpiryDate   string      `xml:"ExpiryDate"`
	StartDate    string      `xml:"StartDate"`
	CardType     string      `xml:"CardType"`
	IssueNumber  string      `xml:"IssueNumber,omitempty"`
}
