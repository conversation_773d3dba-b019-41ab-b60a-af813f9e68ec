package requests

// ProcessDetailsHotelType ...
type ProcessDetailsHotelType struct {
	MillisAttr            int       `xml:"millis,attr,omitempty"`
	XmlLoginId            string    `xml:"XmlLoginId"`
	LoginId               string    `xml:"LoginId"`
	RoutingId             *CommonId `xml:"RoutingId"`
	OptionId              *CommonId `xml:"OptionId"`
	HandoffParametersOnly bool      `xml:"HandoffParametersOnly"`
}
