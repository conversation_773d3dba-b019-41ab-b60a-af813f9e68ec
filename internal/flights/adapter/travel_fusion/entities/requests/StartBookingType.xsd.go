package requests

// StartBookingType ...
type StartBookingType struct {
	EcodeAttr                       string              `xml:"ecode,attr,omitempty"`
	EtextAttr                       string              `xml:"etext,attr,omitempty"`
	EdetailAttr                     string              `xml:"edetail,attr,omitempty"`
	EdateAttr                       string              `xml:"edate,attr,omitempty"`
	MillisAttr                      int                 `xml:"millis,attr,omitempty"`
	XmlLoginId                      string              `xml:"XmlLoginId"`
	LoginId                         string              `xml:"LoginId"`
	TFBookingReference              string              `xml:"TFBookingReference"`
	BookingProfile                  *BookingProfileType `xml:"BookingProfile"`
	ExpectedPrice                   *ExpectedPrice      `xml:"ExpectedPrice"`
	JourneyPrice                    *CommonPrice        `xml:"JourneyPrice"`
	FakeBooking                     *FakeBookingType    `xml:"FakeBooking"`
	SupplierVisualAuthorisationText string              `xml:"SupplierVisualAuthorisationText,omitempty"`
}

// FakeBookingType ...
type FakeBookingType struct {
	EnableFakeBooking                bool   `xml:"EnableFakeBooking"`
	FakeBookingSimulatedDelaySeconds int    `xml:"FakeBookingSimulatedDelaySeconds"`
	FakeBookingStatus                string `xml:"FakeBookingStatus"`
	EnableFakeCardVerification       bool   `xml:"EnableFakeCardVerification"`
}
