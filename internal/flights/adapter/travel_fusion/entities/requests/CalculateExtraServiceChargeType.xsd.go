package requests

// CalculateExtraServiceChargeType ...
type CalculateExtraServiceChargeType struct {
	XmlLoginId         string                    `xml:"XmlLoginId"`
	LoginId            string                    `xml:"LoginId"`
	Supplier           *CommonSupplierIdentifier `xml:"Supplier"`
	CalculationProfile *CalculationProfileType   `xml:"CalculationProfile"`
}

// CalculationProfileType ...
type CalculationProfileType struct {
	ServiceNameList *ServiceNameListType `xml:"ServiceNameList"`
	TravellerList   *TravellerListType   `xml:"TravellerList"`
	GroupList       *GroupListType       `xml:"GroupList"`
	BillingDetails  *BillingDetailsType  `xml:"BillingDetails"`
}

// TravellerListType ...
type TravellerListType struct {
	Traveller []*TravellerType `xml:"Traveller"`
}

// GroupListType ...
type GroupListType struct {
	Group *GroupType `xml:"Group"`
}

// BillingDetailsType ...
type BillingDetailsType struct {
	Price      float64         `xml:"Price"`
	Currency   *CommonTLA      `xml:"Currency"`
	CreditCard *CreditCardType `xml:"CreditCard"`
}

// TravellerType ...
type TravellerType struct {
	Age             *CommonAge           `xml:"Age"`
	ServiceNameList *ServiceNameListType `xml:"ServiceNameList"`
}

// ServiceNameListType ...
type ServiceNameListType struct {
	ServiceName []*ServiceNameType `xml:"ServiceName"`
}

// ServiceNameType ...
type ServiceNameType struct {
	Name          *Nonemptystring    `xml:"Name"`
	ParameterList *ParameterListType `xml:"ParameterList"`
}

// ParameterListType ...
type ParameterListType struct {
	NumberOfBags int             `xml:"NumberOfBags"`
	WeightOfBags int             `xml:"WeightOfBags"`
	ServiceType  *Nonemptystring `xml:"ServiceType"`
}

// GroupType ...
type GroupType struct {
	Outward *TripType `xml:"Outward"`
	Return  *TripType `xml:"Return"`
}

// TripType ...
type TripType struct {
	SegmentList *SegmentListType `xml:"SegmentList"`
}

// SegmentListType ...
type SegmentListType struct {
	Segment []*SegmentType `xml:"Segment"`
}

// SegmentType ...
type SegmentType struct {
	DepartureTime      *Nonemptystring `xml:"DepartureTime"`
	ArriveTime         *Nonemptystring `xml:"ArriveTime"`
	Operator           *Nonemptystring `xml:"Operator"`
	FlightNumber       *Nonemptystring `xml:"FlightNumber"`
	OriginAirport      *Nonemptystring `xml:"OriginAirport"`
	DestinationAirport *Nonemptystring `xml:"DestinationAirport"`
	Cabin              *Nonemptystring `xml:"Cabin"`
}

// CreditCardType ...
type CreditCardType struct {
	CardType *Nonemptystring `xml:"CardType"`
}
