package requests

// ResolveLocationType ...
type ResolveLocationType struct {
	MillisAttr        int                                  `xml:"millis,attr,omitempty"`
	XmlLoginId        string                               `xml:"XmlLoginId"`
	LoginId           string                               `xml:"LoginId"`
	CountryCode       string                               `xml:"CountryCode"`
	TypeList          *LocationResolutionTypeList          `xml:"TypeList"`
	Name              string                               `xml:"Name"`
	RequiredFieldList *LocationResolutionRequiredFieldList `xml:"RequiredFieldList"`
}

// LocationResolutionTypeList ...
type LocationResolutionTypeList struct {
	Type []*CommonLocationResolutionPlaceType `xml:"Type"`
}

// LocationResolutionRequiredFieldList ...
type LocationResolutionRequiredFieldList struct {
	RequiredField []string `xml:"RequiredField"`
}

// LocationResolutionRequiredField ...
type LocationResolutionRequiredField string
