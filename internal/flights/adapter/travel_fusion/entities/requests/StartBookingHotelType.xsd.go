package requests

// StartBookingHotelType ...
type StartBookingHotelType struct {
	MillisAttr                      int                 `xml:"millis,attr,omitempty"`
	XmlLoginId                      string              `xml:"XmlLoginId"`
	LoginId                         string              `xml:"LoginId"`
	TFBookingReference              string              `xml:"TFBookingReference"`
	FakeBooking                     *FakeBookingType    `xml:"FakeBooking"`
	BookingProfile                  *BookingProfileType `xml:"BookingProfile"`
	SupplierVisualAuthorisationText string              `xml:"SupplierVisualAuthorisationText"`
}
