package responses

// CalculateExtraServiceChargeResponseType ...
type CalculateExtraServiceChargeResponseType struct {
	MillisAttr          int                                         `xml:"millis,attr,omitempty"`
	LoginId             string                                      `xml:"LoginId"`
	Service             []*CalculateExtraServiceChargeServiceType   `xml:"Service"`
	TotalCharge         *CalculateExtraServiceChargeTotalChargeType `xml:"TotalCharge"`
	Disclaimer          string                                      `xml:"Disclaimer"`
	GeneralInfoItemList *GeneralInfoItemListType                    `xml:"GeneralInfoItemList"`
}

// CalculateExtraServiceChargeServiceType ...
type CalculateExtraServiceChargeServiceType struct {
	ServiceName *ExtraServiceNameType                  `xml:"ServiceName"`
	Charge      *CalculateExtraServiceChargeChargeType `xml:"Charge"`
}

// CalculateExtraServiceChargeTotalChargeType ...
type CalculateExtraServiceChargeTotalChargeType struct {
	Charge *CalculateExtraServiceChargeChargeType `xml:"Charge"`
}

// CalculateExtraServiceChargeChargeType ...
type CalculateExtraServiceChargeChargeType struct {
	EcodeAttr   string     `xml:"ecode,attr,omitempty"`
	EtextAttr   string     `xml:"etext,attr,omitempty"`
	EdetailAttr string     `xml:"edetail,attr,omitempty"`
	EdateAttr   string     `xml:"edate,attr,omitempty"`
	Amount      float64    `xml:"Amount"`
	Currency    *CommonTLA `xml:"Currency"`
}
