package responses

// ProcessDetailsResponseType ...
type ProcessDetailsResponseType struct {
	ErrorAttr
	MillisAttr           int                                            `xml:"millis,attr,omitempty"`
	LoginId              string                                         `xml:"LoginId"`
	RoutingId            *CommonId                                      `xml:"RoutingId"`
	Router               *CommonRouter                                  `xml:"Router"`
	SupplierHandoffData  *ProcessDetailsResponseSupplierHandoffDataType `xml:"SupplierHandoffData,omitempty"`
	ManagedTravellerList *ManagedTravellerListType                      `xml:"ManagedTravellerList,omitempty"`
	SupportedCardList    *SupportedCardListType                         `xml:"SupportedCardList,omitempty"`
	FeesList             *FeesListType                                  `xml:"FeesList,omitempty"`
	GeneralInfoItemList  *GeneralInfoItemListType                       `xml:"GeneralInfoItemList,omitempty"`
}

// ProcessDetailsResponseSupplierHandoffDataType ...
type ProcessDetailsResponseSupplierHandoffDataType struct {
	Method        string                            `xml:"Method"`
	Url           string                            `xml:"Url"`
	ParameterList *DetailsResponseHTTPParameterList `xml:"ParameterList"`
}

// DetailsResponseHTTPMethod ...
type DetailsResponseHTTPMethod string

// DetailsResponseHTTPParameterList ...
type DetailsResponseHTTPParameterList struct {
	Parameter []*DetailsResponseHTTPParameter `xml:"Parameter"`
}

// DetailsResponseHTTPParameter ...
type DetailsResponseHTTPParameter struct {
	Name  string `xml:"Name"`
	Value string `xml:"Value"`
}

// FeesListType ...
type FeesListType struct {
	Luggage        *LuggageFeesInfoType        `xml:"Luggage"`
	SpeedyBoarding *SpeedyBoardingFeesInfoType `xml:"SpeedyBoarding"`
	CheckIn        *CheckInFeesInfoType        `xml:"CheckIn"`
}

// LuggageFeesInfoType ...
type LuggageFeesInfoType struct {
	ItemList *LuggageFeesListType `xml:"ItemList"`
}

// SpeedyBoardingFeesInfoType ...
type SpeedyBoardingFeesInfoType struct {
	ItemList *SpeedyBoardingFeesListType `xml:"ItemList"`
}

// CheckInFeesInfoType ...
type CheckInFeesInfoType struct {
	ItemList *CheckInFeesListType `xml:"ItemList"`
}

// SupportedCardListType ...
type SupportedCardListType struct {
	SupportedCard []*SupportedCardType `xml:"SupportedCard"`
}

// SupportedCardType ...
type SupportedCardType struct {
	CardType             []string                         `xml:"CardType"`
	CardCharge           *SupportedCardChargeType         `xml:"CardCharge"`
	CardChargePercentage float64                          `xml:"CardChargePercentage"`
	CardMinimumCharge    *CommonPrice                     `xml:"CardMinimumCharge"`
	RoundingPolicy       *SupportedCardRoundingPolicyType `xml:"RoundingPolicy"`
}

// SupportedCardRoundingPolicyType ...
type SupportedCardRoundingPolicyType struct {
	Scale int    `xml:"Scale"`
	Mode  string `xml:"Mode"`
}

// SupportedCardChargeType ...
type SupportedCardChargeType struct {
	Charge                  *CommonPrice `xml:"Charge"`
	ChargeIsPerPassengerLeg bool         `xml:"ChargeIsPerPassengerLeg"`
}

// ManagedTravellerListType ...
type ManagedTravellerListType struct {
	ManagedTraveller []*ManagedTravellerType `xml:"ManagedTraveller"`
}

// ManagedTravellerType ...
type ManagedTravellerType struct {
	Name *CommonName `xml:"Name"`
	// TravellerType             *ManagedTravellerTypeRestriction `xml:"TravellerType"`
	Age                       *CommonAge                       `xml:"Age"`
	SupplierTravellerInfoList *SupplierTravellerInfoListType   `xml:"SupplierTravellerInfoList"`
	RequiredParameterList     *RouterRequiredParameterListType `xml:"RequiredParameterList"`
}

// SupplierTravellerInfoListType ...
type SupplierTravellerInfoListType struct {
	SupplierTravellerInfo []*RouterSupplierInfoType `xml:"SupplierTravellerInfo"`
}
