package responses

// ListSupplierRoutesResponseType ...
type ListSupplierRoutesResponseType struct {
	MillisAttr           int                             `xml:"millis,attr,omitempty"`
	LoginId              string                          `xml:"LoginId"`
	RouteList            *ListSupplierRoutesRouteList    `xml:"RouteList"`
	LocationList         *ListSupplierRoutesLocationList `xml:"LocationList"`
	AllowedCountryRoutes *AllowedCountryRoutesType       `xml:"AllowedCountryRoutes"`
}

// ListSupplierRoutesRouteList ...
type ListSupplierRoutesRouteList struct {
	AirportRoutes []string `xml:"AirportRoutes"`
	CityRoutes    []string `xml:"CityRoutes"`
}

// ListSupplierRoutesLocationList ...
type ListSupplierRoutesLocationList struct {
	Location []*ListSupplierRoutesLocation `xml:"Location"`
}

// ListSupplierRoutesLocation ...
type ListSupplierRoutesLocation struct {
	IATACodeAttr    string `xml:"IATACode,attr,omitempty"`
	TypeAttr        string `xml:"Type,attr,omitempty"`
	CountryCodeAttr string `xml:"CountryCode,attr,omitempty"`
}
