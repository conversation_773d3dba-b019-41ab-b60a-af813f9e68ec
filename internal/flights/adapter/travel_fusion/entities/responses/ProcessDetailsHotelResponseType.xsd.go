package responses

// ProcessDetailsHotelResponseType ...
type ProcessDetailsHotelResponseType struct {
	MillisAttr          int                                            `xml:"millis,attr,omitempty"`
	LoginId             string                                         `xml:"LoginId"`
	RoutingId           *CommonId                                      `xml:"RoutingId"`
	Router              *HotelRouter                                   `xml:"Router"`
	SupplierHandoffData *ProcessDetailsResponseSupplierHandoffDataType `xml:"SupplierHandoffData"`
	GeneralInfoItemList *GeneralInfoItemListType                       `xml:"GeneralInfoItemList"`
}
