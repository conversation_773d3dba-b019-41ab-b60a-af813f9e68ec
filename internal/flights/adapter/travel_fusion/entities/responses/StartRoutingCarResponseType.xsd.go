package responses

// StartRoutingCarResponseType ...
type StartRoutingCarResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	RoutingId           *CommonId                `xml:"RoutingId"`
	RouterList          *CarRouterList           `xml:"RouterList"`
	Summary             *CarRoutingSummaryType   `xml:"Summary"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}
