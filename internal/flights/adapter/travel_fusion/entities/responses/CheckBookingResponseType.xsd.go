package responses

// CheckBookingResponseType ...
type CheckBookingResponseType struct {
	ErrorAttr
	MillisAttr                       int                                   `xml:"millis,attr,omitempty"`
	LoginId                          string                                `xml:"LoginId"`
	TFBookingReference               string                                `xml:"TFBookingReference"`
	Status                           *CommonBookingStatusType              `xml:"Status"`
	CardVerificationRequired         bool                                  `xml:"CardVerificationRequired"`
	SupplierReference                string                                `xml:"SupplierReference"`
	SupplierConfirmationDataItemList *SupplierConfirmationDataItemListType `xml:"SupplierConfirmationDataItemList"`
	Router                           *CommonRouter                         `xml:"Router"`
	BookingProfile                   *BookingProfileType                   `xml:"BookingProfile"`
	GeneralInfoItemList              *GeneralInfoItemListType              `xml:"GeneralInfoItemList"`
}

// SupplierConfirmationDataItemListType ...
type SupplierConfirmationDataItemListType struct {
	SupplierConfirmationDataItem []*SupplierConfirmationDataItemType `xml:"SupplierConfirmationDataItem"`
}

// SupplierConfirmationDataItemType ...
type SupplierConfirmationDataItemType struct {
	Name  string `xml:"Name"`
	Value string `xml:"Value"`
}

// CardSubstitutionDataType ...
type CardSubstitutionDataType struct {
	Fee    *CommonPrice `xml:"Fee"`
	CardId string       `xml:"CardId"`
}
