package responses

// CommandList ...
// type CommandList interface{}

type ErrorAttr struct {
	EcodeAttr   string `xml:"ecode,attr,omitempty"`
	EtextAttr   string `xml:"etext,attr,omitempty"`
	EdetailAttr string `xml:"edetail,attr,omitempty"`
	EdateAttr   string `xml:"edate,attr,omitempty"`
}

// GeneralResponseCommandListType ...
type CommandList struct {
	ErrorAttr
	Login        *LoginResponseType        `xml:"Login,omitempty"`
	StartRouting *StartRoutingResponseType `xml:"StartRouting,omitempty"`
	// StartManageBookingPlane          interface{}                    `xml:"StartManageBookingPlane"`
	CheckRouting      *CheckRoutingResponseType      `xml:"CheckRouting,omitempty"`
	ProcessDetails    *ProcessDetailsResponseType    `xml:"ProcessDetails,omitempty"`
	ProcessTerms      *ProcessTermsResponseType      `xml:"ProcessTerms,omitempty"`
	StartBooking      *StartBookingResponseType      `xml:"StartBooking,omitempty"`
	CheckBooking      *CheckBookingResponseType      `xml:"CheckBooking,omitempty"`
	GetBookingDetails *GetBookingDetailsResponseType `xml:"GetBookingDetails,omitempty"`
	// ResolveLocation                  interface{}                    `xml:"ResolveLocation"`
	// GetCurrencies                    interface{}                    `xml:"GetCurrencies"`
	// GetCountries                     interface{}                    `xml:"GetCountries"`
	// GetPrePackagedSupplierSummary    interface{}                    `xml:"GetPrePackagedSupplierSummary"`
	// GetHotelDetails                  interface{}                    `xml:"GetHotelDetails"`
	// GetMultipleHotelDetails          interface{}                    `xml:"GetMultipleHotelDetails"`
	// GetStats                         interface{}                    `xml:"GetStats"`
	// GetErrorCodes                    interface{}                    `xml:"GetErrorCodes"`
	// StartRoutingHotel                interface{}                    `xml:"StartRoutingHotel"`
	// CheckRoutingHotel                interface{}                    `xml:"CheckRoutingHotel"`
	// ProcessDetailsHotel              interface{}                    `xml:"ProcessDetailsHotel"`
	// StartDetailsHotel                interface{}                    `xml:"StartDetailsHotel"`
	// CheckDetailsHotel                interface{}                    `xml:"CheckDetailsHotel"`
	// StartTermsHotel                  interface{}                    `xml:"StartTermsHotel"`
	// CheckTermsHotel                  interface{}                    `xml:"CheckTermsHotel"`
	// StartBookingHotel                interface{}                    `xml:"StartBookingHotel"`
	// CheckBookingHotel                interface{}                    `xml:"CheckBookingHotel"`
	// StartRoutingCar                  interface{}                    `xml:"StartRoutingCar"`
	// CheckRoutingCar                  interface{}                    `xml:"CheckRoutingCar"`
	// ProcessDetailsCar                interface{}                    `xml:"ProcessDetailsCar"`
	// StartRoutingPrePackaged          interface{}                    `xml:"StartRoutingPrePackaged"`
	// CheckRoutingPrePackaged          interface{}                    `xml:"CheckRoutingPrePackaged"`
	// LogPrePackagedReferral           interface{}                    `xml:"LogPrePackagedReferral"`
	// ProcessCardVerification          interface{}                    `xml:"ProcessCardVerification"`
	// GetSupportedCardTypeList         interface{}                    `xml:"GetSupportedCardTypeList"`
	// StoreValueInVault                interface{}                    `xml:"StoreValueInVault"`
	// DeleteValueInVault               interface{}                    `xml:"DeleteValueInVault"`
	// GetSupplierRoutes                interface{}                    `xml:"GetSupplierRoutes"`
	ListSupplierRoutes *ListSupplierRoutesResponseType `xml:"ListSupplierRoutes"`
	// ListTrainStations                interface{}                    `xml:"ListTrainStations"`
	// GetSuppliersTravellerInfo        interface{}                    `xml:"GetSuppliersTravellerInfo"`
	// GetExtraServiceCharge            interface{}                    `xml:"GetExtraServiceCharge"`
	// CalculateExtraServiceCharge      interface{}                    `xml:"CalculateExtraServiceCharge"`
	GetBranchSupplierList *GetBranchSupplierListResponseType `xml:"GetBranchSupplierList"`
	// GetSuppliersRoutingParameterList interface{}                    `xml:"GetSuppliersRoutingParameterList"`
	DataValidationFailure   *CommandList `xml:"DataValidationFailure"`
	CommandExecutionFailure *struct {
		ProcessTerms *ProcessTermsResponseType `xml:"ProcessTerms,omitempty"`
	} `xml:"CommandExecutionFailure"`
	// XMLValidationError      interface{} `xml:"XMLValidationError"`
	// GeneralInfoItemList              interface{}                    `xml:"GeneralInfoItemList"`
}

// XMLValidationErrorType ...
type XMLValidationErrorType struct {
	RequestOrResponse string `xml:"RequestOrResponse"`
	ReferenceNumber   string `xml:"ReferenceNumber"`
	ReferenceDate     string `xml:"ReferenceDate"`
	ErrorDetails      string `xml:"ErrorDetails"`
	Message           string `xml:"Message"`
}

// CommandExecutionFailureType ...
type CommandExecutionFailureType struct {
	Login                            interface{} `xml:"Login"`
	StartRouting                     interface{} `xml:"StartRouting"`
	StartManageBookingPlane          interface{} `xml:"StartManageBookingPlane"`
	CheckRouting                     interface{} `xml:"CheckRouting"`
	ProcessDetails                   interface{} `xml:"ProcessDetails"`
	ProcessTerms                     interface{} `xml:"ProcessTerms"`
	StartBooking                     interface{} `xml:"StartBooking"`
	CheckBooking                     interface{} `xml:"CheckBooking"`
	GetBookingDetails                interface{} `xml:"GetBookingDetails"`
	ResolveLocation                  interface{} `xml:"ResolveLocation"`
	GetCurrencies                    interface{} `xml:"GetCurrencies"`
	GetCountries                     interface{} `xml:"GetCountries"`
	GetStats                         interface{} `xml:"GetStats"`
	GetErrorCodes                    interface{} `xml:"GetErrorCodes"`
	StartRoutingHotel                interface{} `xml:"StartRoutingHotel"`
	CheckRoutingHotel                interface{} `xml:"CheckRoutingHotel"`
	ProcessDetailsHotel              interface{} `xml:"ProcessDetailsHotel"`
	StartDetailsHotel                interface{} `xml:"StartDetailsHotel"`
	CheckDetailsHotel                interface{} `xml:"CheckDetailsHotel"`
	StartTermsHotel                  interface{} `xml:"StartTermsHotel"`
	CheckTermsHotel                  interface{} `xml:"CheckTermsHotel"`
	StartBookingHotel                interface{} `xml:"StartBookingHotel"`
	CheckBookingHotel                interface{} `xml:"CheckBookingHotel"`
	StartRoutingCar                  interface{} `xml:"StartRoutingCar"`
	CheckRoutingCar                  interface{} `xml:"CheckRoutingCar"`
	ProcessDetailsCar                interface{} `xml:"ProcessDetailsCar"`
	GetPrePackagedSupplierSummary    interface{} `xml:"GetPrePackagedSupplierSummary"`
	StartRoutingPrePackaged          interface{} `xml:"StartRoutingPrePackaged"`
	CheckRoutingPrePackaged          interface{} `xml:"CheckRoutingPrePackaged"`
	GetHotelDetails                  interface{} `xml:"GetHotelDetails"`
	GetMultipleHotelDetails          interface{} `xml:"GetMultipleHotelDetails"`
	LogPrePackagedReferral           interface{} `xml:"LogPrePackagedReferral"`
	ProcessCardVerification          interface{} `xml:"ProcessCardVerification"`
	GetSupplierRoutes                interface{} `xml:"GetSupplierRoutes"`
	ListSupplierRoutes               interface{} `xml:"ListSupplierRoutes"`
	ListTrainStations                interface{} `xml:"ListTrainStations"`
	GetSuppliersTravellerInfo        interface{} `xml:"GetSuppliersTravellerInfo"`
	StoreValueInVault                interface{} `xml:"StoreValueInVault"`
	DeleteValueInVault               interface{} `xml:"DeleteValueInVault"`
	GetExtraServiceCharge            interface{} `xml:"GetExtraServiceCharge"`
	CalculateExtraServiceCharge      interface{} `xml:"CalculateExtraServiceCharge"`
	GetBranchSupplierList            interface{} `xml:"GetBranchSupplierList"`
	GetSuppliersRoutingParameterList interface{} `xml:"GetSuppliersRoutingParameterList"`
}

// SpecificCommandExecutionFailureType ...
type SpecificCommandExecutionFailureType struct {
	EcodeAttr   string `xml:"ecode,attr,omitempty"`
	EtextAttr   string `xml:"etext,attr,omitempty"`
	EdetailAttr string `xml:"edetail,attr,omitempty"`
	EdateAttr   string `xml:"edate,attr,omitempty"`
}

// GeneralInfoItemListType ...
type GeneralInfoItemListType struct {
	GeneralInfoItem []interface{} `xml:"GeneralInfoItem"`
}

// GeneralInfoItemType ...
type GeneralInfoItemType struct {
	Name  string `xml:"Name"`
	Value string `xml:"Value"`
}
