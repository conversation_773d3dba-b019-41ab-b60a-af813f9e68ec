package responses

// BookingProfileType ...
type BookingProfileType struct {
	CustomSupplierParameterList *CommonCustomSupplierParameterList `xml:"CustomSupplierParameterList"`
	TravellerList               *BookingProfileTravellerList       `xml:"TravellerList"`
	ContactDetails              *BookingProfileContactDetailsType  `xml:"ContactDetails"`
	BillingDetails              *BookingProfileBillingDetailsType  `xml:"BillingDetails"`
}

// BookingProfileTravellerList ...
type BookingProfileTravellerList struct {
	Traveller []*BookingProfileTravellerType `xml:"Traveller"`
}

// BookingProfileTravellerType ...
type BookingProfileTravellerType struct {
	IdAttr                      int                                `xml:"id,attr,omitempty"`
	Age                         *CommonAge                         `xml:"Age"`
	Name                        *CommonName                        `xml:"Name"`
	CustomSupplierParameterList *CommonCustomSupplierParameterList `xml:"CustomSupplierParameterList"`
	SplitTravellerId            string                             `xml:"SplitTravellerId"`
}

// BookingProfileAddressType ...
type BookingProfileAddressType struct {
	Company        string `xml:"Company"`
	Flat           string `xml:"Flat"`
	BuildingName   string `xml:"BuildingName"`
	BuildingNumber string `xml:"BuildingNumber"`
	Street         string `xml:"Street"`
	Locality       string `xml:"Locality"`
	City           string `xml:"City"`
	Province       string `xml:"Province"`
	Postcode       string `xml:"Postcode"`
	CountryCode    string `xml:"CountryCode"`
}

// BookingProfileBillingDetailsType ...
type BookingProfileBillingDetailsType struct {
	Name       *CommonName                   `xml:"Name"`
	Address    *BookingProfileAddressType    `xml:"Address"`
	CreditCard *BookingProfileCreditCardType `xml:"CreditCard"`
}

// BookingProfileContactDetailsType ...
type BookingProfileContactDetailsType struct {
	EcodeAttr   string                     `xml:"ecode,attr,omitempty"`
	EtextAttr   string                     `xml:"etext,attr,omitempty"`
	EdetailAttr string                     `xml:"edetail,attr,omitempty"`
	EdateAttr   string                     `xml:"edate,attr,omitempty"`
	Name        *CommonName                `xml:"Name"`
	Address     *BookingProfileAddressType `xml:"Address"`
	HomePhone   *CommonPhone               `xml:"HomePhone"`
	WorkPhone   *CommonPhone               `xml:"WorkPhone"`
	MobilePhone *CommonPhone               `xml:"MobilePhone"`
	Fax         *CommonPhone               `xml:"Fax"`
	Email       string                     `xml:"Email"`
}

// BookingProfileCreditCardType ...
type BookingProfileCreditCardType struct {
	Company      string      `xml:"Company"`
	NameOnCard   *CommonName `xml:"NameOnCard"`
	Number       string      `xml:"Number"`
	SecurityCode string      `xml:"SecurityCode"`
	ExpiryDate   string      `xml:"ExpiryDate"`
	StartDate    string      `xml:"StartDate"`
	CardType     string      `xml:"CardType"`
	IssueNumber  string      `xml:"IssueNumber"`
}
