package responses

// StartRoutingPrePackagedResponseType ...
type StartRoutingPrePackagedResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	RoutingId           *CommonId                `xml:"RoutingId"`
	RouterList          *PrePackagedRouterList   `xml:"RouterList"`
	RequestOrdinalId    int                      `xml:"RequestOrdinalId"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}
