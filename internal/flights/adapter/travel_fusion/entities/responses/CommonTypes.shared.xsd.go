package responses

// CommonSupplierList ...
type CommonSupplierList struct {
	Supplier []string `xml:"Supplier"`
}

// CommonSupplierIdentifier ...
type CommonSupplierIdentifier string

// CommonPositiveInteger ...
type CommonPositiveInteger struct {
	CommonEmptyElement *CommonEmptyElement
	PositiveInteger    int
}

// CommonEmptyElement ...
type CommonEmptyElement string

// CommonOriginAirportCodeList ...
type CommonOriginAirportCodeList struct {
	OriginAirportCode []string `xml:"OriginAirportCode"`
}

// CommonDestinationAirportCodeList ...
type CommonDestinationAirportCodeList struct {
	DestinationAirportCode []string `xml:"DestinationAirportCode"`
}

// CommonAirportCode ...
type CommonAirportCode string

// CommonTypeOfList ...
type CommonTypeOfList string

// CommonBookingStatusType ...
type CommonBookingStatusType string

// CommonBookingV2StatusType ...
type CommonBookingV2StatusType struct {
}

// CommonFinalBookingStatusType ...
type CommonFinalBookingStatusType string

// CommonOperatorCode ...
type CommonOperatorCode string

// CommonExtendedOperatorCode ...
type CommonExtendedOperatorCode string

// CommonLocationTypeType ...
type CommonLocationTypeType string

// CommonTLA ...
type CommonTLA string

// TF7Alpha ...
type TF7Alpha string

// TrainCode ...
type TrainCode string

// CommonFullCountryType ...
type CommonFullCountryType struct {
	Name string `xml:"Name"`
	Code string `xml:"Code"`
}

// CommonFullCountryTypeWithDetails ...
type CommonFullCountryTypeWithDetails struct {
	Name         string               `xml:"Name"`
	Code         string               `xml:"Code"`
	Region       string               `xml:"Region"`
	CurrencyList *CountryCurrencyList `xml:"CurrencyList"`
}

// WorldRegionType ...
type WorldRegionType string

// CountryCurrencyList ...
type CountryCurrencyList struct {
	Currency []*CountryCurrency `xml:"Currency"`
}

// CountryCurrency ...
type CountryCurrency struct {
	Code string `xml:"Code"`
}

// CommonMode ...
type CommonMode string

// Nonemptystring ...
type Nonemptystring string

// CommonId ...
type CommonId struct {
	EcodeAttr string `xml:"ecode,attr,omitempty"`
	EtextAttr string `xml:"etext,attr,omitempty"`
	Value     string `xml:",chardata"`
}

// CommonLoginType ...
type CommonLoginType string

// CommonName ...
type CommonName struct {
	Title        string                  `xml:"Title"`
	NamePartList *CommonNamePartListType `xml:"NamePartList"`
}

// CommonNamePartType ...
type CommonNamePartType string

// CommonNamePartListType ...
type CommonNamePartListType struct {
	NamePart []string `xml:"NamePart"`
}

// CommonTitleType ...
type CommonTitleType string

// CommonPhone ...
type CommonPhone struct {
	EcodeAttr         string `xml:"ecode,attr,omitempty"`
	EtextAttr         string `xml:"etext,attr,omitempty"`
	EdetailAttr       string `xml:"edetail,attr,omitempty"`
	EdateAttr         string `xml:"edate,attr,omitempty"`
	InternationalCode string `xml:"InternationalCode"`
	AreaCode          string `xml:"AreaCode"`
	Number            string `xml:"Number"`
	Extension         string `xml:"Extension"`
}

// CommonCoordinates ...
type CommonCoordinates struct {
	Latitude  float64 `xml:"Latitude"`
	Longitude float64 `xml:"Longitude"`
}

// CommonAge ...
type CommonAge int

// Value ...
type Value struct {
	EcodeAttr string `xml:"ecode,attr,omitempty"`
	EtextAttr string `xml:"etext,attr,omitempty"`
	Value     string `xml:",chardata"`
}

// CustomSupplierParameterType ...
type CustomSupplierParameterType struct {
	Supplier string `xml:"Supplier"`
	Name     string `xml:"Name"`
	Value    *Value `xml:"Value"`
}

// CommonCustomSupplierParameterList ...
type CommonCustomSupplierParameterList struct {
	CustomSupplierParameter []*CustomSupplierParameterType `xml:"CustomSupplierParameter"`
}

// CommonSupplierResponseListType ...
type CommonSupplierResponseListType struct {
	SupplierResponse []*CommonSupplierResponseType `xml:"SupplierResponse"`
}

// CommonSupplierResponseType ...
type CommonSupplierResponseType struct {
	Name string `xml:"Name"`
	Type string `xml:"Type"`
	Data string `xml:"Data"`
}

// CommonSupplierResponseDataType ...
type CommonSupplierResponseDataType string

// CommonLocationResolutionPlaceType ...
type CommonLocationResolutionPlaceType string

// CommonString ...
type CommonString struct {
	EcodeAttr   string `xml:"ecode,attr,omitempty"`
	EtextAttr   string `xml:"etext,attr,omitempty"`
	EdetailAttr string `xml:"edetail,attr,omitempty"`
	EdateAttr   string `xml:"edate,attr,omitempty"`
	Value       string `xml:",chardata"`
}

// LuggageFeesListType ...
type LuggageFeesListType struct {
	Item []*LuggageFeesListItemType `xml:"Item"`
}

// LuggageFeesListItemType ...
type LuggageFeesListItemType struct {
	Quantity    int     `xml:"Quantity"`
	Weight      int     `xml:"Weight"`
	MaxWeight   int     `xml:"MaxWeight"`
	MaxQuantity int     `xml:"MaxQuantity"`
	Amount      float64 `xml:"Amount"`
	Currency    string  `xml:"Currency"`
}

// SpeedyBoardingFeesListType ...
type SpeedyBoardingFeesListType struct {
	Item []*SpeedyBoardingFeesListItemType `xml:"Item"`
}

// SpeedyBoardingFeesListItemType ...
type SpeedyBoardingFeesListItemType struct {
	Amount   float64 `xml:"Amount"`
	Currency string  `xml:"Currency"`
}

// CheckInFeesListType ...
type CheckInFeesListType struct {
	Item []*CheckInFeesListItemType `xml:"Item"`
}

// CheckInFeesListItemType ...
type CheckInFeesListItemType struct {
	Type     string  `xml:"Type"`
	Amount   float64 `xml:"Amount"`
	Currency string  `xml:"Currency"`
}
