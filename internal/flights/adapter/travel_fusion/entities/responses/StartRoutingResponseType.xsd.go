package responses

// StartRoutingResponseType ...
type StartRoutingResponseType struct {
	ErrorAttr
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	RoutingId           *CommonId                `xml:"RoutingId"`
	RouterList          *CommonRouterList        `xml:"RouterList"`
	Summary             *RoutingSummaryType      `xml:"Summary"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}
