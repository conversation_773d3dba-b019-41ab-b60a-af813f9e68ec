package responses

// CheckRoutingResponseType ...
type CheckRoutingResponseType struct {
	ErrorAttr
	MillisAttr            int                                    `xml:"millis,attr,omitempty"`
	LoginId               string                                 `xml:"LoginId"`
	RoutingId             *CommonId                              `xml:"RoutingId"`
	Mode                  *CommonMode                            `xml:"Mode"`
	RouterList            *CommonRouterList                      `xml:"RouterList"`
	DisallowedPairingList *CheckRoutingDisallowedPairingListType `xml:"DisallowedPairingList"`
	Summary               *RoutingSummaryType                    `xml:"Summary"`
	GeneralInfoItemList   *GeneralInfoItemListType               `xml:"GeneralInfoItemList"`
}

// CheckRoutingIDPairType ...
type CheckRoutingIDPairType struct {
	OutwardId *CommonId `xml:"OutwardId"`
	ReturnId  *CommonId `xml:"ReturnId"`
}

// CheckRoutingDisallowedPairingListType ...
type CheckRoutingDisallowedPairingListType struct {
	DisallowedPairing []*CheckRoutingIDPairType `xml:"DisallowedPairing"`
}
