package responses

// GetExtraServiceChargesResponseType ...
type GetExtraServiceChargesResponseType struct {
	MillisAttr            int                          `xml:"millis,attr,omitempty"`
	LoginId               string                       `xml:"LoginId"`
	Disclaimer            string                       `xml:"Disclaimer"`
	SupplierExtraServices []*SupplierExtraServicesType `xml:"SupplierExtraServices"`
	GeneralInfoItemList   *GeneralInfoItemListType     `xml:"GeneralInfoItemList"`
}

// SupplierExtraServicesType ...
type SupplierExtraServicesType struct {
	EcodeAttr    string                    `xml:"ecode,attr,omitempty"`
	EtextAttr    string                    `xml:"etext,attr,omitempty"`
	EdetailAttr  string                    `xml:"edetail,attr,omitempty"`
	EdateAttr    string                    `xml:"edate,attr,omitempty"`
	SupplierName *CommonSupplierIdentifier `xml:"SupplierName"`
	ExtraService []*ExtraServiceType1      `xml:"ExtraService"`
}

// ExtraServiceType1 ...
type ExtraServiceType1 struct {
	ExtraServiceFreeTextGroup []*ExtraServiceFreeTextGroup
	ServiceName               *Nonemptystring              `xml:"ServiceName"`
	ServiceChargeList         *ExtraServiceChargeListType1 `xml:"ServiceChargeList"`
}

// ExtraServiceFreeTextGroup ...
type ExtraServiceFreeTextGroup struct {
	FreeTextTitle       string
	FreeTextDescription string
}

// ExtraServiceChargeListType1 ...
type ExtraServiceChargeListType1 struct {
	ServiceCharge []*ExtraServiceChargeType1 `xml:"ServiceCharge"`
}

// ExtraServiceChargeType1 ...
type ExtraServiceChargeType1 struct {
	ConditionList         *ServiceChargeConditionListType   `xml:"ConditionList"`
	ChargeCalculationRule *ServiceChargeCalculationRuleType `xml:"ChargeCalculationRule"`
	ChargeList            *ExtraServiceActualChargeListType `xml:"ChargeList"`
}

// ServiceChargeConditionListType ...
type ServiceChargeConditionListType struct {
	Condition []*ServiceChargeConditionType `xml:"Condition"`
}

// ServiceChargeConditionType ...
type ServiceChargeConditionType struct {
	ServiceChargeConditionOtherExprGroup *ServiceChargeConditionOtherExprGroup
	ExtraServiceFreeTextGroup            *ExtraServiceFreeTextGroup
	Item                                 *Nonemptystring                      `xml:"Item"`
	Unit                                 *Nonemptystring                      `xml:"Unit"`
	Equals                               *Nonemptystring                      `xml:"Equals"`
	NotEquals                            *Nonemptystring                      `xml:"NotEquals"`
	MoreThan                             *Nonemptystring                      `xml:"MoreThan"`
	MoreThanOrEquals                     *Nonemptystring                      `xml:"MoreThanOrEquals"`
	LessThan                             *Nonemptystring                      `xml:"LessThan"`
	LessThanOrEquals                     *Nonemptystring                      `xml:"LessThanOrEquals"`
	RouteList                            *ServiceChargeConditionRouteListType `xml:"RouteList"`
}

// ServiceChargeConditionOtherExprGroup ...
type ServiceChargeConditionOtherExprGroup struct {
	Operator *Nonemptystring
	Operand  *Nonemptystring
}

// ServiceChargeConditionRouteListType ...
type ServiceChargeConditionRouteListType struct {
	Route []*ServiceChargeConditionRouteType `xml:"Route"`
}

// ServiceChargeConditionRouteType ...
type ServiceChargeConditionRouteType struct {
	ServiceChargeConditionRouteOtherGroup *ServiceChargeConditionRouteOtherGroup
	ServiceChargeConditionRouteTypeGroup  *ServiceChargeConditionRouteTypeGroup
	From                                  []*Nonemptystring `xml:"From"`
	To                                    []*Nonemptystring `xml:"To"`
	In                                    []*Nonemptystring `xml:"In"`
	NotIn                                 []*Nonemptystring `xml:"NotIn"`
}

// ServiceChargeConditionRouteOtherGroup ...
type ServiceChargeConditionRouteOtherGroup struct {
	RouteOperator *Nonemptystring
	Value         *Nonemptystring
}

// ServiceChargeConditionRouteTypeGroup ...
type ServiceChargeConditionRouteTypeGroup struct {
	Type  *Nonemptystring
	Value *Nonemptystring
}

// ServiceChargeCalculationRuleType ...
type ServiceChargeCalculationRuleType struct {
	Type  *Nonemptystring `xml:"Type"`
	Item  *Nonemptystring `xml:"Item"`
	Unit  *Nonemptystring `xml:"Unit"`
	Value *Nonemptystring `xml:"Value"`
}

// ExtraServiceActualChargeListType ...
type ExtraServiceActualChargeListType struct {
	Charge []*ExtraServiceActualChargeType `xml:"Charge"`
}

// ExtraServiceActualChargeType ...
type ExtraServiceActualChargeType struct {
	Currency              *Nonemptystring `xml:"Currency"`
	Amount                *Nonemptystring `xml:"Amount"`
	Percentage            *Nonemptystring `xml:"Percentage"`
	FixedAdditionalAmount *Nonemptystring `xml:"FixedAdditionalAmount"`
}
