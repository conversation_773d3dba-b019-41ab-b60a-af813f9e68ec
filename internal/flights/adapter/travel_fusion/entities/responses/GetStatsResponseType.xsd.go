package responses

// GetStatsResponseType ...
type GetStatsResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	StatsData           *StatsDataType           `xml:"StatsData"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// StatsDataType ...
type StatsDataType struct {
	StatsPeriodList *StatsPeriodListType `xml:"StatsPeriodList"`
}

// StatsPeriodListType ...
type StatsPeriodListType struct {
	StatsPeriod []*StatsPeriodType `xml:"StatsPeriod"`
}

// StatsPeriodType ...
type StatsPeriodType struct {
	StartDate                         string                                 `xml:"StartDate"`
	EndDate                           string                                 `xml:"EndDate"`
	ReferralsPeriodData               *ReferralsPeriodDataType               `xml:"referralsPeriodData"`
	SuppliersearchesPeriodData        *SupplierSearchesPeriodDataType        `xml:"suppliersearchesPeriodData"`
	UsersearchsummaryPeriodData       *UserSearchSummaryPeriodDataType       `xml:"usersearchsummaryPeriodData"`
	SimpleusersearchsummaryPeriodData *SimpleUserSearchSummaryPeriodDataType `xml:"simpleusersearchsummaryPeriodData"`
	CommandsummaryPeriodData          *CommandSummaryPeriodDataType          `xml:"commandsummaryPeriodData"`
	ClearstatsdataPeriodData          *ClearStatsDataPeriodDataType          `xml:"clearstatsdataPeriodData"`
	CommandSummaryPeriodData          *NewCommandSummaryPeriodDataType       `xml:"CommandSummaryPeriodData"`
	ReferralSummaryPeriodData         *ReferralSummaryPeriodDataType         `xml:"ReferralSummaryPeriodData"`
	UserSearchSummaryPeriodData       *NewUserSearchSummaryPeriodDataType    `xml:"UserSearchSummaryPeriodData"`
	SupplierSearchSummaryPeriodData   *SupplierSearchSummaryPeriodDataType   `xml:"SupplierSearchSummaryPeriodData"`
	ConvertStatsSummaryPeriodData     *ConvertStatsSummaryPeriodDataType     `xml:"ConvertStatsSummaryPeriodData"`
}

// ReferralsPeriodDataType ...
type ReferralsPeriodDataType struct {
	SupplierList *SimpleHashtableOutputType `xml:"SupplierList"`
	EndUserList  *SimpleHashtableOutputType `xml:"EndUserList"`
}

// SimpleHashtableOutputType ...
type SimpleHashtableOutputType struct {
	Entry []*SimpleCountHashtableOutputEntryType `xml:"Entry"`
}

// SimpleCountHashtableOutputEntryType ...
type SimpleCountHashtableOutputEntryType struct {
	Name  string `xml:"Name"`
	Count int    `xml:"Count"`
}

// ComplexHashtableOutputType ...
type ComplexHashtableOutputType struct {
	Entry []*ComplexHashtableOutputEntryType `xml:"Entry"`
}

// ComplexHashtableOutputEntryType ...
type ComplexHashtableOutputEntryType struct {
	Name      string                     `xml:"Name"`
	Breakdown *SimpleHashtableOutputType `xml:"Breakdown"`
}

// SupplierSearchesPeriodDataType ...
type SupplierSearchesPeriodDataType struct {
	TOTALSEARCHES             int                            `xml:"TOTALSEARCHES"`
	CACHEHITSPERCENT          float64                        `xml:"CACHEHITSPERCENT"`
	CACHEMISSESPERCENT        float64                        `xml:"CACHEMISSESPERCENT"`
	CACHEUNKNOWNPERCENT       float64                        `xml:"CACHEUNKNOWNPERCENT"`
	SupplierSearchSummaryList *SupplierSearchSummaryListType `xml:"SupplierSearchSummaryList"`
}

// SupplierSearchSummaryListType ...
type SupplierSearchSummaryListType struct {
	SupplierSearchSummary []*SupplierSearchSummaryType `xml:"SupplierSearchSummary"`
}

// SupplierSearchSummaryType ...
type SupplierSearchSummaryType struct {
	Supplier            *CommonSupplierIdentifier `xml:"Supplier"`
	TotalSearches       int                       `xml:"TotalSearches"`
	CacheHitsPercent    float64                   `xml:"CacheHitsPercent"`
	CacheMissesPercent  float64                   `xml:"CacheMissesPercent"`
	CacheUnknownPercent float64                   `xml:"CacheUnknownPercent"`
	UncachedResults     *UncachedResultsType      `xml:"UncachedResults"`
	CachedResults       *CachedResultsType        `xml:"CachedResults"`
}

// UncachedResultsType ...
type UncachedResultsType struct {
	TotalUncachedSearches        int                      `xml:"TotalUncachedSearches"`
	AverageConnectionTimeSeconds float64                  `xml:"AverageConnectionTimeSeconds"`
	SearchBreakdown              *UncachedSearchBreakdown `xml:"SearchBreakdown"`
}

// UncachedSearchBreakdown ...
type UncachedSearchBreakdown struct {
	TotalCompleteSlowSearchesPercent float64 `xml:"TotalCompleteSlowSearchesPercent"`
	TotalIncompleteSearchesPercent   float64 `xml:"TotalIncompleteSearchesPercent"`
	TotalTooFastSearchesPercent      float64 `xml:"TotalTooFastSearchesPercent"`
}

// CachedResultsType ...
type CachedResultsType struct {
	TotalCachedSearches          int     `xml:"TotalCachedSearches"`
	AverageConnectionTimeSeconds float64 `xml:"AverageConnectionTimeSeconds"`
	Incomplete                   int     `xml:"Incomplete"`
}

// UserSearchSummaryPeriodDataType ...
type UserSearchSummaryPeriodDataType struct {
	UserSearchesList              *SimpleHashtableOutputType  `xml:"UserSearchesList"`
	UserSupplierSearchesList      *ComplexHashtableOutputType `xml:"UserSupplierSearchesList"`
	FlightRouteCounts             *SimpleHashtableOutputType  `xml:"FlightRouteCounts"`
	FlightOriginCounts            *SimpleHashtableOutputType  `xml:"FlightOriginCounts"`
	FlightDestinationCounts       *SimpleHashtableOutputType  `xml:"FlightDestinationCounts"`
	FlightCountryRouteCounts      *SimpleHashtableOutputType  `xml:"FlightCountryRouteCounts"`
	CarOriginCounts               *SimpleHashtableOutputType  `xml:"CarOriginCounts"`
	CarCountryRouteCounts         *SimpleHashtableOutputType  `xml:"CarCountryRouteCounts"`
	HotelDestinationCounts        *SimpleHashtableOutputType  `xml:"HotelDestinationCounts"`
	HotelDestinationCountryCounts *SimpleHashtableOutputType  `xml:"HotelDestinationCountryCounts"`
	CountryRouteCounts            *SimpleHashtableOutputType  `xml:"CountryRouteCounts"`
}

// SimpleUserSearchSummaryPeriodDataType ...
type SimpleUserSearchSummaryPeriodDataType struct {
	Searches     *SimpleHashtableOutputType `xml:"Searches"`
	SupplierList *SimpleHashtableOutputType `xml:"SupplierList"`
}

// CommandSummaryPeriodDataType ...
type CommandSummaryPeriodDataType struct {
	CommandBreakdown  *SimpleHashtableOutputType  `xml:"CommandBreakdown"`
	UserCommandCounts *ComplexHashtableOutputType `xml:"UserCommandCounts"`
	CommandUserCounts *ComplexHashtableOutputType `xml:"CommandUserCounts"`
}

// ClearStatsDataPeriodDataType ...
type ClearStatsDataPeriodDataType struct {
	StatsDataClearingStatus *SimpleHashtableOutputType `xml:"StatsDataClearingStatus"`
}

// NewCommandSummaryPeriodDataType ...
type NewCommandSummaryPeriodDataType struct {
	CommandBreakdown *SimpleHashtableOutputType `xml:"CommandBreakdown"`
}

// ReferralSummaryPeriodDataType ...
type ReferralSummaryPeriodDataType struct {
	ReferralBreakdown *SimpleHashtableOutputType `xml:"ReferralBreakdown"`
}

// SupplierSearchSummaryPeriodDataType ...
type SupplierSearchSummaryPeriodDataType struct {
	SupplierSearchBreakdown *SimpleHashtableOutputType `xml:"SupplierSearchBreakdown"`
}

// NewUserSearchSummaryPeriodDataType ...
type NewUserSearchSummaryPeriodDataType struct {
	UserSearchBreakdown *SimpleHashtableOutputType `xml:"UserSearchBreakdown"`
}

// ConvertStatsSummaryPeriodDataType ...
type ConvertStatsSummaryPeriodDataType struct {
	ConvertStatsBreakdown *SimpleHashtableOutputType `xml:"ConvertStatsBreakdown"`
}
