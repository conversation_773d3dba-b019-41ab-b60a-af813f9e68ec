package responses

// StartRoutingHotelResponseType ...
type StartRoutingHotelResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	RoutingId           *CommonId                `xml:"RoutingId"`
	RouterList          *HotelRouterList         `xml:"RouterList"`
	Summary             *HotelRoutingSummaryType `xml:"Summary"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}
