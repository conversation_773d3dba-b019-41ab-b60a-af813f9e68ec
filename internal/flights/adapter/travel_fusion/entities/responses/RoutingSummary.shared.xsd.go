package responses

// RoutingSummaryType ...
type RoutingSummaryType struct {
	Origin             *FullLocationType                     `xml:"Origin"`
	Destination        *FullLocationType                     `xml:"Destination"`
	Distance           string                                `xml:"Distance"`
	OutwardDate        string                                `xml:"OutwardDate"`
	ReturnDate         string                                `xml:"ReturnDate"`
	AdditionalTripList *RoutingSummaryAdditionalTripListType `xml:"AdditionalTripList"`
}

// FullLocationType ...
type FullLocationType struct {
	Location    *LocationWithoutLists          `xml:"Location"`
	AirportList *RoutingSummaryAirportListType `xml:"AirportList"`
	CityList    *RoutingSummaryCityListType    `xml:"CityList"`
	StationList *RoutingSummaryStationListType `xml:"StationList"`
}

// LocationWithoutLists ...
type LocationWithoutLists struct {
	Id      int                                      `xml:"Id"`
	Name    string                                   `xml:"Name"`
	Country *CommonFullCountryType                   `xml:"Country"`
	Type    *CommonLocationTypeType                  `xml:"Type"`
	City    *FullAirportOrCityTypeWithOptionalCode   `xml:"City"`
	Airport *FullAirportOrCityTypeWithCompulsoryCode `xml:"Airport"`
	Station *FullAirportOrCityTypeWithOptionalCode   `xml:"Station"`
}

// FullAirportOrCityTypeWithOptionalCode ...
type FullAirportOrCityTypeWithOptionalCode struct {
	Name string    `xml:"Name"`
	Code *TF7Alpha `xml:"Code"`
}

// FullAirportOrCityTypeWithCompulsoryCode ...
type FullAirportOrCityTypeWithCompulsoryCode struct {
	Name string     `xml:"Name"`
	Code *CommonTLA `xml:"Code"`
}

// RoutingSummaryAirportListType ...
type RoutingSummaryAirportListType struct {
	Airport []*FullAirportWithCityAndDistanceType `xml:"Airport"`
}

// FullAirportWithCityAndDistanceType ...
type FullAirportWithCityAndDistanceType struct {
	Name     string                                 `xml:"Name"`
	Code     *CommonTLA                             `xml:"Code"`
	City     *FullAirportOrCityTypeWithOptionalCode `xml:"City"`
	Distance string                                 `xml:"Distance"`
}

// RoutingSummaryCityListType ...
type RoutingSummaryCityListType struct {
	City []*FullCityWithDistanceType `xml:"City"`
}

// RoutingSummaryStationListType ...
type RoutingSummaryStationListType struct {
	Station []*FullStationWithCityType `xml:"Station"`
}

// FullStationWithCityType ...
type FullStationWithCityType struct {
	Name string                           `xml:"Name"`
	Code *TrainCode                       `xml:"Code"`
	City *FullStationCityWithDistanceType `xml:"City"`
}

// FullStationCityWithDistanceType ...
type FullStationCityWithDistanceType struct {
	Name     string     `xml:"Name"`
	Code     *TrainCode `xml:"Code"`
	Distance string     `xml:"Distance"`
}

// FullCityWithDistanceType ...
type FullCityWithDistanceType struct {
	Name     string    `xml:"Name"`
	Code     *TF7Alpha `xml:"Code"`
	Distance string    `xml:"Distance"`
}

// RoutingSummaryAdditionalTripListType ...
type RoutingSummaryAdditionalTripListType struct {
	AdditionalTrip []*RoutingSummaryType `xml:"AdditionalTrip"`
}
