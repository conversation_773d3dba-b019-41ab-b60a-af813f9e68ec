package responses

import (
	"encoding/xml"
)

// GetBookingDetailsResponseType ...
type GetBookingDetailsResponseType struct {
	ErrorAttr
	MillisAttr                       int                                      `xml:"millis,attr,omitempty"`
	LoginId                          string                                   `xml:"LoginId"`
	Mode                             *CommonMode                              `xml:"Mode"`
	Status                           string                                   `xml:"Status"`
	BranchId                         string                                   `xml:"BranchId"`
	TFReference                      string                                   `xml:"TFReference"`
	SupplierReference                string                                   `xml:"SupplierReference"`
	LinkedTFRef                      string                                   `xml:"LinkedTFRef"`
	ChangeLinkRefsList               *ChangeLinkRefsListType                  `xml:"ChangeLinkRefsList"`
	SupplierName                     *CommonSupplierIdentifier                `xml:"SupplierName"`
	SupplierConfirmationDataItemList *SupplierConfirmationDataItemListType    `xml:"SupplierConfirmationDataItemList"`
	TicketInfo                       *TicketInfoType                          `xml:"TicketInfo"`
	EmdList                          *EmdListType                             `xml:"EmdList"`
	IsFakeBooking                    bool                                     `xml:"IsFakeBooking"`
	RouterHistory                    *GetBookingDetailsRouterHistoryType      `xml:"RouterHistory"`
	HotelRouterHistory               *GetBookingDetailsHotelRouterHistoryType `xml:"HotelRouterHistory"`
	BookingProfile                   *BookingProfileType                      `xml:"BookingProfile"`
	AirportNamePair                  *GetBookingDetailsAirportNamePairType    `xml:"AirportNamePair"`
	DateOfCancel                     string                                   `xml:"DateOfCancel"`
	SubstituteCardId                 string                                   `xml:"SubstituteCardId"`
	CardSubstitutionFee              string                                   `xml:"CardSubstitutionFee"`
	GeneralInfoItemList              *GeneralInfoItemListType                 `xml:"GeneralInfoItemList"`
}

// ChangeLinkRefsListType ...
type ChangeLinkRefsListType struct {
	ChangeLinkRefs []string `xml:"ChangeLinkRefs"`
}

// TicketInfoType ...
type TicketInfoType struct {
	Amount     float64            `xml:"Amount"`
	Currency   *CommonTLA         `xml:"Currency"`
	Commission *CommonSimplePrice `xml:"Commission"`
	Fee        *CommonSimplePrice `xml:"Fee"`
	TicketList *TicketListType    `xml:"TicketList"`
}

// TicketListType ...
type TicketListType struct {
	Ticket []*TicketType `xml:"Ticket"`
}

// TicketType ...
type TicketType struct {
	TicketNumber    string                 `xml:"TicketNumber"`
	TicketStatus    string                 `xml:"TicketStatus"`
	Requester       *RequesterType         `xml:"Requester"`
	AgentId         string                 `xml:"AgentId"`
	TicketIssueDate *TicketIssueDateType   `xml:"TicketIssueDate"`
	Amount          float64                `xml:"Amount"`
	Currency        *CommonTLA             `xml:"Currency"`
	Commission      *CommonSimplePrice     `xml:"Commission"`
	Fee             *CommonSimplePrice     `xml:"Fee"`
	TaxList         *TicketTaxListType     `xml:"TaxList"`
	PaymentMethod   string                 `xml:"PaymentMethod"`
	TicketCoupons   *TicketCouponsType     `xml:"TicketCoupons"`
	RefundPrice     *TicketRefundPriceType `xml:"RefundPrice"`
}

// TicketStatusEnumeration ...
type TicketStatusEnumeration string

// RequesterType ...
type RequesterType struct {
	SourceAttr   string `xml:"source,attr,omitempty"`
	InferredAttr bool   `xml:"inferred,attr,omitempty"`
	Value        string `xml:",chardata"`
}

// TicketIssueDateType ...
type TicketIssueDateType struct {
	TimezoneSourceAttr string `xml:"timezoneSource,attr,omitempty"`
	IgnoreTimeAttr     bool   `xml:"ignoreTime,attr,omitempty"`
	Value              string `xml:",chardata"`
}

// TicketTaxListType ...
type TicketTaxListType struct {
	Tax []*TicketTaxType `xml:"Tax"`
}

// TicketTaxType ...
type TicketTaxType struct {
	Name     string     `xml:"Name"`
	Amount   float64    `xml:"Amount"`
	Currency *CommonTLA `xml:"Currency"`
}

// TicketCouponsType ...
type TicketCouponsType struct {
	Coupon []*TicketCouponType `xml:"Coupon"`
}

// TicketCouponType ...
type TicketCouponType struct {
	CouponPrice          *CommonSimplePrice `xml:"CouponPrice"`
	SegmentRefs          string             `xml:"SegmentRefs"`
	TravellerRefs        string             `xml:"TravellerRefs"`
	SupplierCouponStatus string             `xml:"SupplierCouponStatus"`
	TFCouponStatus       string             `xml:"TFCouponStatus"`
}

// TicketRefundPriceType ...
type TicketRefundPriceType struct {
	Amount     float64            `xml:"Amount"`
	Currency   *CommonTLA         `xml:"Currency"`
	Commission *CommonSimplePrice `xml:"Commission"`
	Fee        *CommonSimplePrice `xml:"Fee"`
	TaxList    *TicketTaxListType `xml:"TaxList"`
}

// EmdListType ...
type EmdListType struct {
	Emd []*EmdType `xml:"Emd"`
}

// EmdType ...
type EmdType struct {
	DocumentNumber string             `xml:"DocumentNumber"`
	EmdStatus      string             `xml:"EmdStatus"`
	Requester      *RequesterType     `xml:"Requester"`
	EmdPrice       *CommonSimplePrice `xml:"EmdPrice"`
	EmdCoupons     *EmdCouponsType    `xml:"EmdCoupons"`
	EmdData        *EmdDataType       `xml:"EmdData"`
}

// EmdStatusEnumeration ...
type EmdStatusEnumeration string

// EmdCouponsType ...
type EmdCouponsType struct {
	Coupon []*EmdCouponType `xml:"Coupon"`
}

// EmdCouponType ...
type EmdCouponType struct {
	TicketNumberRefs     string             `xml:"TicketNumberRefs"`
	RFIC                 *RFICType          `xml:"RFIC"`
	RFIS                 *RFISType          `xml:"RFIS"`
	CouponPrice          *CommonSimplePrice `xml:"CouponPrice"`
	SegmentRefs          string             `xml:"SegmentRefs"`
	TravellerRefs        string             `xml:"TravellerRefs"`
	SupplierCouponStatus string             `xml:"SupplierCouponStatus"`
	TFCouponStatus       string             `xml:"TFCouponStatus"`
}

// RFICType ...
type RFICType struct {
	Code  string `xml:"Code"`
	Label string `xml:"Label"`
}

// RFISType ...
type RFISType struct {
	Code        string `xml:"Code"`
	Description string `xml:"Description"`
	Detail      string `xml:"Detail"`
}

// EmdDataType ...
type EmdDataType struct {
	TicketNumberRefs string    `xml:"TicketNumberRefs"`
	RFIC             *RFICType `xml:"RFIC"`
	RFIS             *RFISType `xml:"RFIS"`
	SegmentRefs      string    `xml:"SegmentRefs"`
	TravellerRefs    string    `xml:"TravellerRefs"`
}

// GetBookingDetailsRouterHistoryType ...
type GetBookingDetailsRouterHistoryType struct {
	RoutingId             *CommonId           `xml:"RoutingId"`
	SearchRouter          *CommonRouter       `xml:"SearchRouter"`
	SearchBookingProfile  *BookingProfileType `xml:"SearchBookingProfile"`
	DetailsRouter         *CommonRouter       `xml:"DetailsRouter"`
	DetailsBookingProfile *BookingProfileType `xml:"DetailsBookingProfile"`
	TermsRouter           *CommonRouter       `xml:"TermsRouter"`
	TermsBookingProfile   *BookingProfileType `xml:"TermsBookingProfile"`
	BookingRouter         *CommonRouter       `xml:"BookingRouter"`
}

// GetBookingDetailsAirportNamePairType ...
type GetBookingDetailsAirportNamePairType struct {
	OriginAirport      string `xml:"OriginAirport"`
	DestinationAirport string `xml:"DestinationAirport"`
}

// GetBookingDetailsHotelRouterHistoryType ...
type GetBookingDetailsHotelRouterHistoryType struct {
	RoutingId            *CommonId           `xml:"RoutingId"`
	SearchRouter         *HotelRouter        `xml:"SearchRouter"`
	SearchBookingProfile *BookingProfileType `xml:"SearchBookingProfile"`
	DetailsRouter        *HotelRouter        `xml:"DetailsRouter"`
	TermsRouter          *HotelRouter        `xml:"TermsRouter"`
	TermsBookingProfile  *BookingProfileType `xml:"TermsBookingProfile"`
	BookingRouter        *HotelRouter        `xml:"BookingRouter"`
	CancelRouter         *HotelRouter        `xml:"CancelRouter"`
}

// GetCountriesResponseType ...
type GetCountriesResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	CountryList         *GetCountriesCountryList `xml:"CountryList"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// GetCountriesCountryList ...
type GetCountriesCountryList struct {
	XMLName xml.Name                            `xml:"getCountriesCountryList"`
	Country []*CommonFullCountryTypeWithDetails `xml:"Country"`
}

// GetCurrenciesResponseType ...
type GetCurrenciesResponseType struct {
	MillisAttr          int                                 `xml:"millis,attr,omitempty"`
	LoginId             string                              `xml:"LoginId"`
	CurrencyList        *CurrenciesResponseCurrencyListType `xml:"CurrencyList"`
	GeneralInfoItemList *GeneralInfoItemListType            `xml:"GeneralInfoItemList"`
}

// CurrenciesResponseCurrencyListType ...
type CurrenciesResponseCurrencyListType struct {
	Currency []*CurrenciesResponseCurrencyType `xml:"Currency"`
}

// CurrenciesResponseCurrencyType ...
type CurrenciesResponseCurrencyType struct {
	Name    string     `xml:"Name"`
	Code    *CommonTLA `xml:"Code"`
	UsdRate float64    `xml:"UsdRate"`
	NokRate float64    `xml:"NokRate"`
	Rate    float64    `xml:"Rate"`
}

// GetErrorCodesResponseType ...
type GetErrorCodesResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	ErrorCodeGroupList  *GetErrorCodesGroupList  `xml:"ErrorCodeGroupList"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// GetErrorCodesCodeGroup ...
type GetErrorCodesCodeGroup struct {
	Code               string                  `xml:"Code"`
	Name               string                  `xml:"Name"`
	Description        string                  `xml:"Description"`
	ErrorCodeGroupList *GetErrorCodesGroupList `xml:"ErrorCodeGroupList"`
	ErrorCodeList      *GetErrorCodesCodeList  `xml:"ErrorCodeList"`
}

// GetErrorCodesErrorCode ...
type GetErrorCodesErrorCode struct {
	Code        string `xml:"Code"`
	Name        string `xml:"Name"`
	Description string `xml:"Description"`
	Visibility  string `xml:"Visibility"`
}

// GetErrorCodesGroupList ...
type GetErrorCodesGroupList struct {
	ErrorCodeGroup []*GetErrorCodesCodeGroup `xml:"ErrorCodeGroup"`
}

// GetErrorCodesCodeList ...
type GetErrorCodesCodeList struct {
	ErrorCode []*GetErrorCodesErrorCode `xml:"ErrorCode"`
}

// LoginResponseType ...
type LoginResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// GetPrePackagedSupplierSummaryResponseType ...
type GetPrePackagedSupplierSummaryResponseType struct {
	MillisAttr                     int                                 `xml:"millis,attr,omitempty"`
	LoginId                        string                              `xml:"LoginId"`
	PrePackagedSupplierSummaryList *PrePackagedSupplierSummaryListType `xml:"PrePackagedSupplierSummaryList"`
	GeneralInfoItemList            *GeneralInfoItemListType            `xml:"GeneralInfoItemList"`
}

// PrePackagedSupplierSummaryListType ...
type PrePackagedSupplierSummaryListType struct {
	PrePackagedSupplierSummary []*PrePackagedSupplierSummaryType `xml:"PrePackagedSupplierSummary"`
}

// PrePackagedSupplierSummaryType ...
type PrePackagedSupplierSummaryType struct {
	Supplier                            *CommonSupplierIdentifier  `xml:"Supplier"`
	SupportedDurationList               *SupportedDurationListType `xml:"SupportedDurationList"`
	RecommendedDepartureDateRange       int                        `xml:"RecommendedDepartureDateRange"`
	LastPossibleDepartureDate           string                     `xml:"LastPossibleDepartureDate"`
	RouteList                           *PrePackagedRouteListType  `xml:"RouteList"`
	RequestDestinationsSeparately       bool                       `xml:"RequestDestinationsSeparately"`
	RecommendedPreFetchFrequencyMinutes int                        `xml:"RecommendedPreFetchFrequencyMinutes"`
}

// SupportedDurationListType ...
type SupportedDurationListType struct {
	// SupportedDuration []*PrePackagedDurationType `xml:"SupportedDuration"`
}

// PrePackagedRouteListType ...
type PrePackagedRouteListType struct {
	Route []*PrePackagedRouteType `xml:"Route"`
}

// PrePackagedRouteType ...
type PrePackagedRouteType struct {
	Origin      *PrePackagedOriginType      `xml:"Origin"`
	Destination *PrePackagedDestinationType `xml:"Destination"`
}

// PrePackagedOriginType ...
type PrePackagedOriginType struct {
	Code *CommonTLA `xml:"Code"`
}

// PrePackagedDestinationType ...
type PrePackagedDestinationType struct {
	Code                string                 `xml:"Code"`
	DisplayName         string                 `xml:"DisplayName"`
	GeoCoordinates      *CommonCoordinates     `xml:"GeoCoordinates"`
	TFLocationId        int                    `xml:"TFLocationId"`
	TFNearestLocationId int                    `xml:"TFNearestLocationId"`
	Country             *CommonFullCountryType `xml:"Country"`
	Admin1Region        string                 `xml:"Admin1Region"`
	Admin2Region        string                 `xml:"Admin2Region"`
}

// GetHotelDetailsResponseType ...
type GetHotelDetailsResponseType struct {
	MillisAttr               int                           `xml:"millis,attr,omitempty"`
	LoginId                  string                        `xml:"LoginId"`
	SupplierHotelDetailsList *SupplierHotelDetailsListType `xml:"SupplierHotelDetailsList"`
	GeneralInfoItemList      *GeneralInfoItemListType      `xml:"GeneralInfoItemList"`
}

// GetMultipleHotelDetailsResponseType ...
type GetMultipleHotelDetailsResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	HotelDetailsList    *HotelDetailsListType    `xml:"HotelDetailsList"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// HotelDetailsListType ...
type HotelDetailsListType struct {
	HotelDetails []*HotelDetailsListItemType `xml:"HotelDetails"`
}

// HotelDetailsListItemType ...
type HotelDetailsListItemType struct {
	EcodeAttr                string                        `xml:"ecode,attr,omitempty"`
	EtextAttr                string                        `xml:"etext,attr,omitempty"`
	EdetailAttr              string                        `xml:"edetail,attr,omitempty"`
	EdateAttr                string                        `xml:"edate,attr,omitempty"`
	HotelId                  *CommonId                     `xml:"HotelId"`
	IsLocked                 bool                          `xml:"IsLocked"`
	SupplierHotelDetailsList *SupplierHotelDetailsListType `xml:"SupplierHotelDetailsList"`
}

// SupplierHotelDetailsListType ...
type SupplierHotelDetailsListType struct {
	SupplierHotelDetails []*SupplierHotelDetailsType `xml:"SupplierHotelDetails"`
}

// SupplierHotelDetailsType ...
type SupplierHotelDetailsType struct {
	Supplier     *CommonSupplierIdentifier `xml:"Supplier"`
	HotelDetails *HotelDetailsType         `xml:"HotelDetails"`
}

// HotelDetailsType ...
type HotelDetailsType struct {
	AccommodationId              int       `xml:"AccommodationId"`
	TfAccommodationId            *CommonId `xml:"TfAccommodationId"`
	HotelSupplierAccommodationId string    `xml:"HotelSupplierAccommodationId"`
	Stars                        float64   `xml:"Stars"`
	SupplierSpecialRanking       string    `xml:"SupplierSpecialRanking"`
	// Type                         *AccommodationTypesEnumeration          `xml:"Type"`
	Name                  string                                  `xml:"Name"`
	GeoCoordinates        *CommonCoordinates                      `xml:"GeoCoordinates"`
	Description           string                                  `xml:"Description"`
	Address               string                                  `xml:"Address"`
	AddressDetails        *HotelAddressDetailsType                `xml:"AddressDetails"`
	GDSChain              *HotelGDSChainType                      `xml:"GDSChain"`
	GDSInfo               string                                  `xml:"GDSInfo"`
	ImageList             *HotelAndCarImageListType               `xml:"ImageList"`
	FacilityList          *AccommodationFacilitiesListType        `xml:"FacilityList"`
	MiscellaneousInfoList *MiscellaneousAccommodationInfoListType `xml:"MiscellaneousInfoList"`
	CityCode              string                                  `xml:"CityCode"`
	CountryCode           string                                  `xml:"CountryCode"`
}

// AccommodationDistanceType ...
type AccommodationDistanceType struct {
	Type             string `xml:"Type"`
	DistanceInMetres int    `xml:"DistanceInMetres"`
}

// AccommodationDistanceTypeEnumeration ...
type AccommodationDistanceTypeEnumeration string

// LogPrePackagedReferralResponseType ...
type LogPrePackagedReferralResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// ProcessCardVerificationResponseType ...
type ProcessCardVerificationResponseType struct {
	MillisAttr           int                       `xml:"millis,attr,omitempty"`
	LoginId              string                    `xml:"LoginId"`
	CardVerificationData *CardVerificationDataType `xml:"CardVerificationData"`
	GeneralInfoItemList  *GeneralInfoItemListType  `xml:"GeneralInfoItemList"`
}

// CardVerificationDataType ...
type CardVerificationDataType struct {
	VerificationType string `xml:"VerificationType"`
	Url              string `xml:"Url"`
}

// CardVerificationTypeType ...
type CardVerificationTypeType string
