package responses

// CheckRoutingHotelResponseType ...
type CheckRoutingHotelResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	RoutingId           *CommonId                `xml:"RoutingId"`
	Mode                *CommonMode              `xml:"Mode"`
	RouterList          *HotelRouterList         `xml:"RouterList"`
	Summary             *HotelRoutingSummaryType `xml:"Summary"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// HotelRouterList ...
type HotelRouterList struct {
	Router []*HotelRouter `xml:"Router"`
}

// HotelRouter ...
type HotelRouter struct {
	RequiredParameterList *RouterRequiredParameterListType   `xml:"RequiredParameterList"`
	Supplier              *CommonSupplierIdentifier          `xml:"Supplier"`
	LogoNameSuffix        string                             `xml:"LogoNameSuffix"`
	Vendor                *RouterVendorType                  `xml:"Vendor"`
	Complete              bool                               `xml:"Complete"`
	RequestedLocations    *HotelRouterRequestedLocationsType `xml:"RequestedLocations"`
	// AccommodationType     *AccommodationTypesEnumeration     `xml:"AccommodationType"
	ResultList       *AccommodationResultListType `xml:"ResultList"`
	SupplierInfoList *RouterSupplierInfoListType  `xml:"SupplierInfoList"`
}

// HotelRouterRequestedLocationsType ...
type HotelRouterRequestedLocationsType struct {
	Destination *HotelAndCarRouterRequestedLocationType `xml:"Destination"`
}

// HotelAndCarRouterRequestedLocationType ...
type HotelAndCarRouterRequestedLocationType struct {
	Type string `xml:"Type"`
	Code string `xml:"Code"`
}

// HotelAndCarRouterRequestedLocationTypeEnumeration ...
type HotelAndCarRouterRequestedLocationTypeEnumeration string

// AccommodationResultListType ...
type AccommodationResultListType struct {
	EcodeAttr   string                     `xml:"ecode,attr,omitempty"`
	EtextAttr   string                     `xml:"etext,attr,omitempty"`
	EdetailAttr string                     `xml:"edetail,attr,omitempty"`
	EdateAttr   string                     `xml:"edate,attr,omitempty"`
	Result      []*AccommodationResultType `xml:"Result"`
}

// AccommodationResultType ...
type AccommodationResultType struct {
	Id             *CommonId `xml:"Id"`
	HotelId        *CommonId `xml:"HotelId"`
	Stars          float64   `xml:"Stars"`
	SpecialRanking string    `xml:"SpecialRanking"`
	CheckinDate    string    `xml:"CheckinDate"`
	CheckoutDate   string    `xml:"CheckoutDate"`
	// Type                  *AccommodationTypesEnumeration          `xml:"Type"`
	Name                  string                                  `xml:"Name"`
	GeoCoordinates        *CommonCoordinates                      `xml:"GeoCoordinates"`
	Description           string                                  `xml:"Description"`
	Address               string                                  `xml:"Address"`
	AddressDetails        *HotelAddressDetailsType                `xml:"AddressDetails"`
	NearestIATACityCode   string                                  `xml:"NearestIATACityCode"`
	GDSChain              *HotelGDSChainType                      `xml:"GDSChain"`
	ImageList             *HotelAndCarImageListType               `xml:"ImageList"`
	OptionList            *AccommodationOptionsListType           `xml:"OptionList"`
	RoomExtraList         *RoomExtraListType                      `xml:"RoomExtraList"`
	FacilityList          *AccommodationFacilitiesListType        `xml:"FacilityList"`
	MiscellaneousInfoList *MiscellaneousAccommodationInfoListType `xml:"MiscellaneousInfoList"`
}

// AccommodationOptionsListType ...
type AccommodationOptionsListType struct {
	Option []*AccommodationOptionType `xml:"Option"`
}

// AccommodationOptionType ...
type AccommodationOptionType struct {
	Id                    *CommonId                               `xml:"Id"`
	Price                 *CommonPrice                            `xml:"Price"`
	MiscellaneousInfoList *MiscellaneousAccommodationInfoListType `xml:"MiscellaneousInfoList"`
	Terms                 *GenericTermType                        `xml:"Terms"`
	ImageList             *HotelAndCarImageListType               `xml:"ImageList"`
	HotelData             *HotelAndHostelDataType                 `xml:"HotelData"`
	ApartmentData         *ApartmentDataType                      `xml:"ApartmentData"`
	HouseData             *HouseDataType                          `xml:"HouseData"`
	HostelData            *HotelAndHostelDataType                 `xml:"HostelData"`
}

// HotelAndHostelDataType ...
type HotelAndHostelDataType struct {
	RoomList *HotelRoomListType `xml:"RoomList"`
	MealList *HotelMealListType `xml:"MealList"`
}

// ApartmentDataType ...
type ApartmentDataType struct {
	MealList     *HotelMealListType `xml:"MealList"`
	NumberOfBeds int                `xml:"NumberOfBeds"`
}

// HouseDataType ...
type HouseDataType struct {
	NumberOfBeds int `xml:"NumberOfBeds"`
}

// MiscellaneousAccommodationInfoListType ...
type MiscellaneousAccommodationInfoListType struct {
	MiscellaneousInfo []*MiscellaneousAccommodationInfoType `xml:"MiscellaneousInfo"`
}

// MiscellaneousAccommodationInfoType ...
type MiscellaneousAccommodationInfoType struct {
	Name  string `xml:"Name"`
	Value string `xml:"Value"`
}

// GenericTermType ...
type GenericTermType struct {
	CancellationTerm *CancellationTermType `xml:"CancellationTerm"`
}

// CancellationTermType ...
type CancellationTermType struct {
	Cancellable            string                       `xml:"Cancellable"`
	Refundable             string                       `xml:"Refundable"`
	CancellationPolicyList *CancellationsPolicyListType `xml:"CancellationPolicyList"`
}

// CancellationsPolicyListType ...
type CancellationsPolicyListType struct {
	CancellationPolicy []*CancellationPolicyType `xml:"CancellationPolicy"`
}

// CancellationPolicyType ...
type CancellationPolicyType struct {
	Code string               `xml:"Code"`
	Fee  *CancellationFeeType `xml:"Fee"`
}

// CancellationFeeType ...
type CancellationFeeType struct {
	Type  string `xml:"Type"`
	Value string `xml:"Value"`
}

// HotelAddressDetailsType ...
type HotelAddressDetailsType struct {
	PostalCode string `xml:"PostalCode"`
	Country    string `xml:"Country"`
	Province   string `xml:"Province"`
	City       string `xml:"City"`
	Region     string `xml:"Region"`
}

// HotelGDSChainType ...
type HotelGDSChainType struct {
	GDSChainCode     string `xml:"GDSChainCode"`
	GDSChainCodeName string `xml:"GDSChainCodeName"`
}

// HotelAndCarImageListType ...
type HotelAndCarImageListType struct {
	Image []*HotelAndCarImageType `xml:"Image"`
}

// HotelAndCarImageType ...
type HotelAndCarImageType struct {
	Width       int    `xml:"Width"`
	Height      int    `xml:"Height"`
	Description string `xml:"Description"`
	ThumbUrl    string `xml:"ThumbUrl"`
	Url         string `xml:"Url"`
}

// HotelRoomListType ...
type HotelRoomListType struct {
	Room []*HotelRoomType `xml:"Room"`
}

// HotelRoomType ...
type HotelRoomType struct {
	Id    int          `xml:"Id"`
	Price *CommonPrice `xml:"Price"`
	// Type                  *RoomTypesEnumeration                   `xml:"Type"`
	TfType         string `xml:"TfType"`
	MaleFemaleOnly string `xml:"MaleFemaleOnly"`
	// TravellerList         *StartRoutingTravellerList              `xml:"TravellerList"`
	MiscellaneousInfoList *MiscellaneousAccommodationInfoListType `xml:"MiscellaneousInfoList"`
	NumberOfBeds          int                                     `xml:"NumberOfBeds"`
	NumberOfExtraBeds     int                                     `xml:"NumberOfExtraBeds"`
	RoomExtraList         *RoomExtraListType                      `xml:"RoomExtraList"`
	Terms                 *GenericTermType                        `xml:"Terms"`
}

// TfRoomTypesEnumeration ...
type TfRoomTypesEnumeration string

// HotelMealListType ...
type HotelMealListType struct {
	Meal []*HotelMealType `xml:"Meal"`
}

// HotelMealType ...
type HotelMealType struct {
	Type string `xml:"Type"`
}

// HotelMealEnumeration ...
type HotelMealEnumeration string

// AccommodationFacilitiesListType ...
type AccommodationFacilitiesListType struct {
	Facility []*AccommodationFacilityType `xml:"Facility"`
}

// AccommodationFacilityType ...
type AccommodationFacilityType struct {
	Type         string `xml:"Type"`
	Availability string `xml:"Availability"`
	Details      string `xml:"Details"`
}

// AccommodationFacilitiesTypeType ...
type AccommodationFacilitiesTypeType string

// AccommodationFacilitiesAvailabilityType ...
type AccommodationFacilitiesAvailabilityType string

// RoomExtraListType ...
type RoomExtraListType struct {
	RoomExtra []*RoomExtraType `xml:"RoomExtra"`
}

// RoomExtraType ...
type RoomExtraType struct {
	Id          *CommonId               `xml:"Id"`
	Type        string                  `xml:"Type"`
	PriceList   *RoomExtraPriceListType `xml:"PriceList"`
	Description string                  `xml:"Description"`
}

// RoomExtraPriceListType ...
type RoomExtraPriceListType struct {
	Price []*CommonPrice `xml:"Price"`
}

// RoomExtraTypeType ...
type RoomExtraTypeType string
