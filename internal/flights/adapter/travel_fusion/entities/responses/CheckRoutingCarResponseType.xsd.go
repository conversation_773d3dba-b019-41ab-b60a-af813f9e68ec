package responses

// CheckRoutingCarResponseType ...
type CheckRoutingCarResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	RoutingId           *CommonId                `xml:"RoutingId"`
	Mode                *CommonMode              `xml:"Mode"`
	RouterList          *CarRouterList           `xml:"RouterList"`
	Summary             *CarRoutingSummaryType   `xml:"Summary"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// CarRouterList ...
type CarRouterList struct {
	Router []*CarRouter `xml:"Router"`
}

// CarRouter ...
type CarRouter struct {
	Supplier            *CommonSupplierIdentifier        `xml:"Supplier"`
	LogoNameSuffix      string                           `xml:"LogoNameSuffix"`
	Vendor              *RouterVendorType                `xml:"Vendor"`
	Complete            bool                             `xml:"Complete"`
	RequestedLocations  *CarRouterRequestedLocationsType `xml:"RequestedLocations"`
	ResultList          *CarResultListType               `xml:"ResultList"`
	LocationDetailsList *LocationDetailsListType         `xml:"LocationDetailsList"`
}

// LocationDetailsListType ...
type LocationDetailsListType struct {
	LocationDetails []*LocationDetailsType `xml:"LocationDetails"`
}

// LocationDetailsType ...
type LocationDetailsType struct {
	LocationDetailsId   string                      `xml:"LocationDetailsId"`
	LocationDetailsData *SegmentLocationDetailsType `xml:"LocationDetailsData"`
}

// CarRouterRequestedLocationsType ...
type CarRouterRequestedLocationsType struct {
	Pickup  *HotelAndCarRouterRequestedLocationType `xml:"Pickup"`
	Setdown *HotelAndCarRouterRequestedLocationType `xml:"Setdown"`
}

// CarResultListType ...
type CarResultListType struct {
	EcodeAttr   string           `xml:"ecode,attr,omitempty"`
	EtextAttr   string           `xml:"etext,attr,omitempty"`
	EdetailAttr string           `xml:"edetail,attr,omitempty"`
	EdateAttr   string           `xml:"edate,attr,omitempty"`
	Result      []*CarResultType `xml:"Result"`
}

// CarResultType ...
type CarResultType struct {
	Id                    *CommonId                     `xml:"Id"`
	PickupList            *CarPickupListType            `xml:"PickupList"`
	SetdownList           *CarSetdownListType           `xml:"SetdownList"`
	PickupDate            string                        `xml:"PickupDate"`
	SetdownDate           string                        `xml:"SetdownDate"`
	AcrissVehicleType     *AcrissVehicleTypeType        `xml:"AcrissVehicleType"`
	SupplierVehicleType   string                        `xml:"SupplierVehicleType"`
	Make                  string                        `xml:"Make"`
	Model                 string                        `xml:"Model"`
	DriverAge             *CarAgeRange                  `xml:"DriverAge"`
	ImageList             *HotelAndCarImageListType     `xml:"ImageList"`
	MiscellaneousInfoList *MiscellaneousCarInfoListType `xml:"MiscellaneousInfoList"`
}

// CarPickupListType ...
type CarPickupListType struct {
	Pickup []*CarPickupType `xml:"Pickup"`
}

// CarPickupType ...
type CarPickupType struct {
	Price    *CommonPrice         `xml:"Price"`
	Location *SegmentLocationType `xml:"Location"`
}

// CarSetdownListType ...
type CarSetdownListType struct {
	Setdown []*CarSetdownType `xml:"Setdown"`
}

// CarSetdownType ...
type CarSetdownType struct {
	Location *SegmentLocationType `xml:"Location"`
}

// MiscellaneousCarInfoListType ...
type MiscellaneousCarInfoListType struct {
	MiscellaneousInfo []*MiscellaneousCarInfoType `xml:"MiscellaneousInfo"`
}

// MiscellaneousCarInfoType ...
type MiscellaneousCarInfoType struct {
	Name  string `xml:"Name"`
	Value string `xml:"Value"`
}

// AcrissVehicleTypeType ...
type AcrissVehicleTypeType struct {
	VehicleClass    string `xml:"VehicleClass"`
	VehicleType     string `xml:"VehicleType"`
	Transmission    string `xml:"Transmission"`
	AirConditioning string `xml:"AirConditioning"`
}

// CarAgeRange ...
type CarAgeRange struct {
	MinAge int `xml:"MinAge"`
	MaxAge int `xml:"MaxAge"`
}
