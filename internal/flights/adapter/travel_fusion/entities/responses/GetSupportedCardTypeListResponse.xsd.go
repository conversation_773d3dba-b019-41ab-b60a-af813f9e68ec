package responses

// GetSupportedCardTypeListResponse ...
type GetSupportedCardTypeListResponse struct {
	MillisAttr            int                      `xml:"millis,attr,omitempty"`
	LoginId               string                   `xml:"LoginId"`
	SupportedCardTypeList *SupportedCardTypeList   `xml:"SupportedCardTypeList"`
	GeneralInfoItemList   *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// SupportedCardTypeList ...
type SupportedCardTypeList struct {
	SupportedCardType []string `xml:"SupportedCardType"`
}
