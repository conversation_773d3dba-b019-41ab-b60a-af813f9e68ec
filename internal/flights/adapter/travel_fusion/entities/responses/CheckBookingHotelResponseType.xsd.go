package responses

// CheckBookingHotelResponseType ...
type CheckBookingHotelResponseType struct {
	MillisAttr                       int                                   `xml:"millis,attr,omitempty"`
	LoginId                          string                                `xml:"LoginId"`
	TFBookingReference               string                                `xml:"TFBookingReference"`
	Status                           *CommonBookingV2StatusType            `xml:"Status"`
	CardVerificationRequired         bool                                  `xml:"CardVerificationRequired"`
	CardSubstitutionData             *CardSubstitutionDataType             `xml:"CardSubstitutionData"`
	SupplierReference                string                                `xml:"SupplierReference"`
	SupplierConfirmationDataItemList *SupplierConfirmationDataItemListType `xml:"SupplierConfirmationDataItemList"`
	Router                           *HotelRouter                          `xml:"Router"`
	BookingProfile                   *BookingProfileType                   `xml:"BookingProfile"`
	SupplierResponseList             *CommonSupplierResponseListType       `xml:"SupplierResponseList"`
	GeneralInfoItemList              *GeneralInfoItemListType              `xml:"GeneralInfoItemList"`
}
