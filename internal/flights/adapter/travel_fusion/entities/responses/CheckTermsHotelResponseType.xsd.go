package responses

// CheckTermsHotelResponseType ...
type CheckTermsHotelResponseType struct {
	MillisAttr                          int                             `xml:"millis,attr,omitempty"`
	LoginId                             string                          `xml:"LoginId"`
	TFBookingReference                  string                          `xml:"TFBookingReference"`
	Status                              *CommonBookingV2StatusType      `xml:"Status"`
	CardSubstitutionData                *CardSubstitutionDataType       `xml:"CardSubstitutionData"`
	Router                              *HotelRouter                    `xml:"Router"`
	SupplierResponseList                *CommonSupplierResponseListType `xml:"SupplierResponseList"`
	SupplierVisualAuthorisationImageURL string                          `xml:"SupplierVisualAuthorisationImageURL"`
	BookingProfile                      *BookingProfileType             `xml:"BookingProfile"`
	GeneralInfoItemList                 *GeneralInfoItemListType        `xml:"GeneralInfoItemList"`
}
