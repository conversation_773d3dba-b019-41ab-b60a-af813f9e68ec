package responses

// CheckDetailsHotelResponseType ...
type CheckDetailsHotelResponseType struct {
	MillisAttr          int                                            `xml:"millis,attr,omitempty"`
	LoginId             string                                         `xml:"LoginId"`
	RoutingId           *CommonId                                      `xml:"RoutingId"`
	OptionId            *CommonId                                      `xml:"OptionId"`
	Router              *HotelRouter                                   `xml:"Router"`
	SupplierHandoffData *ProcessDetailsResponseSupplierHandoffDataType `xml:"SupplierHandoffData"`
	SupportedCardList   *SupportedCardListType                         `xml:"SupportedCardList"`
	GeneralInfoItemList *GeneralInfoItemListType                       `xml:"GeneralInfoItemList"`
}
