package responses

// CheckRoutingPrePackagedResponseType ...
type CheckRoutingPrePackagedResponseType struct {
	MillisAttr          int                      `xml:"millis,attr,omitempty"`
	LoginId             string                   `xml:"LoginId"`
	RoutingId           *CommonId                `xml:"RoutingId"`
	Mode                *CommonMode              `xml:"Mode"`
	RouterList          *PrePackagedRouterList   `xml:"RouterList"`
	GeneralInfoItemList *GeneralInfoItemListType `xml:"GeneralInfoItemList"`
}

// PrePackagedRouterList ...
type PrePackagedRouterList struct {
	Router []*PrePackagedRouter `xml:"Router"`
}

// PrePackagedRouter ...
type PrePackagedRouter struct {
	Supplier         *CommonSupplierIdentifier        `xml:"Supplier"`
	Vendor           *RouterVendorType                `xml:"Vendor"`
	Complete         bool                             `xml:"Complete"`
	ResultList       *PrePackagedResultListType       `xml:"ResultList"`
	FlightResultList *PrePackagedFlightResultListType `xml:"FlightResultList"`
	HotelResultList  *PrePackagedHotelResultListType  `xml:"HotelResultList"`
}

// PrePackagedResultListType ...
type PrePackagedResultListType struct {
	EcodeAttr   string                   `xml:"ecode,attr,omitempty"`
	EtextAttr   string                   `xml:"etext,attr,omitempty"`
	EdetailAttr string                   `xml:"edetail,attr,omitempty"`
	EdateAttr   string                   `xml:"edate,attr,omitempty"`
	Result      []*PrePackagedResultType `xml:"Result"`
}

// PrePackagedResultType ...
type PrePackagedResultType struct {
	Id             *CommonId    `xml:"Id"`
	FlightResultId *CommonId    `xml:"FlightResultId"`
	HotelResultId  *CommonId    `xml:"HotelResultId"`
	Price          *CommonPrice `xml:"Price"`
	// RoomType            *RoomTypesEnumeration                          `xml:"RoomType"`
	NumberOfBeds        int                                            `xml:"NumberOfBeds"`
	NumberOfExtraBeds   int                                            `xml:"NumberOfExtraBeds"`
	SupplierHandoffData *ProcessDetailsResponseSupplierHandoffDataType `xml:"SupplierHandoffData"`
	NumberOfNights      int                                            `xml:"NumberOfNights"`
	Destination         string                                         `xml:"Destination"`
}

// PrePackagedFlightResultListType ...
type PrePackagedFlightResultListType struct {
	FlightResult []*RouterGroupType `xml:"FlightResult"`
}

// PrePackagedHotelResultListType ...
type PrePackagedHotelResultListType struct {
	HotelResult []*PrePackagedAccommodationResultType `xml:"HotelResult"`
}

// PrePackagedAccommodationResultType ...
type PrePackagedAccommodationResultType struct {
	Id           *CommonId                     `xml:"Id"`
	HotelId      *CommonId                     `xml:"HotelId"`
	CheckinDate  string                        `xml:"CheckinDate"`
	CheckoutDate string                        `xml:"CheckoutDate"`
	OptionList   *AccommodationOptionsListType `xml:"OptionList"`
}
