package responses

// GetSuppliersTravellerInfoResponseType ...
type GetSuppliersTravellerInfoResponseType struct {
	MillisAttr   int                                 `xml:"millis,attr,omitempty"`
	SupplierList *SuppliersTravellerInfoSupplierList `xml:"SupplierList"`
}

// SuppliersTravellerInfoSupplierList ...
type SuppliersTravellerInfoSupplierList struct {
	Supplier []*SuppliersTravellerInfoSupplier `xml:"Supplier"`
}

// SuppliersTravellerInfoSupplier ...
type SuppliersTravellerInfoSupplier struct {
	NameAttr         string                                            `xml:"Name,attr,omitempty"`
	ModeAttr         string                                            `xml:"Mode,attr,omitempty"`
	TravellerAgeList []*SuppliersTravellerInfoSupplierTravellerAgeList `xml:"TravellerAgeList"`
}

// SuppliersTravellerInfoSupplierTravellerAgeList ...
type SuppliersTravellerInfoSupplierTravellerAgeList struct {
	TravellerAge []*SuppliersTravellerInfoSupplierTravellerAge `xml:"TravellerAge"`
}

// SuppliersTravellerInfoSupplierTravellerAge ...
type SuppliersTravellerInfoSupplierTravellerAge struct {
	TypeAttr string `xml:"Type,attr,omitempty"`
	MinAttr  int    `xml:"Min,attr,omitempty"`
	MaxAttr  int    `xml:"Max,attr,omitempty"`
}
