package responses

// GetSupplierRoutesResponseType ...
type GetSupplierRoutesResponseType struct {
	MillisAttr           int                            `xml:"millis,attr,omitempty"`
	LoginId              string                         `xml:"LoginId"`
	RouteList            *GetSupplierRoutesRouteList    `xml:"RouteList"`
	LocationList         *GetSupplierRoutesLocationList `xml:"LocationList"`
	AllowedCountryRoutes *AllowedCountryRoutesType      `xml:"AllowedCountryRoutes"`
}

// GetSupplierRoutesRouteList ...
type GetSupplierRoutesRouteList struct {
	Route []*GetSupplierRoutesRouteType `xml:"Route"`
}

// GetSupplierRoutesRouteType ...
type GetSupplierRoutesRouteType struct {
	From *FromAndToType `xml:"From"`
	To   *FromAndToType `xml:"To"`
}

// FromAndToType ...
type FromAndToType struct {
	Type       string `xml:"Type"`
	Descriptor string `xml:"Descriptor"`
}

// GetSupplierRoutesLocationList ...
type GetSupplierRoutesLocationList struct {
	Location []*GetSupplierRoutesLocationType `xml:"Location"`
}

// GetSupplierRoutesLocationType ...
type GetSupplierRoutesLocationType struct {
	IATACode    string `xml:"IATACode"`
	Type        string `xml:"Type"`
	CountryCode string `xml:"CountryCode"`
}

// GetSupplierRoutesLocationTypeType ...
type GetSupplierRoutesLocationTypeType string

// AllowedCountryRoutesType ...
type AllowedCountryRoutesType struct {
	Route *AllowedCountryRoutesRouteType `xml:"Route"`
}

// AllowedCountryRoutesRouteType ...
type AllowedCountryRoutesRouteType struct {
	From string `xml:"From"`
	To   string `xml:"To"`
}
