package responses

// GetSuppliersRoutingParameterListResponseType ...
type GetSuppliersRoutingParameterListResponseType struct {
	MillisAttr   int                                   `xml:"millis,attr,omitempty"`
	LoginId      string                                `xml:"LoginId"`
	SupplierList *ListSupplierRoutingParameterListType `xml:"SupplierList"`
}

// ListSupplierRoutingParameterListType ...
type ListSupplierRoutingParameterListType struct {
	Supplier []*SupplierRoutingParameterListType `xml:"Supplier"`
}

// SupplierRoutingParameterListType ...
type SupplierRoutingParameterListType struct {
	Name                  string                           `xml:"Name"`
	Vendor                *RouterVendorType                `xml:"Vendor"`
	RequiredParameterList *RouterRequiredParameterListType `xml:"RequiredParameterList"`
}
