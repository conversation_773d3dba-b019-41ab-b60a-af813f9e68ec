package responses

// ProcessDetailsCarResponseType ...
type ProcessDetailsCarResponseType struct {
	MillisAttr          int                                            `xml:"millis,attr,omitempty"`
	LoginId             string                                         `xml:"LoginId"`
	RoutingId           *CommonId                                      `xml:"RoutingId"`
	Router              *CarRouter                                     `xml:"Router"`
	SupplierHandoffData *ProcessDetailsResponseSupplierHandoffDataType `xml:"SupplierHandoffData"`
	GeneralInfoItemList *GeneralInfoItemListType                       `xml:"GeneralInfoItemList"`
}
