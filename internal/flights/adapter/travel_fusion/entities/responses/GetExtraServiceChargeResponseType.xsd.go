package responses

// GetExtraServiceChargeResponseType ...
type GetExtraServiceChargeResponseType struct {
	MillisAttr          int                         `xml:"millis,attr,omitempty"`
	LoginId             string                      `xml:"LoginId"`
	Supplier            []*SupplierExtraServiceType `xml:"Supplier"`
	Disclaimer          string                      `xml:"Disclaimer"`
	GeneralInfoItemList *GeneralInfoItemListType    `xml:"GeneralInfoItemList"`
}

// SupplierExtraServiceType ...
type SupplierExtraServiceType struct {
	EcodeAttr   string                    `xml:"ecode,attr,omitempty"`
	EtextAttr   string                    `xml:"etext,attr,omitempty"`
	EdetailAttr string                    `xml:"edetail,attr,omitempty"`
	EdateAttr   string                    `xml:"edate,attr,omitempty"`
	Name        *CommonSupplierIdentifier `xml:"Name"`
	Service     []*ExtraServiceType       `xml:"Service"`
}

// ExtraServiceType ...
type ExtraServiceType struct {
	ServiceName                string                                     `xml:"ServiceName"`
	ChargeList                 *ExtraServiceChargeListType                `xml:"ChargeList"`
	RestrictionDescriptionList *ExtraServiceChargeRestrictionDescListType `xml:"RestrictionDescriptionList"`
}

// ExtraServiceNameType ...
type ExtraServiceNameType string

// ExtraServiceChargeListType ...
type ExtraServiceChargeListType struct {
	Charge []*ExtraServiceChargeType `xml:"Charge"`
}

// ExtraServiceChargeType ...
type ExtraServiceChargeType struct {
	RestrictionList       *ExtraServiceChargeRestrictionListType `xml:"RestrictionList"`
	Amount                float64                                `xml:"Amount"`
	Currency              *CommonTLA                             `xml:"Currency"`
	Percentage            float32                                `xml:"Percentage"`
	FixedAdditionalAmount float32                                `xml:"FixedAdditionalAmount"`
}

// ExtraServiceChargeRestrictionListType ...
type ExtraServiceChargeRestrictionListType struct {
	Restriction []*ExtraServiceChargeRestrictionType `xml:"Restriction"`
}

// ExtraServiceChargeRestrictionType ...
type ExtraServiceChargeRestrictionType struct {
	Type string `xml:"Type"`
	// Value *AnySimpleType `xml:"Value"`
}

// ExtraServiceChargeRestrictionTypeType ...
type ExtraServiceChargeRestrictionTypeType string

// ExtraServiceChargeRestrictionDescListType ...
type ExtraServiceChargeRestrictionDescListType struct {
	RestrictionDescription []*ExtraServiceChargeRestrictionDescType `xml:"RestrictionDescription"`
}

// ExtraServiceChargeRestrictionDescType ...
type ExtraServiceChargeRestrictionDescType struct {
	Restriction string `xml:"Restriction"`
	Description string `xml:"Description"`
}
