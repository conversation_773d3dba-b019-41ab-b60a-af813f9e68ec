package responses

// ListTrainStationsResponseType ...
type ListTrainStationsResponseType struct {
	MillisAttr  int                   `xml:"millis,attr,omitempty"`
	LoginId     string                `xml:"LoginId"`
	StationList *SupplierStationsList `xml:"StationList"`
}

// SupplierStationsList ...
type SupplierStationsList struct {
	Station []*SupplierStations `xml:"Station"`
}

// SupplierStations ...
type SupplierStations struct {
	DataAttr    string `xml:"Data,attr,omitempty"`
	CodeAttr    string `xml:"Code,attr,omitempty"`
	NameAttr    string `xml:"Name,attr,omitempty"`
	CountryAttr string `xml:"Country,attr,omitempty"`
	LatAttr     string `xml:"Lat,attr,omitempty"`
	LonAttr     string `xml:"Lon,attr,omitempty"`
}
