package responses

// ResolveLocationResponseType ...
type ResolveLocationResponseType struct {
	MillisAttr          int                                         `xml:"millis,attr,omitempty"`
	LoginId             string                                      `xml:"LoginId"`
	LocationList        *LocationResolutionResponseLocationListType `xml:"LocationList"`
	GeneralInfoItemList *GeneralInfoItemListType                    `xml:"GeneralInfoItemList"`
}

// LocationResolutionResponseLocationListType ...
type LocationResolutionResponseLocationListType struct {
	CountAttr int                                       `xml:"count,attr"`
	Location  []*LocationResolutionResponseLocationType `xml:"Location"`
}

// LocationResolutionResponseLocationType ...
type LocationResolutionResponseLocationType struct {
	RecommendedDisplayText  string                             `xml:"RecommendedDisplayText"`
	Id                      int                                `xml:"Id"`
	Type                    *CommonLocationResolutionPlaceType `xml:"Type"`
	Code                    *TrainCode                         `xml:"Code"`
	Name                    string                             `xml:"Name"`
	Admin2                  string                             `xml:"Admin2"`
	Admin1                  string                             `xml:"Admin1"`
	Country                 *CommonFullCountryType             `xml:"Country"`
	Admin2ShouldBeDisplayed bool                               `xml:"Admin2ShouldBeDisplayed"`
	Coordinate              *LRCoordinate                      `xml:"Coordinate"`
	AirportCity             *LRAirportCity                     `xml:"AirportCity"`
	TimeZone                *LRTimeZone                        `xml:"TimeZone"`
}

// LRTimeZone ...
type LRTimeZone struct {
	StandardOffset string `xml:"StandardOffset"`
}

// LRCoordinate ...
type LRCoordinate struct {
	Latitude  string `xml:"Latitude"`
	Longitude string `xml:"Longitude"`
}

// LRAirportCity ...
type LRAirportCity struct {
	Name string     `xml:"Name"`
	Code *CommonTLA `xml:"Code"`
	Id   int        `xml:"Id"`
}
