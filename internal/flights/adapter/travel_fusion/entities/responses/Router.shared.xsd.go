package responses

// CommonRouterList ...
type CommonRouterList struct {
	Router []*CommonRouter `xml:"Router"`
}

// CommonRouter ...
type CommonRouter struct {
	RequiredParameterList                *RouterRequiredParameterListType             `xml:"RequiredParameterList"`
	Supplier                             *CommonSupplierIdentifier                    `xml:"Supplier"`
	ProductType                          *CommonMode                                  `xml:"ProductType"`
	LogoNameSuffix                       string                                       `xml:"LogoNameSuffix"`
	Vendor                               *RouterVendorType                            `xml:"Vendor"`
	Complete                             bool                                         `xml:"Complete"`
	RequestedLocations                   *RouterRequestedLocationsType                `xml:"RequestedLocations"`
	AdditionalTripRequestedLocationsList *RouterAdditionalTripsRequestedLocationsType `xml:"AdditionalTripRequestedLocationsList"`
	GroupList                            *RouterGroupListType                         `xml:"GroupList"`
	AlternativeFares                     *RouterAlternativeGroupsType                 `xml:"AlternativeFares"`
	Features                             *RouterSupplierFeatureListType               `xml:"Features"`
	SupplierInfoList                     *RouterSupplierInfoListType                  `xml:"SupplierInfoList"`
}

// RouterRequiredParameterListType ...
type RouterRequiredParameterListType struct {
	RequiredParameter []*RouterRequiredParameterType `xml:"RequiredParameter"`
}

// RouterRequiredParameterType ...
type RouterRequiredParameterType struct {
	PerRoom             bool   `xml:"PerRoom"`
	Name                string `xml:"Name"`
	Type                string `xml:"Type"`
	DisplayText         string `xml:"DisplayText"`
	PerPassenger        bool   `xml:"PerPassenger"`
	IsOptional          string `xml:"IsOptional"`
	IsSometimesRequired string `xml:"IsSometimesRequired"`
}

// RouterRequestedLocationsType ...
type RouterRequestedLocationsType struct {
	Origin      *RequestedLocationsLocationType `xml:"Origin"`
	Destination *RequestedLocationsLocationType `xml:"Destination"`
}

// RouterAdditionalTripsRequestedLocationsType ...
type RouterAdditionalTripsRequestedLocationsType struct {
	AdditionalTripRequestedLocations *RouterRequestedLocationsType `xml:"AdditionalTripRequestedLocations"`
}

// RequestedLocationsLocationType ...
type RequestedLocationsLocationType struct {
	// Type *RequestedLocationsTypeType `xml:"Type"`
	Code string `xml:"Code"`
}

// RouterSupplierFeatureListType ...
type RouterSupplierFeatureListType struct {
	Feature []*RouterSupplierFeatureType `xml:"Feature"`
}

// RouterSupplierFeatureType ...
type RouterSupplierFeatureType struct {
	TypeAttr  string                             `xml:"Type,attr"`
	LabelAttr string                             `xml:"Label,attr,omitempty"`
	Option    []*RouterSupplierFeatureOptionType `xml:"Option"`
}

// RouterSupplierFeatureOptionType ...
type RouterSupplierFeatureOptionType struct {
	IdAttr         int                                    `xml:"Id,attr"`
	UsableAttr     string                                 `xml:"Usable,attr,omitempty"`
	CurrencyAttr   string                                 `xml:"Currency,attr,omitempty"`
	PercentageAttr float64                                `xml:"Percentage,attr,omitempty"`
	ValueAttr      float64                                `xml:"Value,attr,omitempty"`
	MinValueAttr   float64                                `xml:"MinValue,attr,omitempty"`
	MaxValueAttr   float64                                `xml:"MaxValue,attr,omitempty"`
	Condition      []*RouterSupplierFeatureConditionType  `xml:"Condition"`
	Dependency     []*RouterSupplierFeatureDependencyType `xml:"Dependency"`
}

// RouterSupplierFeatureConditionType ...
type RouterSupplierFeatureConditionType struct {
	TypeAttr  string `xml:"Type,attr"`
	ValueAttr string `xml:"Value,attr,omitempty"`
}

// RouterSupplierFeatureDependencyType ...
type RouterSupplierFeatureDependencyType struct {
	TypeAttr string `xml:"Type,attr"`
}

// RouterGroupListType ...
type RouterGroupListType struct {
	EcodeAttr   string             `xml:"ecode,attr,omitempty"`
	EtextAttr   string             `xml:"etext,attr,omitempty"`
	EdetailAttr string             `xml:"edetail,attr,omitempty"`
	EdateAttr   string             `xml:"edate,attr,omitempty"`
	Group       []*RouterGroupType `xml:"Group"`
}

// RouterAlternativeGroupsType ...
type RouterAlternativeGroupsType struct {
	GroupList *RouterGroupListType `xml:"GroupList"`
}

// MinimumCardCharge ...
type MinimumCardCharge struct {
	OutwardId *CommonId  `xml:"OutwardId"`
	ReturnId  *CommonId  `xml:"ReturnId"`
	Amount    float64    `xml:"Amount"`
	Currency  *CommonTLA `xml:"Currency"`
}

// MinimumCardChargeList ...
type MinimumCardChargeList struct {
	MinimumCardCharge []*MinimumCardCharge `xml:"MinimumCardCharge"`
}

// CardCharge ...
type CardCharge struct {
	OutwardId *CommonId  `xml:"OutwardId"`
	ReturnId  *CommonId  `xml:"ReturnId"`
	Type      string     `xml:"Type"`
	Amount    float64    `xml:"Amount"`
	Currency  *CommonTLA `xml:"Currency"`
}

// CardChargeList ...
type CardChargeList struct {
	CardCharge []*CardCharge `xml:"CardCharge"`
}

// CardTypeCharge ...
type CardTypeCharge struct {
	Type       string     `xml:"Type"`
	Amount     float64    `xml:"Amount"`
	Currency   *CommonTLA `xml:"Currency"`
	Percentage float64    `xml:"Percentage"`
	Matrix     string     `xml:"Matrix"`
}

// CardTypeChargeList ...
type CardTypeChargeList struct {
	CardTypeCharge []*CardTypeCharge `xml:"CardTypeCharge"`
}

// LuggageCharge ...
type LuggageCharge struct {
	CombinationList *CombinationListType `xml:"CombinationList"`
	ItemList        *LuggageFeesListType `xml:"ItemList"`
}

// LuggageChargeList ...
type LuggageChargeList struct {
	LuggageCharge []*LuggageCharge `xml:"LuggageCharge"`
}

// SpeedyBoardingCharge ...
type SpeedyBoardingCharge struct {
	CombinationList *CombinationListType        `xml:"CombinationList"`
	ItemList        *SpeedyBoardingFeesListType `xml:"ItemList"`
}

// SpeedyBoardingChargeList ...
type SpeedyBoardingChargeList struct {
	SpeedyBoardingCharge []*SpeedyBoardingCharge `xml:"SpeedyBoardingCharge"`
}

// CheckInCharge ...
type CheckInCharge struct {
	CombinationList *CombinationListType `xml:"CombinationList"`
	ItemList        *CheckInFeesListType `xml:"ItemList"`
}

// CheckInChargeList ...
type CheckInChargeList struct {
	CheckInCharge []*CheckInCharge `xml:"CheckInCharge"`
}

// RouterGroupType ...
type RouterGroupType struct {
	Id               *CommonId                   `xml:"Id"`
	OutwardList      *RouterOutwardListType      `xml:"OutwardList"`
	ReturnList       *RouterReturnListType       `xml:"ReturnList"`
	Price            *CommonPrice                `xml:"Price"`
	ChangePrice      *CommonPrice                `xml:"ChangePrice"`
	PriceWithLuggage *EnhancedPriceWithLuggage   `xml:"PriceWithLuggage"`
	RefundPrice      *CommonPrice                `xml:"RefundPrice"`
	SupplierInfoList *RouterSupplierInfoListType `xml:"SupplierInfoList"`
	// BookingOnHold            *BookingOnHoldType          `xml:"BookingOnHold"`
	SubMerchant              string                    `xml:"SubMerchant"`
	MinimumCardChargeList    *MinimumCardChargeList    `xml:"MinimumCardChargeList"`
	CardChargeList           *CardChargeList           `xml:"CardChargeList"`
	CardTypeChargeList       *CardTypeChargeList       `xml:"CardTypeChargeList"`
	LuggageChargeList        *LuggageChargeList        `xml:"LuggageChargeList"`
	SpeedyBoardingChargeList *SpeedyBoardingChargeList `xml:"SpeedyBoardingChargeList"`
	CheckInChargeList        *CheckInChargeList        `xml:"CheckInChargeList"`
}

// RouterOutwardListType ...
type RouterOutwardListType struct {
	Outward []*FlightLegData `xml:"Outward"`
}

// RouterReturnListType ...
type RouterReturnListType struct {
	Return []*FlightLegData `xml:"Return"`
}

// FlightLegData ...
type FlightLegData struct {
	Id                    *CommonId                        `xml:"Id"`
	Index                 int                              `xml:"Index"`
	CacheInfo             *CacheInfoType                   `xml:"CacheInfo"`
	Price                 *CommonPrice                     `xml:"Price"`
	ChangePrice           *CommonPrice                     `xml:"ChangePrice"`
	PriceWithLuggage      *EnhancedPriceWithLuggage        `xml:"PriceWithLuggage"`
	Duration              int                              `xml:"Duration"`
	CO2EmissionsTonnes    float64                          `xml:"CO2EmissionsTonnes"`
	SeatsRemaining        int                              `xml:"SeatsRemaining"`
	StopList              *RouterStopListType              `xml:"StopList"`
	SegmentList           *RouterSegmentListType           `xml:"SegmentList"`
	CompressedSegmentList *RouterCompressedSegmentListType `xml:"CompressedSegmentList"`
	Vendor                *RouterVendorType                `xml:"Vendor"`
	SupplierInfoList      *RouterSupplierInfoListType      `xml:"SupplierInfoList"`
	// BookingOnHold         *BookingOnHoldType               `xml:"BookingOnHold"`
}

// CacheInfoType ...
type CacheInfoType struct {
	CacheDataAgeSeconds int `xml:"CacheDataAgeSeconds"`
}

// RouterStopListType ...
type RouterStopListType struct {
	Stop []*RouterStopType `xml:"Stop"`
}

// RouterStopType ...
type RouterStopType struct {
	ArriveDate string               `xml:"ArriveDate"`
	DepartDate string               `xml:"DepartDate"`
	Duration   int                  `xml:"Duration"`
	Location   *SegmentLocationType `xml:"Location"`
}

// RouterSegmentListType ...
type RouterSegmentListType struct {
	Segment []*RouterSegmentType `xml:"Segment"`
}

// RouterCompressedSegmentListType ...
type RouterCompressedSegmentListType struct {
	CompressedSegment []string `xml:"CompressedSegment"`
}

// RouterSegmentType ...
type RouterSegmentType struct {
	IdAttr                 int                         `xml:"id,attr,omitempty"`
	Origin                 *SegmentLocationType        `xml:"Origin"`
	Destination            *SegmentLocationType        `xml:"Destination"`
	DepartDate             string                      `xml:"DepartDate"`
	ArriveDate             string                      `xml:"ArriveDate"`
	Duration               int                         `xml:"Duration"`
	TfOperator             *RouterTFOperator           `xml:"TfOperator"`
	TfVendingOperator      *RouterTFOperator           `xml:"TfVendingOperator"`
	Operator               *RouterOperatorFromSupplier `xml:"Operator"`
	VendingOperator        *RouterOperatorFromSupplier `xml:"VendingOperator"`
	FlightId               *RouterFlightIdType         `xml:"FlightId"`
	TravelClass            *RouterTravelClassType      `xml:"TravelClass"`
	AircraftType           *RouterAircraftType         `xml:"AircraftType"`
	SupplierInfoList       *RouterSupplierInfoListType `xml:"SupplierInfoList"`
	StopList               *RouterStopListType         `xml:"StopList"`
	SegmentMayEndWithAStop bool                        `xml:"SegmentMayEndWithAStop"`
}

// SegmentLocationType ...
type SegmentLocationType struct {
	Type                string `xml:"Type"`
	Code                string `xml:"Code"`
	Terminal            string `xml:"Terminal"`
	SupplierDisplayName string `xml:"SupplierDisplayName"`
	LocationDetailsId   string `xml:"LocationDetailsId"`
}

// SegmentLocationDetailsType ...
type SegmentLocationDetailsType struct {
	Name         string `xml:"Name"`
	Address      string `xml:"Address"`
	Phone        string `xml:"Phone"`
	Fax          string `xml:"Fax"`
	OpeningHours string `xml:"OpeningHours"`
}

// RouterFlightIdType ...
type RouterFlightIdType struct {
	Code   string `xml:"Code"`
	Number int    `xml:"Number"`
}

// RouterTravelClassType ...
type RouterTravelClassType struct {
	// TfClass                  *TfClassEnumerationType `xml:"TfClass"`
	SupplierClass            string `xml:"SupplierClass"`
	SupplierClassDescription string `xml:"SupplierClassDescription"`
	SupplierFareBasisCode    string `xml:"SupplierFareBasisCode"`
	SupplierRBDCode          string `xml:"SupplierRBDCode"`
}

// RouterAircraftType ...
type RouterAircraftType struct {
	AircraftName string `xml:"AircraftName"`
	AircraftCode string `xml:"AircraftCode"`
}

// RouterOperatorFromSupplier ...
type RouterOperatorFromSupplier struct {
	Name string `xml:"Name"`
	Code string `xml:"Code"`
}

// RouterTFOperator ...
type RouterTFOperator struct {
	Name string                      `xml:"Name"`
	Code *CommonExtendedOperatorCode `xml:"Code"`
}

// RouterVendorType ...
type RouterVendorType struct {
	Name string `xml:"Name"`
	Url  string `xml:"Url"`
}

// RouterSupplierInfoListType ...
type RouterSupplierInfoListType struct {
	SupplierInfo []*RouterSupplierInfoType `xml:"SupplierInfo"`
}

// RouterSupplierInfoType ...
type RouterSupplierInfoType struct {
	DisplayName string `xml:"DisplayName"`
	InfoType    string `xml:"InfoType"`
	Info        string `xml:"Info"`
}

// CombinationListType ...
type CombinationListType struct {
	Combination []*CombinationType `xml:"Combination"`
}

// CombinationType ...
type CombinationType struct {
	OutwardId *CommonId `xml:"OutwardId"`
	ReturnId  *CommonId `xml:"ReturnId"`
}
