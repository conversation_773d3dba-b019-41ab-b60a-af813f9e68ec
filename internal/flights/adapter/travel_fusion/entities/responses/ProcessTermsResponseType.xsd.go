package responses

// ProcessTermsResponseType ...
type ProcessTermsResponseType struct {
	ErrorAttr
	MillisAttr                          int                             `xml:"millis,attr,omitempty"`
	LoginId                             string                          `xml:"LoginId"`
	RoutingId                           *CommonId                       `xml:"RoutingId"`
	TFBookingReference                  string                          `xml:"TFBookingReference"`
	CardSubstitutionData                *CardSubstitutionDataType       `xml:"CardSubstitutionData"`
	Router                              *CommonRouter                   `xml:"Router"`
	SupplierResponseList                *CommonSupplierResponseListType `xml:"SupplierResponseList"`
	SupplierVisualAuthorisationImageURL string                          `xml:"SupplierVisualAuthorisationImageURL"`
	BookingProfile                      *BookingProfileType             `xml:"BookingProfile"`
	GeneralInfoItemList                 *GeneralInfoItemListType        `xml:"GeneralInfoItemList"`
}
