﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:complexType name="ListTrainStationsResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="StationList" type="SupplierStationsList" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>

	<xs:complexType name="SupplierStationsList">
		<xs:sequence>
			<xs:element name="Station" type="SupplierStations" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SupplierStations">
		<xs:attribute name="Data" type="xs:string" use="optional" />
		<xs:attribute name="Code" type="xs:string" use="optional" />
		<xs:attribute name="Name" type="xs:string" use="optional" />
		<xs:attribute name="Country" type="xs:string" use="optional" />
		<xs:attribute name="Lat" type="xs:string" use="optional" />
		<xs:attribute name="Lon" type="xs:string" use="optional" />
	</xs:complexType>
</xs:schema>