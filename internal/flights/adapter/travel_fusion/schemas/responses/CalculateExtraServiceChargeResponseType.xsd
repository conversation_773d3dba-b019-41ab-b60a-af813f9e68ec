﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CalculateExtraServiceChargeResponseType">
		<xs:sequence >
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Service" type="CalculateExtraServiceChargeServiceType" maxOccurs="unbounded"/>
			<xs:element name="TotalCharge" type="CalculateExtraServiceChargeTotalChargeType" minOccurs="0"/>			
			<xs:element name="Disclaimer" type="xs:string" />
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	
	<xs:complexType name="CalculateExtraServiceChargeServiceType">
		<xs:sequence>
			<xs:element name="ServiceName" type="ExtraServiceNameType"/>
			<xs:element name="Charge" type="CalculateExtraServiceChargeChargeType"/>
		</xs:sequence>	
	</xs:complexType>
	
	<xs:complexType name="CalculateExtraServiceChargeTotalChargeType">
		<xs:sequence>
			<xs:element name="Charge" type="CalculateExtraServiceChargeChargeType" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="CalculateExtraServiceChargeChargeType">
		<xs:sequence>
			<xs:element name="Amount" type="xs:decimal" minOccurs="0"/>
			<xs:element name="Currency" type="CommonTLA" minOccurs="0"/>			
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional" />
		<xs:attribute name="etext" type="xs:string" use="optional" />
		<xs:attribute name="edetail" type="xs:string" use="optional" />
		<xs:attribute name="edate" type="xs:string" use="optional" />
	</xs:complexType>

</xs:schema>