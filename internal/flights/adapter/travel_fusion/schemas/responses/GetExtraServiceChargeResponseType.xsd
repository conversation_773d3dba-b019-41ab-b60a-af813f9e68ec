﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetExtraServiceChargeResponseType">
		<xs:sequence >
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Supplier" type="SupplierExtraServiceType" maxOccurs="unbounded"/>
			<xs:element name="Disclaimer" type="xs:string" />
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	
	<xs:complexType name="SupplierExtraServiceType">
		<xs:sequence>
			<xs:element name="Name" type="CommonSupplierIdentifier" minOccurs="0"/>
			<xs:element name="Service" type="ExtraServiceType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional" />
		<xs:attribute name="etext" type="xs:string" use="optional" />
		<xs:attribute name="edetail" type="xs:string" use="optional" />
		<xs:attribute name="edate" type="xs:string" use="optional" />
	</xs:complexType>
 
	<xs:complexType name="ExtraServiceType">
		<xs:sequence>
			<xs:element name="ServiceName" type="ExtraServiceNameType" />
			<xs:element name="ChargeList" type="ExtraServiceChargeListType"/>
			<xs:element name="RestrictionDescriptionList" type="ExtraServiceChargeRestrictionDescListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:simpleType name="ExtraServiceNameType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Baggage" />
			<xs:enumeration value="Meal" />
			<xs:enumeration value="Insurance" />
			<xs:enumeration value="Speedy Boarding" />
			<xs:enumeration value="Online Check In" />
			<xs:enumeration value="Airport Check In" />
			<xs:enumeration value="Seat Assignment" />		
		    <xs:enumeration value="Card Charge" />
		    <xs:enumeration value="Equipment" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:complexType name="ExtraServiceChargeListType">
		<xs:sequence>
			<xs:element name="Charge" type="ExtraServiceChargeType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ExtraServiceChargeType">
		<xs:sequence>
			<xs:element name="RestrictionList" type="ExtraServiceChargeRestrictionListType" minOccurs="0" />
			<xs:element name="Amount" type="xs:decimal" minOccurs="0"/>
			<xs:element name="Currency" type="CommonTLA" minOccurs="0"/>
			<xs:element name="Percentage" type="xs:float" minOccurs="0"/>
			<xs:element name="FixedAdditionalAmount" type="xs:float" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ExtraServiceChargeRestrictionListType">
		<xs:sequence>
			<xs:element name="Restriction" type="ExtraServiceChargeRestrictionType"  maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="ExtraServiceChargeRestrictionType">
		<xs:sequence>
			<xs:element name="Type" type="ExtraServiceChargeRestrictionTypeType"/>
			<xs:element name="Value" type="xs:anySimpleType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ExtraServiceChargeRestrictionTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Charge Type" />
			<xs:enumeration value="Departure Time Before" />
			<xs:enumeration value="Departure Time After" />
			<xs:enumeration value="Area" />
			<xs:enumeration value="Booking Time Before" />
			<xs:enumeration value="Booking Time After" />
			<xs:enumeration value="Cabin" />
			<xs:enumeration value="Currency" />
			<xs:enumeration value="Operator" />
			<xs:enumeration value="Flight Number" />
			<xs:enumeration value="Number" />
			<xs:enumeration value="Weight" />
			<xs:enumeration value="Passenger Type" />
			<xs:enumeration value="Route" />
			<xs:enumeration value="Service Type" />
			<xs:enumeration value="Number Larger Than" />
			<xs:enumeration value="Number Larger Than Or Equals" />
			<xs:enumeration value="Number Less Than" />
			<xs:enumeration value="Number Less Than Or Equals" />
			<xs:enumeration value="Weight Larger Than" />
			<xs:enumeration value="Weight Larger Than Or Equals" />
			<xs:enumeration value="Weight Less Than" />
			<xs:enumeration value="Weight Less Than Or Equals" />
			<xs:enumeration value="Price Larger Than" />
			<xs:enumeration value="Price Larger Than Or Equals" />
			<xs:enumeration value="Price Less Than" />
			<xs:enumeration value="Price Less Than Or Equals" />
		</xs:restriction>
	</xs:simpleType>
	
	<xs:complexType name="ExtraServiceChargeRestrictionDescListType">
		<xs:sequence>
			<xs:element name="RestrictionDescription" type="ExtraServiceChargeRestrictionDescType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ExtraServiceChargeRestrictionDescType">
		<xs:sequence>
			<xs:element name="Restriction" type="xs:string"/>
			<xs:element name="Description" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	
</xs:schema>