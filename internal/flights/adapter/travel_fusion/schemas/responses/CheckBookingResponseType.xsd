﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CheckBookingResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="TFBookingReference" type="xs:string"/>
			<xs:element name="Status" type="CommonBookingStatusType"/>
			<!-- 'CardVerificationRequired' will be omitted or will have value 'false' if verification is not needed -->
			<xs:element name="CardVerificationRequired" type="xs:boolean" minOccurs="0"/>
			<xs:element name="SupplierReference" type="CommonString" minOccurs="0"/>
			<xs:element name="SupplierConfirmationDataItemList" type="SupplierConfirmationDataItemListType" minOccurs="0"/>
			<xs:element name="Router" type="CommonRouter" minOccurs="0"/>
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="SupplierConfirmationDataItemListType">
		<xs:sequence>
			<xs:element name="SupplierConfirmationDataItem" type="SupplierConfirmationDataItemType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierConfirmationDataItemType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Value" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CardSubstitutionDataType">
		<xs:sequence>
			<xs:element name="Fee" type="CommonPrice"/>
			<xs:element name="CardId" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
