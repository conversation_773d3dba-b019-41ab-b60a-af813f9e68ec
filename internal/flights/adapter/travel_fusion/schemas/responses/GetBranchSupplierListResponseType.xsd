﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:complexType name="GetBranchSupplierListResponseType">
		<xs:sequence>
			<xs:element name="BranchSupplierList" type="BranchSupplierListType" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>

	<xs:complexType name="BranchSupplierListType">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonString" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

</xs:schema>