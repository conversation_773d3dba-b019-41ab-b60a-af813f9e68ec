﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="ProcessTermsResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="TFBookingReference" type="xs:string"/>
			<!-- the card substitution fee will always be omitted if card sub is NOT taking place, and will always be present if it is taking place -->
			<!-- only the Amount and Currency elements will be set -->
			<xs:element name="CardSubstitutionData" type="CardSubstitutionDataType" minOccurs="0"/>
			<xs:element name="Router" type="CommonRouter" minOccurs="0"/>
			<xs:element name="SupplierResponseList" type="CommonSupplierResponseListType" minOccurs="0"/>
			<xs:element name="SupplierVisualAuthorisationImageURL" type="xs:string" minOccurs="0"/>
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
</xs:schema>
