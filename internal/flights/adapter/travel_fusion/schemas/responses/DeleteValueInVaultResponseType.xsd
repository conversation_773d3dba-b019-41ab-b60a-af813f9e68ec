﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="DeleteValueInVaultResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Succeeded" type="xs:boolean" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
</xs:schema>