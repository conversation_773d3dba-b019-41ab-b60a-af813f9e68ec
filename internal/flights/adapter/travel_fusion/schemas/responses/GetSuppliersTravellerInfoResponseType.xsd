﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">

	<xs:complexType name="GetSuppliersTravellerInfoResponseType">
		<xs:sequence>
			<xs:element name="SupplierList" type="SuppliersTravellerInfoSupplierList" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>

	<xs:complexType name="SuppliersTravellerInfoSupplierList">
		<xs:sequence>
			<xs:element name="Supplier" type="SuppliersTravellerInfoSupplier" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SuppliersTravellerInfoSupplier">
		<xs:sequence>
			<xs:element name="TravellerAgeList" type="SuppliersTravellerInfoSupplierTravellerAgeList" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="Name" type="xs:string" />
		<xs:attribute name="Mode" type="xs:string" />
	</xs:complexType>

	<xs:complexType name="SuppliersTravellerInfoSupplierTravellerAgeList">
		<xs:sequence>
			<xs:element name="TravellerAge" type="SuppliersTravellerInfoSupplierTravellerAge" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SuppliersTravellerInfoSupplierTravellerAge">
		<xs:attribute name="Type" type="xs:string" />
		<xs:attribute name="Min" type="xs:integer" /><!-- Inclusive -->
		<xs:attribute name="Max" type="xs:integer" use="optional" /><!-- Exclusive -->
	</xs:complexType>

</xs:schema>