﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StartBookingResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="TFBookingReference" type="xs:string"/>
				<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>		
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
</xs:schema>
