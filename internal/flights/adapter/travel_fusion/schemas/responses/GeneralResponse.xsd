﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="MiscellaneousResponses.xsd"/>
	<xs:include schemaLocation="../shared/CommonTypes.xsd"/>
	<xs:include schemaLocation="../shared/Price.xsd"/>
	<xs:include schemaLocation="../shared/Router.xsd"/>
	<xs:include schemaLocation="../shared/RoutingSummary.xsd"/>
	<xs:include schemaLocation="../shared/HotelRoutingSummary.xsd"/>
	<xs:include schemaLocation="../shared/CarRoutingSummary.xsd"/>
	<xs:include schemaLocation="../requests/GeneralRequestCommandList.xsd"/>
	<xs:include schemaLocation="../bookingprofile/BookingProfile.xsd"/>
	<xs:include schemaLocation="StartRoutingResponseType.xsd"/>
	<xs:include schemaLocation="CheckRoutingResponseType.xsd"/>
	<xs:include schemaLocation="ProcessDetailsResponseType.xsd"/>
	<xs:include schemaLocation="ProcessTermsResponseType.xsd"/>
	<xs:include schemaLocation="StartBookingResponseType.xsd"/>
	<xs:include schemaLocation="CheckBookingResponseType.xsd"/>
	<xs:include schemaLocation="ResolveLocationResponseType.xsd"/>
	<xs:include schemaLocation="GetStatsResponseType.xsd"/>
	<xs:include schemaLocation="StartRoutingHotelResponseType.xsd"/>
	<xs:include schemaLocation="CheckRoutingHotelResponseType.xsd"/>
	<xs:include schemaLocation="ProcessDetailsHotelResponseType.xsd"/>
	<xs:include schemaLocation="StartDetailsHotelResponseType.xsd"/>
	<xs:include schemaLocation="CheckDetailsHotelResponseType.xsd"/>
	<xs:include schemaLocation="StartTermsHotelResponseType.xsd"/>
	<xs:include schemaLocation="CheckTermsHotelResponseType.xsd"/>
	<xs:include schemaLocation="StartBookingHotelResponseType.xsd"/>
	<xs:include schemaLocation="CheckBookingHotelResponseType.xsd"/>
	<xs:include schemaLocation="StartRoutingCarResponseType.xsd"/>
	<xs:include schemaLocation="CheckRoutingCarResponseType.xsd"/>
	<xs:include schemaLocation="ProcessDetailsCarResponseType.xsd"/>
	<xs:include schemaLocation="StartRoutingPrePackagedResponseType.xsd"/>
	<xs:include schemaLocation="CheckRoutingPrePackagedResponseType.xsd"/>
	<xs:include schemaLocation="GetSupportedCardTypeListResponse.xsd"/>
	<xs:include schemaLocation="StoreValueInVaultResponseType.xsd"/>
	<xs:include schemaLocation="DeleteValueInVaultResponseType.xsd"/>
	<xs:include schemaLocation="GetSupplierRoutesResponseType.xsd"/>
	<xs:include schemaLocation="ListSupplierRoutesResponseType.xsd"/>
	<xs:include schemaLocation="ListTrainStationsResponseType.xsd"/>
	<xs:include schemaLocation="GetSuppliersTravellerInfoResponseType.xsd"/>
	<xs:include schemaLocation="GetExtraServiceChargeResponseType.xsd"/>
	<xs:include schemaLocation="CalculateExtraServiceChargeResponseType.xsd"/>
	<xs:include schemaLocation="GetBranchSupplierListResponseType.xsd"/>
	<xs:include schemaLocation="GetSuppliersRoutingParameterListResponseType.xsd"/>
	<!-- xs:include schemaLocation="GetExtraServiceChargesResponseType.xsd"/ -->
	<xs:element name="CommandList" type="GeneralResponseCommandListType"/>
	<xs:complexType name="GeneralResponseCommandListType">
		<xs:sequence>
			<xs:element name="Login" type="LoginResponseType" minOccurs="0"/>
			<xs:element name="StartRouting" type="StartRoutingResponseType" minOccurs="0"/>
			<xs:element name="StartManageBookingPlane" type="StartRoutingResponseType" minOccurs="0"/>
			<xs:element name="CheckRouting" type="CheckRoutingResponseType" minOccurs="0"/>
			<xs:element name="ProcessDetails" type="ProcessDetailsResponseType" minOccurs="0"/>
			<xs:element name="ProcessTerms" type="ProcessTermsResponseType" minOccurs="0"/>
			<xs:element name="StartBooking" type="StartBookingResponseType" minOccurs="0"/>
			<xs:element name="CheckBooking" type="CheckBookingResponseType" minOccurs="0"/>
			<xs:element name="GetBookingDetails" type="GetBookingDetailsResponseType" minOccurs="0"/>
			<xs:element name="ResolveLocation" type="ResolveLocationResponseType" minOccurs="0"/>
			<xs:element name="GetCurrencies" type="GetCurrenciesResponseType" minOccurs="0"/>
			<xs:element name="GetCountries" type="GetCountriesResponseType" minOccurs="0"/>
			<xs:element name="GetPrePackagedSupplierSummary" type="GetPrePackagedSupplierSummaryResponseType" minOccurs="0"/>
			<xs:element name="GetHotelDetails" type="GetHotelDetailsResponseType" minOccurs="0"/>
			<xs:element name="GetMultipleHotelDetails" type="GetMultipleHotelDetailsResponseType" minOccurs="0"/>
			<xs:element name="GetStats" type="GetStatsResponseType" minOccurs="0"/>
			<xs:element name="GetErrorCodes" type="GetErrorCodesResponseType" minOccurs="0"/>
			<xs:element name="StartRoutingHotel" type="StartRoutingHotelResponseType" minOccurs="0"/>
			<xs:element name="CheckRoutingHotel" type="CheckRoutingHotelResponseType" minOccurs="0"/>
			<xs:element name="ProcessDetailsHotel" type="ProcessDetailsHotelResponseType" minOccurs="0"/>
			<xs:element name="StartDetailsHotel" type="StartDetailsHotelResponseType" minOccurs="0"/>
			<xs:element name="CheckDetailsHotel" type="CheckDetailsHotelResponseType" minOccurs="0"/>
			<xs:element name="StartTermsHotel" type="StartTermsHotelResponseType" minOccurs="0"/>
			<xs:element name="CheckTermsHotel" type="CheckTermsHotelResponseType" minOccurs="0"/>
			<xs:element name="StartBookingHotel" type="StartBookingHotelResponseType" minOccurs="0"/>
			<xs:element name="CheckBookingHotel" type="CheckBookingHotelResponseType" minOccurs="0"/>
			<xs:element name="StartRoutingCar" type="StartRoutingCarResponseType" minOccurs="0"/>
			<xs:element name="CheckRoutingCar" type="CheckRoutingCarResponseType" minOccurs="0"/>
			<xs:element name="ProcessDetailsCar" type="ProcessDetailsCarResponseType" minOccurs="0"/>
			<xs:element name="StartRoutingPrePackaged" type="StartRoutingPrePackagedResponseType" minOccurs="0"/>
			<xs:element name="CheckRoutingPrePackaged" type="CheckRoutingPrePackagedResponseType" minOccurs="0"/>
			<xs:element name="LogPrePackagedReferral" type="LogPrePackagedReferralResponseType" minOccurs="0"/>
			<xs:element name="ProcessCardVerification" type="ProcessCardVerificationResponseType" minOccurs="0"/>
			<xs:element name="GetSupportedCardTypeList" type="GetSupportedCardTypeListResponse" minOccurs="0"/>
			<xs:element name="StoreValueInVault" type="StoreValueInVaultResponseType" minOccurs="0"/>
			<xs:element name="DeleteValueInVault" type="DeleteValueInVaultResponseType" minOccurs="0"/>
			<xs:element name="GetSupplierRoutes" type="GetSupplierRoutesResponseType" minOccurs="0"/>
			<xs:element name="ListSupplierRoutes" type="ListSupplierRoutesResponseType" minOccurs="0"/>
			<xs:element name="ListTrainStations" type="ListTrainStationsResponseType" minOccurs="0"/>
			<xs:element name="GetSuppliersTravellerInfo" type="GetSuppliersTravellerInfoResponseType" minOccurs="0" />
			<xs:element name="GetExtraServiceCharge" type="GetExtraServiceChargeResponseType" minOccurs="0"/>
			<xs:element name="CalculateExtraServiceCharge" type="CalculateExtraServiceChargeResponseType" minOccurs="0"/>
			<xs:element name="GetBranchSupplierList" type="GetBranchSupplierListResponseType" minOccurs="0"/>
			<xs:element name="GetSuppliersRoutingParameterList" type="GetSuppliersRoutingParameterListResponseType" minOccurs="0"/>
			<!-- xs:element name="GetExtraServiceCharges" type="GetExtraServiceChargesResponseType" minOccurs="0"/ -->
			<!--These are top level failure cases -->
			<xs:element name="DataValidationFailure" type="GeneralRequestCommandListType" minOccurs="0"/>
			<xs:element name="CommandExecutionFailure" type="CommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="XMLValidationError" type="XMLValidationErrorType" minOccurs="0"/>
			<!-- This is for miscellaneous technical info -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="XMLValidationErrorType">
		<xs:sequence>
			<xs:element name="RequestOrResponse" type="xs:string"/>
			<xs:element name="ReferenceNumber" type="xs:string"/>
			<xs:element name="ReferenceDate" type="xs:string"/>
			<xs:element name="ErrorDetails" type="xs:string" minOccurs="0"/>
			<xs:element name="Message" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommandExecutionFailureType">
		<xs:sequence>
			<xs:element name="Login" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartRouting" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartManageBookingPlane" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckRouting" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ProcessDetails" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ProcessTerms" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartBooking" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckBooking" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetBookingDetails" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ResolveLocation" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetCurrencies" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetCountries" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetStats" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetErrorCodes" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartRoutingHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckRoutingHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ProcessDetailsHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartDetailsHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckDetailsHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartTermsHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckTermsHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartBookingHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckBookingHotel" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartRoutingCar" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckRoutingCar" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ProcessDetailsCar" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetPrePackagedSupplierSummary" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="StartRoutingPrePackaged" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CheckRoutingPrePackaged" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetHotelDetails" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetMultipleHotelDetails" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="LogPrePackagedReferral" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ProcessCardVerification" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetSupplierRoutes" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ListSupplierRoutes" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="ListTrainStations" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetSuppliersTravellerInfo" type="SpecificCommandExecutionFailureType" minOccurs="0" />
			<xs:element name="StoreValueInVault" type="SpecificCommandExecutionFailureType" minOccurs="0" />
			<xs:element name="DeleteValueInVault" type="SpecificCommandExecutionFailureType" minOccurs="0" />
			<xs:element name="GetExtraServiceCharge" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="CalculateExtraServiceCharge" type="SpecificCommandExecutionFailureType" minOccurs="0"/>
			<xs:element name="GetBranchSupplierList" type="GetBranchSupplierListResponseType" minOccurs="0"/>
			<xs:element name="GetSuppliersRoutingParameterList" type="GetSuppliersRoutingParameterListResponseType" minOccurs="0"/>
			<!-- xs:element name="GetExtraServiceCharges" type="SpecificCommandExecutionFailureType" minOccurs="0"/ -->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SpecificCommandExecutionFailureType">
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="GeneralInfoItemListType">
		<xs:sequence>
			<xs:element name="GeneralInfoItem" type="GeneralInfoItemType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GeneralInfoItemType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Value" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
