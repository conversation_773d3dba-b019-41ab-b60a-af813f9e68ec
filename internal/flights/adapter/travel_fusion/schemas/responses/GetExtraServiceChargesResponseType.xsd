﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetExtraServiceChargesResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Disclaimer" type="xs:string" />
			<xs:element name="SupplierExtraServices" type="SupplierExtraServicesType" maxOccurs="unbounded" />
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
	<xs:complexType name="SupplierExtraServicesType">
		<xs:sequence>
			<xs:element name="SupplierName" type="CommonSupplierIdentifier" />
			<xs:element name="ExtraService" type="ExtraServiceType1" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional" />
		<xs:attribute name="etext" type="xs:string" use="optional" />
		<xs:attribute name="edetail" type="xs:string" use="optional" />
		<xs:attribute name="edate" type="xs:string" use="optional" />
	</xs:complexType>
	<xs:complexType name="ExtraServiceType1">
		<xs:sequence>
			<xs:element name="ServiceName" type="non-empty-string" />
			<xs:element name="ServiceChargeList" type="ExtraServiceChargeListType1" minOccurs="0" />
			<xs:group ref="ExtraServiceFreeTextGroup" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:group name="ExtraServiceFreeTextGroup">
		<xs:sequence>
			<xs:element name="FreeTextTitle" type="xs:string" minOccurs="0" />
			<xs:element name="FreeTextDescription" type="xs:string" />
		</xs:sequence>
	</xs:group>
	<xs:complexType name="ExtraServiceChargeListType1">
		<xs:sequence>
			<xs:element name="ServiceCharge" type="ExtraServiceChargeType1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ExtraServiceChargeType1">
		<xs:sequence>
			<xs:element name="ConditionList" type="ServiceChargeConditionListType" />
			<xs:element name="ChargeCalculationRule" type="ServiceChargeCalculationRuleType" minOccurs="0" />
			<xs:element name="ChargeList" type="ExtraServiceActualChargeListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ServiceChargeConditionListType">
		<xs:sequence>
			<xs:element name="Condition" type="ServiceChargeConditionType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ServiceChargeConditionType">
		<xs:sequence>
			<xs:element name="Item" type="non-empty-string" />
			<xs:element name="Unit" type="non-empty-string" minOccurs="0" />
			<xs:choice>
				<xs:element name="Equals" type="non-empty-string" />
				<xs:element name="NotEquals" type="non-empty-string" />
				<xs:element name="MoreThan" type="non-empty-string" />
				<xs:element name="MoreThanOrEquals" type="non-empty-string" />
				<xs:element name="LessThan" type="non-empty-string" />
				<xs:element name="LessThanOrEquals" type="non-empty-string" />
				<xs:group ref="ServiceChargeConditionOtherExprGroup" />
				<xs:element name="RouteList" type="ServiceChargeConditionRouteListType" />
			</xs:choice>
			<xs:group ref="ExtraServiceFreeTextGroup" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:group name="ServiceChargeConditionOtherExprGroup">
		<xs:sequence>
			<xs:element name="Operator" type="non-empty-string" />
			<xs:element name="Operand" type="non-empty-string" />
		</xs:sequence>
	</xs:group>
	<xs:complexType name="ServiceChargeConditionRouteListType">
		<xs:sequence>
			<xs:element name="Route" type="ServiceChargeConditionRouteType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ServiceChargeConditionRouteType">
		<xs:choice>
			<xs:choice minOccurs="1" maxOccurs="2">
				<xs:element name="From" type="non-empty-string" />
				<xs:element name="To" type="non-empty-string" />
				<xs:element name="In" type="non-empty-string" />
				<xs:element name="NotIn" type="non-empty-string" />
			</xs:choice>
			<xs:group ref="ServiceChargeConditionRouteOtherGroup" />
			<xs:group ref="ServiceChargeConditionRouteTypeGroup" />
		</xs:choice>
	</xs:complexType>
	<xs:group name="ServiceChargeConditionRouteOtherGroup">
		<xs:sequence>
			<xs:element name="RouteOperator" type="non-empty-string" />
			<xs:element name="Value" type="non-empty-string" />
		</xs:sequence>
	</xs:group>
	<xs:group name="ServiceChargeConditionRouteTypeGroup">
		<xs:sequence>
			<xs:element name="Type" type="non-empty-string" />
			<xs:element name="Value" type="non-empty-string" />
		</xs:sequence>
	</xs:group>
	<xs:complexType name="ServiceChargeCalculationRuleType">
		<xs:sequence>
			<xs:element name="Type" type="non-empty-string" />
			<xs:element name="Item" type="non-empty-string" />
			<xs:element name="Unit" type="non-empty-string" minOccurs="0"/>
			<xs:element name="Value" type="non-empty-string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ExtraServiceActualChargeListType">
		<xs:sequence>
			<xs:element name="Charge" type="ExtraServiceActualChargeType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ExtraServiceActualChargeType">
		<xs:sequence>
			<xs:element name="Currency" type="non-empty-string" />
			<xs:element name="Amount" type="non-empty-string" minOccurs="0"/>
			<xs:element name="Percentage" type="non-empty-string" minOccurs="0"/>
			<xs:element name="FixedAdditionalAmount" type="non-empty-string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
