﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CheckRoutingResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="Mode" type="CommonMode"/>

			<!-- Around 12th Jan 2010 Made these 3 items optional as we plan to remove them from the response XML, but we cannot remove them from the schema due to some JaxB users needing to regen their Java from the schemas -->
			<!-- In next full release, we'll fully remove them from the XML and this schema....-->
			<!-- xs:element name="TotalResults" type="xs:integer" minOccurs="0"/>
			<xs:element name="TotalRouters" type="xs:integer" minOccurs="0"/>
			<xs:element name="TotalRoutersComplete" type="xs:integer" minOccurs="0"/-->
			
			<xs:element name="RouterList" type="CommonRouterList" minOccurs="0"/>
			<!-- XMLPROD-66, sorted List and cheapest List are no logner used -->
			<!-- xs:element name="SortedList" type="CheckRoutingSortedListType" minOccurs="0"/>
			<xs:element name="CheapestList" type="CheckRoutingCheapestListType" minOccurs="0"/-->
			<xs:element name="DisallowedPairingList" type="CheckRoutingDisallowedPairingListType" minOccurs="0"/>
			<xs:element name="Summary" type="RoutingSummaryType"/>
				<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>		
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- XMLPROD-66, sorted List and cheapest List are no logner used -->
	<!-- 
	<xs:complexType name="CheckRoutingSortedListType">
		<xs:sequence>
			<xs:element name="Sorted" type="CheckRoutingIDPairType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	-->
	<xs:complexType name="CheckRoutingIDPairType">
		<xs:sequence>
			<xs:element name="OutwardId" type="CommonId"/>
			<xs:element name="ReturnId" type="CommonId" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- XMLPROD-66, sorted List and cheapest List are no logner used -->
	<!--
	<xs:complexType name="CheckRoutingCheapestListType">
		<xs:sequence>
			<xs:element name="Cheapest" type="CheckRoutingIDPairType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	-->
	<xs:complexType name="CheckRoutingDisallowedPairingListType">
		<xs:sequence>
			<xs:element name="DisallowedPairing" type="CheckRoutingIDPairType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
