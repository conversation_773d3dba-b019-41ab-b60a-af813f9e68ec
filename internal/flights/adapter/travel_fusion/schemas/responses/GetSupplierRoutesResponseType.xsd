﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
<xs:complexType name="GetSupplierRoutesResponseType">
<xs:sequence>
<xs:element name="LoginId" type="CommonLoginType"/>
<xs:element name="RouteList" type="GetSupplierRoutesRouteList" minOccurs="0"/>
<xs:element name="LocationList" type="GetSupplierRoutesLocationList" minOccurs="0"/>
<xs:element name="AllowedCountryRoutes" type="AllowedCountryRoutesType" minOccurs="0"/>
</xs:sequence>
<xs:attribute name="millis" type="xs:integer" use="optional"/>
</xs:complexType>

<xs:complexType name="GetSupplierRoutesRouteList">
<xs:sequence>
<xs:element name="Route" type="GetSupplierRoutesRouteType" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>

<xs:complexType name="GetSupplierRoutesRouteType">
<xs:sequence>
<xs:element name="From" type="FromAndToType" />
<xs:element name="To" type="FromAndToType" />
</xs:sequence>
</xs:complexType>

<xs:complexType name="FromAndToType">
<xs:sequence>
<xs:element name="Type" type="GetSupplierRoutesLocationTypeType" />
<xs:element name="Descriptor" type="CommonString" />
</xs:sequence>
</xs:complexType>

<xs:complexType name="GetSupplierRoutesLocationList">
<xs:sequence>
<xs:element name="Location" type="GetSupplierRoutesLocationType" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>

<xs:complexType name="GetSupplierRoutesLocationType">
<xs:sequence>
<xs:element name="IATACode" type="CommonString" />
<xs:element name="Type" type="GetSupplierRoutesLocationTypeType" />
<xs:element name="CountryCode" type="CommonString" />
</xs:sequence>
</xs:complexType>

<!-- added to support the new tfcitycode type -->
<!-- added to support the new tfstationcode type -->
<xs:simpleType name="GetSupplierRoutesLocationTypeType">
<xs:restriction base="xs:string">
<xs:enumeration value="trainstationcode"/>
<xs:enumeration value="tfstationcode"/>
<xs:enumeration value="airportcode"/>
<xs:enumeration value="citycode"/>
<xs:enumeration value="tfcitycode"/>
</xs:restriction>
</xs:simpleType>

<xs:complexType name="AllowedCountryRoutesType">
<xs:sequence>
<xs:element name="Route" type="AllowedCountryRoutesRouteType" />
</xs:sequence>
</xs:complexType>

<xs:complexType name="AllowedCountryRoutesRouteType">
<xs:sequence>
<xs:element name="From" type="xs:string" default="*"/>
<xs:element name="To" type="xs:string" default="*"/>
</xs:sequence>
</xs:complexType>

</xs:schema>