﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CheckRoutingCarResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="Mode" type="CommonMode"/>
			<xs:element name="RouterList" type="CarRouterList" minOccurs="0"/>
			<xs:element name="Summary" type="CarRoutingSummaryType"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="CarRouterList">
		<xs:sequence>
			<xs:element name="Router" type="CarRouter" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarRouter">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonSupplierIdentifier"/>
			<xs:element name="LogoNameSuffix" type="xs:string" minOccurs="0"/>
			<xs:element name="Vendor" type="RouterVendorType"/>
			<xs:element name="Complete" type="xs:boolean"/>
			<xs:element name="RequestedLocations" type="CarRouterRequestedLocationsType"/>
			<xs:element name="ResultList" type="CarResultListType" minOccurs="0"/>
			<xs:element name="LocationDetailsList" type="LocationDetailsListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationDetailsListType">
		<xs:sequence>
			<xs:element name="LocationDetails" type="LocationDetailsType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationDetailsType">
		<xs:sequence>
			<xs:element name="LocationDetailsId" type="xs:string"/>
			<xs:element name="LocationDetailsData" type="SegmentLocationDetailsType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarRouterRequestedLocationsType">
		<xs:sequence>
			<xs:element name="Pickup" type="HotelAndCarRouterRequestedLocationType"/>
			<xs:element name="Setdown" type="HotelAndCarRouterRequestedLocationType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarResultListType">
		<xs:sequence>
			<xs:element name="Result" type="CarResultType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="CarResultType">
		<xs:sequence>
			<xs:element name="Id" type="CommonId"/>
			<!-- The price is moved in the PickupList->Pickup -->
			<!--xs:element name="Price" type="CommonPrice"/-->
			<!-- The location elements may need to handle cases where suppliers do not return a TLA as per flight response locations-->
			<!--xs:element name="Pickup" type="SegmentLocationType"/-->
			<!--xs:element name="Setdown" type="SegmentLocationType"/-->
			<xs:element name="PickupList" type="CarPickupListType"/>
			<xs:element name="SetdownList" type="CarSetdownListType"/>
			<xs:element name="PickupDate" type="CommonString"/>
			<xs:element name="SetdownDate" type="CommonString"/>
			<!-- see http://www.acriss.org/reference/ for spec -->
			<xs:element name="AcrissVehicleType" type="AcrissVehicleTypeType"/>
			<!-- free text vehicle description from supplier's site. -->
			<xs:element name="SupplierVehicleType" type="xs:string"/>
			<xs:element name="Make" type="xs:string"/>
			<xs:element name="Model" type="xs:string"/>
			<xs:element name="DriverAge" type="CarAgeRange"/>
			<xs:element name="ImageList" type="HotelAndCarImageListType" minOccurs="0"/>
			<xs:element name="MiscellaneousInfoList" type="MiscellaneousCarInfoListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarPickupListType">
		<xs:sequence>
			<!--xs:element name="Pickup" type="SegmentLocationType" maxOccurs="unbounded"/-->
			<xs:element name="Pickup" type="CarPickupType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarPickupType">
		<xs:sequence>
			<xs:element name="Price" type="CommonPrice"/>
			<xs:element name="Location" type="SegmentLocationType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarSetdownListType">
		<xs:sequence>
			<!--xs:element name="Setdown" type="SegmentLocationType" maxOccurs="unbounded"/-->
			<xs:element name="Setdown" type="CarSetdownType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarSetdownType">
		<xs:sequence>
			<xs:element name="Location" type="SegmentLocationType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MiscellaneousCarInfoListType">
		<xs:sequence>
			<xs:element name="MiscellaneousInfo" type="MiscellaneousCarInfoType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MiscellaneousCarInfoType">
		<xs:sequence>
			<!-- The name will be one of the following: 'Number of doors', 'Luggage capacity', 'Air conditioning', or it may be any free text string which will be appriopriate for 
					display to an end user. This is to cover the common case where a supplier gives miscellaneous information that cannot be pre-specified. -->
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Value" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AcrissVehicleTypeType">
		<!-- see http://www.acriss.org/reference/ for spec -->
		<xs:sequence>
			<xs:element name="VehicleClass" type="xs:string"/>
			<xs:element name="VehicleType" type="xs:string"/>
			<xs:element name="Transmission" type="xs:string"/>
			<xs:element name="AirConditioning" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CarAgeRange">
		<xs:sequence>
			<xs:element name="MinAge" type="xs:integer"/>
			<xs:element name="MaxAge" type="xs:integer"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
