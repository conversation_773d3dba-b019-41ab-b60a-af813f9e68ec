﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CheckRoutingHotelResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="Mode" type="CommonMode"/>
			<xs:element name="RouterList" type="HotelRouterList" minOccurs="0"/>
			<xs:element name="Summary" type="HotelRoutingSummaryType"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="HotelRouterList">
		<xs:sequence>
			<xs:element name="Router" type="HotelRouter" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelRouter">
		<xs:sequence>
			<xs:element name="RequiredParameterList" type="RouterRequiredParameterListType" minOccurs="0"/>
			<xs:element name="Supplier" type="CommonSupplierIdentifier"/>
			<xs:element name="LogoNameSuffix" type="xs:string" minOccurs="0"/>
			<xs:element name="Vendor" type="RouterVendorType"/>
			<xs:element name="Complete" type="xs:boolean"/>
			<xs:element name="RequestedLocations" type="HotelRouterRequestedLocationsType"/>
			<xs:element name="AccommodationType" type="AccommodationTypesEnumeration" minOccurs="0"/>
			<xs:element name="ResultList" type="AccommodationResultListType" minOccurs="0"/>
			<xs:element name="SupplierInfoList" type="RouterSupplierInfoListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelRouterRequestedLocationsType">
		<xs:sequence>
			<xs:element name="Destination" type="HotelAndCarRouterRequestedLocationType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelAndCarRouterRequestedLocationType">
		<xs:sequence>
			<xs:element name="Type" type="HotelAndCarRouterRequestedLocationTypeEnumeration"/>
			<!-- this code will be a TLA in all cases except where type is supplierLocationIdentifier. Then it will be a string value (currently only occurs for hotels/ cars). -->
			<xs:element name="Code" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="HotelAndCarRouterRequestedLocationTypeEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="airportcode"/>
			<xs:enumeration value="citycode"/>
			<xs:enumeration value="hotelcode"/>
			<xs:enumeration value="supplierLocationIdentifier"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AccommodationResultListType">
		<xs:sequence>
			<xs:element name="Result" type="AccommodationResultType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<!-- This should be adjusted so that static data is removed. FINN can then call the GetHotelDetails Request. We are waiting for the hotel details database with static id to be complete. Note that prepackaged currently defines an accommodation type. Need to get rid of that and use this one. See PrePackagedAccommodationResultType -->
	<xs:complexType name="AccommodationResultType">
		<xs:sequence>
			<xs:element name="Id" type="CommonId"/>
			<!-- The HotelId identifies this hotel within the Travelfusion database of hotels. Occasionally the same hotel may be returned with different ids. This is in cases
							 where Travelfusion has not recognised them as the same hotel. Will be omitted if the hotel has not been identified by Travelfusion -->
			<xs:element name="HotelId" type="CommonId"/>
			<xs:element name="Stars" type="xs:decimal" minOccurs="0"/>
			<xs:element name="SpecialRanking" type="xs:string" minOccurs="0"/>
			<xs:element name="CheckinDate" type="CommonString"/>
			<xs:element name="CheckoutDate" type="CommonString"/>
			<xs:element name="Type" type="AccommodationTypesEnumeration"/>
			<xs:element name="Name" type="xs:string" minOccurs="0"/>
			<xs:element name="GeoCoordinates" type="CommonCoordinates" minOccurs="0"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="Address" type="xs:string" minOccurs="0"/>
			<xs:element name="AddressDetails" type="HotelAddressDetailsType" minOccurs="0"/>
			<xs:element name="NearestIATACityCode" type="xs:string" minOccurs="0"/>
			<xs:element name="GDSChain" type="HotelGDSChainType" minOccurs="0"/>
			<xs:element name="ImageList" type="HotelAndCarImageListType" minOccurs="0"/>
			<xs:element name="OptionList" type="AccommodationOptionsListType"/>
			<xs:element name="RoomExtraList" type="RoomExtraListType" minOccurs="0"/>
			<xs:element name="FacilityList" type="AccommodationFacilitiesListType" minOccurs="0"/>
			<xs:element name="MiscellaneousInfoList" type="MiscellaneousAccommodationInfoListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AccommodationOptionsListType">
		<xs:sequence>
			<xs:element name="Option" type="AccommodationOptionType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AccommodationOptionType">
		<xs:sequence>
			<xs:element name="Id" type="CommonId"/>
			<!-- Price is optional for prepackaged results only -->
			<xs:element name="Price" type="CommonPrice" minOccurs="0"/>
			<xs:element name="MiscellaneousInfoList" type="MiscellaneousAccommodationInfoListType" minOccurs="0"/>
			<xs:element name="Terms" type="GenericTermType" minOccurs="0"/>
			<xs:element name="ImageList" type="HotelAndCarImageListType" minOccurs="0"/>
			<xs:element name="HotelData" type="HotelAndHostelDataType" minOccurs="0"/>
			<xs:element name="ApartmentData" type="ApartmentDataType" minOccurs="0"/>
			<xs:element name="HouseData" type="HouseDataType" minOccurs="0"/>
			<xs:element name="HostelData" type="HotelAndHostelDataType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelAndHostelDataType">
		<xs:sequence>
			<xs:element name="RoomList" type="HotelRoomListType"/>
			<xs:element name="MealList" type="HotelMealListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ApartmentDataType">
		<xs:sequence>
			<xs:element name="MealList" type="HotelMealListType" minOccurs="0"/>
			<xs:element name="NumberOfBeds" type="xs:integer" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HouseDataType">
		<xs:sequence>
			<xs:element name="NumberOfBeds" type="xs:integer" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MiscellaneousAccommodationInfoListType">
		<xs:sequence>
			<xs:element name="MiscellaneousInfo" type="MiscellaneousAccommodationInfoType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="MiscellaneousAccommodationInfoType">
		<xs:sequence>
			<!-- The name will be one of the following: , or it may be any free text string which will be appropriate for 
					display to an end user. This is to cover the common case where a supplier gives miscellaneous information that cannot be pre-specified. -->
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Value" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
    <!-- Adding general terms element -->
	<xs:complexType name="GenericTermType">
	   <xs:sequence>
            <xs:element name="CancellationTerm" type="CancellationTermType"/>
        </xs:sequence>
	</xs:complexType>
    <xs:complexType name="CancellationTermType">
        <xs:sequence>
            <xs:element name="Cancellable" type="xs:string" minOccurs="0"/>
            <xs:element name="Refundable" type="xs:string" minOccurs="0"/>
            <xs:element name="CancellationPolicyList" type="CancellationsPolicyListType"/>
        </xs:sequence>
    </xs:complexType>
	
	<xs:complexType name="CancellationsPolicyListType">
        <xs:sequence>
            <xs:element name="CancellationPolicy" type="CancellationPolicyType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="CancellationPolicyType">
        <xs:sequence>
            <xs:element name="Code"  type="xs:string"/>
            <xs:element name="Fee"  type="CancellationFeeType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CancellationFeeType">
        <xs:sequence>
            <xs:element name="Type"  type="xs:string"/>
            <xs:element name="Value"  type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <!-- Finish  general terms element -->
	<xs:complexType name="HotelAddressDetailsType">
		<xs:sequence>
			<xs:element name="PostalCode" type="xs:string" />
			<xs:element name="Country" type="xs:string" />
			<xs:element name="Province" type="xs:string" />
			<xs:element name="City" type="xs:string" />
			<xs:element name="Region" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelGDSChainType">
		<xs:sequence>
			<xs:element name="GDSChainCode" type="xs:string" />
			<xs:element name="GDSChainCodeName" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelAndCarImageListType">
		<xs:sequence>
			<xs:element name="Image" type="HotelAndCarImageType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelAndCarImageType">
		<xs:sequence>
			<xs:element name="Width" type="xs:integer"/>
			<xs:element name="Height" type="xs:integer"/>
			<xs:element name="Description" type="xs:string"/>
			<xs:element name="ThumbUrl" type="xs:string"/>
			<xs:element name="Url" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelRoomListType">
		<xs:sequence>
			<xs:element name="Room" type="HotelRoomType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelRoomType">
		<xs:sequence>
			<xs:element name="Id" type="xs:integer" minOccurs="0"/>
			<xs:element name="Price" type="CommonPrice" minOccurs="0"/>
			<xs:element name="Type" type="RoomTypesEnumeration"/>
			<xs:element name="TfType" type="TfRoomTypesEnumeration" minOccurs="0"/>
			<!-- MaleFemaleOnly will have value 'Male' if the room only supports male residents. Will have value 'Female' if only female are allowed. Will be omitted if there is no restriction -->
			<xs:element name="MaleFemaleOnly" type="xs:string" minOccurs="0"/>
			<xs:element name="TravellerList" type="StartRoutingTravellerList" minOccurs="0"/>
			<xs:element name="MiscellaneousInfoList" type="MiscellaneousAccommodationInfoListType" minOccurs="0"/>
			<xs:element name="NumberOfBeds" type="xs:integer" minOccurs="0"/>
			<xs:element name="NumberOfExtraBeds" type="xs:integer" minOccurs="0"/>
			<xs:element name="RoomExtraList" type="RoomExtraListType" minOccurs="0"/>
			<xs:element name="Terms" type="GenericTermType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
    <!-- 	TF Room Type -->
	<xs:simpleType name="TfRoomTypesEnumeration">
	   <xs:restriction base="xs:string">
	       <xs:enumeration value="Unknown" />
	       <xs:enumeration value="Single" />
		   <xs:enumeration value="Double" />
	       <xs:enumeration value="Twin" />
	   	   <xs:enumeration value="Triple" />
	       <xs:enumeration value="Quad" />
		   <xs:enumeration value="Family" />
		   <xs:enumeration value="Shared" />
		   <xs:enumeration value="Private" />
		   <xs:enumeration value="Dorm" /> 
		   <xs:enumeration value="Apartment" />
	   </xs:restriction>
	</xs:simpleType>
	
	<xs:complexType name="HotelMealListType">
		<xs:sequence>
			<xs:element name="Meal" type="HotelMealType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelMealType">
		<xs:sequence>
			<xs:element name="Type" type="HotelMealEnumeration"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="HotelMealEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Breakfast"/>
			<xs:enumeration value="Lunch"/>
			<xs:enumeration value="Dinner"/>
			<xs:enumeration value="Half Board"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AccommodationFacilitiesListType">
		<xs:sequence>
			<xs:element name="Facility" type="AccommodationFacilityType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<!-- this will be a list of facilites - e.g. Internet, Restaurant etc. If a particualr facility does not appear in the list, then information is not available about that facility.
				 If the facility is 'partlyavailable', further details can be found in the 'Details' field -->
	</xs:complexType>
	<xs:complexType name="AccommodationFacilityType">
		<xs:sequence>
			<xs:element name="Type" type="AccommodationFacilitiesTypeType"/>
			<xs:element name="Availability" type="AccommodationFacilitiesAvailabilityType"/>
			<xs:element name="Details" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="AccommodationFacilitiesTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Internet"/>
			<xs:enumeration value="Wheel chair"/>
			<xs:enumeration value="Fitness facility"/>
			<xs:enumeration value="Bar"/>
			<xs:enumeration value="Pool"/>
			<xs:enumeration value="Pets permitted"/>
			<xs:enumeration value="Air conditioning"/>
			<xs:enumeration value="Handicap accessible"/>
			<xs:enumeration value="Restaurant"/>
			<xs:enumeration value="Children pool"/>
			<xs:enumeration value="Lift"/>
			<xs:enumeration value="Child care"/>
			<xs:enumeration value="Child activity"/>
			<xs:enumeration value="Meeting facility"/>
			<xs:enumeration value="Banquet facility"/>
			<xs:enumeration value="Room service"/>
			<xs:enumeration value="Kitchen"/>
			<xs:enumeration value="Parking"/>
			<xs:enumeration value="Breakfast"/>
			<xs:enumeration value="Cancellation policy"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AccommodationFacilitiesAvailabilityType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="available"/>
			<xs:enumeration value="notavailable"/>
			<xs:enumeration value="partlyavailable"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="RoomExtraListType">
		<xs:sequence>
			<xs:element name="RoomExtra" type="RoomExtraType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoomExtraType">
		<xs:sequence>
			<xs:element name="Id" type="CommonId" minOccurs="0"/>
			<xs:element name="Type" type="RoomExtraTypeType"/>			
			<xs:element name="PriceList" type="RoomExtraPriceListType" minOccurs="0"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoomExtraPriceListType">
		<xs:sequence>
			<xs:element name="Price" type="CommonPrice" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="RoomExtraTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Breakfast"/>			
			<xs:enumeration value="Bar Cafe Breakfast"/>
			<xs:enumeration value="Continental Breakfast"/>
			<xs:enumeration value="Dinner"/>
			<xs:enumeration value="Bar Cafe Dinner"/>
			<xs:enumeration value="Early Check In"/>
			<xs:enumeration value="Late Check Out"/>
			<xs:enumeration value="WiFi Access"/>			
			<xs:enumeration value="SMS Confirmation"/>
			<xs:enumeration value="Domestic Pet"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>