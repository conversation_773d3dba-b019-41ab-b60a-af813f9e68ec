﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetSuppliersRoutingParameterListResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="SupplierList" type="ListSupplierRoutingParameterListType" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
	<xs:complexType name="ListSupplierRoutingParameterListType">
		<xs:sequence>
			<xs:element name="Supplier" type="SupplierRoutingParameterListType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierRoutingParameterListType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="1" maxOccurs="1" />
			<xs:element name="Vendor" type="RouterVendorType" minOccurs="1" maxOccurs="1" />
			<xs:element name="RequiredParameterList" type="RouterRequiredParameterListType" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
</xs:schema>
