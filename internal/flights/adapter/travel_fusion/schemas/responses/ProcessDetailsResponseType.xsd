﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="ProcessDetailsResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="Router" type="CommonRouter" minOccurs="0"/>
			<xs:element name="SupplierHandoffData" type="ProcessDetailsResponseSupplierHandoffDataType" minOccurs="0"/>
			<xs:element name="ManagedTravellerList" type="ManagedTravellerListType" minOccurs="0" maxOccurs="1"/>
			<xs:element name="SupportedCardList" type="SupportedCardListType" minOccurs="0"/>
			<xs:element name="FeesList" type="FeesListType" minOccurs="0" />
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="ProcessDetailsResponseSupplierHandoffDataType">
		<xs:sequence>
			<xs:element name="Method" type="DetailsResponseHTTPMethod"/>
			<xs:element name="Url" type="xs:string"/>
			<xs:element name="ParameterList" type="DetailsResponseHTTPParameterList" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="DetailsResponseHTTPMethod">
		<xs:restriction base="xs:string">
			<xs:enumeration value="POST"/>
			<xs:enumeration value="GET"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="DetailsResponseHTTPParameterList">
		<xs:sequence>
			<xs:element name="Parameter" type="DetailsResponseHTTPParameter" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DetailsResponseHTTPParameter">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Value" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FeesListType">
		<xs:sequence>
			<xs:element name="Luggage" type="LuggageFeesInfoType" minOccurs="0" maxOccurs="1" />
			<xs:element name="SpeedyBoarding" type="SpeedyBoardingFeesInfoType" minOccurs="0" maxOccurs="1" />
			<xs:element name="CheckIn" type="CheckInFeesInfoType" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LuggageFeesInfoType">
		<xs:sequence>
			<xs:element name="ItemList" type="LuggageFeesListType" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SpeedyBoardingFeesInfoType">
		<xs:sequence>
			<xs:element name="ItemList" type="SpeedyBoardingFeesListType" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
		<xs:complexType name="CheckInFeesInfoType">
		<xs:sequence>
			<xs:element name="ItemList" type="CheckInFeesListType" minOccurs="0" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupportedCardListType">
		<xs:sequence>
			<xs:element name="SupportedCard" type="SupportedCardType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupportedCardType">
		<xs:sequence>
			<xs:element name="CardType" type="CommonString" minOccurs="0" maxOccurs="unbounded" />
			<!-- CardCharge and CardChargePercentage may both be omitted if this data is not available from the supplier -->
			<xs:element name="CardCharge" type="SupportedCardChargeType" minOccurs="0" />
			<xs:element name="CardChargePercentage" type="xs:decimal" minOccurs="0" />
			<xs:element name="CardMinimumCharge" type="CommonPrice" minOccurs="0" />
			<xs:element name="RoundingPolicy" type="SupportedCardRoundingPolicyType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupportedCardRoundingPolicyType">
		<xs:sequence>
			<xs:element name="Scale" type="xs:integer" />
			<xs:element name="Mode" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="UP" />
						<xs:enumeration value="DOWN" />
						<xs:enumeration value="CEILING" />
						<xs:enumeration value="FLOOR" />
						<xs:enumeration value="HALF_UP" />
						<xs:enumeration value="HALF_DOWN" />
						<xs:enumeration value="HALF_EVEN" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupportedCardChargeType">
		<xs:sequence>
			<xs:element name="Charge" type="CommonPrice" minOccurs="0" />
			<xs:element name="ChargeIsPerPassengerLeg" type="xs:boolean" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ManagedTravellerListType">
		<xs:sequence>
			<xs:element name="ManagedTraveller" type="ManagedTravellerType" minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ManagedTravellerType">
		<xs:sequence>
			<xs:element name="Name" type="CommonName" minOccurs="1" maxOccurs="1" />
			<xs:element name="TravellerType" type="ManagedTravellerTypeRestriction" minOccurs="1" />
			<xs:element name="Age" type="CommonAge" minOccurs="0" maxOccurs="1" />
			<xs:element name="SupplierTravellerInfoList" type="SupplierTravellerInfoListType" minOccurs="0" />
			<xs:element name="RequiredParameterList" type="RouterRequiredParameterListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierTravellerInfoListType">
		<xs:sequence>
			<xs:element name="SupplierTravellerInfo" type="RouterSupplierInfoType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="ManagedTravellerTypeRestriction" final="restriction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Senior" />
			<xs:enumeration value="Adult" />
			<xs:enumeration value="Youth" />
			<xs:enumeration value="Student" />
			<xs:enumeration value="Child" />
			<xs:enumeration value="Infant" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>