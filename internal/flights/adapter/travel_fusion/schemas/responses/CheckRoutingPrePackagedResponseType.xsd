﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CheckRoutingPrePackagedResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="Mode" type="CommonMode"/>
			<xs:element name="RouterList" type="PrePackagedRouterList" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PrePackagedRouterList">
		<xs:sequence>
			<xs:element name="Router" type="PrePackagedRouter" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedRouter">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonSupplierIdentifier"/>
			<xs:element name="Vendor" type="RouterVendorType"/>
			<xs:element name="Complete" type="xs:boolean"/>
			<xs:element name="ResultList" type="PrePackagedResultListType" minOccurs="0"/>
			<xs:element name="FlightResultList" type="PrePackagedFlightResultListType" minOccurs="0"/>
			<xs:element name="HotelResultList" type="PrePackagedHotelResultListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedResultListType">
		<xs:sequence>
			<xs:element name="Result" type="PrePackagedResultType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PrePackagedResultType">
		<xs:sequence>
			<xs:element name="Id" type="CommonId" minOccurs="0"/>
			<xs:element name="FlightResultId" type="CommonId" minOccurs="0"/>
			<xs:element name="HotelResultId" type="CommonId" minOccurs="0"/>
			<xs:element name="Price" type="CommonPrice"/>
			<xs:element name="RoomType" type="RoomTypesEnumeration"/>
			<!-- these two elements refer to the number of spaces available for people, not actually the number of beds -->
			<xs:element name="NumberOfBeds" type="xs:integer" minOccurs="0"/>
			<xs:element name="NumberOfExtraBeds" type="xs:integer" minOccurs="0"/>
			<xs:element name="SupplierHandoffData" type="ProcessDetailsResponseSupplierHandoffDataType"/>
			<xs:element name="NumberOfNights" type="xs:integer"/>
			<xs:element name="Destination" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedFlightResultListType">
		<xs:sequence>
			<!-- THE RouterGroup in pre packaged will only have one outward and one return option and no prices will be supplied (the price is for the package - see one level up -->
			<xs:element name="FlightResult" type="RouterGroupType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedHotelResultListType">
		<xs:sequence>
			<xs:element name="HotelResult" type="PrePackagedAccommodationResultType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- This should reuse the AccommodationResultType in CheckRoutingHotelResponseType, but we are waiting for the hotel details database with static id to be complete -->
	<xs:complexType name="PrePackagedAccommodationResultType">
		<xs:sequence>
			<!-- this is the temporary id as referenced above in the PrePackagedResultType -->
			<xs:element name="Id" type="CommonId"/>
			<!-- The HotelId identifies this hotel within the Travelfusion database of hotels. Occasionally the same hotel may be returned with different ids. This is in cases
							 where Travelfusion has not recognised them as the same hotel. Will be omitted if the hotel has not been identified by Travelfusion BECAUSE some prepackaged offers have no hotel. Mark is confirming with Finn and if they do not want these we'll need to make this compulsory again BUT stop sending these results from server -->
			<xs:element name="HotelId" type="CommonId" minOccurs="0"/>
			<xs:element name="CheckinDate" type="CommonString"/>
			<xs:element name="CheckoutDate" type="CommonString"/>
			<!-- OptionList is optional only for pre-packaged -->
			<xs:element name="OptionList" type="AccommodationOptionsListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
