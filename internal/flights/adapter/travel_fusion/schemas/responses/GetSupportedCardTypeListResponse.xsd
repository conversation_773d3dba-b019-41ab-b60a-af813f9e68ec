﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetSupportedCardTypeListResponse">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="SupportedCardTypeList" type="SupportedCardTypeList" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="SupportedCardTypeList">
		<xs:sequence>
			<xs:element name="SupportedCardType" type="xs:string" maxOccurs="unbounded" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
