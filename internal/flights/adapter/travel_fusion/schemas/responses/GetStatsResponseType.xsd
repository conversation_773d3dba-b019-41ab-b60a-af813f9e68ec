﻿<?xml version="1.0" encoding="UTF-8"?>
<!--Need to solve problem of period data being different structure for different request types-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetStatsResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="StatsData" type="StatsDataType"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="StatsDataType">
		<xs:sequence>
			<xs:element name="StatsPeriodList" type="StatsPeriodListType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StatsPeriodListType">
		<xs:sequence>
			<xs:element name="StatsPeriod" type="StatsPeriodType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StatsPeriodType">
		<xs:sequence>
			<xs:element name="StartDate" type="xs:string"/>
			<xs:element name="EndDate" type="xs:string"/>
			<xs:element name="referralsPeriodData" type="ReferralsPeriodDataType" minOccurs="0"/>
			<xs:element name="suppliersearchesPeriodData" type="SupplierSearchesPeriodDataType" minOccurs="0"/>
			<xs:element name="usersearchsummaryPeriodData" type="UserSearchSummaryPeriodDataType" minOccurs="0"/>
			<xs:element name="simpleusersearchsummaryPeriodData" type="SimpleUserSearchSummaryPeriodDataType" minOccurs="0"/>
			<xs:element name="commandsummaryPeriodData" type="CommandSummaryPeriodDataType" minOccurs="0"/>
			<xs:element name="clearstatsdataPeriodData" type="ClearStatsDataPeriodDataType" minOccurs="0"/>
			<xs:element name="CommandSummaryPeriodData" type="NewCommandSummaryPeriodDataType" minOccurs="0"/>
			<xs:element name="ReferralSummaryPeriodData" type="ReferralSummaryPeriodDataType" minOccurs="0"/>
			<xs:element name="UserSearchSummaryPeriodData" type="NewUserSearchSummaryPeriodDataType" minOccurs="0"/>
			<xs:element name="SupplierSearchSummaryPeriodData" type="SupplierSearchSummaryPeriodDataType" minOccurs="0"/>
			<xs:element name="ConvertStatsSummaryPeriodData" type="ConvertStatsSummaryPeriodDataType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--*********************** -->
	<!--*********************** -->
	<!--REFERRALS -->
	<!--*********************** -->
	<!--*********************** -->
	<xs:complexType name="ReferralsPeriodDataType">
		<xs:sequence>
			<xs:element name="SupplierList" type="SimpleHashtableOutputType"/>
			<xs:element name="EndUserList" type="SimpleHashtableOutputType"/>
			<!--xs:element name="EndUserSupplierList" type="ComplexHashtableOutputType"/>
			<xs:element name="SupplierEndUserList" type="ComplexHashtableOutputType"/-->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SimpleHashtableOutputType">
		<xs:sequence>
			<xs:element name="Entry" type="SimpleCountHashtableOutputEntryType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SimpleCountHashtableOutputEntryType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Count" type="xs:integer"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ComplexHashtableOutputType">
		<xs:sequence>
			<xs:element name="Entry" type="ComplexHashtableOutputEntryType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ComplexHashtableOutputEntryType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Breakdown" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
	<!--xs:complexType name="SupplierEndUserListType">
		<xs:sequence>
			<xs:element name="SupplierBreakdown" type="SupplierBreakdownType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierBreakdownType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="UserBreakdown" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType-->
	<!--*********************** -->
	<!--*********************** -->
	<!--SUPPLIERSEARCHES -->
	<!--*********************** -->
	<!--*********************** -->
	<xs:complexType name="SupplierSearchesPeriodDataType">
		<xs:sequence>
			<xs:element name="TOTALSEARCHES" type="xs:integer"/>
			<xs:element name="CACHEHITSPERCENT" type="xs:decimal"/>
			<xs:element name="CACHEMISSESPERCENT" type="xs:decimal"/>
			<xs:element name="CACHEUNKNOWNPERCENT" type="xs:decimal"/>
			<xs:element name="SupplierSearchSummaryList" type="SupplierSearchSummaryListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierSearchSummaryListType">
		<xs:sequence>
			<xs:element name="SupplierSearchSummary" type="SupplierSearchSummaryType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierSearchSummaryType">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonSupplierIdentifier"/>
			<xs:element name="TotalSearches" type="xs:integer"/>
			<xs:element name="CacheHitsPercent" type="xs:decimal"/>
			<xs:element name="CacheMissesPercent" type="xs:decimal"/>
			<xs:element name="CacheUnknownPercent" type="xs:decimal"/>
			<xs:element name="UncachedResults" type="UncachedResultsType"/>
			<xs:element name="CachedResults" type="CachedResultsType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UncachedResultsType">
		<xs:sequence>
			<xs:element name="TotalUncachedSearches" type="xs:integer"/>
			<xs:element name="AverageConnectionTimeSeconds" type="xs:decimal"/>
			<xs:element name="SearchBreakdown" type="UncachedSearchBreakdown"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UncachedSearchBreakdown">
		<xs:sequence>
			<xs:element name="TotalCompleteSlowSearchesPercent" type="xs:decimal"/>
			<xs:element name="TotalIncompleteSearchesPercent" type="xs:decimal"/>
			<xs:element name="TotalTooFastSearchesPercent" type="xs:decimal"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CachedResultsType">
		<xs:sequence>
			<xs:element name="TotalCachedSearches" type="xs:integer"/>
			<xs:element name="AverageConnectionTimeSeconds" type="xs:decimal"/>
			<xs:element name="Incomplete" type="xs:integer"/>
		</xs:sequence>
	</xs:complexType>
	<!--*********************** -->
	<!--*********************** -->
	<!--USERSEARCHSUMMARY -->
	<!--*********************** -->
	<!--*********************** -->
	<xs:complexType name="UserSearchSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="UserSearchesList" type="SimpleHashtableOutputType"/>
			<xs:element name="UserSupplierSearchesList" type="ComplexHashtableOutputType"/>
			<xs:element name="FlightRouteCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<xs:element name="FlightOriginCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<xs:element name="FlightDestinationCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<xs:element name="FlightCountryRouteCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<!--xs:element name="CarRouteCounts" type="SimpleHashtableOutputType" minOccurs="0"/-->
			<xs:element name="CarOriginCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<!--xs:element name="CarDestinationCounts" type="SimpleHashtableOutputType" minOccurs="0"/-->
			<xs:element name="CarCountryRouteCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<xs:element name="HotelDestinationCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<xs:element name="HotelDestinationCountryCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
			<xs:element name="CountryRouteCounts" type="SimpleHashtableOutputType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--*********************** -->
	<!--*********************** -->
	<!--SIMPLEUSERSEARCHSUMMARY -->
	<!--*********************** -->
	<!--*********************** -->
	<xs:complexType name="SimpleUserSearchSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="Searches" type="SimpleHashtableOutputType"/>
			<xs:element name="SupplierList" type="SimpleHashtableOutputType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--*********************** -->
	<!--*********************** -->
	<!--COMMANDSUMMARY -->
	<!--*********************** -->
	<!--*********************** -->
	<xs:complexType name="CommandSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="CommandBreakdown" type="SimpleHashtableOutputType"/>
			<xs:element name="UserCommandCounts" type="ComplexHashtableOutputType"/>
			<xs:element name="CommandUserCounts" type="ComplexHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
	<!--*********************** -->
	<!--*********************** -->
	<!--DATA CLEARING -->
	<!--*********************** -->
	<!--*********************** -->
	<xs:complexType name="ClearStatsDataPeriodDataType">
		<xs:sequence>
			<xs:element name="StatsDataClearingStatus" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
	<!--*********************** -->
	<!--*********************** -->
	<!--NEW STATS INTERFACE -->
	<!--*********************** -->
	<!--*********************** -->
	<xs:complexType name="NewCommandSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="CommandBreakdown" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ReferralSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="ReferralBreakdown" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierSearchSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="SupplierSearchBreakdown" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="NewUserSearchSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="UserSearchBreakdown" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="ConvertStatsSummaryPeriodDataType">
		<xs:sequence>
			<xs:element name="ConvertStatsBreakdown" type="SimpleHashtableOutputType"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
