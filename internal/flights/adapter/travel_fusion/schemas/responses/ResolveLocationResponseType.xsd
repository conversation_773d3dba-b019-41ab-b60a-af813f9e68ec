﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="ResolveLocationResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="LocationList" type="LocationResolutionResponseLocationListType"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="LocationResolutionResponseLocationListType">
		<xs:sequence>
			<xs:element name="Location" type="LocationResolutionResponseLocationType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="count" type="xs:integer" use="required"/>
	</xs:complexType>
	<xs:complexType name="LocationResolutionResponseLocationType">
		<xs:sequence>
			<xs:element name="RecommendedDisplayText" type="xs:string" minOccurs="0"/>
			<xs:element name="Id" type="xs:integer" minOccurs="0"/>
			<xs:element name="Type" type="CommonLocationResolutionPlaceType" minOccurs="0"/>
			<!-- okay, I'm using TrainCode here as it's more liberal than CommonTLA -->
			<xs:element name="Code" type="TrainCode" minOccurs="0"/>
			<xs:element name="Name" type="xs:string" minOccurs="0"/>
			<xs:element name="Admin2" type="xs:string" minOccurs="0"/>
			<xs:element name="Admin1" type="xs:string" minOccurs="0"/>
			<xs:element name="Country" type="CommonFullCountryType" minOccurs="0"/>
			<xs:element name="Admin2ShouldBeDisplayed" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Coordinate" type="LRCoordinate" minOccurs="0"/>
			<xs:element name="AirportCity" type="LRAirportCity" minOccurs="0"/>
			<xs:element name="TimeZone" type="LRTimeZone" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LRTimeZone">
		<xs:sequence>
			<xs:element name="StandardOffset" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LRCoordinate">
		<xs:sequence>
	<!-- scientific notation may be used here e.g. 2.778E-4 -->
			<xs:element name="Latitude" type="xs:string"/>
			<xs:element name="Longitude" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LRAirportCity">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<!--CODE SHOULD BE COMPULSORY -->
			<xs:element name="Code" type="CommonTLA" minOccurs="0"/>
			<xs:element name="Id" type="xs:integer" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
