﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetBookingDetailsResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Mode" type="CommonMode" minOccurs="0"/>
			<xs:element name="Status" type="CommonBookingV2StatusType"/>
			<xs:element name="BranchId" type="xs:string" minOccurs="0"/>
			<xs:element name="TFReference" type="xs:string" minOccurs="0"/>
			<xs:element name="SupplierReference" type="xs:string" minOccurs="0"/>
			<xs:element name="LinkedTFRef" type="xs:string" minOccurs="0"/>
			<xs:element name="ChangeLinkRefsList" type="ChangeLinkRefsListType" minOccurs="0"/>
			<xs:element name="SupplierName" type="CommonSupplierIdentifier" minOccurs="0"/>
			<xs:element name="SupplierConfirmationDataItemList" type="SupplierConfirmationDataItemListType" minOccurs="0"/>
			<xs:element name="TicketInfo" type="TicketInfoType" minOccurs="0"/>
			<xs:element name="EmdList" type="EmdListType" minOccurs="0"/>
			<xs:element name="IsFakeBooking" type="xs:boolean" minOccurs="0"/>
			<xs:choice>
				<xs:element name="RouterHistory" type="GetBookingDetailsRouterHistoryType"/>
				<xs:element name="HotelRouterHistory" type="GetBookingDetailsHotelRouterHistoryType"/>
			</xs:choice>
			<xs:element name="BookingProfile" type="BookingProfileType"/>
			<xs:element name="AirportNamePair" type="GetBookingDetailsAirportNamePairType" minOccurs="0" />
			<xs:element name="DateOfCancel" type="CommonString" minOccurs="0" />
			<xs:element name="SubstituteCardId" type="xs:string" minOccurs="0"/>
			<xs:element name="CardSubstitutionFee" type="xs:string" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="ChangeLinkRefsListType">
		<xs:sequence>
	        <!-- Pipe separated list of TF booking references, e.g. TFREF000A|TFREF000B|TFREF000C -->
			<xs:element name="ChangeLinkRefs" type="xs:string" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TicketInfoType">
		<xs:sequence>
			<xs:element name="Amount" type="xs:decimal"/>
			<xs:element name="Currency" type="CommonTLA"/>
			<xs:element name="Commission" type="CommonSimplePrice"/>
			<xs:element name="Fee" type="CommonSimplePrice" minOccurs="0"/>
			<xs:element name="TicketList" type="TicketListType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TicketListType">
		<xs:sequence>
			<xs:element name="Ticket" type="TicketType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TicketType">
		<xs:sequence>
			<xs:element name="TicketNumber" type="xs:string"/>
			<xs:element name="TicketStatus" type="TicketStatusEnumeration" minOccurs="0"/>
			<xs:element name="Requester" type="RequesterType" minOccurs="0"/>
			<xs:element name="AgentId" type="xs:string" minOccurs="0"/>
			<xs:element name="TicketIssueDate" type="TicketIssueDateType" minOccurs="0"/>
			<xs:element name="Amount" type="xs:decimal"/>
			<xs:element name="Currency" type="CommonTLA"/>
			<xs:element name="Commission" type="CommonSimplePrice"/>
			<xs:element name="Fee" type="CommonSimplePrice" minOccurs="0"/>
			<xs:element name="TaxList" type="TicketTaxListType" minOccurs="0"/>
			<xs:element name="PaymentMethod" type="xs:string"/>
			<xs:element name="TicketCoupons" type="TicketCouponsType" minOccurs="0"/>
			<xs:element name="RefundPrice" type="TicketRefundPriceType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TicketStatusEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="TICKETED"/>
			<xs:enumeration value="VOIDED"/>
			<xs:enumeration value="EXCHANGED"/>
			<xs:enumeration value="REFUNDED"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="RequesterType">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="source" type="xs:string" use="optional"/>
				<xs:attribute name="inferred" type="xs:boolean" use="optional"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="TicketIssueDateType">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="timezoneSource" type="xs:string" use="optional"/>
				<xs:attribute name="ignoreTime" type="xs:boolean" use="optional"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="TicketTaxListType">
		<xs:sequence>
			<xs:element name="Tax" type="TicketTaxType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TicketTaxType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Amount" type="xs:decimal"/>
			<xs:element name="Currency" type="CommonTLA"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TicketCouponsType">
		<xs:sequence>
			<xs:element name="Coupon" type="TicketCouponType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TicketCouponType">
		<xs:sequence>
			<xs:element name="CouponPrice" type="CommonSimplePrice" minOccurs="0"/>
			<xs:element name="SegmentRefs" type="xs:string" minOccurs="0"/>
			<xs:element name="TravellerRefs" type="xs:string" minOccurs="0"/>
			<xs:element name="SupplierCouponStatus" type="xs:string" minOccurs="0"/>
			<xs:element name="TFCouponStatus" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TicketRefundPriceType">
		<xs:sequence>
			<xs:element name="Amount" type="xs:decimal"/>
			<xs:element name="Currency" type="CommonTLA"/>
			<xs:element name="Commission" type="CommonSimplePrice" minOccurs="0"/>
			<xs:element name="Fee" type="CommonSimplePrice" minOccurs="0"/>
			<xs:element name="TaxList" type="TicketTaxListType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EmdListType">
		<xs:sequence>
			<xs:element name="Emd" type="EmdType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EmdType">
		<xs:sequence>
			<xs:element name="DocumentNumber" type="xs:string" minOccurs="0"/>
			<xs:element name="EmdStatus" type="EmdStatusEnumeration" minOccurs="0"/>
			<xs:element name="Requester" type="RequesterType" minOccurs="0"/>
			<xs:element name="EmdPrice" type="CommonSimplePrice" minOccurs="0"/>
			<xs:element name="EmdCoupons" type="EmdCouponsType" minOccurs="0"/>
			<xs:element name="EmdData" type="EmdDataType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="EmdStatusEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ISSUED"/>
			<xs:enumeration value="VOIDED"/>
			<xs:enumeration value="EXCHANGED"/>
			<xs:enumeration value="REFUNDED"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="EmdCouponsType">
		<xs:sequence>
			<xs:element name="Coupon" type="EmdCouponType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EmdCouponType">
		<xs:sequence>
			<xs:element name="TicketNumberRefs" type="xs:string" minOccurs="0"/>
			<xs:element name="RFIC" type="RFICType" minOccurs="0"/>
			<xs:element name="RFIS" type="RFISType" minOccurs="0"/>
			<xs:element name="CouponPrice" type="CommonSimplePrice" minOccurs="0"/>
			<xs:element name="SegmentRefs" type="xs:string" minOccurs="0"/>
			<xs:element name="TravellerRefs" type="xs:string" minOccurs="0"/>
			<xs:element name="SupplierCouponStatus" type="xs:string" minOccurs="0"/>
			<xs:element name="TFCouponStatus" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RFICType">
		<xs:sequence>
			<xs:element name="Code" type="xs:string"/>
			<xs:element name="Label" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RFISType">
		<xs:sequence>
			<xs:element name="Code" type="xs:string"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="Detail" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EmdDataType">
		<xs:sequence>
			<xs:element name="TicketNumberRefs" type="xs:string" minOccurs="0"/>
			<xs:element name="RFIC" type="RFICType" minOccurs="0"/>
			<xs:element name="RFIS" type="RFISType" minOccurs="0"/>
			<xs:element name="SegmentRefs" type="xs:string" minOccurs="0"/>
			<xs:element name="TravellerRefs" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GetBookingDetailsRouterHistoryType">
		<xs:sequence>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="SearchRouter" type="CommonRouter"/>
			<xs:element name="SearchBookingProfile" type="BookingProfileType" minOccurs="0" />
			<xs:element name="DetailsRouter" type="CommonRouter"/>
			<xs:element name="DetailsBookingProfile" type="BookingProfileType" minOccurs="0" />
			<xs:element name="TermsRouter" type="CommonRouter"/>
			<xs:element name="TermsBookingProfile" type="BookingProfileType" minOccurs="0" />
			<xs:element name="BookingRouter" type="CommonRouter" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GetBookingDetailsAirportNamePairType">
		<xs:sequence>
			<xs:element name="OriginAirport" type="xs:string"/>
			<xs:element name="DestinationAirport" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GetBookingDetailsHotelRouterHistoryType">
		<xs:sequence>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="SearchRouter" type="HotelRouter"/>
			<xs:element name="SearchBookingProfile" type="BookingProfileType" minOccurs="0" />
			<xs:element name="DetailsRouter" type="HotelRouter"/>
			<xs:element name="TermsRouter" type="HotelRouter"/>
			<xs:element name="TermsBookingProfile" type="BookingProfileType" minOccurs="0" />
			<xs:element name="BookingRouter" type="HotelRouter" minOccurs="0"/>
            <xs:element name="CancelRouter" type="HotelRouter" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="GetCountriesResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="CountryList" type="getCountriesCountryList"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="getCountriesCountryList">
		<xs:sequence>
			<xs:element name="Country" type="CommonFullCountryTypeWithDetails" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="GetCurrenciesResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="CurrencyList" type="CurrenciesResponseCurrencyListType"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="CurrenciesResponseCurrencyListType">
		<xs:sequence>
			<xs:element name="Currency" type="CurrenciesResponseCurrencyType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CurrenciesResponseCurrencyType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Code" type="CommonTLA"/>
			<xs:element name="UsdRate" type="xs:decimal" minOccurs="0"/>
			<xs:element name="NokRate" type="xs:decimal" minOccurs="0"/>
			<xs:element name="Rate" type="xs:decimal" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="GetErrorCodesResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="ErrorCodeGroupList" type="GetErrorCodesGroupList"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="GetErrorCodesCodeGroup">
		<xs:sequence>
			<xs:element name="Code" type="xs:string"/>
			<xs:element name="Name" type="xs:string" minOccurs="0"/>
			<xs:element name="Description" type="xs:string"/>
			<xs:element name="ErrorCodeGroupList" type="GetErrorCodesGroupList" minOccurs="0"/>
			<xs:element name="ErrorCodeList" type="GetErrorCodesCodeList" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GetErrorCodesErrorCode">
		<xs:sequence>
			<xs:element name="Code" type="xs:string"/>
			<xs:element name="Name" type="xs:string" minOccurs="0"/>
			<xs:element name="Description" type="xs:string"/>
			<xs:element name="Visibility" type="xs:string" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GetErrorCodesGroupList">
		<xs:sequence>
			<xs:element name="ErrorCodeGroup" type="GetErrorCodesCodeGroup" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="GetErrorCodesCodeList">
		<xs:sequence>
			<xs:element name="ErrorCode" type="GetErrorCodesErrorCode" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="LoginResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="GetPrePackagedSupplierSummaryResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="PrePackagedSupplierSummaryList" type="PrePackagedSupplierSummaryListType"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="PrePackagedSupplierSummaryListType">
		<xs:sequence>
			<xs:element name="PrePackagedSupplierSummary" type="PrePackagedSupplierSummaryType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedSupplierSummaryType">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonSupplierIdentifier"/>
			<xs:element name="SupportedDurationList" type="SupportedDurationListType"/>
			<!-- this describes the range (number of days) of departure dates that can be requested in a single search - see 'StartRoutingPrePackagedType'-->
			<xs:element name="RecommendedDepartureDateRange" type="xs:integer"/>
			<!-- this describes the latest possible departure date that the supplier can return holidays for.-->
			<xs:element name="LastPossibleDepartureDate" type="CommonString"/>
			<xs:element name="RouteList" type="PrePackagedRouteListType" minOccurs="0"/>
			<!-- If this is omitted or has value 'true', then separate searches should be submitted for each route for this supplier 
					however if it is false, a single request should be made with the destination omitted. In this case all destinations
					will be returned in the response.-->
			<xs:element name="RequestDestinationsSeparately" type="xs:boolean"/>
			<!-- TF recommended period between each refresh of the pre-fetched data. If this is omitted, every 24 hours at night is recommended by default -->
			<xs:element name="RecommendedPreFetchFrequencyMinutes" type="xs:integer" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupportedDurationListType">
		<xs:sequence>
			<xs:element name="SupportedDuration" type="PrePackagedDurationType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedRouteListType">
		<xs:sequence>
			<xs:element name="Route" type="PrePackagedRouteType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedRouteType">
		<xs:sequence>
			<!-- the origin will be omitted for suppliers that can return all origins in a single request -->
			<xs:element name="Origin" type="PrePackagedOriginType" minOccurs="0"/>
			<xs:element name="Destination" type="PrePackagedDestinationType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedOriginType">
		<xs:sequence>
			<!--airport code-->
			<xs:element name="Code" type="CommonTLA" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PrePackagedDestinationType">
		<xs:sequence>
			<!--resort code-->
			<xs:element name="Code" type="xs:string" minOccurs="0"/>
			<xs:element name="DisplayName" type="xs:string" minOccurs="0"/>
			<xs:element name="GeoCoordinates" type="CommonCoordinates" minOccurs="0"/>
			<!-- TFLocationId will be omitted if there is no corresponding location in the TF database. In this case, the TFNearestLocationId will be supplied -->
			<xs:element name="TFLocationId" type="xs:integer" minOccurs="0"/>
			<xs:element name="TFNearestLocationId" type="xs:integer" minOccurs="0"/>
			<xs:element name="Country" type="CommonFullCountryType"/>
			<xs:element name="Admin1Region" type="xs:string" minOccurs="0"/>
			<xs:element name="Admin2Region" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="GetHotelDetailsResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" minOccurs="0"/>
			<xs:element name="SupplierHotelDetailsList" type="SupplierHotelDetailsListType" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="GetMultipleHotelDetailsResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" minOccurs="0"/>
			<xs:element name="HotelDetailsList" type="HotelDetailsListType" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="HotelDetailsListType">
		<xs:sequence>
			<xs:element name="HotelDetails" type="HotelDetailsListItemType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelDetailsListItemType">
		<xs:sequence>
			<xs:element name="HotelId" type="CommonId"/>
			<xs:element name="IsLocked" type="xs:boolean" minOccurs="0"/>
			<!-- this element will be omitted if there are no details available for the hotel -->
			<xs:element name="SupplierHotelDetailsList" type="SupplierHotelDetailsListType" minOccurs="0"/>			
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="SupplierHotelDetailsListType">
		<xs:sequence>
			<xs:element name="SupplierHotelDetails" type="SupplierHotelDetailsType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierHotelDetailsType">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonSupplierIdentifier"/>
			<xs:element name="HotelDetails" type="HotelDetailsType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelDetailsType">
		<xs:sequence>
			<xs:element name="AccommodationId" type="xs:integer"/>
			<xs:element name="TfAccommodationId" type="CommonId"/>
			<xs:element name="HotelSupplierAccommodationId" type="xs:string"/>
			<xs:element name="Stars" type="xs:decimal" minOccurs="0"/>
			<xs:element name="SupplierSpecialRanking" type="xs:string" minOccurs="0"/>
			<xs:element name="Type" type="AccommodationTypesEnumeration"/>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="GeoCoordinates" type="CommonCoordinates" minOccurs="0"/>
			<xs:element name="Description" type="xs:string" minOccurs="0"/>
			<xs:element name="Address" type="xs:string" minOccurs="0"/>
			<xs:element name="AddressDetails" type="HotelAddressDetailsType" minOccurs="0"/>
			<xs:element name="GDSChain" type="HotelGDSChainType" minOccurs="0"/>
			<xs:element name="GDSInfo" type="xs:string" minOccurs="0"/>
			<xs:element name="ImageList" type="HotelAndCarImageListType" minOccurs="0"/>
			<xs:element name="FacilityList" type="AccommodationFacilitiesListType" minOccurs="0"/>
			<xs:element name="MiscellaneousInfoList" type="MiscellaneousAccommodationInfoListType" minOccurs="0"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="CityCode" type="xs:string" minOccurs="0"/>
			<xs:element name="CountryCode" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="AccommodationDistanceType">
		<xs:sequence>
			<xs:element name="Type" type="AccommodationDistanceTypeEnumeration"/>
			<xs:element name="DistanceInMetres" type="xs:integer"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="AccommodationDistanceTypeEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ToBeach"/>
			<xs:enumeration value="ToLocalCentre"/>
			<xs:enumeration value="ToAirport"/>
			<xs:enumeration value="ToSki"/>
		</xs:restriction>
	</xs:simpleType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="LogPrePackagedReferralResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT RESPONSE -->
	<!-- -->
	<xs:complexType name="ProcessCardVerificationResponseType">
		<xs:sequence>
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="CardVerificationData" type="CardVerificationDataType" />
			<!-- This is for internal Travelfusion use -->
			<xs:element name="GeneralInfoItemList" type="GeneralInfoItemListType" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
	<xs:complexType name="CardVerificationDataType">
		<xs:sequence>
			<xs:element name="VerificationType" type="CardVerificationTypeType"/>
			<!-- the 'Url' parameter will only be supplied for verification type 'url' -->
			<xs:element name="Url" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CardVerificationTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="url"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>