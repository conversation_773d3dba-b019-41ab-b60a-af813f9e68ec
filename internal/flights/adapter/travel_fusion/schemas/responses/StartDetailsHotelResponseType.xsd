﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:complexType name="StartDetailsHotelResponseType">
        <xs:sequence>
            <xs:element name="LoginId" type="CommonLoginType"/>
            <xs:element name="RoutingId" type="CommonId"/>
            <xs:element name="OptionId" type="CommonId"/>
        </xs:sequence>
        <xs:attribute name="millis" type="xs:integer" use="optional"/>
    </xs:complexType>
</xs:schema>
