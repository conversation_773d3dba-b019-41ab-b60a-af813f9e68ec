﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
<xs:complexType name="ListSupplierRoutesResponseType">
<xs:sequence>
<xs:element name="LoginId" type="CommonLoginType"/>
<xs:element name="RouteList" type="ListSupplierRoutesRouteList" minOccurs="0"/>
<xs:element name="LocationList" type="ListSupplierRoutesLocationList" minOccurs="0"/>
<xs:element name="AllowedCountryRoutes" type="AllowedCountryRoutesType" minOccurs="0"/>
</xs:sequence>
<xs:attribute name="millis" type="xs:integer" use="optional"/>
</xs:complexType>

<xs:complexType name="ListSupplierRoutesRouteList">
<xs:sequence>
<xs:element name="AirportRoutes" type="xs:string" maxOccurs="unbounded"/>
<xs:element name="CityRoutes" type="xs:string" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>

<xs:complexType name="ListSupplierRoutesLocationList">
<xs:sequence>
<xs:element name="Location" type="ListSupplierRoutesLocation" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>

<xs:complexType name="ListSupplierRoutesLocation">
<xs:attribute name="IATACode" type="xs:string" use="optional" />
<xs:attribute name="Type" type="xs:string" use="optional" />
<xs:attribute name="CountryCode" type="xs:string" use="optional"/>
</xs:complexType>

</xs:schema>