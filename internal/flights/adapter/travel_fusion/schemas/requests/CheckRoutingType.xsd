﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CheckRoutingType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="Filter" type="CheckRoutingFilterType" minOccurs="0"/>
			<xs:element name="SortList" type="CheckRoutingSortListType" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="CheckRoutingFilterType">
		<xs:sequence>
			<xs:element name="FirstResult" type="xs:integer" minOccurs="0"/>
			<xs:element name="NumberOfResults" type="xs:integer" minOccurs="0"/>
			<xs:element name="SupplierList" type="CommonSupplierList" minOccurs="0"/>
			<xs:element name="OriginAirportCodeList" type="CommonOriginAirportCodeList" minOccurs="0"/>
			<xs:element name="DestinationAirportCodeList" type="CommonDestinationAirportCodeList" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CheckRoutingSortOrderType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="forward"/>
			<xs:enumeration value="reverse"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CheckRoutingSortType">
		<xs:sequence>
			<xs:element name="Order" type="CheckRoutingSortOrderType"/>
			<xs:element name="Type" type="CheckRoutingSortTypeType"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CheckRoutingSortListType">
		<xs:sequence>
			<xs:element name="Sort" type="CheckRoutingSortType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CheckRoutingSortTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="price"/>
			<xs:enumeration value="outwardDepartDate"/>
			<xs:enumeration value="provider"/>
			<xs:enumeration value="outwardArriveDate"/>
			<xs:enumeration value="returnDepartDate"/>
			<xs:enumeration value="returnArriveDate"/>
			<xs:enumeration value="totalDuration"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
