﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StartRoutingType">
		<xs:sequence>
			<xs:element name="HandlerSelect" type="xs:string" minOccurs="0" />
			<xs:element name="XmlLoginId" type="CommonLoginType" />
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Mode" type="CommonMode" />
			<xs:element name="ProductType" type="xs:string" minOccurs="0"/>
			<xs:element name="Origin" type="StartRoutingLocation" />
			<xs:element name="Destination" type="StartRoutingLocation" />
			<xs:element name="OutwardDates" type="StartRoutingDatesType" />
			<xs:choice minOccurs="0" maxOccurs="1">
			  <xs:element name="ReturnDates" type="StartRoutingDatesType"/>
			  <xs:element name="AdditionalTripList" type="StartRoutingAdditionalTripListType"/>
			</xs:choice>
			<xs:element name="MaxChanges" type="xs:integer" />
			<xs:element name="MaxHops" type="xs:positiveInteger" />
			<xs:element name="BookingChange" type="BookingChangeType" minOccurs="0" />
			<xs:element name="SupplierList" type="CommonSupplierList" minOccurs="0" />
			<xs:element name="SupplierListType" type="CommonTypeOfList" minOccurs="0" />
			<xs:element name="RealOperatorFilter" type="OperatorFilterType" minOccurs="0" />
			<xs:element name="VendingOperatorFilter" type="OperatorFilterType" minOccurs="0" />
			<xs:element name="Timeout" type="xs:integer" />
			<xs:element name="TravelClass" type="TfClassEnumerationType" minOccurs="0" />
			<xs:element name="TravellerList" type="StartRoutingTravellerList" />
			<xs:element name="NumberOfBags" minOccurs="0">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="CommonPositiveInteger">
							<xs:attribute name="ecode" type="xs:string" use="optional" />
							<xs:attribute name="etext" type="xs:string" use="optional" />
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="ShowCardCharges" type="xs:boolean" minOccurs="0" />
			<xs:element name="ShowLuggageCharges" type="xs:boolean" minOccurs="0" />
			<xs:element name="ShowCheckInCharges" type="xs:boolean" minOccurs="0" />
			<xs:element name="ShowSpeedyBoardingCharges" type="xs:boolean" minOccurs="0" />
			<xs:element name="MinimumCardChargeTypeList" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MinimumCardChargeType" maxOccurs="unbounded">
							<xs:complexType>
								<xs:simpleContent>
									<xs:extension base="xs:string">
										<xs:attribute name="ecode" type="xs:string" use="optional" />
										<xs:attribute name="etext" type="xs:string" use="optional" />
									</xs:extension>
								</xs:simpleContent>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="IncrementalResults" type="xs:boolean" minOccurs="0" />
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0" />
			<xs:element name="PerformSearch" type="xs:boolean" minOccurs="0" />
			<!-- XMLPROD-47 remove the use of DisplayCheapestSet -->
			<!-- xs:element name="DisplayCheapestSet" type="xs:boolean" minOccurs="0" / -->
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
	<xs:complexType name="UseAirportIntelligenceType">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="0" />
			<xs:element name="FirstCheck" type="xs:integer" minOccurs="0" />
			<xs:element name="SecondCheck" type="xs:integer" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BookingChangeType">
		<xs:sequence>
			<xs:element name="TFBookingReference" type="xs:string" minOccurs="0" />
			<xs:element name="AuthOption" type="AuthOptionType" minOccurs="0" />
			<xs:element name="ChangeAttributeList" type="BookingChangeAttributeListType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BookingChangeAttributeListType">
		<xs:sequence>
			<xs:element name="ChangeOutwardDate" type="xs:boolean"  minOccurs="0" />
			<xs:element name="ChangeReturnDate" type="xs:boolean"  minOccurs="0" />
			<xs:element name="ChangeOrigin" type="xs:boolean"  minOccurs="0" />
			<xs:element name="ChangeDestination" type="xs:boolean"  minOccurs="0"/>
			<xs:element name="ChangeBaggage" type="xs:boolean"  minOccurs="0"/>
			<xs:element name="ChangeInsurance" type="xs:boolean"  minOccurs="0"/>
			<xs:element name="ChangeSeat" type="xs:boolean"  minOccurs="0"/>
			<xs:element name="Cancel" type="xs:boolean"  minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="StartRoutingLocation">
		<xs:sequence>
			<xs:element name="Descriptor" type="CommonString" />
			<xs:element name="Type" type="StartRoutingLocationTypeType" />
			<xs:element name="ResolutionTypeList" type="StartRoutingResolutionTypeListType" minOccurs="0" />
			<xs:element name="Radius" type="xs:integer" minOccurs="0" />
			<!-- THIS TAG SHOULD BE IGNORED -->
			<xs:element name="Text" type="xs:string" minOccurs="0" />
			<xs:element name="UseAirportIntelligence" type="UseAirportIntelligenceType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="StartRoutingLocationTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="airportcode" />
			<xs:enumeration value="airportgroup" />
			<xs:enumeration value="citycode" />
			<xs:enumeration value="tfcitycode" />
			<xs:enumeration value="trainstationcode" />
			<xs:enumeration value="tfstationcode" />
			<xs:enumeration value="hotelcode" />
			<xs:enumeration value="auto" />
			<xs:enumeration value="locationid" />
			<xs:enumeration value="coordinate" />
			<xs:enumeration value="locationResolutionItemId" />
			<!-- e.g. alcudia -->
			<xs:enumeration value="hotelresort" />
			<!-- LAT,LONG spaces ignored. Doubles. -->
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="StartRoutingResolutionTypeListType">
		<xs:sequence>
			<xs:element name="ResolutionType" type="StartRoutingLocationTypeType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StartRoutingDatesType">
		<xs:sequence>
			<!-- dd/mm/yyyy-hh:mm:ss -->
			<xs:element name="DateOfSearch" type="CommonString" />
			<!-- these should not be used for pre-packaged -->
			<xs:element name="DepartDateFilter" type="DateFilterType" minOccurs="0" />
			<xs:element name="ArriveDateFilter" type="DateFilterType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="DateFilterType">
		<xs:sequence>
			<xs:element name="DiscardBefore" type="CommonString" minOccurs="0" />
			<xs:element name="DiscardAfter" type="CommonString" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="OperatorFilterType">
		<xs:sequence>
			<xs:element name="Type" type="OperatorFilterTypeType" />
			<xs:element name="AllowPartial" type="xs:boolean" />
			<xs:element name="OperatorList" type="StartRoutingOperatorListType" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="OperatorFilterTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="select" />
			<xs:enumeration value="reject" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="StartRoutingOperatorListType">
		<xs:sequence>
			<xs:element name="Operator" type="CommonOperatorCode" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StartRoutingTravellerList">
		<xs:sequence>
			<xs:element name="Traveller" type="StartRoutingTraveller" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StartRoutingTraveller">
		<xs:sequence>
			<xs:element name="Age" type="CommonAge" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StartRoutingAdditionalTripType">
		<xs:sequence>
			<xs:element name="Origin" type="StartRoutingLocation" />
			<xs:element name="Destination" type="StartRoutingLocation" />
			<xs:element name="OutwardDates" type="StartRoutingDatesType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="StartRoutingAdditionalTripListType">
		<xs:sequence>
			<xs:element name="AdditionalTrip" type="StartRoutingAdditionalTripType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
