﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="ResolveLocationType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="CountryCode" type="CommonString" minOccurs="0"/>
			<xs:element name="TypeList" type="LocationResolutionTypeList" minOccurs="0"/>
			<xs:element name="Name" type="xs:string" minOccurs="0"/>
			<xs:element name="RequiredFieldList" type="LocationResolutionRequiredFieldList"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="LocationResolutionTypeList">
		<xs:sequence>
			<xs:element name="Type" type="CommonLocationResolutionPlaceType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationResolutionRequiredFieldList">
		<xs:sequence>
			<xs:element name="RequiredField" type="LocationResolutionRequiredField" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="LocationResolutionRequiredField">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Admin1"/>
			<xs:enumeration value="Admin2"/>
			<xs:enumeration value="Admin2ShouldBeDisplayed"/>
			<xs:enumeration value="AirportCity"/>
			<xs:enumeration value="Code"/>
			<xs:enumeration value="Coordinate"/>
			<xs:enumeration value="Country"/>
			<xs:enumeration value="Id"/>
			<xs:enumeration value="Name"/>
			<xs:enumeration value="RecommendedDisplayText"/>
			<xs:enumeration value="Type"/>
			<xs:enumeration value="TimeZone"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
