﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="ProcessTermsType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Mode" type="CommonMode" minOccurs="0"/>
			<xs:element name="RoutingId" type="CommonId"/>
			<xs:element name="OutwardId" type="CommonId" minOccurs="0"/>
			<xs:element name="ReturnId" type="CommonId" minOccurs="0"/>
			<xs:element name="BookingProfile" type="BookingProfileType"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
</xs:schema>
