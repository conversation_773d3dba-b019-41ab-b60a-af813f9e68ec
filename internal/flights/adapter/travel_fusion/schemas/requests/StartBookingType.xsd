﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StartBookingType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="TFBookingReference" type="xs:string"/>
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0"/>
			<xs:element name="ExpectedPrice" type="CommonPrice" minOccurs="0" />
			<xs:element name="JourneyPrice" type="CommonPrice" minOccurs="0" />
			<xs:element name="FakeBooking" type="FakeBookingType" minOccurs="0"/>
			<xs:element name="SupplierVisualAuthorisationText" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional" />
		<xs:attribute name="etext" type="xs:string" use="optional" />
		<xs:attribute name="edetail" type="xs:string" use="optional" />
		<xs:attribute name="edate" type="xs:string" use="optional" />
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="FakeBookingType">
		<xs:sequence>
			<xs:element name="EnableFakeBooking" type="xs:boolean"/>
			<xs:element name="FakeBookingSimulatedDelaySeconds" type="xs:integer" minOccurs="0"/>
			<xs:element name="FakeBookingStatus" type="CommonFinalBookingStatusType" minOccurs="0"/>
			<!-- 'EnableFakeCardVerification' defaults to 'false' if not specified -->
			<xs:element name="EnableFakeCardVerification" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
