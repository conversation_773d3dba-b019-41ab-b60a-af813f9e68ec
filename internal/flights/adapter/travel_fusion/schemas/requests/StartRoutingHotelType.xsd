﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StartRoutingHotelType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Destination" type="StartRoutingLocation"/>
			<xs:element name="CheckinDates" type="StartRoutingDatesType"/>
			<xs:element name="CheckoutDates" type="StartRoutingDatesType"/>
			<xs:element name="SupplierList" type="CommonSupplierList" minOccurs="0"/>
			<xs:element name="Timeout" type="xs:integer"/>
			<xs:element name="AccommodationTypeList" type="AccommodationTypeListType"/>
			<xs:element name="RoomList" type="HotelRequestRoomListType"/>
			<!-- This must be set to false. Need to make this mandatory -->
			<xs:element name="IncludeHotelDetails" type="IncludeHotelDetailsFalseEnumeration" minOccurs="0"/>
			<xs:element name="IncrementalResults" type="xs:boolean" minOccurs="0"/>
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0"/>
			<xs:element name="PerformSearch" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:simpleType name="IncludeHotelDetailsFalseEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="false"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="AccommodationTypesEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="hotel"/>
			<xs:enumeration value="apartment"/>
			<xs:enumeration value="house"/>
			<xs:enumeration value="hostel"/>
			<xs:enumeration value="bedandbreakfast"/>
			<xs:enumeration value="dormitory"/>
			<xs:enumeration value="boat"/>
			<xs:enumeration value="camper"/>
			<!-- related to listings -->
			<xs:enumeration value="holidaylets"/>
			<xs:enumeration value="spa"/>
			<xs:enumeration value="timeshare"/>
			<xs:enumeration value="camping"/>
			<xs:enumeration value="rental"/>
			<!-- not specified -->
			<xs:enumeration value="other"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="AccommodationTypeListType">
		<xs:sequence>
			<xs:element name="AccommodationType" type="AccommodationTypesEnumeration" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelRequestRoomListType">
		<xs:sequence>
			<xs:element name="Room" type="HotelRequestRoomType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="HotelRequestRoomType">
		<xs:sequence>
			<xs:element name="Type" type="RoomTypesEnumeration"/>
			<xs:element name="TravellerList" type="StartRoutingTravellerList"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="RoomTypesEnumeration">
		<xs:restriction base="xs:string"/>
		<!-- This item is a half-enumeration. It will either have one of the values in comments below, or it will be an arbitrary free text string if the supplier's room type is not recognised -->
		<!--xs:restriction base="xs:string">
			<xs:enumeration value="single"/>
			<xs:enumeration value="double"/>
			<xs:enumeration value="twin"/>
			<xs:enumeration value="triple"/>
			<xs:enumeration value="quad"/>
			<xs:enumeration value="unspecified"/>
		</xs:restriction-->
	</xs:simpleType>
</xs:schema>
