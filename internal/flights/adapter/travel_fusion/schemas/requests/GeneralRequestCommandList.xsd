﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:include schemaLocation="MiscellaneousRequests.xsd"/>
	<xs:include schemaLocation="StartRoutingType.xsd"/>
	<xs:include schemaLocation="StartManageBookingPlaneType.xsd" />
	<xs:include schemaLocation="CheckRoutingType.xsd"/>
	<xs:include schemaLocation="ProcessDetailsType.xsd"/>
	<xs:include schemaLocation="ProcessTermsType.xsd"/>
	<xs:include schemaLocation="StartBookingType.xsd"/>
	<xs:include schemaLocation="CheckBookingType.xsd"/>
	<xs:include schemaLocation="GetSupplierRoutesType.xsd" />
	<xs:include schemaLocation="ResolveLocationType.xsd"/>
	<xs:include schemaLocation="GetStatsType.xsd"/>
	<xs:include schemaLocation="StartRoutingHotelType.xsd"/>
	<xs:include schemaLocation="CheckRoutingHotelType.xsd"/>
	<xs:include schemaLocation="ProcessDetailsHotelType.xsd"/>
	<xs:include schemaLocation="StartDetailsHotelType.xsd"/>
	<xs:include schemaLocation="CheckDetailsHotelType.xsd"/>
	<xs:include schemaLocation="StartTermsHotelType.xsd"/>
	<xs:include schemaLocation="CheckTermsHotelType.xsd"/>
	<xs:include schemaLocation="StartBookingHotelType.xsd"/>
	<xs:include schemaLocation="CheckBookingHotelType.xsd"/>
	<xs:include schemaLocation="StartRoutingCarType.xsd"/>
	<xs:include schemaLocation="CheckRoutingCarType.xsd"/>
	<xs:include schemaLocation="ProcessDetailsCarType.xsd"/>
	<xs:include schemaLocation="StartRoutingPrePackagedType.xsd"/>
	<xs:include schemaLocation="CheckRoutingPrePackagedType.xsd"/>
	<xs:include schemaLocation="CalculateExtraServiceChargeType.xsd"/>
	<xs:include schemaLocation="GetSupportedCardTypeList.xsd"/>
	<xs:include schemaLocation="StoreValueInVaultType.xsd"/>
	<xs:include schemaLocation="DeleteValueInVaultType.xsd"/>
	<xs:include schemaLocation="GetExtraServiceChargeType.xsd" />
	<xs:include schemaLocation="ListSupplierRoutesType.xsd"/>
	<xs:include schemaLocation="ListTrainStationsType.xsd"/>
	<xs:include schemaLocation="GetSuppliersTravellerInfo.xsd"/>
	<xs:include schemaLocation="GetBranchSupplierListType.xsd"/>
	<xs:include schemaLocation="GetSuppliersRoutingParameterListType.xsd"/>
	<xs:complexType name="GeneralRequestCommandListType">
		<xs:sequence>
			<xs:element name="Login" type="LoginType" minOccurs="0"/>
			<xs:element name="StartRouting" type="StartRoutingType" minOccurs="0"/>
			<xs:element name="StartManageBookingPlane" type="StartManageBookingPlaneType" minOccurs="0"/>
			<xs:element name="CheckRouting" type="CheckRoutingType" minOccurs="0"/>
			<xs:element name="ProcessDetails" type="ProcessDetailsType" minOccurs="0"/>
			<xs:element name="ProcessTerms" type="ProcessTermsType" minOccurs="0"/>
			<xs:element name="StartBooking" type="StartBookingType" minOccurs="0"/>
			<xs:element name="CheckBooking" type="CheckBookingType" minOccurs="0"/>
			<xs:element name="GetBookingDetails" type="GetBookingDetailsType" minOccurs="0"/>
			<xs:element name="ResolveLocation" type="ResolveLocationType" minOccurs="0"/>
			<xs:element name="GetCurrencies" type="GetCurrenciesType" minOccurs="0"/>
			<xs:element name="GetCountries" type="GetCountriesType" minOccurs="0"/>
			<xs:element name="GetPrePackagedSupplierSummary" type="GetPrePackagedSupplierSummaryType" minOccurs="0"/>
			<xs:element name="GetHotelDetails" type="GetHotelDetailsType" minOccurs="0"/>
			<xs:element name="GetMultipleHotelDetails" type="GetMultipleHotelDetailsType" minOccurs="0"/>
			<xs:element name="GetStats" type="GetStatsType" minOccurs="0"/>
			<xs:element name="GetErrorCodes" type="GetErrorCodesType" minOccurs="0"/>
			<xs:element name="StartRoutingHotel" type="StartRoutingHotelType" minOccurs="0"/>
			<xs:element name="CheckRoutingHotel" type="CheckRoutingHotelType" minOccurs="0"/>
			<xs:element name="ProcessDetailsHotel" type="ProcessDetailsHotelType" minOccurs="0"/>
			<xs:element name="StartDetailsHotel" type="StartDetailsHotelType" minOccurs="0"/>
			<xs:element name="CheckDetailsHotel" type="CheckDetailsHotelType" minOccurs="0"/>
			<xs:element name="StartTermsHotel" type="StartTermsHotelType" minOccurs="0"/>
			<xs:element name="CheckTermsHotel" type="CheckTermsHotelType" minOccurs="0"/>
			<xs:element name="StartBookingHotel" type="StartBookingHotelType" minOccurs="0"/>
			<xs:element name="CheckBookingHotel" type="CheckBookingHotelType" minOccurs="0"/>
			<xs:element name="StartRoutingCar" type="StartRoutingCarType" minOccurs="0"/>
			<xs:element name="CheckRoutingCar" type="CheckRoutingCarType" minOccurs="0"/>
			<xs:element name="ProcessDetailsCar" type="ProcessDetailsCarType" minOccurs="0"/>
			<xs:element name="StartRoutingPrePackaged" type="StartRoutingPrePackagedType" minOccurs="0"/>
			<xs:element name="StartRoutingPrePackagedTF" type="StartRoutingPrePackagedType" minOccurs="0"/>
			<xs:element name="CheckRoutingPrePackaged" type="CheckRoutingPrePackagedType" minOccurs="0"/>
			<xs:element name="CheckRoutingPrePackagedTF" type="CheckRoutingPrePackagedType" minOccurs="0"/>
			<xs:element name="LogPrePackagedReferral" type="LogPrePackagedReferralType" minOccurs="0"/>
			<xs:element name="ProcessCardVerification" type="ProcessCardVerificationType" minOccurs="0"/>
			<xs:element name="CalculateExtraServiceCharge" type="CalculateExtraServiceChargeType" minOccurs="0"/>
			<xs:element name="GetSupportedCardTypeList" type="GetSupportedCardTypeList" minOccurs="0"/>
			<xs:element name="StoreValueInVault" type="StoreValueInVaultRequestType" minOccurs="0"/>
			<xs:element name="DeleteValueInVault" type="DeleteValueInVaultRequestType" minOccurs="0"/>
			<xs:element name="GetSupplierRoutes" type="GetSupplierRoutesType" minOccurs="0"/>
			<xs:element name="GetExtraServiceCharge" type="GetExtraServiceChargeType" minOccurs="0"/>
			<xs:element name="ListSupplierRoutes" type="ListSupplierRoutesType" minOccurs="0"/>
			<xs:element name="ListTrainStations" type="ListTrainStationsType" minOccurs="0"/>
			<xs:element name="GetSuppliersTravellerInfo" type="GetSuppliersTravellerInfoType" minOccurs="0" />
			<xs:element name="GetBranchSupplierList" type="GetBranchSupplierListType" minOccurs="0" />
			<xs:element name="GetSuppliersRoutingParameterList" type="GetSuppliersRoutingParameterListType" minOccurs="0" />
			<!-- xs:element name="GetExtraServiceCharges" type="GetExtraServiceChargeType" minOccurs="0"/ -->
		</xs:sequence>
	</xs:complexType>
</xs:schema>