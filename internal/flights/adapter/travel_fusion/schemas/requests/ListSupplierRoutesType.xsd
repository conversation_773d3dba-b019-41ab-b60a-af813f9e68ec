﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="ListSupplierRoutesType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType" />
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Supplier" type="CommonSupplierIdentifier" />
			<xs:element name="OneWayOnlyAirportRoutes" type="xs:boolean" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
</xs:schema>