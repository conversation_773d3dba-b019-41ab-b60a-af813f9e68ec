﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CalculateExtraServiceChargeType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType" />
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Supplier" type="CommonSupplierIdentifier" />
			<xs:element name="CalculationProfile" type="CalculationProfileType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CalculationProfileType">
		<xs:sequence>
			<xs:element name="ServiceNameList" type="ServiceNameListType" />
			<xs:element name="TravellerList" type="TravellerListType" minOccurs="0"/>
			<xs:element name="GroupList" type="GroupListType"  minOccurs="0"/>
			<xs:element name="BillingDetails" type="BillingDetailsType"  minOccurs="0" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="TravellerListType">
		<xs:sequence>
			<xs:element name="Traveller" type="TravellerType"  minOccurs="0"  maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="GroupListType">
		<xs:sequence>
			<xs:element name="Group" type="GroupType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="BillingDetailsType">
		<xs:sequence>
			<xs:element name="Price" type="xs:decimal" minOccurs="0" />
			<xs:element name="Currency" type="CommonTLA" minOccurs="0" />
			<xs:element name="CreditCard" type="CreditCardType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="TravellerType">
		<xs:sequence>
			<xs:element name="Age" type="CommonAge" />
			<xs:element name="ServiceNameList" type="ServiceNameListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="ServiceNameListType">
		<xs:sequence>
			<xs:element name="ServiceName" type="ServiceNameType"  minOccurs="1" maxOccurs="unbounded" />
		</xs:sequence>	
	</xs:complexType>
	
	
	<xs:complexType name="ServiceNameType">
		<xs:sequence>
			<xs:element name="Name" type="non-empty-string"  minOccurs="1"/>
			<xs:element name="ParameterList" type="ParameterListType"  minOccurs="0" />
		</xs:sequence>	
	</xs:complexType>
	
	<xs:complexType name="ParameterListType">
		<xs:sequence>
			<xs:element name="NumberOfBags" type="xs:integer" nillable="false" minOccurs="0" />
			<xs:element name="WeightOfBags" type="xs:integer" nillable="false"  minOccurs="0" />
			<xs:element name="ServiceType" type="non-empty-string"  minOccurs="0" />
		</xs:sequence>	
	</xs:complexType>

	<xs:complexType name="GroupType">
		<xs:sequence>
			<xs:element name="Outward" type="TripType"/>
			<xs:element name="Return" type="TripType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="TripType">
		<xs:sequence>
			<xs:element name="SegmentList" type="SegmentListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>

	<xs:complexType name="SegmentListType">
		<xs:sequence>
			<xs:element name="Segment" type="SegmentType" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	
	<xs:complexType name="SegmentType">
		<xs:sequence>
			<xs:element name="DepartureTime" type="non-empty-string"  minOccurs="0"  />
			<xs:element name="ArriveTime" type="non-empty-string"  minOccurs="0"  />
			<xs:element name="Operator" type="non-empty-string"  minOccurs="0" />
			<xs:element name="FlightNumber" type="non-empty-string"  minOccurs="0" />
			<xs:element name="OriginAirport" type="non-empty-string"  minOccurs="0" />
			<xs:element name="DestinationAirport" type="non-empty-string"  minOccurs="0" />
			<xs:element name="Cabin" type="non-empty-string"  minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	
    <xs:complexType name="CreditCardType">
		<xs:sequence>
			<xs:element name="CardType" type="non-empty-string"  />
		</xs:sequence>
	</xs:complexType>
</xs:schema>