﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetBookingDetailsType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType" />
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Supplier" type="CommonSupplierIdentifier" minOccurs="0"/>
			<xs:element name="SourceRef" type="xs:string" minOccurs="0" />
			<xs:element name="AuthenticateByProfile" type="xs:string" minOccurs="0" />
			<xs:choice minOccurs="0">
				<xs:element name="TFBookingReference" type="xs:string" />
				<xs:element name="SupplierReference" type="xs:string" />
			</xs:choice>
			<xs:element name="AuthOption" type="AuthOptionType" minOccurs="0" />
			<xs:element name="IncludeBookingProfileHistory" type="xs:string" minOccurs="0" />
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="GetCountriesType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="GetCurrenciesType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Code" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="GetErrorCodesType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="LoginType">
		<xs:sequence>
			<xs:element name="Username" type="xs:string"/>
			<xs:element name="Password" type="xs:string"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="GetPrePackagedSupplierSummaryType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
            <xs:element name="Supplier" type="xs:string" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="GetHotelDetailsType">
		<xs:sequence>
			<!-- these 3 fields are not actually optional for submission, but they are made optional here for internal reasons -->
			<xs:element name="XmlLoginId" type="CommonLoginType" minOccurs="0"/>
			<xs:element name="LoginId" type="CommonLoginType" minOccurs="0"/>
			<xs:element name="HotelId" type="CommonId" minOccurs="0"/>
			<xs:element name="Language" type="xs:string" minOccurs="0"/>
			<xs:element name="SupplierList" type="CommonSupplierList" minOccurs="0"/>
			<xs:element name="IncludeHotelDetails" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="GetMultipleHotelDetailsType">
		<xs:sequence>
			<!-- these 3 fields are not actually optional for submission, but they are made optional here for internal reasons -->
			<xs:element name="XmlLoginId" type="CommonLoginType" minOccurs="0"/>
			<xs:element name="LoginId" type="CommonLoginType" minOccurs="0"/>
			<xs:element name="HotelIdList" type="HotelIdListType" minOccurs="0"/>
			<xs:element name="Language" type="xs:string" minOccurs="0"/>
			<xs:element name="SupplierList" type="CommonSupplierList" minOccurs="0"/>
			<xs:element name="IncludeHotelDetails" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="HotelIdListType">
		<xs:sequence>
			<xs:element name="HotelId" type="CommonId" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="LogPrePackagedReferralType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Supplier" type="xs:string"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<!-- -->
	<!-- NEXT REQUEST -->
	<!-- -->
	<xs:complexType name="ProcessCardVerificationType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType" />
			<xs:element name="LoginId" type="CommonLoginType" />
			<!-- 'Command' has been deprecated, but kept for backward compatibility -->
			<xs:element name="Command" type="xs:string" minOccurs="0" />
			<xs:element name="TFBookingReference" type="xs:string" />
			<xs:element name="ReturnUrl" type="xs:string" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
</xs:schema>