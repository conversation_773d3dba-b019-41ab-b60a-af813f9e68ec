﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StartManageBookingPlaneType">
		<xs:sequence>
			<xs:element name="HandlerSelect" type="xs:string" minOccurs="0" />
			<xs:element name="XmlLoginId" type="CommonLoginType" />
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Mode" type="CommonMode" />
			<xs:element name="Supplier" type="CommonSupplierIdentifier" />
			<xs:element name="TFBookingReference" type="xs:string" minOccurs="0" />
			<xs:element name="AuthOption" type="AuthOptionType" minOccurs="0" />
			<xs:element name="Timeout" type="xs:integer" />
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
	<xs:complexType name="AuthOptionType">
		<xs:sequence>
			<xs:element name="AuthOptionItemList">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="AuthOptionItem" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Name" type="xs:string" />
									<xs:element name="Value" type="xs:string" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
</xs:schema>