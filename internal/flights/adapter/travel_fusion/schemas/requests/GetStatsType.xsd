﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="GetStatsType">
		<xs:sequence>
			<xs:element name="HandlerSelect" type="xs:string" minOccurs="0"/>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="StatsType" type="StatsTypeEnumeration"/>
			<xs:element name="StartDate" type="xs:string"/>
			<xs:element name="EndDate" type="xs:string"/>
			<xs:element name="DateInterval" type="xs:integer"/>
			<xs:element name="EndUserName" type="xs:string"/>
			<xs:element name="SupplierSearchesParameters" type="SupplierSearchesParametersType" minOccurs="0"/>
			<xs:element name="ReferralsParameters" type="ReferralsParametersType" minOccurs="0"/>
			<xs:element name="CommandSummaryParameters" type="CommandSummaryParametersType" minOccurs="0"/>
			<xs:element name="UserSearchesParameters" type="UserSearchesParametersType" minOccurs="0"/>
			<xs:element name="Password" type="xs:string"/>
			<xs:element name="MinimumNumberOfItemsPerPeriod" type="xs:integer"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:simpleType name="StatsTypeEnumeration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="referrals"/>
			<xs:enumeration value="suppliersearches"/>
			<xs:enumeration value="usersearchsummary"/>
			<xs:enumeration value="simpleusersearchsummary"/>
			<xs:enumeration value="commandsummary"/>
			<xs:enumeration value="clearstatsdata"/>
			<xs:enumeration value="CommandSummary"/>
			<xs:enumeration value="ReferralSummary"/>
			<xs:enumeration value="UserSearchSummary"/>
			<xs:enumeration value="SupplierSearchSummary"/>
			<xs:enumeration value="ConvertStatsSummary"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="ReferralsParametersType">
		<xs:sequence>
			<!-- Can be 'all' -->
			<xs:element name="SupplierName" type="CommonSupplierIdentifier"/>
			<!-- Can be 'all' -->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommandSummaryParametersType">
		<xs:sequence>
			<!-- Can be 'all' -->
			<xs:element name="CommandName" type="xs:string"/>
			<!-- Can be 'all' -->
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SupplierSearchesParametersType">
		<xs:sequence>
			<xs:element name="ShowSupplierTotalsOnly" type="xs:boolean"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="UserSearchesParametersType">
		<xs:sequence>
			<xs:element name="Mode" type="xs:string"/>
			<xs:element name="Type" type="xs:string"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
