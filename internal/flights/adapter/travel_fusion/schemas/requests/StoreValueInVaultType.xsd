﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StoreValueInVaultRequestType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType" />
			<xs:element name="LoginId" type="CommonLoginType" />
			<xs:element name="Value" type="CommonString" />
			<xs:element name="TimeoutInSec" type="CommonPositiveInteger" />
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional" />
	</xs:complexType>
</xs:schema>