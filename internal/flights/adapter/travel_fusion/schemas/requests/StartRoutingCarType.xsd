﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StartRoutingCarType">
		<xs:sequence>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<xs:element name="Pickup" type="StartRoutingLocation"/>
			<xs:element name="Setdown" type="StartRoutingLocation"/>
			<xs:element name="PickupDates" type="StartRoutingDatesType"/>
			<xs:element name="SetdownDates" type="StartRoutingDatesType"/>
			<xs:element name="SupplierList" type="CommonSupplierList" minOccurs="0"/>
			<xs:element name="Timeout" type="xs:integer"/>
			<xs:element name="TravellerList" type="StartRoutingTravellerList"/>
			<xs:element name="IncrementalResults" type="xs:boolean" minOccurs="0"/>
			<xs:element name="BookingProfile" type="BookingProfileType" minOccurs="0"/>
			<xs:element name="PerformSearch" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
	</xs:complexType>
</xs:schema>

