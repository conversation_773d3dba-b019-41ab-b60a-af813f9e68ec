﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xs:complexType name="StartDetailsHotelType">
        <xs:sequence>
            <xs:element name="XmlLoginId" type="CommonLoginType"/>
            <xs:element name="LoginId" type="CommonLoginType"/>
            <xs:element name="RoutingId" type="CommonId"/>
            <xs:element name="OptionId" type="CommonId"/>
            <xs:element name="RoomExtraList" type="RoomExtraListDetailsType" minOccurs="0"/>
            <xs:element name="HandoffParametersOnly" type="xs:boolean" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="millis" type="xs:integer" use="optional"/>
    </xs:complexType>
    <xs:complexType name="RoomExtraListDetailsType">
		<xs:sequence>
			<xs:element name="RoomExtra" type="RoomExtra" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoomExtra">			
		<xs:sequence>
			<xs:element name="ExtraId" type="CommonId" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="roomId" type="xs:integer"/>
	</xs:complexType>
</xs:schema>
