﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="StartRoutingPrePackagedType">
		<xs:sequence>
			<xs:element name="HandlerSelect" type="xs:string" minOccurs="0"/>
			<xs:element name="XmlLoginId" type="CommonLoginType"/>
			<xs:element name="LoginId" type="CommonLoginType"/>
			<!-- WILL BE ONLY AN AIRPORT TLA FOR NOW. RADIUS SHOULD BE SET TO 0. If origin is omitted, no airport will be submitted to the supplier. This can only be done for those suppliers where no origin is returned in the supplier summary routes list -->
			<xs:element name="Origin" type="StartRoutingLocation" minOccurs="0"/>
			<!-- WILL BE ONLY A HOTEL RESORT FOR NOW. RADIUS SHOULD BE SET TO 0. The destination should only be omitted for suppliers where
				 the 'RequestDestinationsSeparately' element is false in the GetPrePackagedSupplierSummaryResponse -->
			<xs:element name="Destination" type="StartRoutingLocation" minOccurs="0"/>
			<!-- Only 2 adults are currently supported. Other passenger types not supported -->
			<xs:element name="NumberOfAdults" type="StartRoutingPrePackagedNumberOfAdultsType"/>
			<!-- date filters will be ignored -->
			<xs:element name="DepartureDates" type="StartRoutingDatesType"/>
			<!--e.g. if the departure date is 6th Oct, and the DepartureDateRange is 7, this means that ALL results will be obtained from the supplier with departure dates on any day from 6th Oct to 12th October inclusive. The range MUST NOT exceed, but may be less than, the 'RecommendedDepartureDateRange' stated in the 'GetPrePackagedSupplierSummaryResponseType'. If multiple suppliers are requested, this value should not be greater than the lowest RecommendedDepartureDateRange of all the requested suppliers-->
			<xs:element name="DepartureDateRange" type="xs:integer"/>
			<xs:element name="Duration" type="PrePackagedDurationType"/>
			<xs:element name="SupplierList" type="CommonSupplierList" minOccurs="0"/>
			<xs:element name="Timeout" type="xs:integer"/>
			<!-- For internal use V2 prefetcher -->
			<xs:element name="RequestOrdinalId" type="xs:integer" minOccurs="0"/>
			<!-- For the use of prepakcaged prefetcher -->
			<xs:element name="FromPrefetcher" type="xs:boolean" minOccurs="0"/> 
			<!--xs:element name="IncrementalResults" type="xs:boolean" minOccurs="0"/-->
		</xs:sequence>
		<xs:attribute name="millis" type="xs:integer" use="optional"/>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:simpleType name="StartRoutingPrePackagedNumberOfAdultsType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PrePackagedDurationType">
		<xs:sequence>
			<!-- Note that if the supplier supports 'all', then this means that all durations can be requested from the supplier in a single request. In this case, no other options will be supported by Travelfusion even if they are supported by the supplier -->
			<xs:element name="Type" type="PrePackagedDurationTypeType"/>
			<!-- Quantity will be ignored if type is 'weekend' or 'all'-->
			<xs:element name="Quantity" type="xs:integer"/>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PrePackagedDurationTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="all"/>
			<xs:enumeration value="weekend"/>
			<xs:enumeration value="week"/>
			<xs:enumeration value="day"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
