﻿<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--W3C Schema generated by XMLSpy v2005 rel. 3 U (http://www.altova.com)-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<xs:complexType name="BookingProfileType">
		<xs:sequence>
			<xs:element name="CustomSupplierParameterList" type="CommonCustomSupplierParameterList" minOccurs="0"/>
			<xs:element name="TravellerList" type="BookingProfileTravellerList" minOccurs="0"/>
			<xs:element name="ContactDetails" type="BookingProfileContactDetailsType" minOccurs="0"/>
			<xs:element name="BillingDetails" type="BookingProfileBillingDetailsType" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BookingProfileTravellerList">
		<xs:sequence>
			<xs:element name="Traveller" type="BookingProfileTravellerType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BookingProfileTravellerType">
		<xs:sequence>
			<xs:element name="Age" type="CommonAge" minOccurs="0"/>
			<xs:element name="Name" type="CommonName"/>
			<xs:element name="CustomSupplierParameterList" type="CommonCustomSupplierParameterList" minOccurs="0"/>
			<xs:element name="SplitTravellerId" type="CommonString" minOccurs="0"/>
		</xs:sequence>
		<!-- This is meant to be used only in GetBookingDetails and GetLatestBookingDetails response. -->
		<xs:attribute name="id" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="BookingProfileAddressType">
		<xs:sequence>
			<xs:element name="Company" type="CommonString"/>
			<xs:element name="Flat" type="CommonString"/>
			<xs:element name="BuildingName" type="CommonString"/>
			<xs:element name="BuildingNumber" type="CommonString"/>
			<xs:element name="Street" type="CommonString"/>
			<xs:element name="Locality" type="CommonString"/>
			<xs:element name="City" type="CommonString"/>
			<xs:element name="Province" type="CommonString"/>
			<xs:element name="Postcode" type="CommonString" minOccurs="0"/>
			<xs:element name="CountryCode" type="CommonString" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="BookingProfileBillingDetailsType">
		<xs:all>
			<xs:element name="Name" type="CommonName"/>
			<xs:element name="Address" type="BookingProfileAddressType" minOccurs="0"/>
			<xs:element name="CreditCard" type="BookingProfileCreditCardType" minOccurs="0"/>
		</xs:all>
	</xs:complexType>
	<xs:complexType name="BookingProfileContactDetailsType">
		<xs:sequence>
			<xs:element name="Name" type="CommonName"/>
			<xs:element name="Address" type="BookingProfileAddressType"/>
			<xs:element name="HomePhone" type="CommonPhone" minOccurs="0"/>
			<xs:element name="WorkPhone" type="CommonPhone" minOccurs="0"/>
			<xs:element name="MobilePhone" type="CommonPhone" minOccurs="0"/>
			<xs:element name="Fax" type="CommonPhone" minOccurs="0"/>
			<xs:element name="Email" type="CommonString"/>
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional"/>
		<xs:attribute name="etext" type="xs:string" use="optional"/>
		<xs:attribute name="edetail" type="xs:string" use="optional"/>
		<xs:attribute name="edate" type="xs:string" use="optional"/>
	</xs:complexType>
	<xs:complexType name="BookingProfileCreditCardType">
		<xs:sequence>
			<xs:element name="Company" type="CommonString"/>
			<xs:element name="NameOnCard" type="CommonName"/>
			<xs:element name="Number" type="CommonString"/>
			<xs:element name="SecurityCode" type="CommonString" minOccurs="0"/>
			<xs:element name="ExpiryDate" type="CommonString"/>
			<xs:element name="StartDate" type="CommonString" minOccurs="0"/>
			<xs:element name="CardType" type="CommonString"/>
			<xs:element name="IssueNumber" type="CommonString" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
