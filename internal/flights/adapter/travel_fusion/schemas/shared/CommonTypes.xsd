﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CommonSupplierList">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonSupplierIdentifier" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CommonSupplierIdentifier">
		<xs:restriction base="xs:string">
			<xs:minLength value="2" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CommonPositiveInteger">
		<xs:union memberTypes="xs:positiveInteger CommonEmptyElement"/>
	</xs:simpleType>
	<xs:simpleType name="CommonEmptyElement">
		<xs:restriction base="xs:string">
			<xs:enumeration value="" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CommonOriginAirportCodeList">
		<xs:sequence>
			<xs:element name="OriginAirportCode" type="CommonAirportCode" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommonDestinationAirportCodeList">
		<xs:sequence>
			<xs:element name="DestinationAirportCode" type="CommonAirportCode" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CommonAirportCode">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:simpleType name="CommonTypeOfList">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Exclude" />
			<xs:enumeration value="Include" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CommonBookingStatusType">
		<xs:restriction base="xs:string">
			<!-- TERMS -->
			<xs:enumeration value="BookingCreated" />
			<xs:enumeration value="TermsStartFailed" />
			<xs:enumeration value="TermsInProgress" />
			<xs:enumeration value="TermsFailed" />
			<xs:enumeration value="TermsSucceeded" />
			<!-- BOOKING -->
			<xs:enumeration value="Succeeded" />
			<xs:enumeration value="BookingInProgress" />
			<xs:enumeration value="Failed" />
			<xs:enumeration value="Unconfirmed" />
			<xs:enumeration value="UnconfirmedBySupplier" />
			<xs:enumeration value="BookingOnHold" />
			<!-- Internal statuses - should not be returned to customer -->
			<xs:enumeration value="ErrorStartingBooking" />
			<xs:enumeration value="Duplicate" />
			<xs:enumeration value="Cancelled" />
			<xs:enumeration value="ErrorProcessingBooking" />
			<xs:enumeration value="BookingNotLaunched" />
			<xs:enumeration value="BookingSuperseded" />
			<!-- BOOKING CHANGE -->
			<xs:enumeration value="BookingChanged" />
			<xs:enumeration value="BookingChangeUnconfirmed" />
			<xs:enumeration value="BookingChangeFailed" />
			<xs:enumeration value="BookingChangeSucceeded" />
			<!-- CANCEL -->
			<xs:enumeration value="CancelUnconfirmed" />
			<xs:enumeration value="CancelFailed" />
			<xs:enumeration value="CancelSucceeded" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CommonBookingV2StatusType">
		<xs:simpleContent>
			<xs:restriction base="CommonString">
				<!-- TERMS -->
				<xs:enumeration value="BookingCreated" />
				<xs:enumeration value="TermsInProgress" />
				<xs:enumeration value="TermsStartFailed" />
				<xs:enumeration value="TermsUnconfirmed" />
				<xs:enumeration value="TermsFailed" />
				<xs:enumeration value="TermsSucceeded" />
				<!-- BOOKING -->
				<xs:enumeration value="BookingInProgress" />
				<xs:enumeration value="Succeeded" />
				<xs:enumeration value="Failed" />
				<xs:enumeration value="FailedByTF" />
				<xs:enumeration value="Unconfirmed" />
				<xs:enumeration value="UnconfirmedBySupplier" />
				<xs:enumeration value="UnconfirmedByTF" />
				<xs:enumeration value="BookingOnHold" />
				<!-- Internal statuses - should not be returned to customer -->
				<xs:enumeration value="Cancelled" />
				<xs:enumeration value="Duplicate" />
				<xs:enumeration value="DuplicateFailed" />
				<xs:enumeration value="ErrorStartingBooking" />
				<xs:enumeration value="ErrorProcessingBooking" />
				<xs:enumeration value="BookingNotLaunched" />
				<xs:enumeration value="BookingSuperseded" />
				<!-- BOOKING CHANGE -->
				<xs:enumeration value="BookingChanged" />
				<xs:enumeration value="BookingChangeUnconfirmed" />
				<xs:enumeration value="BookingChangeFailed" />
				<xs:enumeration value="BookingChangeSucceeded" />
				<!-- CANCEL -->
				<xs:enumeration value="CancelInProgress" />
				<xs:enumeration value="CancelUnconfirmed" />
				<xs:enumeration value="CancelUnconfirmedBySupplier" />
				<xs:enumeration value="CancelUnconfirmedByTF" />
				<xs:enumeration value="CancelFailed" />
				<xs:enumeration value="CancelFailedByTF" />
				<xs:enumeration value="CancelSucceeded" />
			</xs:restriction>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="CommonFinalBookingStatusType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Succeeded" />
			<xs:enumeration value="Failed" />
			<xs:enumeration value="Unconfirmed" />
			<xs:enumeration value="UnconfirmedBySupplier" />
			<xs:enumeration value="BookingChanged" />
			<xs:enumeration value="BookingChangeSucceeded" />
			<xs:enumeration value="BookingChangeFailed" />
			<xs:enumeration value="BookingChangeUnconfirmed" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CommonOperatorCode">
		<xs:restriction base="xs:string">
			<xs:maxLength value="2" />
			<xs:minLength value="2" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CommonExtendedOperatorCode">
		<xs:restriction base="xs:string">
			<!-- Actually there should be no max because the spec is 2chars and a number, but I don't know how to specify unbounded restrictions (maybe omit?) -->
			<xs:maxLength value="10" />
			<xs:minLength value="2" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CommonLocationTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Capital" />
			<xs:enumeration value="Capital Of Region" />
			<xs:enumeration value="City" />
			<xs:enumeration value="Airport" />
			<xs:enumeration value="Trainstation" />
			<xs:enumeration value="Populated Place" />
			<xs:enumeration value="Section of City" />
			<xs:enumeration value="Locality" />
			<xs:enumeration value="Hotel" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CommonTLA">
		<xs:restriction base="xs:string">
			<xs:maxLength value="3" />
			<xs:minLength value="3" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="TF7Alpha">
		<xs:restriction base="xs:string">
			<xs:maxLength value="7" />
			<xs:minLength value="3" />
		</xs:restriction>
	</xs:simpleType>
	<!-- A TrainCode (four letter acronym) can be between 3 and 4 characters, and is
		used mainly for Station codes -->
	<xs:simpleType name="TrainCode">
		<xs:restriction base="xs:string">
			<xs:maxLength value="6" />
			<xs:minLength value="3" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CommonFullCountryType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="CommonString" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommonFullCountryTypeWithDetails">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="CommonString" />
			<xs:element name="Region" type="WorldRegionType" />
			<xs:element name="CurrencyList" type="CountryCurrencyList" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="WorldRegionType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Africa" />
			<xs:enumeration value="Antarctic Region" />
			<xs:enumeration value="Arctic Region" />
			<xs:enumeration value="Asia" />
			<xs:enumeration value="Central America and the Caribbean" />
			<xs:enumeration value="Europe" />
			<xs:enumeration value="Global" />
			<xs:enumeration value="Middle East" />
			<xs:enumeration value="North America" />
			<xs:enumeration value="Oceania" />
			<xs:enumeration value="South America" />
			<xs:enumeration value="Southeast Asia" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CountryCurrencyList">
		<xs:sequence>
			<xs:element name="Currency" type="CountryCurrency" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CountryCurrency">
		<xs:sequence>
			<xs:element name="Code" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CommonMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="plane" />
			<xs:enumeration value="Plane" />
			<xs:enumeration value="hotel" />
			<xs:enumeration value="Hotel" />
			<xs:enumeration value="car" />
			<xs:enumeration value="Car" />
			<xs:enumeration value="prepackaged" />
			<xs:enumeration value="train" />
			<xs:enumeration value="Train" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="non-empty-string">
		<xs:restriction base="xs:string">
			<xs:minLength value="1" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CommonId">
		<xs:simpleContent>
			<xs:extension base="non-empty-string">
				<!-- allowing ecode and etext since it is common for customers to send bad/ expired routing ids and we then return an error code -->
				<xs:attribute name="ecode" type="xs:string" use="optional" />
				<xs:attribute name="etext" type="xs:string" use="optional" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:simpleType name="CommonLoginType">
		<xs:restriction base="xs:string" />
	</xs:simpleType>
	<xs:complexType name="CommonName">
		<xs:sequence>
			<xs:element name="Title" type="CommonTitleType" minOccurs="0" />
			<xs:element name="NamePartList" type="CommonNamePartListType" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CommonNamePartType">
		<xs:restriction base="xs:normalizedString">
			<xs:minLength value="0" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CommonNamePartListType">
		<xs:sequence>
			<xs:element name="NamePart" type="CommonNamePartType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CommonTitleType">
		<xs:restriction base="xs:string">
			<xs:minLength value="0" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CommonPhone">
		<xs:sequence>
			<!-- NOTE THAT THESE 4 FIELDS ARE NOT OPTIONAL. THEY WILL ONLY BE OMITTED IN THE TFRESPONSE WHEN A DATA VALIDATION FAILURE OCCURS. -->
			<!-- THEY MUST ALL BE SUBMITTED IN ANY REQUEST TO TRAVELFUSION -->
			<xs:element name="InternationalCode" type="CommonString" minOccurs="0" />
			<xs:element name="AreaCode" type="CommonString" minOccurs="0" />
			<xs:element name="Number" type="CommonString" minOccurs="0" />
			<xs:element name="Extension" type="CommonString" minOccurs="0" />
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional" />
		<xs:attribute name="etext" type="xs:string" use="optional" />
		<xs:attribute name="edetail" type="xs:string" use="optional" />
		<xs:attribute name="edate" type="xs:string" use="optional" />
	</xs:complexType>
	<xs:complexType name="CommonCoordinates">
		<xs:sequence>
			<xs:element name="Latitude" type="xs:decimal" />
			<xs:element name="Longitude" type="xs:decimal" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CommonAge">
		<xs:restriction base="xs:integer" />
	</xs:simpleType>
	<xs:complexType name="CustomSupplierParameterType">
		<xs:sequence>
			<xs:element name="Supplier" type="CommonSupplierIdentifier" minOccurs="0" />
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Value">
				<xs:complexType>
					<xs:simpleContent>
						<xs:extension base="xs:string">
							<xs:attribute name="ecode" type="xs:string" use="optional" />
							<xs:attribute name="etext" type="xs:string" use="optional" />
						</xs:extension>
					</xs:simpleContent>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommonCustomSupplierParameterList">
		<xs:sequence>
			<xs:element name="CustomSupplierParameter" type="CustomSupplierParameterType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommonSupplierResponseListType">
		<xs:sequence>
			<xs:element name="SupplierResponse" type="CommonSupplierResponseType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommonSupplierResponseType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Type" type="CommonSupplierResponseDataType" />
			<xs:element name="Data" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="CommonSupplierResponseDataType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="xml" />
			<xs:enumeration value="html" />
			<xs:enumeration value="other" />
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="CommonLocationResolutionPlaceType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Capital" />
			<xs:enumeration value="Capital Of Region" />
			<xs:enumeration value="City" />
			<xs:enumeration value="Airport" />
			<xs:enumeration value="Populated Place" />
			<xs:enumeration value="Section of City" />
			<xs:enumeration value="Locality" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="CommonString">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="ecode" type="xs:string" use="optional" />
				<xs:attribute name="etext" type="xs:string" use="optional" />
				<xs:attribute name="edetail" type="xs:string" use="optional" />
				<xs:attribute name="edate" type="xs:string" use="optional" />
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<xs:complexType name="LuggageFeesListType">
		<xs:sequence>
			<xs:element name="Item" type="LuggageFeesListItemType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LuggageFeesListItemType">
		<xs:sequence>
			<xs:element name="Quantity" type="xs:integer" minOccurs="0" maxOccurs="1" />
			<xs:element name="Weight" type="xs:integer" minOccurs="0" maxOccurs="1" />
			<xs:element name="MaxWeight" type="xs:integer" minOccurs="0" maxOccurs="1" />
			<xs:element name="MaxQuantity" type="xs:integer" minOccurs="0" maxOccurs="1" />
			<xs:element name="Amount" type="xs:decimal" minOccurs="1" maxOccurs="1" />
			<xs:element name="Currency" type="CommonTLA" minOccurs="1" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SpeedyBoardingFeesListType">
		<xs:sequence>
			<xs:element name="Item" type="SpeedyBoardingFeesListItemType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SpeedyBoardingFeesListItemType">
		<xs:sequence>
			<xs:element name="Amount" type="xs:decimal" minOccurs="1" maxOccurs="1" />
			<xs:element name="Currency" type="CommonTLA" minOccurs="1" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CheckInFeesListType">
		<xs:sequence>
			<xs:element name="Item" type="CheckInFeesListItemType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CheckInFeesListItemType">
		<xs:sequence>
		    <xs:element name="Type" type="xs:string" minOccurs="0" maxOccurs="1" />
			<xs:element name="Amount" type="xs:decimal" minOccurs="1" maxOccurs="1" />
			<xs:element name="Currency" type="CommonTLA" minOccurs="1" maxOccurs="1" />
		</xs:sequence>
	</xs:complexType>
</xs:schema>