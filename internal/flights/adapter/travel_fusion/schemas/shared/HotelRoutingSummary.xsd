﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="HotelRoutingSummaryType">
		<xs:sequence>
			<xs:element name="Destination" type="FullLocationType"/>
			<xs:element name="CheckinDate" type="CommonString"/>
			<xs:element name="CheckoutDate" type="CommonString" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
