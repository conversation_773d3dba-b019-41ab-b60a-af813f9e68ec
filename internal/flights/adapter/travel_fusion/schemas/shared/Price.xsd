﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CommonPrice">
		<xs:sequence>
			<!-- Type will appear only for room extras to indicate if the price is per adult/child/infant/room/booking. -->
			<xs:element name="Type" type="PriceTypeType" minOccurs="0"/>
			<!-- Amount and currency are optional for cases where a group tax is supplied but not a group price -->			
			<xs:element name="Amount" type="xs:decimal" minOccurs="0"/>
			<xs:element name="Currency" type="CommonTLA" minOccurs="0"/>
			<xs:element name="BillingAmount" type="xs:decimal" minOccurs="0"/>
			<xs:element name="BillingCurrency" type="CommonTLA" minOccurs="0"/>
			<xs:element name="AmountWithoutTax" type="xs:decimal" minOccurs="0"/>
			<xs:element name="PriceIncludesTax" type="xs:boolean" minOccurs="0"/>
			<xs:element name="TaxIsEstimated" type="xs:boolean" minOccurs="0"/>
			<xs:element name="TaxItemList" type="PriceTaxItemListType" minOccurs="0"/>
			<xs:element name="PassengerPriceList" type="PricePassengerPriceListType" minOccurs="0"/>
			<xs:element name="Age" type="CommonAge" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="EnhancedPriceWithLuggage">
		<xs:sequence>
			<xs:element name="Amount" type="xs:decimal"/>
			<xs:element name="Currency" type="CommonTLA"/>
			<xs:element name="MinimumCardCharge" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Amount" type="xs:decimal" />
						<xs:element name="Currency" type="CommonTLA" />
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="PriceTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Adult"/>
			<xs:enumeration value="Child"/>
			<xs:enumeration value="Infant"/>
			<xs:enumeration value="Person"/>
			<xs:enumeration value="Night"/>
			<xs:enumeration value="Room"/>
			<xs:enumeration value="Booking"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="PriceTaxItemListType">
		<xs:sequence>
			<xs:element name="TaxItem" type="TaxItemType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="TaxItemType">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" minOccurs="0" />
			<xs:element name="Code" type="xs:string" minOccurs="0" />
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Amount" type="xs:decimal" nillable="true"/>
			<xs:element name="Currency" type="CommonTLA"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="PricePassengerPriceListType">
		<xs:sequence>
			<xs:element name="PassengerPrice" type="CommonPrice" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommonSimplePrice">
		<xs:sequence>
			<xs:element name="Amount" type="xs:decimal"/>
			<xs:element name="Currency" type="CommonTLA"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
