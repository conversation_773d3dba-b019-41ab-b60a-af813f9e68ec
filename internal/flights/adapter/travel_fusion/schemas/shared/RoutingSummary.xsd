﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="RoutingSummaryType">
		<xs:sequence>
			<xs:element name="Origin" type="FullLocationType" />
			<xs:element name="Destination" type="FullLocationType" />
			<xs:element name="Distance" type="xs:string" />
			<xs:element name="OutwardDate" type="CommonString" />
			<xs:choice minOccurs="0" maxOccurs="1">
			  <xs:element name="ReturnDate" type="CommonString"/>
			  <xs:element name="AdditionalTripList" type="RoutingSummaryAdditionalTripListType"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FullLocationType">
		<xs:sequence>
			<xs:element name="Location" type="LocationWithoutLists" />
			<xs:element name="AirportList" type="RoutingSummaryAirportListType" minOccurs="0" />
			<xs:element name="CityList" type="RoutingSummaryCityListType" minOccurs="0" />
			<xs:element name="StationList" type="RoutingSummaryStationListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="LocationWithoutLists">
		<xs:sequence>
			<xs:element name="Id" type="xs:integer" minOccurs="0" />
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Country" type="CommonFullCountryType" />
			<!--xs:element name="Country" type="xs:string"/-->
			<!--xs:element name="Text" type="xs:string"/-->
			<xs:element name="Type" type="CommonLocationTypeType" />
			<xs:element name="City" type="FullAirportOrCityTypeWithOptionalCode" minOccurs="0" />
			<xs:element name="Airport" type="FullAirportOrCityTypeWithCompulsoryCode" minOccurs="0" />
			<xs:element name="Station" type="FullAirportOrCityTypeWithOptionalCode" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FullAirportOrCityTypeWithOptionalCode">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="TF7Alpha" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FullAirportOrCityTypeWithCompulsoryCode">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="CommonTLA" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoutingSummaryAirportListType">
		<xs:sequence>
			<xs:element name="Airport" type="FullAirportWithCityAndDistanceType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FullAirportWithCityAndDistanceType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="CommonTLA" />
			<!--SHOULD BE COMPULSORY CITY CODE - waiting for milan/ rome fix-->
			<xs:element name="City" type="FullAirportOrCityTypeWithOptionalCode" minOccurs="0" />
			<xs:element name="Distance" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoutingSummaryCityListType">
		<xs:sequence>
			<xs:element name="City" type="FullCityWithDistanceType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoutingSummaryStationListType">
		<xs:sequence>
			<xs:element name="Station" type="FullStationWithCityType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FullStationWithCityType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="TrainCode" />
			<xs:element name="City" type="FullStationCityWithDistanceType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FullStationCityWithDistanceType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="TrainCode" minOccurs="0" />
			<xs:element name="Distance" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<!-- changed Code to TF7Alpha to allow extended tfcitycode type -->
	<xs:complexType name="FullCityWithDistanceType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="TF7Alpha" />
			<xs:element name="Distance" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RoutingSummaryAdditionalTripListType">
		<xs:sequence>
			<xs:element name="AdditionalTrip" type="RoutingSummaryType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>