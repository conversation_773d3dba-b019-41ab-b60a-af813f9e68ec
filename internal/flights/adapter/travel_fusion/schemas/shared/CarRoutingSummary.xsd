﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CarRoutingSummaryType">
		<xs:sequence>
			<xs:element name="Pickup" type="FullLocationType"/>
			<xs:element name="Setdown" type="FullLocationType"/>
			<xs:element name="Distance" type="xs:string"/>
			<xs:element name="PickupDate" type="CommonString"/>
			<xs:element name="SetdownDate" type="CommonString" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
