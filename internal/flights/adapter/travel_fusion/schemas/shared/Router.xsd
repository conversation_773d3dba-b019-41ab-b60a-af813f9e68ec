﻿<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:complexType name="CommonRouterList">
		<xs:sequence>
			<xs:element name="Router" type="CommonRouter" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CommonRouter">
		<xs:sequence>
			<xs:element name="RequiredParameterList" type="RouterRequiredParameterListType" minOccurs="0" />
			<xs:element name="Supplier" type="CommonSupplierIdentifier" />
			<xs:element name="ProductType" type="CommonMode" minOccurs="0"/>
			<xs:element name="LogoNameSuffix" type="xs:string" minOccurs="0" />
			<!-- Vendor will be omitted in rare cases where old Router XML has been stored (before the Vendor element was introduced) -->
			<xs:element name="Vendor" type="RouterVendorType" minOccurs="0" />
			<xs:element name="Complete" type="xs:boolean" />
			<xs:element name="RequestedLocations" type="RouterRequestedLocationsType" />
			<xs:element name="AdditionalTripRequestedLocationsList" type="RouterAdditionalTripsRequestedLocationsType" minOccurs="0"/>
			<xs:element name="GroupList" type="RouterGroupListType" minOccurs="0" />
			<xs:element name="AlternativeFares" type="RouterAlternativeGroupsType" minOccurs="0" />
			<xs:element name="Features" type="RouterSupplierFeatureListType" minOccurs="0" />
			<xs:element name="SupplierInfoList" type="RouterSupplierInfoListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterRequiredParameterListType">
		<xs:sequence>
			<xs:element name="RequiredParameter" type="RouterRequiredParameterType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterRequiredParameterType">
		<xs:sequence>
			<xs:element name="PerRoom" type="xs:boolean" minOccurs="0" />
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Type" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="text" />
						<xs:enumeration value="boolean" />
						<xs:enumeration value="formatted_text" />
						<xs:enumeration value="value_select" />
						<xs:enumeration value="custom" />
						<xs:enumeration value="notice" />
						<xs:enumeration value="multi_select" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="DisplayText" type="xs:string" />
			<xs:element name="PerPassenger" type="xs:boolean" />
			<xs:element name="IsOptional" type="xs:string" minOccurs="0" />
			<xs:element name="IsSometimesRequired" type="xs:string" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<!-- : -->
	<!-- RequestedLocations -->
	<!-- : -->
	<xs:complexType name="RouterRequestedLocationsType">
		<xs:sequence>
			<xs:element name="Origin" type="RequestedLocationsLocationType" />
			<xs:element name="Destination" type="RequestedLocationsLocationType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterAdditionalTripsRequestedLocationsType">
			<xs:sequence maxOccurs="unbounded">
			<xs:element name="AdditionalTripRequestedLocations" type="RouterRequestedLocationsType" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RequestedLocationsLocationType">
		<xs:sequence>
			<xs:element name="Type" type="RequestedLocationsTypeType" />
			<!-- the 'Code' will be a TLA for airport, city or trainstation types only. Otherwise it may be free-text -->
			<xs:element name="Code" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="RequestedLocationsTypeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="airport" />
			<xs:enumeration value="city" />
			<xs:enumeration value="trainstation" />
		</xs:restriction>
	</xs:simpleType>
	<!-- : -->
	<!-- Features -->
	<!-- : -->
	<xs:complexType name="RouterSupplierFeatureListType">
		<xs:sequence>
			<xs:element name="Feature" type="RouterSupplierFeatureType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterSupplierFeatureType">
		<xs:sequence>
			<xs:element name="Option" type="RouterSupplierFeatureOptionType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="Type" type="xs:string" use="required" />
		<xs:attribute name="Label" type="xs:string" use="optional" />
	</xs:complexType>
	<xs:complexType name="RouterSupplierFeatureOptionType">
		<xs:sequence>
			<xs:element name="Condition" type="RouterSupplierFeatureConditionType" minOccurs="0" maxOccurs="unbounded" />
			<xs:element name="Dependency" type="RouterSupplierFeatureDependencyType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="Id" type="xs:nonNegativeInteger" use="required" />
		<xs:attribute name="Usable" type="xs:string" use="optional" />
		<xs:attribute name="Currency" type="xs:string" use="optional" />
		<xs:attribute name="Percentage" type="xs:decimal" use="optional" />
		<xs:attribute name="Value" type="xs:decimal" use="optional" />
		<xs:attribute name="MinValue" type="xs:decimal" use="optional" />
		<xs:attribute name="MaxValue" type="xs:decimal" use="optional" />
	</xs:complexType>
	<xs:complexType name="RouterSupplierFeatureConditionType">
		<xs:attribute name="Type" type="xs:string" use="required" />
		<xs:attribute name="Value" type="xs:string" use="optional" />
	</xs:complexType>
	<xs:complexType name="RouterSupplierFeatureDependencyType">
		<xs:attribute name="Type" type="xs:string" use="required" />
	</xs:complexType>
	<!-- : -->
	<!-- GroupList -->
	<!-- : -->
	<xs:complexType name="RouterGroupListType">
		<xs:sequence>
			<xs:element name="Group" type="RouterGroupType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
		<xs:attribute name="ecode" type="xs:string" use="optional" />
		<xs:attribute name="etext" type="xs:string" use="optional" />
		<xs:attribute name="edetail" type="xs:string" use="optional" />
		<xs:attribute name="edate" type="xs:string" use="optional" />
	</xs:complexType>
	<xs:complexType name="RouterAlternativeGroupsType">
		<xs:sequence>
			<xs:element name="GroupList" type="RouterGroupListType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterGroupType">
		<xs:sequence>
			<xs:element name="Id" type="CommonId" />
			<xs:element name="OutwardList" type="RouterOutwardListType" />
			<xs:element name="ReturnList" type="RouterReturnListType" minOccurs="0" />
			<xs:element name="Price" type="CommonPrice" minOccurs="0" />
			<xs:element name="ChangePrice" type="CommonPrice" minOccurs="0" />
			<xs:element name="PriceWithLuggage" type="EnhancedPriceWithLuggage" minOccurs="0" />
			<xs:element name="RefundPrice" type="CommonPrice" minOccurs="0" />
			<xs:element name="SupplierInfoList" type="RouterSupplierInfoListType" minOccurs="0" />
			<xs:element name="BookingOnHold" type="BookingOnHoldType" minOccurs="0" />
			<xs:element name="SubMerchant" type="xs:string" minOccurs="0" />
			<xs:element name="MinimumCardChargeList" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="MinimumCardCharge" minOccurs="1" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<!-- If OutwardId and ReturnId not present, minimum card charge is same for all possible leg combination's of the particular group -->
									<xs:element name="OutwardId" type="CommonId" minOccurs="0" />
									<xs:element name="ReturnId" type="CommonId" minOccurs="0" />
									<xs:element name="Amount" type="xs:decimal" />
									<xs:element name="Currency" type="CommonTLA" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CardChargeList" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CardCharge" minOccurs="1" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<!-- If OutwardId and ReturnId not present, card charge is same for all possible leg combination's of the particular group -->
									<xs:element name="OutwardId" type="CommonId" minOccurs="0" />
									<xs:element name="ReturnId" type="CommonId" minOccurs="0" />
									<xs:element name="Type" type="CommonString" />
									<xs:element name="Amount" type="xs:decimal" />
									<xs:element name="Currency" type="CommonTLA" />
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CardTypeChargeList" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CardTypeCharge" minOccurs="1" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="Type" type="CommonString" />
									<xs:choice>
										<xs:sequence>
											<xs:element name="Amount" type="xs:decimal" />
											<xs:element name="Currency" type="CommonTLA" />
										</xs:sequence>
										<xs:element name="Percentage" type="xs:decimal" />
										<xs:sequence>
											<xs:element name="Matrix" type="xs:string" />
											<xs:element name="Currency" type="CommonTLA" />
										</xs:sequence>
									</xs:choice>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="LuggageChargeList" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="LuggageCharge" minOccurs="1" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CombinationList" type="CombinationListType" minOccurs="0"/>
									<xs:element name="ItemList" type="LuggageFeesListType"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="SpeedyBoardingChargeList" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="SpeedyBoardingCharge" minOccurs="1" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CombinationList" type="CombinationListType" minOccurs="0"/>
									<xs:element name="ItemList" type="SpeedyBoardingFeesListType"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CheckInChargeList" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CheckInCharge" minOccurs="1" maxOccurs="unbounded">
							<xs:complexType>
								<xs:sequence>
									<xs:element name="CombinationList" type="CombinationListType" minOccurs="0"/>
									<xs:element name="ItemList" type="CheckInFeesListType"/>
								</xs:sequence>
							</xs:complexType>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterOutwardListType">
		<xs:sequence>
			<xs:element name="Outward" type="FlightLegData" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterReturnListType">
		<xs:sequence>
			<xs:element name="Return" type="FlightLegData" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="FlightLegData">
		<xs:sequence>
			<xs:element name="Id" type="CommonId" />
			<xs:element name="Index" type="xs:integer" minOccurs="0" />
			<xs:element name="CacheInfo" type="CacheInfoType" />
			<xs:element name="Price" type="CommonPrice" minOccurs="0" />
			<xs:element name="ChangePrice" type="CommonPrice" minOccurs="0" />
			<xs:element name="PriceWithLuggage" type="EnhancedPriceWithLuggage" minOccurs="0" />
			<!-- omitted if duration cannot be calculated -->
			<xs:element name="Duration" type="xs:integer" minOccurs="0" />
			<xs:element name="CO2EmissionsTonnes" type="xs:decimal" minOccurs="0" />
			<xs:element name="SeatsRemaining" type="xs:integer" minOccurs="0" />
			<xs:element name="StopList" type="RouterStopListType" minOccurs="0" />
			<xs:element name="SegmentList" type="RouterSegmentListType" minOccurs="0" />
			<!-- If segments data compression is requested 'SegmentList' will be replaced with 'CompressedSegmentList' -->
			<xs:element name="CompressedSegmentList" type="RouterCompressedSegmentListType" minOccurs="0" />
			<!-- optional for prepackaged -->
			<xs:element name="Vendor" type="RouterVendorType" minOccurs="0" />
			<xs:element name="SupplierInfoList" type="RouterSupplierInfoListType" minOccurs="0" />
			<xs:element name="BookingOnHold" type="BookingOnHoldType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CacheInfoType">
		<xs:sequence>
			<xs:element name="CacheDataAgeSeconds" type="xs:integer" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterStopListType">
		<xs:sequence>
			<xs:element name="Stop" type="RouterStopType" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterStopType">
		<xs:sequence>
			<xs:element name="ArriveDate" type="CommonString" minOccurs="0" />
			<xs:element name="DepartDate" type="CommonString" minOccurs="0" />
			<!-- omitted if duration cannot be calculated -->
			<xs:element name="Duration" type="xs:integer" minOccurs="0" />
			<xs:element name="Location" type="SegmentLocationType" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterSegmentListType">
		<xs:sequence>
			<xs:element name="Segment" type="RouterSegmentType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterCompressedSegmentListType">
		<xs:sequence>
			<xs:element name="CompressedSegment" type="xs:string" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterSegmentType">
		<xs:sequence>
			<xs:element name="Origin" type="SegmentLocationType" minOccurs="0" />
			<xs:element name="Destination" type="SegmentLocationType" minOccurs="0" />
			<xs:element name="DepartDate" type="CommonString" minOccurs="0" />
			<xs:element name="ArriveDate" type="CommonString" minOccurs="0" />
			<!-- omitted if duration cannot be calculated -->
			<xs:element name="Duration" type="xs:integer" minOccurs="0" />
			<!-- If the Operator is specified, the the TfOperator should be as well -->
			<!-- <TfOperator> is introduced to try to give out a standard operator name and code -->
			<xs:element name="TfOperator" type="RouterTFOperator" minOccurs="0" />
			<!-- TfVendingOperator: see comments above for TfOperator. TfVendingOperator corresponds to VendingOperator as TfOperator corresponds to Operator -->
			<xs:element name="TfVendingOperator" type="RouterTFOperator" minOccurs="0" />
			<xs:element name="Operator" type="RouterOperatorFromSupplier" minOccurs="0" />
			<xs:element name="VendingOperator" type="RouterOperatorFromSupplier" minOccurs="0" />
			<xs:element name="FlightId" type="RouterFlightIdType" minOccurs="0" />
			<xs:element name="TravelClass" type="RouterTravelClassType" minOccurs="0" />
			<xs:element name="AircraftType" type="RouterAircraftType" minOccurs="0" />
			<xs:element name="SupplierInfoList" type="RouterSupplierInfoListType" minOccurs="0" />
			<xs:element name="StopList" type="RouterStopListType" minOccurs="0" />
			<xs:element name="SegmentMayEndWithAStop" type="xs:boolean" minOccurs="0" />
		</xs:sequence>
		<!-- This is meant to be used only in GetBookingDetails and GetLatestBookingDetails response. -->
		<xs:attribute name="id" type="xs:integer" use="optional"/>
	</xs:complexType>
	<xs:complexType name="SegmentLocationType">
		<xs:sequence>
			<xs:element name="Type" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="city" />
						<xs:enumeration value="airport" />
						<xs:enumeration value="trainstation" />
						<xs:enumeration value="other" />
						<xs:enumeration value="supplierLocationIdentifier" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<!-- change Code to TF7Alpha to get compatibility with tfcitycode type -->
			<xs:element name="Code" type="TF7Alpha" minOccurs="0" />
			<xs:element name="Terminal" type="xs:string" minOccurs="0" />
			<xs:element name="SupplierDisplayName" type="xs:string" minOccurs="0" />
			<!-- JUST FOR CARS AT THE MOMENT -->
			<!--xs:element name="LocationDetails" type="SegmentLocationDetailsType" minOccurs="0"/ -->
			<xs:element name="LocationDetailsId" type="xs:string" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="SegmentLocationDetailsType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="0" />
			<xs:element name="Address" type="xs:string" minOccurs="0" />
			<xs:element name="Phone" type="xs:string" minOccurs="0" />
			<xs:element name="Fax" type="xs:string" minOccurs="0" />
			<xs:element name="OpeningHours" type="xs:string" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterFlightIdType">
		<xs:sequence>
			<xs:element name="Code" type="xs:string" />
			<xs:element name="Number" type="xs:integer" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterTravelClassType">
		<xs:sequence>
			<xs:element name="TfClass" type="TfClassEnumerationType" />
			<xs:element name="SupplierClass" type="xs:string" />
			<xs:element name="SupplierClassDescription" type="xs:string" minOccurs="0"/>
			<xs:element name="SupplierFareBasisCode" type="xs:string" minOccurs="0" />
			<xs:element name="SupplierRBDCode" type="xs:string" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterAircraftType">
		<xs:sequence>
			<xs:element name="AircraftName" type="xs:string" minOccurs="0" />
			<xs:element name="AircraftCode" type="xs:string" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="TfClassEnumerationType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Economy With Restrictions" />
			<xs:enumeration value="Economy Without Restrictions" />
			<xs:enumeration value="Economy Premium" />
			<xs:enumeration value="Business" />
			<xs:enumeration value="First" />
		</xs:restriction>
	</xs:simpleType>
	<xs:complexType name="RouterOperatorFromSupplier">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="0" />
			<xs:element name="Code" type="CommonOperatorCode" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterTFOperator">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Code" type="CommonExtendedOperatorCode" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterVendorType">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" />
			<xs:element name="Url" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<!-- : -->
	<!-- SupplierInfoList -->
	<!-- : -->
	<xs:complexType name="RouterSupplierInfoListType">
		<xs:sequence>
			<xs:element name="SupplierInfo" type="RouterSupplierInfoType" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="RouterSupplierInfoType">
		<xs:sequence>
			<xs:element name="DisplayName" type="xs:string" />
			<xs:element name="InfoType">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="text" />
						<xs:enumeration value="remark" />
						<xs:enumeration value="url" />
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="Info" type="xs:string" />
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CombinationListType">
		<xs:sequence>
			<xs:element name="Combination" type="CombinationType" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="CombinationType">
		<xs:sequence>
			<xs:element name="OutwardId" type="CommonId"/>
			<xs:element name="ReturnId" type="CommonId" minOccurs="0" />
		</xs:sequence>
	</xs:complexType>
	<xs:simpleType name="BookingOnHoldType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="yes" />
			<xs:enumeration value="no" />
			<xs:enumeration value="maybe" />
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
