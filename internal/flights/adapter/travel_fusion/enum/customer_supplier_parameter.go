package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
)

type CustomSupplierParameterType string

const (
	CustomSupplierParameterTypeNome                   CustomSupplierParameterType = ""
	CustomSupplierParameterTypeDateOfBirth            CustomSupplierParameterType = "DateOfBirth"
	CustomSupplierParameterTypeNationality            CustomSupplierParameterType = "Nationality"
	CustomSupplierParameterTypePassportNumber         CustomSupplierParameterType = "PassportNumber"
	CustomSupplierParameterTypePassportExpiryDate     CustomSupplierParameterType = "PassportExpiryDate"
	CustomSupplierParameterTypePassportCountryOfIssue CustomSupplierParameterType = "PassportCountryOfIssue"
	CustomSupplierParameterTypeCountryOfTheUser       CustomSupplierParameterType = "CountryOfTheUser"
	CustomSupplierParameterTypeEndUserBrowserAgent    CustomSupplierParameterType = "EndUserBrowserAgent"
	CustomSupplierParameterTypeEndUserIPAddress       CustomSupplierParameterType = "EndUserIPAddress"
)

var CustomSupplierParameterTypeName = map[CustomSupplierParameterType]string{
	CustomSupplierParameterTypeNome:                   "",
	CustomSupplierParameterTypeDateOfBirth:            "DateOfBirth",
	CustomSupplierParameterTypeNationality:            "Nationality",
	CustomSupplierParameterTypePassportNumber:         "PassportNumber",
	CustomSupplierParameterTypePassportExpiryDate:     "PassportExpiryDate",
	CustomSupplierParameterTypePassportCountryOfIssue: "PassportCountryOfIssue",
}

var CustomSupplierParameterTypeValue = func() map[string]CustomSupplierParameterType {
	value := map[string]CustomSupplierParameterType{}
	for k, v := range CustomSupplierParameterTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e CustomSupplierParameterType) MarshalJSON() ([]byte, error) {
	v, ok := CustomSupplierParameterTypeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *CustomSupplierParameterType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := CustomSupplierParameterTypeValue[string(data)]
	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (e *CustomSupplierParameterType) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := CustomSupplierParameterTypeValue[s]
	if !ok {
		*e = CustomSupplierParameterType(s)
		log.Warn("CustomSupplierParameterType UnmarshalXML waring missing key", log.String("key", s))
		return nil
	}

	*e = v

	return nil
}

func (e *CustomSupplierParameterType) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(CustomSupplierParameterTypeName[*e], start)
}

func (*CustomSupplierParameterType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range CustomSupplierParameterTypeName {
		vals = append(vals, name)
	}

	return vals
}
