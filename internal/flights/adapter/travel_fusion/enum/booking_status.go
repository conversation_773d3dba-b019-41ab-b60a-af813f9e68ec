package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
)

type BookingStatus string

const (
	BookingStatusNone                  BookingStatus = ""
	BookingStatusSucceeded             BookingStatus = "Succeeded"
	BookingStatusBookingInProgress     BookingStatus = "BookingInProgress"
	BookingStatusBookingFailed         BookingStatus = "Failed"
	BookingStatusUnconfirmed           BookingStatus = "Unconfirmed"
	BookingStatusUnconfirmedBySupplier BookingStatus = "UnconfirmedBySupplier"
	BookingStatusBookingNotLaunched    BookingStatus = "BookingNotLaunched"
)

var BookingStatusName = map[BookingStatus]string{
	BookingStatusNone:                  "",
	BookingStatusSucceeded:             "Succeeded",
	BookingStatusBookingInProgress:     "BookingInProgress",
	BookingStatusBookingFailed:         "Failed",
	BookingStatusUnconfirmed:           "Unconfirmed",
	BookingStatusUnconfirmedBySupplier: "UnconfirmedBySupplier",
	BookingStatusBookingNotLaunched:    "BookingNotLaunched",
}

var BookingStatusValue = func() map[string]BookingStatus {
	value := map[string]BookingStatus{}
	for k, v := range BookingStatusName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e BookingStatus) MarshalJSON() ([]byte, error) {
	v, ok := BookingStatusName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *BookingStatus) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := BookingStatusValue[string(data)]
	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (e *BookingStatus) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := BookingStatusValue[s]
	if !ok {
		*e = BookingStatus(s)
		log.Warn("BookingStatus UnmarshalXML waring missing key", log.String("key", s))
		return nil
	}

	*e = v

	return nil
}

func (e *BookingStatus) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(BookingStatusName[*e], start)
}

func (*BookingStatus) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range BookingStatusName {
		vals = append(vals, name)
	}

	return vals
}
