package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"strings"

	"github.com/pkg/errors"
)

type TravelFusionOriginType uint8

const (
	TravelFusionOriginTypeNone TravelFusionOriginType = iota
	TravelFusionOriginTypeAirportCode
	TravelFusionOriginTypeCityCode
	TravelFusionOriginTypeAirportGroup
	TravelFusionOriginTypeAuto
	TravelFusionOriginTypeLocationID
	TravelFusionOriginTypeCoordinate
	TravelFusionOriginTypeTrainstationCode
)

var TravelFusionOriginTypeName = map[TravelFusionOriginType]string{
	TravelFusionOriginTypeNone:             "",
	TravelFusionOriginTypeAirportCode:      "airportcode",
	TravelFusionOriginTypeCityCode:         "citycode",
	TravelFusionOriginTypeAirportGroup:     "airportgroup",
	TravelFusionOriginTypeAuto:             "auto",
	TravelFusionOriginTypeLocationID:       "locationid",
	TravelFusionOriginTypeCoordinate:       "coordinate",
	TravelFusionOriginTypeTrainstationCode: "trainstationcode",
}

var TravelFusionOriginTypeValue = func() map[string]TravelFusionOriginType {
	value := map[string]TravelFusionOriginType{}
	for k, v := range TravelFusionOriginTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e TravelFusionOriginType) MarshalJSON() ([]byte, error) {
	v, ok := TravelFusionOriginTypeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *TravelFusionOriginType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := TravelFusionOriginTypeValue[string(data)]
	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (e *TravelFusionOriginType) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := TravelFusionOriginTypeValue[strings.ToLower(s)]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", strings.ToLower(s), e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (e *TravelFusionOriginType) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(TravelFusionOriginTypeName[*e], start)
}

func (*TravelFusionOriginType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range TravelFusionOriginTypeName {
		vals = append(vals, name)
	}

	return vals
}
