package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"
	"strings"

	"github.com/pkg/errors"
)

type TravelFusionMode uint8

const (
	TravelFusionModeNone TravelFusionMode = iota
	TravelFusionModePlane
)

var TravelFusionModeName = map[TravelFusionMode]string{
	TravelFusionModeNone:  "",
	TravelFusionModePlane: "plane",
}

var TravelFusionModeValue = func() map[string]TravelFusionMode {
	value := map[string]TravelFusionMode{}
	for k, v := range TravelFusionModeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e TravelFusionMode) MarshalJSON() ([]byte, error) {
	v, ok := TravelFusionModeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *TravelFusionMode) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := TravelFusionModeValue[string(data)]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", strings.ToLower(string(data)), e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (e *TravelFusionMode) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := TravelFusionModeValue[strings.ToLower(s)]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", strings.ToLower(s), e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (e *TravelFusionMode) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(TravelFusionModeName[*e], start)
}

func (*TravelFusionMode) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range TravelFusionModeName {
		vals = append(vals, name)
	}

	return vals
}
