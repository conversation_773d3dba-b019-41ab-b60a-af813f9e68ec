package enum

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type PaxType string

const (
	PaxTypeNone     = ""
	PaxTypeAdult    = "ADT"
	PaxTypeChildren = "CHD"
	PaxTypeInfant   = "INF"
)

var MapFromDomainPaxType = map[enum.PaxType]PaxType{
	enum.PaxTypeNone:     PaxTypeNone,
	enum.PaxTypeAdult:    PaxTypeAdult,
	enum.PaxTypeChildren: PaxTypeChildren,
	enum.PaxTypeInfant:   PaxTypeInfant,
}
