package enum

import (
	"bytes"
	"encoding/xml"
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
)

type LuggageOption string

const (
	LuggageNone               LuggageOption = ""
	LuggageOptions            LuggageOption = "LuggageOptions"
	HandLuggageOptions        LuggageOption = "HandLuggageOptions"
	HandLuggage               LuggageOption = "HandLuggage"
	OutwardHandLuggageOptions LuggageOption = "OutwardHandLuggageOptions"
	OutwardLuggageOptions     LuggageOption = "OutwardLuggageOptions"
	ReturnHandLuggageOptions  LuggageOption = "ReturnHandLuggageOptions"
	ReturnLuggageOptions      LuggageOption = "ReturnLuggageOptions"
)

var LuggageOptionName = map[LuggageOption]string{
	LuggageNone:               "",
	LuggageOptions:            "LuggageOptions",
	HandLuggageOptions:        "HandLuggageOptions",
	HandLuggage:               "HandLuggage",
	OutwardHandLuggageOptions: "OutwardHandLuggageOptions",
	OutwardLuggageOptions:     "OutwardLuggageOptions",
	ReturnHandLuggageOptions:  "ReturnHandLuggageOptions",
	ReturnLuggageOptions:      "ReturnLuggageOptions",
}

var LuggageOptionValue = func() map[string]LuggageOption {
	value := map[string]LuggageOption{}
	for k, v := range LuggageOptionName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e LuggageOption) MarshalJSON() ([]byte, error) {
	v, ok := LuggageOptionName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *LuggageOption) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := LuggageOptionValue[string(data)]
	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (e *LuggageOption) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var s string
	if err := d.DecodeElement(&s, &start); err != nil {
		return errors.Wrap(err, "DecodeElement")
	}

	v, ok := LuggageOptionValue[s]
	if !ok {
		*e = LuggageOption(s)
		log.Warn("LuggageOption UnmarshalXML waring missing key", log.String("key", s))
		return nil
	}

	*e = v

	return nil
}

func (e *LuggageOption) MarshalXML(en *xml.Encoder, start xml.StartElement) error {
	return en.EncodeElement(LuggageOptionName[*e], start)
}

func (*LuggageOption) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range LuggageOptionName {
		vals = append(vals, name)
	}

	return vals
}
