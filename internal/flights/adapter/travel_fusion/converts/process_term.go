package converts

import (
	"sort"
	"strings"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainPaxInfosToTravellers(ins []*domain.PaxInfo, bk *domain.BookingSession) []*entities.Traveller {
	if len(ins) == 0 || ins[0] == nil || bk == nil {
		return nil
	}

	res := make([]*entities.Traveller, 0, len(ins))
	tempLuggageOptionRef := make([]*entities.LuggageOptionsRef, 0)

	if bk.SearchRequest.IsRoundTrip() {
		for _, baggage := range bk.Itineraries[0].FreeBaggage {
			if baggage.Code == "" {
				continue
			}

			if baggage.IsHandBaggage {
				tempLuggageOptionRef = append(tempLuggageOptionRef, &entities.LuggageOptionsRef{
					RefNumber:     baggage.Code,
					LuggageOption: enum.OutwardHandLuggageOptions,
				})

				continue
			}

			tempLuggageOptionRef = append(tempLuggageOptionRef, &entities.LuggageOptionsRef{
				RefNumber:     baggage.Code,
				LuggageOption: enum.OutwardLuggageOptions,
			})
		}

		for _, baggage := range bk.Itineraries[1].FreeBaggage {
			if baggage.Code == "" {
				continue
			}

			if baggage.IsHandBaggage {
				tempLuggageOptionRef = append(tempLuggageOptionRef, &entities.LuggageOptionsRef{
					RefNumber:     baggage.Code,
					LuggageOption: enum.ReturnHandLuggageOptions,
				})

				continue
			}

			tempLuggageOptionRef = append(tempLuggageOptionRef, &entities.LuggageOptionsRef{
				RefNumber:     baggage.Code,
				LuggageOption: enum.ReturnLuggageOptions,
			})
		}
	} else {
		for _, baggage := range bk.Itineraries[0].FreeBaggage {
			if baggage.Code == "" {
				continue
			}

			if baggage.IsHandBaggage {
				tempLuggageOptionRef = append(tempLuggageOptionRef, &entities.LuggageOptionsRef{
					RefNumber:     baggage.Code,
					LuggageOption: enum.HandLuggageOptions,
				})
				continue
			}
			tempLuggageOptionRef = append(tempLuggageOptionRef, &entities.LuggageOptionsRef{
				RefNumber:     baggage.Code,
				LuggageOption: enum.LuggageOptions,
			})
		}
	}

	for _, item := range ins {
		traveller := fromDomainPaxInfoToTraveller(item, tempLuggageOptionRef)
		traveller.SeatOptions, traveller.LuggageOptionsRef = buildTravellerMiscOptions(item, bk)
		res = append(res, traveller)
	}

	return res
}

func buildTravellerMiscOptions(in *domain.PaxInfo, bk *domain.BookingSession) (string, []*entities.LuggageOptionsRef) {
	segmentOptions := []string{}
	luggageOptions := []*entities.LuggageOptionsRef{}

	isFoundAny := false

	for _, iti := range bk.Itineraries {
		for _, segment := range iti.Segments {
			seatFilled := false

			for _, pax := range iti.PaxInfo {
				sort.Slice(pax.Seats, func(i, j int) bool { return pax.Seats[i].SegmentIndex < pax.Seats[j].SegmentIndex })

				if pax.PaxID == in.ID {

					//
					for _, seat := range pax.Seats {
						if seat.SegmentIndex == segment.Index {
							segmentOptions = append(segmentOptions, seat.SeatFacility.SelectionKey)
							seatFilled = true
							isFoundAny = true
						}
					}

					// Baggage
					for _, baggage := range pax.Baggages {
						if baggage != nil && baggage.BaggageInfo != nil && baggage.BaggageInfo.OfferData != nil && baggage.BaggageInfo.OfferData.TFOfferData != nil {
							luggageOptions = append(luggageOptions, &entities.LuggageOptionsRef{
								RefNumber:     baggage.BaggageInfo.OfferData.TFOfferData.ParameterValue,
								LuggageOption: enum.LuggageOption(baggage.BaggageInfo.OfferData.TFOfferData.ParameterName),
							})
						}
					}
				}
			}

			if !seatFilled {
				segmentOptions = append(segmentOptions, "")
			}
		}
	}

	if !isFoundAny {
		return "", luggageOptions
	}

	return strings.Join(segmentOptions, ";") + ";", luggageOptions
}

func fromDomainPaxInfoToTraveller(info *domain.PaxInfo, luggageOptionRef []*entities.LuggageOptionsRef) *entities.Traveller {
	if info == nil || info.DOB == nil {
		return nil
	}

	traveller := &entities.Traveller{
		Type:        enum.MapFromDomainPaxType[info.Type],
		GenderType:  enum.MapFromDomainGenderType[info.Gender],
		FirstName:   info.GivenName,
		LastName:    info.Surname,
		Nationality: info.Nationality,
		DOB:         *info.DOB,
		Age:         info.Age,
		Passport:    fromDomainPassport(info.Passport),
	}

	if traveller.Type != enum.PaxTypeInfant {
		traveller.LuggageOptionsRef = luggageOptionRef
	}

	return traveller
}

func fromDomainPassport(info *domain.PaxPassport) *entities.Passport {
	if info == nil {
		return nil
	}

	return &entities.Passport{
		PassportNumber:         info.Number,
		PassportExpiryDate:     info.ExpiryDate,
		PassportCountryOfIssue: info.IssuingCountry,
	}
}

func FromDomainContactInfo(info *domain.Contact) *entities.ContactInfo {
	if info == nil {
		return nil
	}

	tfGenderMap := map[commonEnum.GenderType]string{
		commonEnum.GenderTypeFeMale: "Mrs",
		commonEnum.GenderTypeMale:   "Mr",
	}

	return &entities.ContactInfo{
		FirstName:   info.GivenName,
		LastName:    info.Surname,
		Email:       "<EMAIL>", // HACK: update by task (https://www.notion.so/deeptechjsc/Th-ng-tin-Li-n-h-th-ng-tin-i-l-8dc5e5ba61024bb380d086ea70da231c)
		PhoneCode:   info.PhoneCode,
		PhoneNumber: info.Phone,
		Title:       tfGenderMap[info.Gender],
	}
}
