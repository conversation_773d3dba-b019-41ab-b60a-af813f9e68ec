package converts

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

// Tra chuyen le va ca chuyen cap tren flight option type
func FlightsToHubV2(input *responses.CommandList, req *domain.SearchFlightsRequest) ([]*domain.ResponseFlight, error) {
	if input == nil || input.CheckRouting == nil || input.CheckRouting.RouterList == nil {
		return nil, nil
	}

	res := []*domain.ResponseFlight{}

	routers := input.CheckRouting.RouterList.Router

	for routerIndex, v := range routers {
		if v.GroupList == nil || len(v.GroupList.Group) == 0 {
			continue
		}

		// Free
		outwardBaggageMap := map[string][]*domain.BaggageInfo{}
		returnBaggageMap := map[string][]*domain.BaggageInfo{}

		if v.Features != nil {
			// Router Structured Features
			baggageTypes := []string{"SmallCabinBag", "HoldBag", "LargeCabinBag"}
			seenBags := map[string]bool{}
			for _, feature := range v.Features.Feature {
				if lo.Contains(baggageTypes, feature.TypeAttr) {
					isHandBag := feature.TypeAttr != "HoldBag"

					// Moi option la baggage(s)
					for _, option := range feature.Option {
						price := option.ValueAttr
						minPrice := option.MinValueAttr
						maxPrice := option.MaxValueAttr

						// Free baggage only
						if price == 0 && minPrice == 0 && maxPrice == 0 {
							var err error
							cabinCodes := []string{}
							weight := ""
							quan := int64(0)
							paxTypes := []string{}
							itiIndex := 0
							dimen := ""

							// Attribute list
							// Ket qua tra ve co the merge nhieu cabin code/paxtype vao chung 1 option
							for _, cond := range option.Condition {
								if cond.TypeAttr == "SupplierClass" {
									cabinCodes = strings.Split(cond.ValueAttr, ",")
									cabinCodes = lo.Uniq[string](cabinCodes)
								}

								if cond.TypeAttr == "Weight" || cond.TypeAttr == "MaxWeight" {
									weight = cond.ValueAttr
								}
								if cond.TypeAttr == "Quantity" {
									strQuan := cond.ValueAttr
									quan, err = strconv.ParseInt(strQuan, 10, 0)
									if err != nil {
										return nil, errors.Wrap(err, "ParseInt convQuan")
									}
								} else if cond.TypeAttr == "MaxQuantity" {
									quan = 1
								}

								if cond.TypeAttr == "Dimensions" {
									dimen = cond.ValueAttr
								}

								if cond.TypeAttr == "TravellerType" {
									paxTypes = strings.Split(cond.ValueAttr, ",")
								}

								if cond.TypeAttr == "Direction" && cond.ValueAttr == "Outbound" {
									itiIndex = 1
								}

								if cond.TypeAttr == "Direction" && cond.ValueAttr == "Inbound" {
									itiIndex = 2
								}
							}

							// Begin validate pax types
							if quan == 0 {
								continue
							}

							if weight == "" {
								weight = dimen
							}

							// SmallCabinBag not include TravellerType
							if len(paxTypes) == 0 {
								paxTypes = append(paxTypes, string(enum.PaxTypeAdult))

								if req.Passengers.CHD > 0 {
									paxTypes = append(paxTypes, string(enum.PaxTypeChildren))
								}
							}

							// Cover missing children bag
							if req.Passengers.CHD > 0 && !lo.Contains(paxTypes, string(enum.PaxTypeChildren)) {
								paxTypes = append(paxTypes, string(enum.PaxTypeChildren))
							}

							// End validate pax types

							const paxTypeYTH = "YTH"

							for _, code := range cabinCodes {
								for _, paxType := range paxTypes {
									if paxType == string(enum.PaxTypeChildren) && req.Passengers.CHD == 0 ||
										paxType == string(enum.PaxTypeInfant) && req.Passengers.INF == 0 ||
										paxType == paxTypeYTH {
										continue
									}

									mapKey := buildFlightBaggageKey(string(*v.Supplier), code)
									bag := &domain.BaggageInfo{
										Name:          weight,
										Price:         price,
										IsHandBaggage: isHandBag,
										Quantity:      quan,
										PaxType:       enum.PaxType(paxType),
									}

									if !isHandBag {
										bagIdenKey := fmt.Sprintf("%s-%d-%s-%d", bag.Name, bag.Quantity, bag.PaxType, itiIndex)
										if seenBags[bagIdenKey] {
											continue
										}
										seenBags[bagIdenKey] = true
									}

									if itiIndex == 0 {
										outwardBaggageMap[mapKey] = append(outwardBaggageMap[mapKey], bag)

										if req.IsRoundTrip() {
											returnBaggageMap[mapKey] = append(returnBaggageMap[mapKey], bag)
										}
									}

									if itiIndex == 1 {
										outwardBaggageMap[mapKey] = append(outwardBaggageMap[mapKey], bag)
									}

									if itiIndex == 2 {
										returnBaggageMap[mapKey] = append(returnBaggageMap[mapKey], bag)
									}
								}
							}
							//
						}
					}
				}
			}
		}

		for _, g := range v.GroupList.Group {
			if g == nil || g.OutwardList == nil || len(g.OutwardList.Outward) == 0 {
				continue
			}

			if g.Price != nil { // Trả giá cặp
				totalFareInfo := CalculateTotalFarePrice(g.Price, req).ConvertToSearch()
				for _, outward := range g.OutwardList.Outward {
					iter := &domain.ResponseFlight{
						FlightID:   helpers.GenerateFlightID(enum.FlightProviderTravelFusion),
						Provider:   enum.FlightProviderTravelFusion,
						OptionType: enum.FlightOptionTypeRecommend,
					}

					itinerary, err := ItineraryToHub(1, outward, req.Passengers.ADT)
					if err != nil {
						return nil, err
					}

					bagKey := buildFlightBaggageKey(string(*v.Supplier), itinerary.CabinClassCode)
					itinerary.FreeBaggage = outwardBaggageMap[bagKey]

					iter.Itineraries = append(iter.Itineraries, itinerary)

					if totalFareInfo != nil {
						iter.SearchTotalFareInfo = *totalFareInfo
					}

					if g.ReturnList != nil && len(g.ReturnList.Return) > 0 {
						for _, returnItem := range g.ReturnList.Return {
							itineraryReturn, err := ItineraryToHub(2, returnItem, req.Passengers.ADT)
							if err != nil {
								return nil, err
							}

							if itineraryReturn.DepartDate-itinerary.ArrivalDate < (time.Hour.Milliseconds() * 3) {
								continue
							}

							returnBagKey := buildFlightBaggageKey(string(*v.Supplier), itineraryReturn.CabinClassCode)
							itineraryReturn.FreeBaggage = returnBaggageMap[returnBagKey]

							tmpIter := &domain.ResponseFlight{
								FlightID:   helpers.GenerateFlightID(enum.FlightProviderTravelFusion),
								Provider:   enum.FlightProviderTravelFusion,
								OptionType: enum.FlightOptionTypeRecommend,
							}

							tmpIter.Itineraries = append(tmpIter.Itineraries, itinerary, itineraryReturn)

							if totalFareInfo != nil {
								tmpIter.SearchTotalFareInfo = *totalFareInfo
							}

							res = append(res, tmpIter)

							continue
						}
					} else {
						res = append(res, iter)
					}
				}

			} else { // Trả gía lẻ
				hasReturn := g.ReturnList != nil && len(g.ReturnList.Return) > 0

				for _, outward := range g.OutwardList.Outward {
					iter := &domain.ResponseFlight{
						FlightID:   helpers.GenerateFlightID(enum.FlightProviderTravelFusion),
						Provider:   enum.FlightProviderTravelFusion,
						OptionType: enum.FlightOptionSingleTrip,
						GroupID:    utils.GetFlightGroupID(enum.FlightProviderTravelFusion, routerIndex+1),
						Leg:        1,
					}

					itinerary, err := ItineraryToHub(1, outward, req.Passengers.ADT)
					if err != nil {
						return nil, err
					}

					bagKey := buildFlightBaggageKey(string(*v.Supplier), itinerary.CabinClassCode)
					itinerary.FreeBaggage = outwardBaggageMap[bagKey]

					iter.Itineraries = append(iter.Itineraries, itinerary)

					iter.CommitV2(req)

					res = append(res, iter)
				}

				if hasReturn {
					for _, returnItem := range g.ReturnList.Return {
						itineraryReturn, err := ItineraryToHub(2, returnItem, req.Passengers.ADT)
						if err != nil {
							return nil, err
						}

						returnBagKey := buildFlightBaggageKey(string(*v.Supplier), itineraryReturn.CabinClassCode)
						itineraryReturn.FreeBaggage = returnBaggageMap[returnBagKey]

						tmpIter := &domain.ResponseFlight{
							FlightID:   helpers.GenerateFlightID(enum.FlightProviderTravelFusion),
							Provider:   enum.FlightProviderTravelFusion,
							OptionType: enum.FlightOptionSingleTrip,
							GroupID:    utils.GetFlightGroupID(enum.FlightProviderTravelFusion, routerIndex+1),
							Leg:        2,
						}

						tmpIter.Itineraries = append(tmpIter.Itineraries, itineraryReturn)

						tmpIter.CommitV2(req)

						res = append(res, tmpIter)

						continue
					}
				}
			}

		}

	}

	return res, nil
}
