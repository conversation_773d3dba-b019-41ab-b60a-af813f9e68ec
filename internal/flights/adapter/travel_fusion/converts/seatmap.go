package converts

import (
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	tfConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

func ToDomainSeatSegment(res *responses.CommandList, itineraries []*domain.FlightItinerary) ([]*domain.SeatSegment, error) {
	if res == nil || res.ProcessDetails == nil || res.ProcessDetails.Router == nil || res.ProcessDetails.Router.RequiredParameterList == nil || len(res.ProcessDetails.Router.RequiredParameterList.RequiredParameter) == 0 {
		return nil, fmt.Errorf("ToDomainSeatSegment Params nil")
	}
	var result []*domain.SeatSegment
	for _, paramater := range res.ProcessDetails.Router.RequiredParameterList.RequiredParameter {
		if paramater.Name == tfConstants.SeatOptionsName {
			seatsegments, err := handleStringSeatOptions(paramater.DisplayText, itineraries)
			if err != nil {
				return nil, errors.Wrap(err, "handleStringSeatOptions")
			}

			result = seatsegments
		}
	}

	seenSeatSegment := map[string]bool{}
	for _, seatSegment := range result {
		seenSeatSegment[seatSegment.Key] = true
	}

	for _, iti := range itineraries {
		for _, segment := range iti.Segments {
			key := segment.GenerateSegmentID()
			if !seenSeatSegment[key] {
				result = append(result, &domain.SeatSegment{
					Key:          key,
					ItineraryID:  iti.ID,
					SegmentIndex: segment.Index,
					SeatOptions:  []*domain.SeatOption{},
					ExpiredAt:    time.Now().Add(constants.SeatMapEmptyExpireTime).UnixMilli(),
				})
			}
		}
	}

	return result, nil
}

func handleStringSeatOptions(seatOptionsRaw string, itineraries []*domain.FlightItinerary) ([]*domain.SeatSegment, error) {
	result := make([]*domain.SeatSegment, 0)

	flights := strings.Split(seatOptionsRaw, ";")
	if len(flights) > 0 && flights[len(flights)-1] == "" {
		flights = flights[:len(flights)-1]
	}
	rawDataNonSemicolon := !strings.Contains(seatOptionsRaw, ";")
	regexPattern := `(\d+)-(\w+)\(?([^@]+)?\@?([\d.]+)?\ ?([A-Za-z]*)@?(\w+)?\)`
	mapSeatSegmentCaseNonSemicolon := map[string]*domain.SeatSegment{} // dung cho case raw data seatmap ko co ";"
	mapSeatRowCaseNonSemicolon := map[string]map[string][]*domain.SeatFacility{}

	if rawDataNonSemicolon {
		for _, iti := range itineraries {
			for _, segment := range iti.Segments {
				mapSeatSegmentCaseNonSemicolon[segment.FlightNumber] = &domain.SeatSegment{
					Key:          segment.GenerateSegmentID(),
					ItineraryID:  iti.ID,
					SegmentIndex: segment.Index,
					SeatOptions: []*domain.SeatOption{
						{
							CabinClass: segment.CabinClassCode,
						},
					},
					ExpiredAt: time.Now().Add(constants.SeatMapExpireTime).UnixMilli(),
				}

				mapSeatRowCaseNonSemicolon[segment.FlightNumber] = make(map[string][]*domain.SeatFacility)
			}
		}
	}
	regex := regexp.MustCompile(regexPattern)
	if rawDataNonSemicolon {
		for _, flight := range flights {
			matches := regex.FindAllStringSubmatch(flight, -1)
			for _, match := range matches {
				flightnumber := match[1]
				if flightnumber != "" && mapSeatSegmentCaseNonSemicolon[flightnumber] == nil {
					continue
				}
				seatCode := match[2]
				attributes := strings.Split(match[3], "|")
				price := match[4]
				currency := match[5]

				//regex lay seat row and seat column
				regexPatternSeat := `(\d+)([A-Z])`
				regexSeat := regexp.MustCompile(regexPatternSeat)
				rowAndColumnSeat := regexSeat.FindStringSubmatch(seatCode)

				tempSeatFacility := &domain.SeatFacility{
					Type:         enum.SeatTypeSeat,
					Availability: enum.SeatStatusAvailable,
					Property: &domain.SeatProperty{
						Aisle:                 lo.Count(attributes, tfConstants.SeatPropertyAisle) > 0,
						Window:                lo.Count(attributes, tfConstants.SeatPropertyWindow) > 0,
						EmergencyExit:         lo.Count(attributes, tfConstants.SeatPropertyEmergencyExit) > 0,
						OverWing:              lo.Count(attributes, tfConstants.SeatPropertyOverWing) > 0,
						Bassinet:              lo.Count(attributes, tfConstants.SeatPropertyBassinet) > 0,
						NotAllowedForInfant:   lo.Count(attributes, tfConstants.SeatPropertyNotAllowedForInfant) > 0,
						NotSuitableForInfant:  lo.Count(attributes, tfConstants.SeatPropertyNotSuitableForInfant) > 0,
						NotSuitableForChild:   lo.Count(attributes, tfConstants.SeatPropertyNotSuitableForChild) > 0,
						HandicappedFacilities: lo.Count(attributes, tfConstants.SeatPropertyHandicappedFacilities) > 0,
						ExtraLegroom:          lo.Count(attributes, tfConstants.SeatPropertyExtraLegroom) > 0,
					},
					SelectionKey: fmt.Sprintf("%s-%s", match[1], match[2]),
				}

				if len(rowAndColumnSeat) > 2 {
					tempSeatFacility.SeatCode = rowAndColumnSeat[2]
				}

				regexPattern := regexp.MustCompile(`^\+\d+$`)
				// skip extreme case
				if price == "" || currency == "" {
					tempSeatFacility.Availability = enum.SeatStatusBlocked
				} else {
					seatPrice, err := strconv.ParseFloat(price, 64) // 64 cho biết rằng dùng 64 bit để biểu diễn phần thập phân.
					if err != nil {
						return nil, errors.Wrap(err, "strconv.ParseFloat seatPrice")
					}

					tempSeatFacility.SeatCharge = &domain.SeatCharge{
						BaseAmount:  seatPrice,
						TotalAmount: seatPrice,
						Currency:    currency,
					}

					for _, attribute := range attributes {
						match := regexPattern.MatchString(attribute)

						if match || attribute == tfConstants.SeatPropertyUnavailable {
							tempSeatFacility.Availability = enum.SeatStatusOccupied
							break
						}

						if tempSeatFacility.Property.Bassinet ||
							tempSeatFacility.Property.NotAllowedForInfant ||
							tempSeatFacility.Property.NotSuitableForInfant ||
							tempSeatFacility.Property.NotSuitableForChild ||
							tempSeatFacility.Property.HandicappedFacilities {
							tempSeatFacility.Availability = enum.SeatStatusBlocked
							break
						}
					}
				}
				mapSeatRowCaseNonSemicolon[flightnumber][rowAndColumnSeat[1]] = append(mapSeatRowCaseNonSemicolon[flightnumber][rowAndColumnSeat[1]], tempSeatFacility)
			}
		}
		for flightnumber, item := range mapSeatRowCaseNonSemicolon {
			rowSeats := make([]*domain.SeatRow, 0)
			for column, value := range item {
				sort.Slice(value, func(i, j int) bool {
					codeI := strings.ToUpper(value[i].SeatCode)
					codeJ := strings.ToUpper(value[j].SeatCode)
					return codeI < codeJ
				})

				rowSeats = append(rowSeats, &domain.SeatRow{
					RowNumber:  column,
					Facilities: value,
				})
			}

			sort.Slice(rowSeats, func(i, j int) bool {
				iRowNumber, _ := strconv.Atoi(rowSeats[i].RowNumber)
				jRowNumber, _ := strconv.Atoi(rowSeats[j].RowNumber)
				return iRowNumber < jRowNumber
			})

			if mapSeatSegmentCaseNonSemicolon[flightnumber] != nil && len(mapSeatSegmentCaseNonSemicolon[flightnumber].SeatOptions) > 0 {
				mapSeatSegmentCaseNonSemicolon[flightnumber].SeatOptions[0].Rows = rowSeats
			}

			result = append(result, mapSeatSegmentCaseNonSemicolon[flightnumber])
		}
	} else {
		var countIndexReturn = 0
		for index, flight := range flights {

			var (
				tempSeatSegment *domain.SeatSegment
				tempItinerary   *domain.FlightItinerary
			)
			segmentIndex := index

			if segmentIndex > len(itineraries[0].Segments)-1 {
				countIndexReturn += 1
				segmentIndex = countIndexReturn - 1
				tempItinerary = itineraries[1]
			} else {
				tempItinerary = itineraries[0]
			}

			tempSeatSegment = &domain.SeatSegment{
				Key:          tempItinerary.Segments[segmentIndex].GenerateSegmentID(),
				ItineraryID:  tempItinerary.ID,
				SegmentIndex: segmentIndex + 1,
				SeatOptions:  []*domain.SeatOption{},
				ExpiredAt:    time.Now().Add(constants.SeatMapExpireTime).UnixMilli(),
			}

			mapSeatRow := map[string][]*domain.SeatFacility{}
			matches := regex.FindAllStringSubmatch(flight, -1)
			if flight == "" || len(matches) == 0 {
				result = append(result, tempSeatSegment)
				continue
			}

			for _, match := range matches {
				// flightnumber := match[1]
				seatCode := match[2]
				attributes := strings.Split(match[3], "|")
				price := match[4]
				currency := match[5]

				//regex lay seat row and seat column
				regexPatternSeat := `(\d+)([A-Z])`
				regexSeat := regexp.MustCompile(regexPatternSeat)
				rowAndColumnSeat := regexSeat.FindStringSubmatch(seatCode)
				tempSeatFacility := &domain.SeatFacility{
					Type:         enum.SeatTypeSeat,
					Availability: enum.SeatStatusAvailable,
					Property: &domain.SeatProperty{
						Aisle:                 lo.Count(attributes, tfConstants.SeatPropertyAisle) > 0,
						Window:                lo.Count(attributes, tfConstants.SeatPropertyWindow) > 0,
						EmergencyExit:         lo.Count(attributes, tfConstants.SeatPropertyEmergencyExit) > 0,
						OverWing:              lo.Count(attributes, tfConstants.SeatPropertyOverWing) > 0,
						Bassinet:              lo.Count(attributes, tfConstants.SeatPropertyBassinet) > 0,
						NotAllowedForInfant:   lo.Count(attributes, tfConstants.SeatPropertyNotAllowedForInfant) > 0,
						NotSuitableForInfant:  lo.Count(attributes, tfConstants.SeatPropertyNotSuitableForInfant) > 0,
						NotSuitableForChild:   lo.Count(attributes, tfConstants.SeatPropertyNotSuitableForChild) > 0,
						HandicappedFacilities: lo.Count(attributes, tfConstants.SeatPropertyHandicappedFacilities) > 0,
						ExtraLegroom:          lo.Count(attributes, tfConstants.SeatPropertyExtraLegroom) > 0,
					},
					SelectionKey: fmt.Sprintf("%s-%s", match[1], match[2]),
				}

				if len(rowAndColumnSeat) > 2 {
					tempSeatFacility.SeatCode = rowAndColumnSeat[2]
				}

				// skip extreme case
				if price == "" || currency == "" {
					tempSeatFacility.Availability = enum.SeatStatusBlocked
				} else {
					seatPrice, err := strconv.ParseFloat(price, 64) // 64 cho biết rằng dùng 64 bit để biểu diễn phần thập phân.
					if err != nil {
						return nil, errors.Wrap(err, "strconv.ParseFloat seatPrice")
					}
					tempSeatFacility.SeatCharge = &domain.SeatCharge{
						BaseAmount:  seatPrice,
						TotalAmount: seatPrice,
						Currency:    currency,
					}
					for _, attribute := range attributes {
						if attribute == tfConstants.SeatPropertyUnavailable {
							tempSeatFacility.Availability = enum.SeatStatusOccupied
							break
						}

						if strings.Contains(attribute, "+") || tempSeatFacility.Property.Bassinet ||
							tempSeatFacility.Property.NotSuitableForChild ||
							tempSeatFacility.Property.HandicappedFacilities {
							tempSeatFacility.Availability = enum.SeatStatusBlocked
						}
					}
				}

				mapSeatRow[rowAndColumnSeat[1]] = append(mapSeatRow[rowAndColumnSeat[1]], tempSeatFacility)
			}

			rowSeats := make([]*domain.SeatRow, 0)
			for column, value := range mapSeatRow {
				sort.Slice(value, func(i, j int) bool {
					codeI := strings.ToUpper(value[i].SeatCode)
					codeJ := strings.ToUpper(value[j].SeatCode)
					return codeI < codeJ
				})

				rowSeats = append(rowSeats, &domain.SeatRow{
					RowNumber:  column,
					Facilities: value,
				})
			}

			sort.Slice(rowSeats, func(i, j int) bool {
				iRowNumber, _ := strconv.Atoi(rowSeats[i].RowNumber)
				jRowNumber, _ := strconv.Atoi(rowSeats[j].RowNumber)
				return iRowNumber < jRowNumber
			})

			tempSeatSegment.SeatOptions = []*domain.SeatOption{
				{
					CabinClass: tempItinerary.Segments[segmentIndex].CabinClassCode,
					Rows:       rowSeats,
				},
			}

			result = append(result, tempSeatSegment)
		}
	}

	return result, nil
}
