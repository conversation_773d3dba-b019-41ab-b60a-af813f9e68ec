package converts

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	tfConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func SyncBooking(pBk *responses.GetBookingDetailsResponseType, dBk *domain.BookingSession) (bool, error) {
	hasChanges := false

	if pBk.RouterHistory == nil || pBk.RouterHistory.TermsRouter == nil {
		return false, nil
	}

	router := pBk.RouterHistory.TermsRouter

	if router.GroupList.Group == nil || router.GroupList.Group[0].OutwardList.Outward == nil {
		return false, nil
	}

	group := router.GroupList.Group[0]
	outwardIti := group.OutwardList.Outward[0]
	hubOutWard := dBk.Itineraries[0]

	itiChange, err := syncSegments(outwardIti, hubOutWard)
	if err != nil {
		return false, errors.Wrap(err, "syncIti outward")
	}

	if itiChange {
		hasChanges = true
		if err := syncItiFromSegs(hubOutWard); err != nil {
			return false, errors.Wrap(err, "syncItiFromSegs outward")
		}
	}

	if len(dBk.Itineraries) == 2 {
		returnIti := group.ReturnList.Return[0]
		hubReturn := dBk.Itineraries[1]

		returnItiChange, err := syncSegments(returnIti, hubReturn)
		if err != nil {
			return false, errors.Wrap(err, "syncIti return")
		}

		if returnItiChange {
			hasChanges = true
			if err := syncItiFromSegs(hubReturn); err != nil {
				return false, errors.Wrap(err, "syncItiFromSegs return")
			}
		}
	}

	return hasChanges, nil
}

func syncItiFromSegs(iti *domain.FlightItinerary) error {
	var err error

	firstSeg := iti.Segments[0]
	lastSeg := iti.Segments[len(iti.Segments)-1]

	iti.DepartDate = firstSeg.DepartDate
	iti.ArrivalDate = lastSeg.ArrivalDate

	iti.DepartDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.DepartDate, "", iti.DepartDt.Location())
	if err != nil {
		return err
	}

	iti.ArrivalDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.ArrivalDate, "", iti.ArrivalDt.Location())
	if err != nil {
		return err
	}

	iti.FlightDuration = int(iti.ArrivalDt.Sub(iti.DepartDt).Minutes())

	return nil
}

func syncSegments(pIti *responses.FlightLegData, dIti *domain.FlightItinerary) (bool, error) {
	if len(pIti.SegmentList.Segment) != len(dIti.Segments) {
		return false, nil
	}

	hasChanges := false

	for i, seg := range dIti.Segments {
		pSeg := pIti.SegmentList.Segment[i]

		pSegDepartDate, err := time.Parse(tfConstants.DDMMYYYHHMMFormat, pSeg.DepartDate)
		if err != nil {
			return false, errors.Wrap(err, fmt.Sprintf("departDate.time.Parse: %v", pSeg.DepartDate))
		}

		pSegArrivalDate, err := time.Parse(tfConstants.DDMMYYYHHMMFormat, pSeg.ArriveDate)
		if err != nil {
			return false, errors.Wrap(err, fmt.Sprintf("arrivalDate.time.Parse: %v", pSeg.ArriveDate))
		}

		if pSegDepartDate.UnixMilli() != seg.DepartDate || pSegArrivalDate.UnixMilli() != seg.ArrivalDate {
			hasChanges = true

			seg.DepartDate = pSegDepartDate.UnixMilli()
			seg.ArrivalDate = pSegArrivalDate.UnixMilli()
			seg.DepartDt = helpers.ToUTCDateTime(seg.DepartDate)
			seg.ArrivalDt = helpers.ToUTCDateTime(seg.ArrivalDate)
		}

	}

	return hasChanges, nil
}
