package converts

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	tfConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/constants"
	tfHelpers "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/helpers"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func segmentToHub(id int, input *responses.RouterSegmentType) (*domain.ItinerarySegment, error) {
	if input == nil || input.TravelClass == nil {
		return nil, nil
	}

	departDate, err := time.Parse(tfConstants.DDMMYYYHHMMFormat, input.DepartDate)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("departDate.time.Parse: %v", input.DepartDate))
	}

	arrivalDate, err := time.Parse(tfConstants.DDMMYYYHHMMFormat, input.ArriveDate)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("arrivalDate.time.Parse: %v", input.ArriveDate))
	}

	return &domain.ItinerarySegment{
		Index:            id,
		DepartPlace:      input.Origin.Code,
		DepartDate:       departDate.UnixMilli(),
		ArrivalPlace:     input.Destination.Code,
		ArrivalDate:      arrivalDate.UnixMilli(),
		DepartTerminal:   getTerminal(input.Origin),
		ArrivalTerminal:  getTerminal(input.Destination),
		Aircraft:         getAirCraft(input.AircraftType),
		DepartDt:         helpers.ToUTCDateTime(departDate.UnixMilli()),
		ArrivalDt:        helpers.ToUTCDateTime(arrivalDate.UnixMilli()),
		CarrierMarketing: input.VendingOperator.Code,
		CarrierOperator:  input.Operator.Code,
		FlightNumber:     fmt.Sprintf("%v", input.FlightId.Number),
		FlightDuration:   input.Duration,
		CabinClassCode:   constants.GetCabinName(input.TravelClass.SupplierClass),
		BookingClass:     input.TravelClass.SupplierRBDCode,
		FareBasis:        input.TravelClass.SupplierFareBasisCode,
	}, nil
}

func getAdditionalTotalTaxAmount(input *responses.PriceTaxItemListType) float64 {
	if input == nil {
		return 0
	}

	out := float64(0)

	for _, tax := range input.TaxItem {
		out += tax.Amount
	}

	return out
}

func ItineraryPaxFareToHub(input *responses.CommonPrice, adtNum int) []*domain.ItineraryPaxFare {
	if input == nil || input.PassengerPriceList == nil {
		return nil
	}

	additionalTaxAmount := getAdditionalTotalTaxAmount(input.TaxItemList) / float64(adtNum)

	mapValue := map[enum.PaxType]*domain.ItineraryPaxFare{}

	for _, v := range input.PassengerPriceList.PassengerPrice {
		pType := helpers.GetPaxTypeFromAge(v.Age)
		if mapValue[pType] != nil {
			continue
		}

		taxAmount := float64(0)
		amount := v.Amount

		if v.TaxItemList == nil {
			v.TaxItemList = &responses.PriceTaxItemListType{}
		}

		for _, v := range v.TaxItemList.TaxItem {
			taxAmount += v.Amount
		}

		if pType == enum.PaxTypeAdult {
			taxAmount += additionalTaxAmount
			amount += additionalTaxAmount
		}

		mapValue[pType] = &domain.ItineraryPaxFare{
			PaxType:    pType,
			FareAmount: amount,
			FareBasic:  amount - taxAmount,
			TaxAmount:  taxAmount,
			Currency:   v.Currency,
		}
	}

	res := make([]*domain.ItineraryPaxFare, 0, len(mapValue))

	for _, v := range mapValue {
		res = append(res, v)
	}

	return res
}

func ItineraryToHub(iIdx int, input *responses.FlightLegData, adtNum int) (*domain.FlightItinerary, error) {
	startSegment := input.SegmentList.Segment[0]
	endSegment := input.SegmentList.Segment[len(input.SegmentList.Segment)-1]

	departDate, err := time.Parse(tfConstants.DDMMYYYHHMMFormat, startSegment.DepartDate)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("departDate.time.Parse: %v", startSegment.DepartDate))
	}

	arrivalDate, err := time.Parse(tfConstants.DDMMYYYHHMMFormat, endSegment.ArriveDate)
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("arrivalDate.time.Parse: %v", endSegment.ArriveDate))
	}

	segments := make([]*domain.ItinerarySegment, 0, len(input.SegmentList.Segment))

	for idx, v := range input.SegmentList.Segment {
		segment, err := segmentToHub(idx+1, v)
		if err != nil {
			return nil, errors.Wrap(err, "segmentToHub")
		}

		segments = append(segments, segment)
	}

	const defaultAvaibility = 9

	res := &domain.FlightItinerary{
		Index:            iIdx,
		StopNumber:       len(input.SegmentList.Segment) - 1,
		CabinClass:       startSegment.TravelClass.SupplierClass,
		CabinClassCode:   startSegment.TravelClass.SupplierClass,
		BookingClass:     startSegment.TravelClass.SupplierRBDCode,
		FareBasis:        startSegment.TravelClass.SupplierFareBasisCode,
		Availability:     defaultAvaibility,
		DepartPlace:      startSegment.Origin.Code,
		DepartDate:       departDate.UnixMilli(),
		ArrivalPlace:     endSegment.Destination.Code,
		ArrivalDate:      arrivalDate.UnixMilli(),
		CarrierMarketing: startSegment.VendingOperator.Code,
		FlightNumber:     fmt.Sprintf("%v", startSegment.FlightId.Number),
		FlightDuration:   input.Duration,
		Segments:         segments,
	}

	if input.Price != nil {
		res.PaxFares = ItineraryPaxFareToHub(input.Price, adtNum)
		res.FareAmount = input.Price.Amount
		res.Currency = input.Price.Currency
	}

	return res, nil
}

func buildFlightBaggageKey(airline, cabin string) string {
	return fmt.Sprintf("%s-%s", strings.ToUpper(airline), strings.ToUpper(cabin))
}

func FlightsToHub(input *responses.CommandList, req *domain.SearchFlightsRequest) ([]*domain.ResponseFlight, error) {
	if input == nil || input.CheckRouting == nil || input.CheckRouting.RouterList == nil {
		return nil, nil
	}

	res := []*domain.ResponseFlight{}

	routers := input.CheckRouting.RouterList.Router

	for _, v := range routers {
		if v.GroupList == nil || len(v.GroupList.Group) == 0 {
			continue
		}

		// Free
		outwardBaggageMap := map[string][]*domain.BaggageInfo{}
		returnBaggageMap := map[string][]*domain.BaggageInfo{}

		if v.Features != nil {
			// Router Structured Features
			baggageTypes := []string{"SmallCabinBag", "HoldBag", "LargeCabinBag"}
			seenBags := map[string]bool{}
			for _, feature := range v.Features.Feature {
				if lo.Contains(baggageTypes, feature.TypeAttr) {
					isHandBag := feature.TypeAttr != "HoldBag"

					// Moi option la baggage(s)
					for _, option := range feature.Option {
						price := option.ValueAttr
						minPrice := option.MinValueAttr
						maxPrice := option.MaxValueAttr

						// Free baggage only
						if price == 0 && minPrice == 0 && maxPrice == 0 {
							var err error
							cabinCodes := []string{}
							weight := ""
							quan := int64(0)
							paxTypes := []string{}
							itiIndex := 0
							dimen := ""

							// Attribute list
							// Ket qua tra ve co the merge nhieu cabin code/paxtype vao chung 1 option
							for _, cond := range option.Condition {
								if cond.TypeAttr == "SupplierClass" {
									cabinCodes = strings.Split(cond.ValueAttr, ",")
									cabinCodes = lo.Uniq[string](cabinCodes)
								}

								if cond.TypeAttr == "Weight" || cond.TypeAttr == "MaxWeight" {
									weight = cond.ValueAttr
								}
								if cond.TypeAttr == "Quantity" {
									strQuan := cond.ValueAttr
									quan, err = strconv.ParseInt(strQuan, 10, 0)
									if err != nil {
										return nil, errors.Wrap(err, "ParseInt convQuan")
									}
								} else if cond.TypeAttr == "MaxQuantity" {
									quan = 1
								}

								if cond.TypeAttr == "Dimensions" {
									dimen = cond.ValueAttr
								}

								if cond.TypeAttr == "TravellerType" {
									paxTypes = strings.Split(cond.ValueAttr, ",")
								}

								if cond.TypeAttr == "Direction" && cond.ValueAttr == "Outbound" {
									itiIndex = 1
								}

								if cond.TypeAttr == "Direction" && cond.ValueAttr == "Inbound" {
									itiIndex = 2
								}
							}

							// Begin validate pax types
							if quan == 0 {
								continue
							}

							if weight == "" {
								weight = dimen
							}

							// SmallCabinBag not include TravellerType
							if len(paxTypes) == 0 {
								paxTypes = append(paxTypes, string(enum.PaxTypeAdult))

								if req.Passengers.CHD > 0 {
									paxTypes = append(paxTypes, string(enum.PaxTypeChildren))
								}
							}

							// Cover missing children bag
							if req.Passengers.CHD > 0 && !lo.Contains(paxTypes, string(enum.PaxTypeChildren)) {
								paxTypes = append(paxTypes, string(enum.PaxTypeChildren))
							}

							// End validate pax types

							const paxTypeYTH = "YTH"

							for _, code := range cabinCodes {
								for _, paxType := range paxTypes {
									if paxType == string(enum.PaxTypeChildren) && req.Passengers.CHD == 0 ||
										paxType == string(enum.PaxTypeInfant) && req.Passengers.INF == 0 ||
										paxType == paxTypeYTH {
										continue
									}

									mapKey := buildFlightBaggageKey(string(*v.Supplier), code)
									bag := &domain.BaggageInfo{
										Name:          weight,
										Price:         price,
										IsHandBaggage: isHandBag,
										Quantity:      quan,
										PaxType:       enum.PaxType(paxType),
									}

									if !isHandBag {
										bagIdenKey := fmt.Sprintf("%s-%d-%s-%d", bag.Name, bag.Quantity, bag.PaxType, itiIndex)
										if seenBags[bagIdenKey] {
											continue
										}
										seenBags[bagIdenKey] = true
									}

									if itiIndex == 0 {
										outwardBaggageMap[mapKey] = append(outwardBaggageMap[mapKey], bag)

										if req.IsRoundTrip() {
											returnBaggageMap[mapKey] = append(returnBaggageMap[mapKey], bag)
										}
									}

									if itiIndex == 1 {
										outwardBaggageMap[mapKey] = append(outwardBaggageMap[mapKey], bag)
									}

									if itiIndex == 2 {
										returnBaggageMap[mapKey] = append(returnBaggageMap[mapKey], bag)
									}
								}
							}
							//
						}
					}
				}
			}
		}

		for _, g := range v.GroupList.Group {
			if g == nil || g.OutwardList == nil || len(g.OutwardList.Outward) == 0 {
				continue
			}
			var totalFareInfo *domain.SearchTotalFareInfo

			if g.Price != nil {
				totalFareInfo = CalculateTotalFarePrice(g.Price, req).ConvertToSearch()
			}

			for _, outward := range g.OutwardList.Outward {
				iter := &domain.ResponseFlight{
					FlightID:   helpers.GenerateFlightID(enum.FlightProviderTravelFusion),
					Provider:   enum.FlightProviderTravelFusion,
					OptionType: enum.FlightOptionTypeRecommend,
				}

				itinerary, err := ItineraryToHub(1, outward, req.Passengers.ADT)
				if err != nil {
					return nil, err
				}

				bagKey := buildFlightBaggageKey(string(*v.Supplier), itinerary.CabinClassCode)
				itinerary.FreeBaggage = outwardBaggageMap[bagKey]

				iter.Itineraries = append(iter.Itineraries, itinerary)

				if g.ReturnList != nil && len(g.ReturnList.Return) > 0 {
					for _, returnItem := range g.ReturnList.Return {
						itineraryReturn, err := ItineraryToHub(2, returnItem, req.Passengers.ADT)
						if err != nil {
							return nil, err
						}

						if itineraryReturn.DepartDate-itinerary.ArrivalDate < (time.Hour.Milliseconds() * 3) {
							continue
						}

						returnBagKey := buildFlightBaggageKey(string(*v.Supplier), itineraryReturn.CabinClassCode)
						itineraryReturn.FreeBaggage = returnBaggageMap[returnBagKey]

						tmpIter := &domain.ResponseFlight{
							FlightID:   helpers.GenerateFlightID(enum.FlightProviderTravelFusion),
							Provider:   enum.FlightProviderTravelFusion,
							OptionType: enum.FlightOptionTypeRecommend,
						}

						tmpIter.Itineraries = append(tmpIter.Itineraries, itinerary, itineraryReturn)
						tmpIter.CommitV2(req)

						if totalFareInfo != nil {
							tmpIter.SearchTotalFareInfo = *totalFareInfo
						}

						res = append(res, tmpIter)

						continue
					}
				} else {
					iter.CommitV2(req)

					if totalFareInfo != nil {
						iter.SearchTotalFareInfo = *totalFareInfo
					}

					res = append(res, iter)
				}
			}
		}

	}

	return res, nil
}

func CalculateTotalFarePrice(price *responses.CommonPrice, req *domain.SearchFlightsRequest) *domain.TotalFareInfo {
	if price == nil || price.PassengerPriceList == nil {
		return nil
	}

	totalOutsideTax := getAdditionalTotalTaxAmount(price.TaxItemList) / float64(req.Passengers.ADT)

	totalFare := &domain.TotalFareInfo{}
	paxTypeMap := map[enum.PaxType]*domain.ItineraryPaxFare{}
	mapPaxQuan := map[enum.PaxType]int{
		enum.PaxTypeAdult:    req.Passengers.ADT,
		enum.PaxTypeChildren: req.Passengers.CHD,
		enum.PaxTypeInfant:   req.Passengers.INF,
	}

	for _, pPrice := range price.PassengerPriceList.PassengerPrice {
		tfPType := tfHelpers.GetPaxTypeFromAge(pPrice.Age)
		pType := enum.PaxType(tfPType)

		if paxTypeMap[pType] != nil {
			continue
		}

		priceAmount := pPrice.Amount
		totalTax := float64(0)

		if pPrice.TaxItemList != nil {
			for _, item := range pPrice.TaxItemList.TaxItem {
				totalTax += item.Amount
			}
		}

		if pType == enum.PaxTypeAdult {
			totalTax += totalOutsideTax
			priceAmount += totalOutsideTax
		}

		if paxTypeMap[pType] == nil {
			paxTypeMap[pType] = &domain.ItineraryPaxFare{
				PaxType:    pType,
				FareAmount: priceAmount,
				FareBasic:  priceAmount - totalTax,
				TaxAmount:  totalTax,
				Currency:   pPrice.Currency,
			}
		}
	}

	var tta, ttt float64
	var tc string

	for _, v := range paxTypeMap {
		totalFare.TotalPaxFares = append(totalFare.TotalPaxFares, v)
		tta += v.FareAmount * float64(mapPaxQuan[v.PaxType])
		ttt += v.TaxAmount * float64(mapPaxQuan[v.PaxType])
		tc = v.Currency
	}

	totalFare.BaseTotalFareAmount = tta
	totalFare.TotalTaxAmount = ttt
	totalFare.TotalFareBasic = tta - ttt
	totalFare.Currency = tc

	return totalFare
}

func getTerminal(segmentLocation *responses.SegmentLocationType) string {
	if segmentLocation == nil || segmentLocation.Terminal == "" {
		return ""
	}

	return segmentLocation.Terminal
}

func getAirCraft(aircraftType *responses.RouterAircraftType) string {
	if aircraftType == nil || aircraftType.AircraftCode == "" && aircraftType.AircraftName == "" {
		return ""
	}

	return constants.GetAirCraftName(aircraftType.AircraftCode)
}
