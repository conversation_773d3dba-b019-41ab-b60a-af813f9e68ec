package helpers

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/nyaruka/phonenumbers"
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities/requests"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities/responses"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func GetRequestAddress(v *entities.Address) *requests.BookingProfileAddressType {
	if v == nil {
		return nil
	}

	return &requests.BookingProfileAddressType{
		Company:        v.Company,
		Flat:           v.Flat,
		BuildingName:   v.BuildingName,
		BuildingNumber: v.BuildingNumber,
		Street:         v.Street,
		Locality:       v.Locality,
		City:           v.City,
		Province:       v.Province,
		Postcode:       v.Postcode,
		CountryCode:    v.CountryCode,
	}
}

func GetRequestCreditCard(v *entities.CreditCard) *requests.BookingProfileCreditCardType {
	if v == nil {
		return nil
	}

	return &requests.BookingProfileCreditCardType{
		Company: v.Company,
		NameOnCard: &requests.CommonName{
			NamePartList: &requests.CommonNamePartListType{
				NamePart: []string{v.OwnerName},
			},
		},
		Number:       v.Number,
		SecurityCode: v.SecurityCode,
		ExpiryDate:   time.UnixMilli(v.ExpiryDate).Format(constants.MMYYFormat),
		StartDate:    time.UnixMilli(v.StartDate).Format(constants.MMYYFormat),
		CardType:     v.CardType,
		IssueNumber:  v.IssueNumber,
	}
}

func getStringValue[T any](i *T) string {
	return fmt.Sprintf("%v", lo.FromPtr(i))
}

func GetPhoneNumber(phoneCode, phoneNumber string) (*requests.CommonPhone, error) {
	phone, err := phonenumbers.Parse(fmt.Sprintf("+%s%s", phoneCode, phoneNumber), "")
	if err != nil {
		return nil, fmt.Errorf("GetPhoneNumber: phonenumbers.Parse")
	}

	return &requests.CommonPhone{
		InternationalCode: "00" + getStringValue(phone.CountryCode),
		Number:            getStringValue(phone.NationalNumber),
		Extension:         getStringValue(phone.Extension),
	}, nil
}

func IsEqualItinerary(itinery *domain.FlightItinerary, legData *responses.FlightLegData) (bool, error) {
	if len(itinery.Segments) == 0 || len(legData.SegmentList.Segment) == 0 {
		return false, fmt.Errorf("IsEqualItinerary: empty segment (segment length = 0)")
	}

	for k, v := range itinery.Segments {
		tfSegment := legData.SegmentList.Segment[k]
		departDate, err := time.Parse(constants.DDMMYYYHHMMFormat, tfSegment.DepartDate)
		if err != nil {
			return false, fmt.Errorf("IsEqualItinerary: departDate.time.Parse")
		}

		arrivalDate, err := time.Parse(constants.DDMMYYYHHMMFormat, tfSegment.ArriveDate)
		if err != nil {
			return false, fmt.Errorf("IsEqualItinerary: arrivalDate.time.Parse")
		}

		hubItinery := fmt.Sprintf("%s-%s-%d-%d-%d-%s-%s-%d-%s", v.DepartPlace, v.ArrivalPlace, v.DepartDate, v.ArrivalDate, v.FlightDuration, v.FlightNumber, v.CabinClassCode, itinery.FlightDuration, v.BookingClass)
		tfItinery := fmt.Sprintf("%s-%s-%d-%d-%d-%d-%s-%d-%s", tfSegment.Origin.Code, tfSegment.Destination.Code, departDate.UnixMilli(), arrivalDate.UnixMilli(), tfSegment.Duration, tfSegment.FlightId.Number, tfSegment.TravelClass.SupplierClass, legData.Duration, tfSegment.TravelClass.SupplierRBDCode)

		if hubItinery != tfItinery {
			return false, nil
		}
	}

	return true, nil
}

func GenerateTFItiKey(in *responses.FlightLegData) (string, error) {
	key := ""

	if len(in.SegmentList.Segment) > 0 {
		key += fmt.Sprintf("%s-%s-", in.SegmentList.Segment[0].TravelClass.SupplierClass, in.SegmentList.Segment[0].TravelClass.SupplierRBDCode)
	}

	for _, seg := range in.SegmentList.Segment {
		departDate, err := time.Parse(constants.DDMMYYYHHMMFormat, seg.DepartDate)
		if err != nil {
			return "", fmt.Errorf("GenerateTFItiKey: departDate.time.Parse")
		}

		arrivalDate, err := time.Parse(constants.DDMMYYYHHMMFormat, seg.ArriveDate)
		if err != nil {
			return "", fmt.Errorf("GenerateTFItiKey: arrivalDate.time.Parse")
		}

		key += fmt.Sprintf("%s%s%s%d%d%d",
			seg.Origin.Code,
			seg.Destination.Code,
			seg.VendingOperator.Code,
			seg.FlightId.Number,
			departDate.UnixMilli(),
			arrivalDate.UnixMilli(),
		)
	}

	hash := md5.Sum([]byte(key))
	return hex.EncodeToString(hash[:]), nil
}
