package helpers

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/entities/requests"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func GetListSearchTraveler(v domain.PaxRequest) []*requests.StartRoutingTraveller {
	travellers := make([]*requests.StartRoutingTraveller, 0, v.ADT+v.CHD+v.INF)

	for i := 0; i < v.ADT; i++ {
		travellers = append(travellers, &requests.StartRoutingTraveller{
			Age: constants.AgeAdt,
		})
	}

	for i := 0; i < v.CHD; i++ {
		travellers = append(travellers, &requests.StartRoutingTraveller{
			Age: constants.AgeChd,
		})
	}

	for i := 0; i < v.INF; i++ {
		travellers = append(travellers, &requests.StartRoutingTraveller{
			Age: constants.AgeInf,
		})
	}

	return travellers
}

func GetPaxTypeFromAge(age int) enum.PaxType {
	if age > 11 {
		return enum.PaxTypeAdult
	}

	if age > 1 && age < 12 {
		return enum.PaxTypeChildren
	}

	if age < 2 && age > -1 {
		return enum.PaxTypeInfant
	}

	return enum.PaxTypeNone
}

func GetTitleFromGenderType(t enum.GenderType) string {
	mapType := map[enum.GenderType]string{
		enum.GenderTypeFemale: "Miss",
		enum.GenderTypeMale:   "Mr",
	}

	return mapType[t]
}

func GetTravelerName(v *entities.Traveller) []string {
	// if v == nil {
	// 	return []requests.Value{}
	// }
	// return []requests.Value{{Value: v.FirstName}, {Value: ""}, {Value: v.LastName}}
	if v == nil {
		return []string{}
	}
	return []string{v.FirstName, v.LastName}
}

func GetTravelerRequestName(v *entities.Traveller) *requests.CommonName {
	if v == nil {
		return nil
	}
	return &requests.CommonName{
		Title: GetTitleFromGenderType(v.GenderType),
		NamePartList: &requests.CommonNamePartListType{
			NamePart: GetTravelerName(v),
		},
	}
}

func GetContactInfoName(title, firstName, lastName string) *requests.CommonName {
	return &requests.CommonName{
		Title: title,
		NamePartList: &requests.CommonNamePartListType{
			NamePart: []string{firstName, lastName},
		},
	}
}
