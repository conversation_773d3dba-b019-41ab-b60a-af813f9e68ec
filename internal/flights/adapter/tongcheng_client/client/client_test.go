package client

import (
	"context"
	"testing"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/encryption"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
)

func getTestInstance() *tongchengClient {
	return &tongchengClient{
		requestRepo: nil,
		encrypt:     encryption.NewAESGzipEncryption("1234567890123456"),
		BaseURL:     "http://www.uat.itravelsaas.com",
		Username:    "LCWY",
		PID:         "LCWY",
		Env:         "dev",
		ProxyURL:    "http://dttechuser:FJpP19Ca2hhO4mXMVAqocrUf@**************:8888",
	}
}

func TestSearchFlight(t *testing.T) {

	// Create a new instance of tongchengClient
	client := getTestInstance()

	// Create a new SearchFlightRequest object
	req := &entities.SearchFlightRequest{
		SearchParams: &entities.SearchParams{
			TripType: enum.TripTypeOneWay,
			FromCity: "BJS",
			FromDate: "20251211",
			RetDate:  "",
			ToCity:   "BKK",
			CabinGrade: []enum.CabinGrade{
				enum.EconomyClass,
			},
			AdultNum: 1,
			ChildNum: 0,
		},
	}

	// Call the SearchFlight function
	res, err := client.SearchFlight(context.Background(), "tracingID", req)
	if err != nil {
		t.Errorf("Error calling SearchFlight: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.Status != constants.SearchSuccessCode {
		t.Errorf("Expected ErrorCode to be %s, but got %s", constants.SearchSuccessCode, res.Status)
	}

}

func TestSearchRefund(t *testing.T) {

	// Create a new instance of tongchengClient
	client := getTestInstance()

	// Create a new SearchRefundRequest object
	req := &entities.SearchRefundRequest{
		SearchRefundParams: &entities.SearchRefundParams{
			DataID: "46ebb7d8765b11f0bce9a5937e703bf7$&$84",
		},
	}

	// Call the SearchRefund function
	res, err := client.SearchRefund(context.Background(), "tracingID", req)
	if err != nil {
		t.Errorf("Error calling SearchRefund: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.IsSuccess != true {
		t.Errorf("Expected ErrorCode to be %v, but got %v", true, res.IsSuccess)
	}

}

func TestVerify(t *testing.T) {

	// Create a new instance of tongchengClient
	client := getTestInstance()

	// Create a new VerifyRequest object
	req := &entities.VerifyRequest{
		VerifyParams: &entities.VerifyParams{
			TripType:    enum.TripTypeOneWay,
			AdultNumber: 1,
			ChildNumber: 0,
			Routing: &entities.Routing{
				Data:      "89449331773511f099a06141d8e4287c$&$68",
				FareBasis: "E1ABDA0T/E1ABDA0T",
				FromSegment: []*entities.Segment{
					{
						CabinGrade:       enum.EconomyClass,
						Cabin:            "B",
						CabinCount:       9,
						DepAirport:       "PEK",
						DepTime:          "************",
						ArrTime:          "************",
						ArrAirport:       "TFU",
						FlightNumber:     "3U3865",
						Carrier:          "3U",
						OperatingCarrier: "",
						AircraftCode:     "332",
						DepTerminal:      "T2",
						ArrTerminal:      "T2",
					},
					{
						CabinGrade:       enum.EconomyClass,
						Cabin:            "E",
						CabinCount:       5,
						DepAirport:       "TFU",
						DepTime:          "************",
						ArrTime:          "************",
						ArrAirport:       "BKK",
						FlightNumber:     "3U3935",
						Carrier:          "3U",
						OperatingCarrier: "",
						AircraftCode:     "320",
						DepTerminal:      "T1",
						ArrTerminal:      "",
					},
				},
				RetSegment: []*entities.Segment{},
			},
		},
	}

	// Call the Verify function
	res, err := client.Verify(context.Background(), "tracingID", req)
	if err != nil {
		t.Errorf("Error calling Verify: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.Status != constants.VerifySuccessCode {
		t.Errorf("Expected ErrorCode to be %s, but got %s", constants.VerifySuccessCode, res.Status)
	}

}

func TestBooking(t *testing.T) {
	// Create a new instance of tongchengClient
	client := getTestInstance()

	// Create a new BookingRequest object
	req := &entities.BookingRequest{
		BookingParams: &entities.BookingParams{
			SessionID: "SYBJSBKKOW11202508121233457354599762680860672",
			TripType:  enum.TripTypeOneWay,
			Routing: &entities.Routing{
				Data:      "89449331773511f099a06141d8e4287c$&$68",
				FareBasis: "E1ABDA0T/E1ABDA0T",
				FromSegment: []*entities.Segment{
					{
						CabinGrade:       enum.EconomyClass,
						Cabin:            "B",
						CabinCount:       9,
						DepAirport:       "PEK",
						DepTime:          "************",
						ArrTime:          "************",
						ArrAirport:       "TFU",
						FlightNumber:     "3U3865",
						Carrier:          "3U",
						OperatingCarrier: "",
						AircraftCode:     "332",
						DepTerminal:      "T2",
						ArrTerminal:      "T2",
					},
					{
						CabinGrade:       enum.EconomyClass,
						Cabin:            "E",
						CabinCount:       5,
						DepAirport:       "TFU",
						DepTime:          "************",
						ArrTime:          "************",
						ArrAirport:       "BKK",
						FlightNumber:     "3U3935",
						Carrier:          "3U",
						OperatingCarrier: "",
						AircraftCode:     "320",
						DepTerminal:      "T1",
						ArrTerminal:      "",
					},
				},
				RetSegment: []*entities.Segment{},
			},
			Passenger: []*entities.Passenger{
				{
					BirthDay:       "19800225",
					CardIssuePlace: "CN",
					LastName:       "Lucas",
					FirstName:      "Morris",
					CardNum:        "G756789",
					Gender:         enum.Male,
					Nationality:    "CN",
					CardExpired:    "20290101",
					CardType:       "PP",
					PassengerID:    "TC1232522",
					AgeType:        enum.Adult,
				},
			},
			Contact: []*entities.Contact{
				{
					Name:     "Lucas Morris",
					Email:    "<EMAIL>",
					Mobile:   "13512345678",
					Address:  "Beijing",
					PostCode: "12345",
				},
			},
		},
	}

	// Call the Booking function
	res, err := client.Booking(context.Background(), "tracingID", req)
	if err != nil {
		t.Errorf("Error calling Booking: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.IsSuccess != true {
		t.Errorf("Expected ErrorCode to be %v, but got %v", true, res.IsSuccess)
	}

}

func TestOrderDetail(t *testing.T) {
	// Create a new instance of tongchengClient
	client := getTestInstance()

	// Create a new OrderDetailRequest object
	req := &entities.OrderDetailRequest{
		OrderNo: "BJ1404806211858993152",
	}

	// Call the OrderDetail function
	res, err := client.OrderDetail(context.Background(), "tracingID", req)
	if err != nil {
		t.Errorf("Error calling OrderDetail: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.IsSuccess != true {
		t.Errorf("Expected ErrorCode to be %v, but got %v", true, res.IsSuccess)
	}

}

func TestIssueTicket(t *testing.T) {
	// Create a new instance of tongchengClient
	client := getTestInstance()

	// Create a new IssueTicketRequest object
	req := &entities.IssueTicketRequest{
		NotificationParams: &entities.NotificationParams{
			EngineSerialNo: "BJ1404806211858993152",
			OrderNo:        "BJ1404806211858993152",
			TcSerialNo:     "BJ1404806211858993152",
			Status:         enum.READY_TO_ISSUE,
			Type:           enum.NotiTypeReadyToIssue,
		},
	}

	// Call the IssueTicket function
	res, err := client.IssueTicket(context.Background(), "tracingID", req)
	if err != nil {
		t.Errorf("Error calling IssueTicket: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.Status != constants.NotiSuccessCode {
		t.Errorf("Expected ErrorCode to be %s, but got %s", constants.NotiSuccessCode, res.Status)
	}

}

func TestCancelBooking(t *testing.T) {
	// Create a new instance of tongchengClient
	client := getTestInstance()

	// Create a new CancelBookingRequest object
	req := &entities.CancelBookingRequest{
		NotificationParams: &entities.NotificationParams{
			EngineSerialNo: "BJ1404806211858993152",
			OrderNo:        "BJ1404806211858993152",
			TcSerialNo:     "BJ1404806211858993152",
			Status:         enum.CANCEL,
			Type:           enum.NotiTypeUserCancel,
		},
	}

	// Call the CancelBooking function
	res, err := client.CancelBooking(context.Background(), "tracingID", req)
	if err != nil {
		t.Errorf("Error calling CancelBooking: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.Status != constants.NotiSuccessCode {
		t.Errorf("Expected ErrorCode to be %s, but got %s", constants.NotiSuccessCode, res.Status)
	}

}
