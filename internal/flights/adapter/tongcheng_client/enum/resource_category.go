package enum

type ResourceCategory uint8

const (
	ResourceCategoryRegular         ResourceCategory = 0 // 0: Regular resource (default)
	ResourceCategoryBundled         ResourceCategory = 1 // 1: Bundled product
	ResourceCategoryLuggage         ResourceCategory = 2 // 2: Luggage package
	ResourceCategoryRequestedTicket ResourceCategory = 3 // 3: Requested ticket
	ResourceCategoryStudent         ResourceCategory = 4 // 4: Student ticket (STU)
	ResourceCategoryYouth           ResourceCategory = 5 // 5: Youth ticket (YTH)
	ResourceCategoryGroup           ResourceCategory = 6 // 6: Group ticket (GV)
)
