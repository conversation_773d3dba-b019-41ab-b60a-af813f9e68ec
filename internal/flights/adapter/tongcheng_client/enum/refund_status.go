package enum

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type RefundStatus string

const (
	RefundStatusUnknown                  RefundStatus = "unknown"
	RefundStatusNonRefundable            RefundStatus = "T"
	RefundStatusRefundableWithConditions RefundStatus = "H"
	RefundStatusFreeRefund               RefundStatus = "F"
)

var RefundIsPermittedMap = map[RefundStatus]enum.Permitted{
	RefundStatusUnknown:                  enum.PermittedMap[enum.PermittedUnknownInt],
	RefundStatusNonRefundable:            enum.PermittedMap[enum.PermittedNotAllowedInt],
	RefundStatusRefundableWithConditions: enum.PermittedMap[enum.PermittedAllowedInt],
	RefundStatusFreeRefund:               enum.PermittedMap[enum.PermittedAllowedInt],
}
