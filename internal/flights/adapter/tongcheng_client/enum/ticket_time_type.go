package enum

import "time"

type TicketTimeType uint8

const (
	TicketTimeUltraFast     TicketTimeType = 0 // 0: ≤10min (ultra-fast ticketing)
	TicketTimeWithin2Hours  TicketTimeType = 1 // 1: within 2 hours after payment
	TicketTimeWithin48Hours TicketTimeType = 2 // 2: within 48 hours after payment
	TicketTimeBefore24Hours TicketTimeType = 3 // 3: before departure 24 hours in advance (only for productType=5)
)

// Duration returns the duration associated with the ticketing type.
func (t TicketTimeType) Duration() time.Duration {
	switch t {
	case TicketTimeUltraFast:
		return 10 * time.Minute
	case TicketTimeWithin2Hours:
		return 2 * time.Hour
	case TicketTimeWithin48Hours:
		return 48 * time.Hour
	case TicketTimeBefore24Hours:
		return 24 * time.Hour
	default:
		return 0
	}
}
