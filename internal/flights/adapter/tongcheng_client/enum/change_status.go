package enum

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type ChangeStatus string

const (
	ChangeStatusUnknown                  ChangeStatus = "unknown"
	ChangeStatusNonChangeable            ChangeStatus = "T"
	ChangeStatusChangeableWithConditions ChangeStatus = "H"
	ChangeStatusFreeChangeable           ChangeStatus = "F"
)

var ChangeStatusIsPermittedMap = map[ChangeStatus]enum.Permitted{
	ChangeStatusUnknown:                  enum.PermittedMap[enum.PermittedUnknownInt],
	ChangeStatusNonChangeable:            enum.PermittedMap[enum.PermittedNotAllowedInt],
	ChangeStatusChangeableWithConditions: enum.PermittedMap[enum.PermittedAllowedInt],
	ChangeStatusFreeChangeable:           enum.PermittedMap[enum.PermittedAllowedInt],
}
