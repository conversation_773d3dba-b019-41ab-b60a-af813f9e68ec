package enum

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type RefundInfoType int32

const (
	RefundInfoTypeNone RefundInfoType = iota - 1
	RefundInfoTypeFull
	RefundInfoTypePartial
)

var RefundInfoTypeMap = map[RefundInfoType]enum.Situation{
	RefundInfoTypeNone:    "",
	RefundInfoTypeFull:    enum.SituationBeforeDeparture,
	RefundInfoTypePartial: enum.SituationAfterDeparture,
}
