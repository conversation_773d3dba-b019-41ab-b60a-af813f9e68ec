package enum

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type ChangeInfoType int32

const (
	ChangeInfoTypeNone ChangeInfoType = iota - 1
	ChangeInfoTypeFull
	ChangeInfoTypePartial
)

var ChangeInfoTypeMap = map[ChangeInfoType]enum.Situation{
	ChangeInfoTypeNone:    "",
	ChangeInfoTypeFull:    enum.SituationBeforeDeparture,
	ChangeInfoTypePartial: enum.SituationAfterDeparture,
}
