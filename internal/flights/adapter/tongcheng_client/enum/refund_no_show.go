package enum

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type RefundNoShow string

const (
	RefundNoShowUnknown                  RefundNoShow = "E"
	RefundNoShowNonRefundable            RefundNoShow = "T"
	RefundNoShowRefundableWithConditions RefundNoShow = "H"
	RefundNoShowFreeRefund               RefundNoShow = "F"
)

var RefundNoShowIsPermittedMap = map[RefundNoShow]enum.Permitted{
	RefundNoShowUnknown:                  enum.PermittedMap[enum.PermittedUnknownInt],
	RefundNoShowNonRefundable:            enum.PermittedMap[enum.PermittedNotAllowedInt],
	RefundNoShowRefundableWithConditions: enum.PermittedMap[enum.PermittedAllowedInt],
	RefundNoShowFreeRefund:               enum.PermittedMap[enum.PermittedAllowedInt],
}
