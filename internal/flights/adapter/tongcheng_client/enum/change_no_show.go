package enum

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type ChangeNoShow string

const (
	ChangeNoShowUnknown                  ChangeNoShow = "E"
	ChangeNoShowNonChangeable            ChangeNoShow = "T"
	ChangeNoShowChangeableWithConditions ChangeNoShow = "H"
	ChangeNoShowFreeChangeable           ChangeNoShow = "F"
)

var ChangeNoShowIsPermittedMap = map[ChangeNoShow]enum.Permitted{
	ChangeNoShowUnknown:                  enum.PermittedMap[enum.PermittedUnknownInt],
	ChangeNoShowNonChangeable:            enum.PermittedMap[enum.PermittedNotAllowedInt],
	ChangeNoShowChangeableWithConditions: enum.PermittedMap[enum.PermittedAllowedInt],
	ChangeNoShowFreeChangeable:           enum.PermittedMap[enum.PermittedAllowedInt],
}