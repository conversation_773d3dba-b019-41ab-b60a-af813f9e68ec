package tongcheng_client

import (
	"context"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/helpers"
	flightEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

type tongchengAdapter struct {
	cfg    *config.Schema
	client client.TongChengClient
}

type TongChengAdapter interface {
	SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error)
	CheckFare(ctx context.Context, req *domain.SearchFlightsRequest, flight *domain.ResponseFlight, tracingID string) (*domain.CheckFareInfo, error)
	FetchEticketInfo(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, bool, error)
	CreateBooking(ctx context.Context, bookingDetails *domain.BookingDetails, pnr *domain.PNR, tracingID string) (*domain.SvcCreateBookingResponse, error)
	CancelBooking(ctx context.Context, tracingID, orderNo string) error
	IssueTicket(ctx context.Context, bkSession *domain.BookingSession, paxInfo []*domain.PaxInfo, tracingID string) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, flightEnum.EMDStatus, bool, int64, error)
}

func NewTongChengAdapter(cfg *config.Schema, requestRepo repositories.RequestRepository) TongChengAdapter {
	{
		return &tongchengAdapter{
			cfg:    cfg,
			client: client.NewTongChengClient(cfg, requestRepo),
		}
	}
}

func (a *tongchengAdapter) SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error) {
	if req == nil || req.IsMultiItinerary() || req.Passengers.INF > 0 {
		return nil, nil
	}

	out := []*domain.ResponseFlight{}
	var (
		wg      sync.WaitGroup
		mu      sync.Mutex
		errChan = make(chan error, len(constants.CabinClasses))
	)

	for _, class := range constants.CabinClasses {
		wg.Add(1)
		go func() {
			defer wg.Done()

			clientReq := converts.ToSearchFlightRequest(req, class)

			data, err := a.client.SearchFlight(ctx, tracingID, clientReq)
			if err != nil {
				errChan <- errors.Wrap(err, "client.SearchFlight")
				return
			}

			res, err := converts.ToDomainResponseFlights(data, req)
			if err != nil {
				errChan <- errors.Wrap(err, "ToDomainResponseFlights")
				return
			}

			mu.Lock()
			out = append(out, res...)
			mu.Unlock()
		}()
	}

	wg.Wait()

	close(errChan)

	for err := range errChan {
		return nil, err
	}

	return out, nil
}

func (a *tongchengAdapter) FetchEticketInfo(
	ctx context.Context,
	booking *domain.BookingSession,
	pnr []*domain.PaxInfo,
) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, bool, error) {
	if booking == nil || len(pnr) == 0 {
		return nil, nil, false, nil
	}

	clientReq := converts.ToOrderDetailRequest(booking)

	data, err := a.client.OrderDetail(ctx, booking.BookingRef, clientReq)
	if err != nil {
		return nil, nil, false, errors.Wrap(err, "client.OrderDetail")
	}

	if data == nil || data.OrderVO == nil {
		return nil, nil, false, nil
	}

	issueFailedStatuses := []enum.Status{
		enum.StatusReservationFailed,
		enum.StatusCanceled,
		enum.StatusRefundToBeReimbursed,
		enum.StatusRefundReimbursed,
	}
	if lo.Contains(issueFailedStatuses, data.OrderVO.Status) {
		return nil, nil, false, domain.ErrIssueTicketFailed
	}

	issueOKStatuses := []enum.Status{
		enum.StatusToBeIssued,
		enum.StatusIssued,
	}

	if !lo.Contains(issueOKStatuses, data.OrderVO.Status) {
		return nil, nil, false, errors.New("invalid order status " + string(data.OrderVO.Status))
	}

	reservationInfos, ticketInfos, err := converts.ToDomainReservationInfos(data.OrderVO, booking, pnr)
	if err != nil {
		return nil, nil, false, errors.Wrap(err, "converts.ToDomainReservationInfos")
	}

	isPending := true

	if data.OrderVO.Status == enum.StatusToBeIssued {
		isPending = false
	}

	return reservationInfos, ticketInfos, isPending, nil
}

func (a *tongchengAdapter) CancelBooking(ctx context.Context, tracingID, orderNo string) error {
	clientReq := converts.ToCancelBookingRequest(orderNo)

	_, err := a.client.CancelBooking(ctx, tracingID, clientReq)

	return err
}

func (a *tongchengAdapter) IssueTicket(
	ctx context.Context,
	bkSession *domain.BookingSession,
	paxInfo []*domain.PaxInfo,
	tracingID string,
) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, flightEnum.EMDStatus, bool, int64, error) {
	if bkSession == nil {
		log.Error("bkSession is nil in IssueTicket")
		return nil, nil, flightEnum.EMDStatusUnknown, false, 0, nil
	}

	clientReq := converts.ToIssueTicketRequest(bkSession.OrderNumRef)

	ticketingRes, err := a.client.IssueTicket(ctx, tracingID, clientReq)
	if err != nil {
		return nil, nil, flightEnum.EMDStatusEmpty, false, 0, errors.Wrap(err, "client.IssueTicket")
	}

	if ticketingRes != nil && ticketingRes.Status == constants.NotiFailCode {
		log.Error("IssueTicket failed", log.String("orderNumRef", bkSession.OrderNumRef), log.String("error", ticketingRes.Msg))
		return nil, nil, flightEnum.EMDStatusEmpty, false, 0, domain.ErrIssueTicketFailed
	}

	orderDetailReq := converts.ToOrderDetailRequest(bkSession)

	data, err := a.client.OrderDetail(ctx, bkSession.BookingRef, orderDetailReq)
	if err != nil {
		return nil, nil, flightEnum.EMDStatusEmpty, false, 0, errors.Wrap(err, "client.OrderDetail")
	}

	issueOKStatuses := []enum.Status{
		enum.StatusToBeIssued,
		enum.StatusIssued,
	}

	if !lo.Contains(issueOKStatuses, data.OrderVO.Status) {
		return nil, nil, flightEnum.EMDStatusEmpty, false, 0, errors.New("invalid order status " + string(data.OrderVO.Status))
	}

	reservationInfos, ticketInfos, err := converts.ToDomainReservationInfos(data.OrderVO, bkSession, paxInfo)
	if err != nil {
		return nil, nil, flightEnum.EMDStatusEmpty, false, 0, errors.Wrap(err, "converts.ToDomainReservationInfos")
	}

	isPending := false
	pendingDl := int64(0)

	routing, err := helpers.GetTongChengRoutingMetadata(bkSession.Metadata)
	if err != nil {
		log.Error("GetTongChengRoutingMetadata failed", log.String("error", err.Error()))
	}

	if data.OrderVO.Status == enum.StatusToBeIssued {
		isPending = true
		if routing != nil {
			pendingDl = time.Now().Add(routing.TicketTimeType.Duration()).UnixMilli()
		}
	}

	return reservationInfos, ticketInfos, flightEnum.EMDStatusEmpty, isPending, pendingDl, nil
}
