package encryption

import (
	"bytes"
	"compress/gzip"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"io"
)

type Encryption interface {
	EncryptAndCompress(plainText string) (string, error)
	DecompressAndDecrypt(compressedText string) (string, error)
}

type AESGzipEncryption struct {
	Key string
}

func NewAESGzipEncryption(key string) Encryption {
	return &AESGzipEncryption{
		Key: key,
	}
}

func (c *AESGzipEncryption) EncryptAndCompress(plainText string) (string, error) {
	encrypted, err := c.encryptAES(plainText)
	if err != nil {
		return "", err
	}
	return compressGzip(encrypted)
}

func (c *AESGzipEncryption) DecompressAndDecrypt(compressedText string) (string, error) {
	decompressed, err := decompressGzip(compressedText)
	if err != nil {
		return "", err
	}
	return c.decryptAES(decompressed)
}

func (c *AESGzipEncryption) encryptAES(plainText string) (string, error) {
	block, err := aes.NewCipher([]byte(c.Key))
	if err != nil {
		return "", err
	}
	iv := make([]byte, aes.BlockSize)
	padded := pad([]byte(plainText), aes.BlockSize)
	cipherText := make([]byte, len(padded))

	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(cipherText, padded)

	return base64.StdEncoding.EncodeToString(cipherText), nil
}

func (c *AESGzipEncryption) decryptAES(cipherTextBase64 string) (string, error) {
	cipherText, err := base64.StdEncoding.DecodeString(cipherTextBase64)
	if err != nil {
		return "", err
	}
	block, err := aes.NewCipher([]byte(c.Key))
	if err != nil {
		return "", err
	}
	iv := make([]byte, aes.BlockSize)
	plainPadded := make([]byte, len(cipherText))

	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(plainPadded, cipherText)

	unpadded, err := unpad(plainPadded)
	if err != nil {
		return "", err
	}
	return string(unpadded), nil
}

func pad(data []byte, blockSize int) []byte {
	p := blockSize - len(data)%blockSize
	return append(data, bytes.Repeat([]byte{byte(p)}, p)...)
}

func unpad(data []byte) ([]byte, error) {
	l := len(data)
	if l == 0 {
		return nil, fmt.Errorf("empty input")
	}
	p := data[l-1]
	if int(p) > l {
		return nil, fmt.Errorf("invalid padding")
	}
	return data[:l-int(p)], nil
}

func compressGzip(data string) (string, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	_, err := writer.Write([]byte(data))
	if err != nil {
		return "", err
	}
	writer.Close() // nolint: errcheck
	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}

func decompressGzip(b64data string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(b64data)
	if err != nil {
		return "", err
	}
	reader, err := gzip.NewReader(bytes.NewReader(data))
	if err != nil {
		return "", err
	}
	defer reader.Close() // nolint: errcheck
	out, err := io.ReadAll(reader)
	return string(out), err
}
