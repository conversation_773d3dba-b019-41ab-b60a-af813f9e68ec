package tongcheng_client

import (
	"context"
	"fmt"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func (a *tongchengAdapter) CreateBooking(ctx context.Context, bookingDetails *domain.BookingDetails, pnr *domain.PNR, tracingID string) (*domain.SvcCreateBookingResponse, error) {
	bookingReq, err := converts.ToTongChengBookingRequest(bookingDetails, pnr)
	if err != nil {
		return nil, err
	}
	res, err := a.client.Booking(ctx, tracingID, bookingReq)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, errors.ErrSomethingOccurred
	}

	if !res.IsSuccess || res.Status != "C0" {
		return nil, fmt.Errorf("TONGCHENG_BOOKING_FAILED: %s", res.ErrorMsg)
	}

	response, err := converts.FromTongChengBookingResponse(res, bookingReq.Routing)
	if err != nil {
		return nil, err
	}

	return response, nil
}
