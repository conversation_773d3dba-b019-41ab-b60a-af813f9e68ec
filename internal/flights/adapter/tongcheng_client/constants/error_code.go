package constants

// Common Tongcheng API Errors
const (
	// "请求参数错误/ Invalid request parameters"
	TongchengStatusA101 = "A101"
	// "参数校验失败/ Parameter error"
	TongchengStatusA106 = "A106"
	// "内部程序异常 / Internal system error"
	TongchengStatusA104 = "A104"
	// "请求接口频繁/ API request frequency exceeded"
	TongchengStatusA102 = "A102"
	TongchengStatusB103 = "B103"
	TongchengStatusC105 = "C105"
	// "预订时间不在销售范围内 / search OD is not in whitelist routes."
	TongchengStatusA103 = "A103"
)

// Search Flight Errors
const (
	// "无此航线报价/There are no flights on this route"
	TongchengStatusA100 = "A100"
)

// Booking Errors
const (
	// "舱位已售完/ Cabin class sold out"
	TongchengStatusB100 = "B100"
	// "价格校验失败/ Price validation failed"
	TongchengStatusB102 = "B102"
)

// Ticketing Errors
const (
	// "其他失败原因/ Other failure reasons"
	TongchengStatusC107 = "C107"
)

// Transfer Booking Errors
const (
	// "其他失败原因 / Other failure reasons"
	TongchengStatusA105 = "A105"
	// "成功 / Success"
	TongchengStatusB0 = "B0"
	// "余位不足/ Insufficient Remaining Seats"
	TongchengStatusB101 = "B101"
	// "内部程序异常/ Internal system error"
	TongchengStatusB104 = "B104"
	// "参数校验失败/ Parameter error"
	TongchengStatusB106 = "B106"
	// "舱位已售完 / Cabin class sold out"
	TongchengStatusC100 = "C100"
	// "余位不足/ Insufficient Remaining Seats"
	TongchengStatusC101 = "C101"
	// "特殊产品预订限制/ Restricted product"
	TongchengStatusC103 = "C103"
	// "同程请求参数错误/ Invalid request parameters"
	TongchengStatusC104 = "C104"
	// "内部程序异常/ Internal system error"
	TongchengStatusC106 = "C106"
	// "参数校验失败/ Parameter error"
	TongchengStatusC108 = "C108"
)

var tongchengErrorCodeText = map[string]string{
	// Common
	TongchengStatusA101: "请求参数错误/ Invalid request parameters",
	TongchengStatusA106: "参数校验失败/ Parameter error",
	TongchengStatusA104: "内部程序异常 / Internal system error",
	TongchengStatusA102: "请求接口频繁/ API request frequency exceeded",
	TongchengStatusB103: "请求接口频繁/ API request frequency exceeded",
	TongchengStatusC105: "请求接口频繁/ API request frequency exceeded",
	TongchengStatusA103: "预订时间不在销售范围内 / search OD is not in whitelist routes.",

	// Search
	TongchengStatusA100: "无此航线报价/There are no flights on this route",

	// Booking
	TongchengStatusB100: "舱位已售完/ Cabin class sold out",
	TongchengStatusB102: "价格校验失败/ Price validation failed",

	// Ticketing
	TongchengStatusC107: "其他失败原因/ Other failure reasons",

	// Transfer Booking
	TongchengStatusA105: "其他失败原因 / Other failure reasons",
	TongchengStatusB0:   "成功 / Success",
	TongchengStatusB101: "余位不足/ Insufficient Remaining Seats",
	TongchengStatusB104: "内部程序异常/ Internal system error",
	TongchengStatusB106: "参数校验失败/ Parameter error",
	TongchengStatusC100: "舱位已售完 / Cabin class sold out",
	TongchengStatusC101: "余位不足/ Insufficient Remaining Seats",
	TongchengStatusC103: "特殊产品预订限制/ Restricted product",
	TongchengStatusC104: "同程请求参数错误/ Invalid request parameters",
	TongchengStatusC106: "内部程序异常/ Internal system error",
	TongchengStatusC108: "参数校验失败/ Parameter error",
}

// TongchengErrorCodeText returns the error message for a given Tongcheng error code.
func TongchengErrorCodeText(code string) string {
	if msg, ok := tongchengErrorCodeText[code]; ok {
		return msg
	}
	return ""
}
