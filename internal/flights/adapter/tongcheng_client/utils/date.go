package utils

import (
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
)

// DateStringToUnixMilli converts a date string to a Unix timestamp in milliseconds.
// Example: "202512111155" => 1765454100000
func DateStringToUnixMilli(date, format string) (int64, error) {
	if format == "" {
		format = constants.DateFmtDefault
	}

	t, err := time.ParseInLocation(format, date, time.UTC)
	if err != nil {
		return 0, errors.Wrap(err, "parse date string")
	}
	return t.UnixMilli(), nil
}
