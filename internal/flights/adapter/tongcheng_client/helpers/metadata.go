package helpers

import (
	"errors"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
)

func GetTongChengRoutingMetadata(metadata []*domain.Metadata) (*entities.Routing, error) {
	var solutionMetadata *domain.Metadata
	for _, md := range metadata {
		if md.Key == domain.MetaKeySolution {
			solutionMetadata = md
			break
		}
	}

	if solutionMetadata == nil {
		return nil, errors.New("metadata key 'solution' not found")
	}

	var routingData entities.Routing
	if err := utils.DecodeLegacyMetadata(solutionMetadata.Value, &routingData); err != nil {
		return nil, fmt.Errorf("failed to decode solution metadata: %w", err)
	}

	return &routingData, nil
}
