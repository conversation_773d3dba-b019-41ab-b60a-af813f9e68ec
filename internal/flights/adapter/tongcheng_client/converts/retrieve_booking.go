package converts

import (
	"fmt"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func ToOrderDetailRequest(booking *domain.BookingSession) *entities.OrderDetailRequest {
	if booking == nil {
		return nil
	}

	return &entities.OrderDetailRequest{
		OrderNo: booking.OrderNumRef,
	}
}

func ToDomainReservationInfos(
	in *entities.OrderVO,
	bkSession *domain.BookingSession,
	paxInfo []*domain.PaxInfo,
) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, error) {

	reserInfo := []*domain.IssueTicketSvcReservationInfo{}

	// pPaxByIndex := map[string]*entities.Passenger{}
	paxByKey := map[string]*domain.PaxInfo{}

	for _, pax := range paxInfo {
		paxByKey[helpers.GetPaxKey(pax.GivenName, pax.Surname, pax.Gender)] = pax
	}

	if len(in.Psis) == 0 {
		for _, iti := range bkSession.Itineraries {
			reserInfo = append(reserInfo, &domain.IssueTicketSvcReservationInfo{
				ItineraryIndex:  iti.Index,
				ReservationCode: in.AirplanePnr,
			})
		}

		etickets, err := mappingTickets(in.Psis, bkSession.Itineraries, paxByKey)
		if err != nil {
			return nil, nil, errors.Wrap(err, "mappingTicketsV2")
		}

		return reserInfo, etickets, nil
	}

	etickets := []*domain.ETicketInfo{}

	itiReserInfoMap := map[int]*domain.IssueTicketSvcReservationInfo{}
	itiETicketMap := map[string]*domain.ETicketInfo{}

	for index, pnrInfo := range in.Psis {
		itiIndex := helpers.GetItiIndexByLinearSegmentIdx(bkSession.Itineraries, index+1)
		pPax := pnrInfo.Passenger
		if itiReserInfoMap[itiIndex] == nil {
			itiReserInfoMap[itiIndex] = &domain.IssueTicketSvcReservationInfo{
				ItineraryIndex:  itiIndex,
				ReservationCode: in.AirplanePnr,
			}
		}

		pax := paxByKey[helpers.GetPaxKey(pPax.FirstName, pPax.LastName, enum.GenderMap[pPax.Gender])]

		if pax == nil {
			log.Error("[ISSUE_TICKET] pax == nil", log.String("pax", helpers.GetPaxKey(pPax.FirstName, pPax.LastName, enum.GenderMap[pPax.Gender])))
			continue
		}

		key := itiETicketMapKey(itiIndex, pax.ID)

		if itiETicketMap[key] == nil {
			itiETicketMap[key] = &domain.ETicketInfo{
				PaxID:           pax.ID,
				PaxName:         fmt.Sprintf("%s %s", pax.Surname, pax.GivenName),
				ItineraryNumber: itiIndex,
				TicketNumber:    pPax.TicketNo,
				SegmentIndex:    index + 1,
				// EMDInfos:        []*domain.EMDInfo{},
			}
		} else if itiETicketMap[key].TicketNumber != pPax.TicketNo {
			if index+1 < itiETicketMap[key].SegmentIndex {
				itiETicketMap[key].TicketNumber = pPax.TicketNo + "/" + itiETicketMap[key].TicketNumber
			} else {
				itiETicketMap[key].TicketNumber += "/" + pPax.TicketNo
			}
		}
	}

	for _, iti := range bkSession.Itineraries {
		info := itiReserInfoMap[iti.Index]
		if info != nil {
			reserInfo = append(reserInfo, info)
		} else {
			reserInfo = append(reserInfo, &domain.IssueTicketSvcReservationInfo{
				ItineraryIndex:  iti.Index,
				ReservationCode: in.AirplanePnr,
			})
		}
	}

	for _, value := range itiETicketMap {
		etickets = append(etickets, value)
	}

	return reserInfo, etickets, nil
}

func mappingTickets(psis []*entities.Psis, itineraries []*domain.FlightItinerary, paxByKey map[string]*domain.PaxInfo) ([]*domain.ETicketInfo, error) {
	out := []*domain.ETicketInfo{}

	for index, val := range psis {
		passenger := val.Passenger

		if passenger == nil {
			continue
		}

		itiIndex := helpers.GetItiIndexByLinearSegmentIdx(itineraries, index+1)
		paxKey := helpers.GetPaxKey(passenger.FirstName, passenger.LastName, enum.GenderMap[passenger.Gender])

		if _, ok := paxByKey[paxKey]; !ok {
			log.Error("mappingTickets: paxByKey is nil for key", log.Any("paxKey", paxKey))
			continue
		}

		out = append(out, &domain.ETicketInfo{
			PaxID:           paxByKey[paxKey].ID,
			PaxName:         passenger.FirstName + " " + passenger.LastName,
			ItineraryNumber: itiIndex,
			TicketNumber:    passenger.TicketNo,
			SegmentIndex:    index + 1,
		})

	}

	return out, nil
}

func itiETicketMapKey(itiIdx, paxId int) string {
	return fmt.Sprintf("%d-%d", itiIdx, paxId)
}
