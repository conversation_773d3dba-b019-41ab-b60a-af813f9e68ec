package converts

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	flightEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func ToSearchFlightRequest(in *domain.SearchFlightsRequest, class enum.CabinGrade) *entities.SearchFlightRequest {
	if in == nil {
		return nil
	}

	result := &entities.SearchFlightRequest{
		SearchParams: &entities.SearchParams{
			TripType:   ToSearchTripTypeRequest(in),
			FromCity:   ToSearchCityRequest(in, in.Itineraries[0].DepartPlace),
			ToCity:     ToSearchCityRequest(in, in.Itineraries[0].ArrivalPlace),
			CabinGrade: []enum.CabinGrade{class},
			AdultNum:   in.Passengers.ADT,
			ChildNum:   in.Passengers.CHD,
			FromDate:   ToSearchDateRequest(in.Itineraries[0]),
		},
	}

	if in.IsRoundTrip() && len(in.Itineraries) >= 2 {
		result.RetDate = ToSearchDateRequest(in.Itineraries[1])
	}

	return result
}

func ToSearchTripTypeRequest(in *domain.SearchFlightsRequest) enum.TripType {
	if in == nil {
		return enum.TripTypeNone
	}

	if in.IsRoundTrip() {
		return enum.TripTypeRoundTrip
	}

	return enum.TripTypeOneWay
}

func ToSearchCityRequest(in *domain.SearchFlightsRequest, code string) string {
	if in == nil {
		return ""
	}

	airport, ok := in.AirportMap[code]
	if !ok {
		return ""
	}

	return airport.City
}

func ToSearchDateRequest(iti *domain.ItineraryRequest) string {
	if iti == nil {
		return ""
	}

	switch {
	case iti.DepartDate > 0:
		return time.UnixMilli(iti.DepartDate).Format(constants.DateFormatYMD)
	case iti.DepartDateStr != "":
		if t, err := time.Parse(constants.DateFormatDMYDash, iti.DepartDateStr); err == nil {
			return t.Format(constants.DateFormatYMD)
		}
	}

	return ""
}

func ToDomainResponseFlights(in *entities.SearchFlightResponse, searchReq *domain.SearchFlightsRequest) ([]*domain.ResponseFlight, error) {
	requestAirports := lo.Map(searchReq.Itineraries, func(itinerary *domain.ItineraryRequest, _ int) string {
		return fmt.Sprintf("%s-%s", itinerary.DepartPlace, itinerary.ArrivalPlace)
	})

	return lo.FilterMap(in.Routings, func(flight *entities.Routing, _ int) (*domain.ResponseFlight, bool) {
		//filter out flights if resource category = 0
		if flight != nil && flight.ResourceCategory != enum.ResourceCategoryRegular {
			return nil, false
		}

		recFlight, err := ToDomainResponseFlight(flight, searchReq)
		if err != nil {
			return nil, false
		}

		for _, iti := range recFlight.Itineraries {
			if !lo.Contains(requestAirports, fmt.Sprintf("%s-%s", iti.DepartPlace, iti.ArrivalPlace)) ||
				iti.FlightNumber == "" || iti.FlightNumber == "null" {
				return nil, false
			}
		}

		return recFlight, true
	}), nil
}

func ToDomainResponseFlight(flight *entities.Routing, searchReq *domain.SearchFlightsRequest) (*domain.ResponseFlight, error) {
	if flight == nil {
		return nil, fmt.Errorf("flight is nil")
	}

	out := &domain.ResponseFlight{
		FlightID:            helpers.GenerateFlightID(flightEnum.FlightProviderTongCheng),
		SearchTotalFareInfo: ToDomainSearchTotalFareInfo(flight, searchReq),
		Provider:            flightEnum.FlightProviderTongCheng,
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeySolution,
				Value: flight,
			},
		},
		OptionType: flightEnum.FlightOptionTypeRecommend,
	}

	ities := []*domain.FlightItinerary{}

	for idx, segs := range [][]*entities.Segment{flight.FromSegment, flight.RetSegment} {
		if len(segs) == 0 {
			continue
		}

		iti, err := toDomainFlightItinerary(segs, flight, idx+1)
		if err != nil {
			return nil, err
		}

		ities = append(ities, iti)
	}

	out.Itineraries = ities

	miniRuleSegmentMap := GetMiniRuleSegementMap(ities)

	out.MiniRules = ToDomainMiniRules(flight, miniRuleSegmentMap)

	return out, nil
}

func ToDomainSearchTotalFareInfo(flight *entities.Routing, searchReq *domain.SearchFlightsRequest) domain.SearchTotalFareInfo {
	var (
		totalFareBasic float64
		totalTaxAmount float64
		paxes          []*domain.ItineraryPaxFare
	)

	addPax := func(paxType flightEnum.PaxType, totalPrice, totalTax float64, paxCount int) {
		if paxCount <= 0 {
			return
		}

		fareBasic := totalPrice / float64(paxCount)
		taxAmount := totalTax / float64(paxCount)
		paxes = append(paxes, &domain.ItineraryPaxFare{
			PaxType:    paxType,
			FareAmount: fareBasic + taxAmount,
			FareBasic:  fareBasic,
			TaxAmount:  taxAmount,
			Currency:   flight.Currency,
		})
	}

	// Tính cho ADT và CHD
	addPax(flightEnum.PaxTypeAdult, flight.AdultPrice, flight.AdultTax, searchReq.Passengers.ADT)
	addPax(flightEnum.PaxTypeChildren, flight.ChildPrice, flight.ChildTax, searchReq.Passengers.CHD)

	totalFareBasic = flight.AdultPrice + flight.ChildPrice
	totalTaxAmount = flight.AdultTax + flight.ChildTax
	totalFareAmount := totalFareBasic + totalTaxAmount

	return domain.SearchTotalFareInfo{
		TotalPaxFares:       paxes,
		BaseTotalFareAmount: totalFareAmount,
		TotalFareAmount:     totalFareAmount,
		TotalFareBasic:      totalFareBasic,
		TotalTaxAmount:      totalTaxAmount,
		Currency:            flight.Currency,
	}
}

func toDomainFlightItinerary(in []*entities.Segment, flight *entities.Routing, idx int) (*domain.FlightItinerary, error) {
	if len(in) == 0 || flight == nil {
		return nil, nil
	}

	segmentIndex := in[0].SegmentIndex
	bookingKey := flight.Data

	fareBasics := []string{}
	// split it by '/' to get individual fare basis codes
	if flight.FareBasis != "" {
		fareBasics = strings.Split(flight.FareBasis, "/")
	}

	segments := []*domain.ItinerarySegment{}
	for index, seg := range in {
		fareBasis := ""
		if index < len(fareBasics) {
			fareBasis = fareBasics[index]
		}

		itiSeg, err := toDomainItinerarySegment(seg, fareBasis, index+1)
		if err != nil {
			return nil, err
		}

		segments = append(segments, itiSeg)
	}

	firstSeg := segments[0]
	lastSeg := segments[len(segments)-1]

	avai := firstSeg.Availability
	if avai > 9 {
		avai = 9
	}

	out := &domain.FlightItinerary{
		Index:              idx,
		FareBasis:          firstSeg.FareBasis,
		CabinClass:         firstSeg.CabbinClass,
		CabinClassCode:     firstSeg.CabinClassCode,
		BookingClass:       firstSeg.BookingClass,
		Availability:       avai,
		DepartPlace:        firstSeg.DepartPlace,
		DepartDate:         firstSeg.DepartDate,
		ArrivalPlace:       lastSeg.ArrivalPlace,
		ArrivalDate:        lastSeg.ArrivalDate,
		CarrierMarketing:   firstSeg.CarrierMarketing,
		CarrierOperator:    firstSeg.CarrierOperator,
		FlightNumber:       firstSeg.FlightNumber,
		StopNumber:         len(segments) - 1,
		FreeBaggage:        getItiFreeBaggs(flight.Rule, segmentIndex),
		ProviderBookingKey: bookingKey,
		Segments:           segments,
	}

	out.GenerateItiID()

	return out, nil
}

func toDomainItinerarySegment(segment *entities.Segment, fareBasis string, idx int) (*domain.ItinerarySegment, error) {
	if segment == nil {
		return nil, fmt.Errorf("segment is nil")
	}

	departDt, err := utils.DateStringToUnixMilli(segment.DepTime, "")
	if err != nil {
		return nil, errors.Wrap(err, "invalid depart date "+segment.DepTime)
	}

	arrivalDt, err := utils.DateStringToUnixMilli(segment.ArrTime, "")
	if err != nil {
		return nil, errors.Wrap(err, "invalid arrival date "+segment.ArrTime)
	}

	return &domain.ItinerarySegment{
		Index:            idx,
		DepartPlace:      segment.DepAirport,
		DepartDate:       departDt,
		DepartTerminal:   segment.DepTerminal,
		ArrivalPlace:     segment.ArrAirport,
		ArrivalDate:      arrivalDt,
		ArrivalTerminal:  segment.ArrTerminal,
		CarrierMarketing: segment.Carrier,
		CarrierOperator:  segment.OperatingCarrier,
		FlightNumber:     strings.TrimPrefix(segment.FlightNumber, segment.Carrier),
		Aircraft:         constants.GetAirCraftName(segment.AircraftCode),
		BookingClass:     segment.Cabin,
		CabinClassCode:   string(segment.CabinGrade),
		FareBasis:        fareBasis,
		FlightDuration:   segment.FlightDuration,
		Availability:     int(segment.CabinCount),
		CabbinClass:      enum.CabinGradeMap[segment.CabinGrade],
	}, nil

}

func getItiFreeBaggs(rule *entities.Rule, segIdx int32) []*domain.BaggageInfo {
	if rule == nil || rule.BaggageInfo == nil || len(rule.BaggageInfo.BaggageRules) == 0 {
		return nil
	}

	out := toDomainBaggageRules(rule.BaggageInfo.BaggageRules, segIdx)

	return out
}

func getBagWeight(piece int32) string {
	if piece == 1 {
		return fmt.Sprintf("%d Piece", piece)
	} else {
		return fmt.Sprintf("%d Pieces", piece)
	}
}

func toDomainBaggageRules(baggageRules []*entities.BaggageRule, segIdx int32) []*domain.BaggageInfo {
	var out []*domain.BaggageInfo

	for _, baggageRule := range baggageRules {
		if baggageRule.SegmentNum != segIdx {
			continue
		}

		baggageInfo := toDomainBaggageRule(baggageRule)
		if baggageInfo != nil {
			out = append(out, baggageInfo)
		}
	}

	return out
}

func toDomainBaggageRule(baggageRule *entities.BaggageRule) *domain.BaggageInfo {
	if baggageRule == nil {
		return nil
	}

	return &domain.BaggageInfo{
		Name:          getBagWeight(baggageRule.BaggagePieces),
		Quantity:      int64(baggageRule.BaggageAllowance),
		PaxType:       enum.PassengerTypeName[baggageRule.PassengerType],
		IsHandBaggage: baggageRule.BaggageType != 0,
	}
}

func GetMiniRuleSegementMap(itineraries []*domain.FlightItinerary) map[int]*domain.MiniRuleSegment {
	segmentMap := make(map[int]*domain.MiniRuleSegment)

	index := 1
	for _, iti := range itineraries {
		for _, seg := range iti.Segments {
			segmentMap[index] = &domain.MiniRuleSegment{
				ItineraryIndex: iti.Index,
				SegmentIndex:   seg.Index,
			}
			index++
		}
	}

	return segmentMap
}

func ToDomainMiniRules(flight *entities.Routing, miniRuleSegmentMap map[int]*domain.MiniRuleSegment) []*domain.MiniRule {
	changeInfos := make([]*entities.ChangeInfo, 0)
	refundInfos := make([]*entities.RefundInfo, 0)

	if flight.Rule != nil {
		changeInfos = flight.Rule.ChangeInfo
		refundInfos = flight.Rule.RefundInfo
	}

	out := make([]*domain.MiniRule, 0)

	out = append(out, mapMiniRuleChangeInfos(changeInfos, miniRuleSegmentMap)...)
	out = append(out, mapMiniRuleRefundInfos(refundInfos, miniRuleSegmentMap)...)

	return out
}

func mapMiniRuleChangeInfos(changeInfos []*entities.ChangeInfo, segmentMap map[int]*domain.MiniRuleSegment) []*domain.MiniRule {
	// map miniruleSegments to key string => PenaltyRule List
	// map passenger types to key string => PaxType List
	groupedRules := make(map[string][]*domain.PenaltyRule)
	passengerTypes := make(map[string]flightEnum.PaxType)

	for _, segment := range segmentMap {
		segmentKey := segmentKey(segment)

		for _, changeInfo := range changeInfos {
			if changeInfo == nil || segmentKey == "" {
				continue
			}

			penaltyRuleChange := &domain.PenaltyRule{
				PenaltyType:    flightEnum.PenaltyTypeMap[flightEnum.PenaltyTypeChangeInt],
				IsPermitted:    enum.ChangeStatusIsPermittedMap[changeInfo.ChangeStatus],
				Situation:      enum.ChangeInfoTypeMap[changeInfo.ChangeType],
				Amount:         &changeInfo.ChangeFee,
				Currency:       changeInfo.Currency,
				NoShowTime:     formatNoShowTime(changeInfo.ChangeNoShowCondition),
				NoShowTimeUnit: constants.Hour,
			}

			penaltyRuleChangeNoShow := &domain.PenaltyRule{
				PenaltyType:    flightEnum.PenaltyTypeMap[flightEnum.PenaltyTypeChangeNoShowInt],
				IsPermitted:    enum.ChangeNoShowIsPermittedMap[changeInfo.ChangeNoShow],
				Situation:      enum.ChangeInfoTypeMap[changeInfo.ChangeType],
				Amount:         &changeInfo.ChangeNoShowFee,
				Currency:       changeInfo.Currency,
				NoShowTime:     formatNoShowTime(changeInfo.ChangeNoShowCondition),
				NoShowTimeUnit: constants.Hour,
			}

			groupedRules[segmentKey] = append(groupedRules[segmentKey], penaltyRuleChange, penaltyRuleChangeNoShow)
			passengerTypes[segmentKey] = enum.PassengerTypeName[changeInfo.PassengerType]
		}

	}

	miniRules := make([]*domain.MiniRule, 0, len(groupedRules))
	for segmentKey, penaltyRules := range groupedRules {
		segments := parseSegmentKeys(segmentKey)

		if len(penaltyRules) == 0 {
			continue
		}

		uniquePenaltyRules := removeDuplicatePenaltyRules(penaltyRules)

		miniRules = append(miniRules, &domain.MiniRule{
			PaxType:      passengerTypes[segmentKey],
			Segments:     segments,
			PenaltyRules: uniquePenaltyRules,
		})
	}

	return miniRules
}

func mapMiniRuleRefundInfos(refundInfos []*entities.RefundInfo, segmentMap map[int]*domain.MiniRuleSegment) []*domain.MiniRule {
	// map miniruleSegments to key string => PenaltyRule List
	// map passenger types to key string => PaxType List
	groupedRules := make(map[string][]*domain.PenaltyRule)
	passengerTypes := make(map[string]flightEnum.PaxType)

	for _, segment := range segmentMap {
		segmentKey := segmentKey(segment)

		for _, refundInfo := range refundInfos {
			if refundInfo == nil || segmentKey == "" {
				continue
			}

			penaltyRuleChange := &domain.PenaltyRule{
				PenaltyType:    flightEnum.PenaltyTypeMap[flightEnum.PenaltyTypeChangeInt],
				IsPermitted:    enum.RefundIsPermittedMap[refundInfo.RefundStatus],
				Situation:      enum.RefundInfoTypeMap[refundInfo.RefundType],
				Amount:         &refundInfo.RefundFee,
				Currency:       refundInfo.Currency,
				NoShowTime:     formatNoShowTime(refundInfo.RefundNoShowCondition),
				NoShowTimeUnit: constants.Hour,
			}

			penaltyRuleChangeNoShow := &domain.PenaltyRule{
				PenaltyType:    flightEnum.PenaltyTypeMap[flightEnum.PenaltyTypeChangeNoShowInt],
				IsPermitted:    enum.RefundNoShowIsPermittedMap[refundInfo.RefundNoShow],
				Situation:      enum.RefundInfoTypeMap[refundInfo.RefundType],
				Amount:         &refundInfo.RefundNoShowFee,
				Currency:       refundInfo.Currency,
				NoShowTime:     formatNoShowTime(refundInfo.RefundNoShowCondition),
				NoShowTimeUnit: constants.Hour,
			}

			groupedRules[segmentKey] = append(groupedRules[segmentKey], penaltyRuleChange, penaltyRuleChangeNoShow)
			passengerTypes[segmentKey] = enum.PassengerTypeName[refundInfo.PassengerType]
		}

	}

	miniRules := make([]*domain.MiniRule, 0, len(groupedRules))
	for segmentKey, penaltyRules := range groupedRules {
		segments := parseSegmentKeys(segmentKey)

		if len(penaltyRules) == 0 {
			continue
		}

		uniquePenaltyRules := removeDuplicatePenaltyRules(penaltyRules)

		miniRules = append(miniRules, &domain.MiniRule{
			PaxType:      passengerTypes[segmentKey],
			Segments:     segments,
			PenaltyRules: uniquePenaltyRules,
		})
	}

	return miniRules
}

func segmentKey(seg *domain.MiniRuleSegment) string {
	if seg == nil {
		return ""
	}
	key := fmt.Sprintf("%d-%d", seg.ItineraryIndex, seg.SegmentIndex)

	return key
}

func parseSegmentKeys(key string) []*domain.MiniRuleSegment {
	if key == "" {
		return nil
	}

	tokens := strings.Split(strings.TrimSpace(key), "-")
	if len(tokens) != 2 {
		return nil
	}

	itineraryIndex, err := strconv.Atoi(tokens[0])
	if err != nil {
		log.Error("invalid itinerary index", log.String("token", tokens[0]), log.Any("err", err))
		return nil
	}

	segmentIndex, err := strconv.Atoi(tokens[1])
	if err != nil {
		log.Error("invalid segment index", log.String("token", tokens[1]), log.Any("err", err))
		return nil
	}

	return []*domain.MiniRuleSegment{
		{
			ItineraryIndex: itineraryIndex,
			SegmentIndex:   segmentIndex,
		},
	}
}

func removeDuplicatePenaltyRules(rules []*domain.PenaltyRule) []*domain.PenaltyRule {
	seen := make(map[string]bool)
	unique := make([]*domain.PenaltyRule, 0, len(rules))

	for _, rule := range rules {
		hash, err := json.Marshal(rule)
		if err != nil {
			continue
		}
		key := string(hash)

		if seen[key] {
			continue
		}
		seen[key] = true
		unique = append(unique, rule)
	}

	return unique
}

func formatNoShowTime(noShowTime int32) string {
	if noShowTime == 0 || noShowTime == -1 {
		noShowTime = 0
	}

	isNegative := noShowTime < 0
	absTime := int(math.Abs(float64(noShowTime)))

	absTime += 6

	if isNegative {
		return "-" + strconv.Itoa(absTime)
	}
	return strconv.Itoa(absTime)
}
