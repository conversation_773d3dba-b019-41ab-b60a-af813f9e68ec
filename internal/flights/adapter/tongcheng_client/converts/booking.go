package converts

import (
	"errors"
	"fmt"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	domainEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func ToTongChengBookingRequest(bookingDetails *domain.BookingDetails, pnr *domain.PNR) (*entities.BookingRequest, error) {
	if bookingDetails == nil || pnr == nil {
		return nil, errors.New("invalid input")
	}

	metadata := bookingDetails.Metadata
	var tongChengSessionID string
	for _, md := range metadata {
		if md.Key == domain.MetaKeySessionID {
			tongChengSessionID = md.Value.(string)
		}
	}

	routing, err := helpers.GetTongChengRoutingMetadata(bookingDetails.Metadata)
	if err != nil {
		return nil, fmt.Errorf("failed to get routing metadata: %w", err)
	}

	bookingRequest := &entities.BookingRequest{
		BookingParams: &entities.BookingParams{
			SessionID: tongChengSessionID,
			TripType:  toTongChengTripType(bookingDetails.Itineraries),
			Routing:   routing,
			Passenger: toTongChengPassengers(pnr.ListPax),
			Contact:   toTongChengContact(pnr.ContactInfo),
		},
	}

	return bookingRequest, nil
}

func toTongChengTripType(itineraries []*domain.FlightItinerary) enum.TripType {
	switch len(itineraries) {
	case 1:
		return enum.TripTypeOneWay
	case 2:
		if itineraries[0].DepartPlace == itineraries[1].ArrivalPlace &&
			itineraries[0].ArrivalPlace == itineraries[1].DepartPlace {
			return enum.TripTypeRoundTrip
		}

		return enum.TripTypeOpenJaw
	default:
		return enum.TripTypeNone
	}
}

func toTongChengPassengers(paxInfos []*domain.PaxInfo) []*entities.Passenger {
	if len(paxInfos) == 0 {
		return nil
	}

	passengers := make([]*entities.Passenger, 0, len(paxInfos))
	for _, pax := range paxInfos {
		if pax == nil {
			continue
		}
		passenger := &entities.Passenger{
			FirstName: pax.GivenName,
			LastName:  pax.Surname,
			Gender:    toTongChengGender(pax.Gender),
			AgeType:   toTongChengAgeType(pax.Type),
		}
		if pax.DOB != nil {
			passenger.BirthDay = fmt.Sprintf("%04d%02d%02d", time.UnixMilli(*pax.DOB).Year(), time.UnixMilli(*pax.DOB).Month(), time.UnixMilli(*pax.DOB).Day())
		}
		if pax.Passport != nil {
			passenger.CardNum = pax.Passport.Number
			passenger.CardType = enum.PassengerCardTypePassport
			passenger.Nationality = pax.Passport.IssuingCountry
			if pax.Passport.ExpiryDate > 0 {
				passenger.CardExpired = fmt.Sprintf("%04d%02d%02d", time.UnixMilli(pax.Passport.ExpiryDate).Year(), time.UnixMilli(pax.Passport.ExpiryDate).Month(), time.UnixMilli(pax.Passport.ExpiryDate).Day())
			}
		}
		passengers = append(passengers, passenger)
	}
	return passengers
}

func toTongChengAgeType(paxType domainEnum.PaxType) enum.AgeType {
	switch paxType {
	case domainEnum.PaxTypeAdult:
		return enum.Adult
	case domainEnum.PaxTypeChildren:
		return enum.Child
	default:
		return enum.Adult
	}
}

func toTongChengGender(gender commonEnum.GenderType) enum.Gender {
	switch gender {
	case commonEnum.GenderTypeMale:
		return enum.Male
	case commonEnum.GenderTypeFeMale:
		return enum.Female
	default:
		return enum.Male
	}
}

func toTongChengContact(contact *domain.Contact) []*entities.Contact {
	if contact == nil {
		return nil
	}
	return []*entities.Contact{
		{
			Email:  contact.Email,
			Name:   fmt.Sprintf("%s/%s", contact.Surname, contact.GivenName),
			Mobile: contact.Phone,
		},
	}
}

func FromTongChengBookingResponse(tcResponse *entities.BookingResponse, originalRouting *entities.Routing) (*domain.SvcCreateBookingResponse, error) {
	if tcResponse == nil {
		return nil, fmt.Errorf("TongCheng booking response is nil")
	}

	// Kiểm tra response có thành công không
	if !tcResponse.IsSuccess {
		return nil, fmt.Errorf("TongCheng booking failed: %s - %s", tcResponse.ErrorCode, tcResponse.ErrorMsg)
	}

	response := &domain.SvcCreateBookingResponse{
		BookingRef:  tcResponse.PnrCode,
		OrderNumRef: tcResponse.OrderNo,
	}

	if tcResponse.GmtPayOut != "" {
		lastTktDate, err := parseTongChengPayoutDate(tcResponse.GmtPayOut)
		if err != nil {
			response.LastTicketingDate = 0
		} else {
			response.LastTicketingDate = lastTktDate
		}
	}

	return response, nil
}

// parseTongChengPayoutDate phân tích chuỗi thời gian GmtPayOut từ TongCheng
// Format thường là: "2024-12-11 11:55:00" hoặc tương tự
func parseTongChengPayoutDate(gmtPayOut string) (int64, error) {
	if gmtPayOut == "" {
		return 0, fmt.Errorf("empty payout date")
	}

	// Thử các format thời gian phổ biến của TongCheng
	formats := []string{
		"2006-01-02 15:04:05",     // Standard datetime format
		"2006-01-02T15:04:05",     // ISO format
		"2006-01-02T15:04:05Z",    // ISO with timezone
		"2006-01-02 15:04:05 MST", // With timezone
		"02/01/2006 15:04:05",     // Alternative format
	}

	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		return 0, fmt.Errorf("load location error: %w", err)
	}

	for _, format := range formats {
		if t, err := time.ParseInLocation(format, gmtPayOut, loc); err == nil {
			return t.UnixMilli(), nil
		}
	}

	return 0, fmt.Errorf("unable to parse payout date: %s", gmtPayOut)
}
