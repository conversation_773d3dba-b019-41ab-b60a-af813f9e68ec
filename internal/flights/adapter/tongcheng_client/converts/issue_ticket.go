package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"
)

func ToIssueTicketRequest(orderNo string) *entities.IssueTicketRequest {
	return &entities.IssueTicketRequest{
		NotificationParams: &entities.NotificationParams{
			OrderNo:        orderNo,
			EngineSerialNo: orderNo,
			TcSerialNo:     orderNo,
			Status:         enum.READY_TO_ISSUE,
			Type:           enum.NotiTypeReadyToIssue,
		},
	}
}
