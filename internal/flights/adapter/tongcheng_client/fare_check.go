package tongcheng_client

import (
	"context"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func (a *tongchengAdapter) CheckFare(ctx context.Context, req *domain.SearchFlightsRequest, flight *domain.ResponseFlight, tracingID string) (*domain.CheckFareInfo, error) {
	verifyReq, err := converts.ToTongChengVerifyRequest(req, flight)
	if err != nil {
		return nil, err
	}

	verifyRes, err := a.client.Verify(ctx, tracingID, verifyReq)
	if err != nil {
		return nil, err
	}

	if verifyRes == nil {
		return nil, nil
	}

	if verifyRes.Status == constants.TongchengStatusA100 || verifyRes.Status == constants.TongchengStatusB102 {
		return nil, domain.ErrItinerarySoldOut
	}

	refundReq, err := converts.ToTongChengSearchRefundRequest(verifyRes.Routing.Data, tracingID)
	if err != nil {
		return nil, err
	}

	searchRefundRes, err := a.client.SearchRefund(ctx, tracingID, refundReq)
	if err != nil {
		return nil, err
	}

	if searchRefundRes == nil || searchRefundRes.ChangeRules == nil || searchRefundRes.RefundRules == nil {
		return nil, nil
	}

	if verifyRes.Status == constants.TongchengStatusA100 {
		return nil, domain.ErrItinerarySoldOut
	}

	recFlight, err := converts.ToDomainResponseFlight(verifyRes.Routing, req)
	if err != nil {
		return nil, fmt.Errorf("converts.ToDomainResponseFlight error: %v", err)
	}

	// append metadata sessionId.
	recFlight.Metadata = append(recFlight.Metadata, &domain.Metadata{
		Key:   domain.MetaKeySessionID,
		Value: verifyRes.SessionID,
	})

	result := &domain.CheckFareInfo{
		ShouldUpdateFlight:  true,
		OriginTotalFareInfo: recFlight.ConvertToTotal(),
		MetaData:            recFlight.Metadata,
	}

	return result, nil
}
