// internal/flights/adapter/tongcheng_client/integration_test.go
package tongcheng_client

import (
	"context"
	"encoding/json"
	"testing"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

func TestTongchengAdapter_CheckFare_Integration(t *testing.T) {
	cfg := &config.Schema{
		TongChengBaseURL:    "xxx",
		TongChengPartnerKey: "xxx",
		TongChengUsername:   "xxx",
		TongChengPartnerID:  "xxx",
		ProxyURL:            "xxx",
	}

	tongchengClient := client.NewTongChengClient(cfg, nil)
	adapter := &tongchengAdapter{
		cfg:    cfg,
		client: tongchengClient,
	}

	req := &domain.SearchFlightsRequest{
		Passengers: domain.PaxRequest{ADT: 1, CHD: 1},
		Itineraries: []*domain.ItineraryRequest{
			{
				DepartPlace:  "CAN",
				DepartDate:   1712764800,
				ArrivalPlace: "BKK",
			},
			{
				DepartPlace:  "BKK",
				DepartDate:   1713369600,
				ArrivalPlace: "CAN",
			},
		},
	}

	jsonData := []byte(`{
      "adultPrice": 1353,
      "adultTax": 1076,
      "childPrice": 1095,
      "childPublishPrice": 1095,
      "childTax": 865,
      "currency": "CNY",
      "data": "91ed112d769611f099a06141d8e4287c$&$165",
      "fareBasis": "E1ABDB0T/E1ABDB0T/E1ABDB0T/E1ABDB0T",
      "fromSegments": [
        {
          "aircraftCode": "321",
          "arrAirport": "TFU",
          "arrAirportName": "成都天府国际机场",
          "arrCity": "CTU",
          "arrCityName": "成都",
          "arrTerminal": "T2",
          "arrTime": "202512100850",
          "cabin": "B",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "CAN",
          "depAirportName": "广州白云国际机场",
          "depCity": "CAN",
          "depCityName": "广州市",
          "depTerminal": "T2",
          "depTime": "202512100625",
          "duration": 145,
          "equip": {
            "craftCode": "321",
            "craftName": "空客A321"
          },
          "flightNumber": "3U6716",
          "segmentIndex": 1,
          "segmentType": 1,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        },
        {
          "aircraftCode": "320",
          "arrAirport": "BKK",
          "arrAirportName": "素万那普国际机场",
          "arrCity": "BKK",
          "arrCityName": "曼谷",
          "arrTime": "202512101720",
          "cabin": "E",
          "cabinCount": 8,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "TFU",
          "depAirportName": "成都天府国际机场",
          "depCity": "CTU",
          "depCityName": "成都",
          "depTerminal": "T1",
          "depTime": "202512101505",
          "duration": 195,
          "equip": {
            "craftCode": "320",
            "craftName": "空客A320"
          },
          "flightNumber": "3U3935",
          "segmentIndex": 2,
          "segmentType": 1,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "groupQualification": 0,
      "maxPassengerCount": 9,
      "minPassengerCount": 1,
      "nationalityType": 0,
      "noCertificateOrder": 0,
      "passengerQualification": 0,
      "productCode": 0,
      "productType": 1,
      "publishPrice": 1353,
      "resouceCategory": 0,
      "retSegments": [
        {
          "aircraftCode": "320",
          "arrAirport": "TFU",
          "arrAirportName": "成都天府国际机场",
          "arrCity": "CTU",
          "arrCityName": "成都",
          "arrTerminal": "T1",
          "arrTime": "202512151610",
          "cabin": "E",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "BKK",
          "depAirportName": "素万那普国际机场",
          "depCity": "BKK",
          "depCityName": "曼谷",
          "depTime": "202512151210",
          "duration": 180,
          "equip": {
            "craftCode": "320",
            "craftName": "空客A320"
          },
          "flightNumber": "3U3938",
          "segmentIndex": 3,
          "segmentType": 2,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        },
        {
          "aircraftCode": "321",
          "arrAirport": "CAN",
          "arrAirportName": "广州白云国际机场",
          "arrCity": "CAN",
          "arrCityName": "广州市",
          "arrTerminal": "T2",
          "arrTime": "202512152145",
          "cabin": "B",
          "cabinCount": 9,
          "cabinGrade": "Y",
          "carrier": "3U",
          "carrierName": "四川航空",
          "codeShare": false,
          "depAirport": "TFU",
          "depAirportName": "成都天府国际机场",
          "depCity": "CTU",
          "depCityName": "成都",
          "depTerminal": "T2",
          "depTime": "202512151930",
          "duration": 135,
          "equip": {
            "craftCode": "321",
            "craftName": "空客A321"
          },
          "flightNumber": "3U6715",
          "segmentIndex": 4,
          "segmentType": 2,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "rule": {
        "baggageInfo": {
          "baggageRules": [
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 1
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 1
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 2
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 2
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 3
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 3
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 0,
              "segmentNum": 4
            },
            {
              "baggageAllowance": 0,
              "baggagePieces": 1,
              "baggageType": 0,
              "passengerType": 1,
              "segmentNum": 4
            }
          ],
          "hasBaggage": 1
        },
        "changeInfos": [
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 1
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 1
          }
        ],
        "refundInfos": [
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          }
        ]
      },
      "ticketInvoiceType": 0,
      "ticketTimeLimit": 0,
      "ticketTimeType": 1,
      "ticketType": 0,
      "validatingCarrier": "3U",
      "visaLimitType": 0
    }`)

	routing := &entities.Routing{}
	_ = json.Unmarshal(jsonData, routing)

	flight := &domain.ResponseFlight{
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeySolution,
				Value: routing,
			},
		},
	}
	tracingID := "integration-test-trace-id"

	_, _ = adapter.CheckFare(context.Background(), req, flight, tracingID)

}

func TestTongchengAdapter_CreateBooking_Integration(t *testing.T) {
	cfg := &config.Schema{
		TongChengBaseURL:    "http://www.uat.itravelsaas.com",
		TongChengPartnerKey: "****************",
		TongChengUsername:   "LCWY",
		TongChengPartnerID:  "LCWY",
		ProxyURL:            "**************************************************************",
	}

	tongchengClient := client.NewTongChengClient(cfg, nil)
	adapter := &tongchengAdapter{
		cfg:    cfg,
		client: tongchengClient,
	}

	jsonData := []byte(`{
      "adultPrice": 1433,
      "adultTax": 332,
      "childPrice": 1433,
      "childPublishPrice": 1433,
      "childTax": 332,
      "currency": "CNY",
      "data": "0c20b5fc774e11f099a06141d8e4287c$&$126",
      "fareBasis": "OH2EXUMN/OH2EXUMN",
      "fromSegments": [
        {
          "aircraftCode": "73J",
          "arrAirport": "SEA",
          "arrAirportName": "塔克马国际机场",
          "arrCity": "SEA",
          "arrCityName": "西雅图",
          "arrTime": "202509201746",
          "cabin": "O",
          "cabinCount": 7,
          "cabinGrade": "Y",
          "carrier": "AS",
          "carrierName": "阿拉斯加航空",
          "codeShare": false,
          "depAirport": "ONT",
          "depAirportName": "安大略国际机场",
          "depCity": "LAX",
          "depCityName": "洛杉矶",
          "depTerminal": "T2",
          "depTime": "202509201506",
          "duration": 160,
          "equip": {
            "craftCode": "73J",
            "craftName": "波音737-900"
          },
          "flightNumber": "AS1119",
          "segmentIndex": 1,
          "segmentType": 1,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "groupQualification": 0,
      "maxPassengerCount": 9,
      "minPassengerCount": 1,
      "nationalityType": 0,
      "noCertificateOrder": 0,
      "passengerQualification": 0,
      "productCode": 0,
      "productType": 1,
      "publishPrice": 1433,
      "resouceCategory": 0,
      "retSegments": [
        {
          "aircraftCode": "73H",
          "arrAirport": "LAX",
          "arrAirportName": "洛杉矶国际机场",
          "arrCity": "LAX",
          "arrCityName": "洛杉矶",
          "arrTerminal": "T6",
          "arrTime": "202509302048",
          "cabin": "O",
          "cabinCount": 7,
          "cabinGrade": "Y",
          "carrier": "AS",
          "carrierName": "阿拉斯加航空",
          "codeShare": false,
          "depAirport": "SEA",
          "depAirportName": "塔克马国际机场",
          "depCity": "SEA",
          "depCityName": "西雅图",
          "depTime": "202509301759",
          "duration": 169,
          "equip": {
            "craftCode": "73H",
            "craftName": "波音737-800"
          },
          "flightNumber": "AS1048",
          "segmentIndex": 2,
          "segmentType": 2,
          "stopAirports": "",
          "stopCities": "",
          "stopCityNames": "",
          "travelType": 0
        }
      ],
      "rule": {
        "baggageInfo": {
          "baggageRules": [],
          "hasBaggage": 0
        },
        "changeInfos": [
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 0
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 0,
            "currency": "CNY",
            "passengerType": 1
          },
          {
            "chaNoShowCondition": 0,
            "chaNoshow": "E",
            "chaNoshowFee": 0,
            "changeFee": 0,
            "changeStatus": "E",
            "changeType": 1,
            "currency": "CNY",
            "passengerType": 1
          }
        ],
        "refundInfos": [
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 0,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 0,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          },
          {
            "currency": "CNY",
            "passengerType": 1,
            "refNoShowCondition": 0,
            "refNoshow": "E",
            "refNoshowFee": 0,
            "refundFee": 0,
            "refundStatus": "E",
            "refundTaxFee": 0,
            "refundTaxStatus": "T",
            "refundType": 1,
            "taxNoshow": "T",
            "taxNoshowFee": 0
          }
        ]
      },
      "ticketInvoiceType": 0,
      "ticketTimeLimit": 0,
      "ticketTimeType": 1,
      "ticketType": 0,
      "validatingCarrier": "AS",
      "visaLimitType": 0
    }`)

	routing := &entities.Routing{}
	_ = json.Unmarshal(jsonData, routing)

	var itineraries []*domain.FlightItinerary
	itinerariesJson := []byte(`[
    {
      "id": "fa4aa8e6e8dfcbc5263edb5f42f7017d",
      "index": 1,
      "key": "",
      "provider_booking_key": "2mIuJ0hRohqvZDFGBw8OXNo3d9HeSC3CuVznIQrEApz4axWiEydmWXEspZqnZyXFrS1GEXqXgafFDEPCqFvgae94yABIVpbogQbLxVOW0EvdF9mCTceVEMG/yY0I9txctSdg7Kny2RPKc0W/v2yy4T+dWz2dzvR9YSV2TN/0/WD/NwvD4bDyhYNMeWlV1F3AjPs6mXfzVdng4T5Utxt5eHVn2kWdg/NGAoYWaBS7FnUlEYAxUQtMkh/y2/9gIQ6pp6eNscvTBNCUtwAr2ExW5EjQubQRbURQNqKuz6Gg7ODTxFbt9bGFNmsTsCnKKIAcDwhFcvrrudlhEJ/xpSRLgcrh6MRRUxQImDZ8V5TmVZTwH7oQKEPkpi/0UAt7qR2qV0qk2lioKHkPFGvhNYBVUgjZuiT/eFsq/+kIm3NOe0PCZwoV17Uy5rj5ZSc1k2vDGaCH4P7JqyEVm4WC/n/Gp32gfpSwnaPIpx66vc3WrA7qMAHbWfBoY2AW2bLr5tHGSo90p54eXqQMAwPHpeye3CLEcI2eTwn1v23rchB0yeY=",
      "stop_number": 1,
      "cabin_class": "ECONOMY",
      "booking_class": "X",
      "cabin_code": "ECONOMY",
      "fare_basis": "YRT",
      "availability": 3,
      "depart_place": "CAN",
      "depart_date": {
        "$numberLong": "1758364500000"
      },
      "depart_date_utc": {
        "$date": "2025-09-20T02:35:00.000Z"
      },
      "depart_dt": "2025-09-20T10:35:00+08:00",
      "arrival_dt": "2025-09-20T19:25:00+07:00",
      "arrival_place": "BKK",
      "arrival_date": {
        "$numberLong": "1758396300000"
      },
      "carrier_marketing": "TR",
      "carrier_operator": "",
      "flight_number": "101",
      "flight_duration": 590,
      "free_baggage": [
        {
          "name": "10kg",
          "is_hand_baggage": true,
          "pax_type": "ADT"
        },
        {
          "name": "10kg",
          "is_hand_baggage": true,
          "pax_type": "CHD"
        }
      ],
      "segments": [
        {
          "itinerary_id": "fa4aa8e6e8dfcbc5263edb5f42f7017d",
          "key": "3ffab52883449fbd16324df6a1c4f862",
          "index": 1,
          "fare_basis": "YRT",
          "depart_place": "CAN",
          "depart_date": {
            "$numberLong": "1758364500000"
          },
          "depart_dt": "2025-09-20 10:35:00",
          "arrival_dt": "2025-09-20 14:40:00",
          "arrival_place": "SIN",
          "arrival_date": {
            "$numberLong": "1758379200000"
          },
          "carrier_marketing": "TR",
          "carrier_operator": "",
          "flight_number": "101",
          "aircraft": "Airbus A320",
          "flight_duration": 245,
          "booking_class": "X",
          "cabin_class_code": "ECONOMY",
          "depart_terminal": "1",
          "arrival_terminal": "1",
          "availability": 3,
          "cabin_class": "ECONOMY"
        },
        {
          "itinerary_id": "fa4aa8e6e8dfcbc5263edb5f42f7017d",
          "key": "cf1911a9a27ae92a782e3ec4be10ca36",
          "index": 2,
          "fare_basis": "YRT",
          "depart_place": "SIN",
          "depart_date": {
            "$numberLong": "1758390900000"
          },
          "depart_dt": "2025-09-20 17:55:00",
          "arrival_dt": "2025-09-20 19:25:00",
          "arrival_place": "BKK",
          "arrival_date": {
            "$numberLong": "1758396300000"
          },
          "carrier_marketing": "TR",
          "carrier_operator": "",
          "flight_number": "616",
          "aircraft": "Airbus A320",
          "flight_duration": 150,
          "booking_class": "X",
          "cabin_class_code": "ECONOMY",
          "depart_terminal": "1",
          "availability": 3,
          "cabin_class": "ECONOMY"
        }
      ],
      "arrival_country": "TH",
      "depart_country": "CN",
      "reservation_code": "WCWRK8"
    },
    {
      "id": "0d5dc8067f31babe34df4ed8cece61b8",
      "index": 2,
      "key": "",
      "provider_booking_key": "2mIuJ0hRohqvZDFGBw8OXNo3d9HeSC3CuVznIQrEApz4axWiEydmWXEspZqnZyXFrS1GEXqXgafFDEPCqFvgae94yABIVpbogQbLxVOW0EvdF9mCTceVEMG/yY0I9txctSdg7Kny2RPKc0W/v2yy4T+dWz2dzvR9YSV2TN/0/WD/NwvD4bDyhYNMeWlV1F3AjPs6mXfzVdng4T5Utxt5eHVn2kWdg/NGAoYWaBS7FnUlEYAxUQtMkh/y2/9gIQ6pp6eNscvTBNCUtwAr2ExW5EjQubQRbURQNqKuz6Gg7ODTxFbt9bGFNmsTsCnKKIAcDwhFcvrrudlhEJ/xpSRLgcrh6MRRUxQImDZ8V5TmVZTwH7oQKEPkpi/0UAt7qR2qV0qk2lioKHkPFGvhNYBVUgjZuiT/eFsq/+kIm3NOe0PCZwoV17Uy5rj5ZSc1k2vDGaCH4P7JqyEVm4WC/n/Gp32gfpSwnaPIpx66vc3WrA7qMAHbWfBoY2AW2bLr5tHGSo90p54eXqQMAwPHpeye3CLEcI2eTwn1v23rchB0yeY=",
      "stop_number": 1,
      "cabin_class": "ECONOMY",
      "booking_class": "W",
      "cabin_code": "ECONOMY",
      "fare_basis": "YRT",
      "availability": 3,
      "depart_place": "BKK",
      "depart_date": {
        "$numberLong": "1759263900000"
      },
      "depart_date_utc": {
        "$date": "2025-09-30T13:25:00.000Z"
      },
      "depart_dt": "2025-09-30T20:25:00+07:00",
      "arrival_dt": "2025-10-01T09:20:00+08:00",
      "arrival_place": "CAN",
      "arrival_date": {
        "$numberLong": "1759310400000"
      },
      "carrier_marketing": "TR",
      "carrier_operator": "",
      "flight_number": "617",
      "flight_duration": 715,
      "free_baggage": [
        {
          "name": "10kg",
          "is_hand_baggage": true,
          "pax_type": "ADT"
        },
        {
          "name": "10kg",
          "is_hand_baggage": true,
          "pax_type": "CHD"
        }
      ],
      "segments": [
        {
          "itinerary_id": "0d5dc8067f31babe34df4ed8cece61b8",
          "key": "a9cf6fa9e889688e9836aa93e6f4fc2a",
          "index": 1,
          "fare_basis": "YRT",
          "depart_place": "BKK",
          "depart_date": {
            "$numberLong": "1759263900000"
          },
          "depart_dt": "2025-09-30 20:25:00",
          "arrival_dt": "2025-10-01 00:10:00",
          "arrival_place": "SIN",
          "arrival_date": {
            "$numberLong": "1759277400000"
          },
          "carrier_marketing": "TR",
          "carrier_operator": "",
          "flight_number": "617",
          "aircraft": "Airbus A320",
          "flight_duration": 165,
          "booking_class": "W",
          "cabin_class_code": "ECONOMY",
          "arrival_terminal": "1",
          "availability": 3,
          "cabin_class": "ECONOMY"
        },
        {
          "itinerary_id": "0d5dc8067f31babe34df4ed8cece61b8",
          "key": "5c86efd49d80e4760b086184f94c4f93",
          "index": 2,
          "fare_basis": "YRT",
          "depart_place": "SIN",
          "depart_date": {
            "$numberLong": "1759295400000"
          },
          "depart_dt": "2025-10-01 05:10:00",
          "arrival_dt": "2025-10-01 09:20:00",
          "arrival_place": "CAN",
          "arrival_date": {
            "$numberLong": "1759310400000"
          },
          "carrier_marketing": "TR",
          "carrier_operator": "",
          "flight_number": "100",
          "aircraft": "Airbus A320",
          "flight_duration": 250,
          "booking_class": "W",
          "cabin_class_code": "ECONOMY",
          "depart_terminal": "1",
          "arrival_terminal": "1",
          "availability": 3,
          "cabin_class": "ECONOMY"
        }
      ],
      "arrival_country": "CN",
      "depart_country": "TH",
      "reservation_code": "WCWRK8"
    }
  ]`)
	_ = json.Unmarshal(itinerariesJson, &itineraries)
	bookingDetails := &domain.BookingDetails{
		FlightType:  enum.FlightTypeInternational,
		Itineraries: itineraries,
		Metadata: []*domain.Metadata{
			{
				Key:   domain.MetaKeySolution,
				Value: routing,
			},
			{
				Key:   domain.MetaKeySessionID,
				Value: "SYLAXSEART21202508121206207354592865893601280",
			},
		},
	}

	pnrJson := []byte(`{
  "_id": {
    "$oid": "689ab99d42ad7782633c0e28"
  },
  "created_at": {
    "$numberLong": "*************"
  },
  "updated_at": {
    "$numberLong": "*************"
  },
  "session_id": "********-7ab1-4b84-8036-d14ab328a093",
  "list_pax": [
    {
      "type": "ADT",
      "id": 1,
      "surname": "NGO",
      "given_name": "TAN SHAO",
      "dob": ************,
      "age": 25,
      "passport": {
        "number": "C1234562",
        "expiry_date": *************,
        "issuing_country": "VN"
      },
      "phone_code": "84",
      "phone": "*********",
      "email": "<EMAIL>",
      "gender": 1,
      "nationality": "VN"
    },
    {
      "type": "ADT",
      "id": 2,
      "surname": "TRAN",
      "given_name": "MINH GA",
      "dob":************,
      "age": 24,
      "passport": {
        "number": "B1234567",
        "expiry_date": 1887494400000,
        "issuing_country": "TH"
      },
      "phone_code": "84",
      "phone": "*********",
      "email": "<EMAIL>",
      "gender": 2,
      "nationality": "VN"
    },
    {
      "type": "CHD",
      "id": 3,
      "surname": "NGUYEN",
      "given_name": "GIA YOU",
      "dob": 1534723200000,
      "age": 7,
      "passport": {
        "number": "B1234567",
        "expiry_date": 1855958400000,
        "issuing_country": "VN"
      },
      "phone_code": "84",
      "phone": "*********",
      "email": "<EMAIL>",
      "gender": 1,
      "nationality": "VN"
    }
  ],
  "contact_info": {
    "surname": "NGO",
    "given_name": "TAN THIEU",
    "phone_code": "84",
    "phone": "*********",
    "email": "<EMAIL>",
    "gender": 1
  }
}`)

	var pnr *domain.PNR
	_ = json.Unmarshal(pnrJson, &pnr)

	tracingID := "integration-test-create-booking"

	_, _ = adapter.CreateBooking(context.Background(), bookingDetails, pnr, tracingID)

}
