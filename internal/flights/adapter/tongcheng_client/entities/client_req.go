package entities

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"

type SearchFlightRequest struct {
	*SearchParams
	AuthInfo
}

type SearchParams struct {
	TripType   enum.TripType     `json:"tripType"`
	FromCity   string            `json:"fromCity"`
	ToCity     string            `json:"toCity"`
	FromDate   string            `json:"fromDate"`
	RetDate    string            `json:"retDate"`
	CabinGrade []enum.CabinGrade `json:"cabinGrade"`
	AdultNum   int               `json:"adultNum"`
	ChildNum   int               `json:"childNum"`
}

type VerifyRequest struct {
	*VerifyParams
	AuthInfo
}

type VerifyParams struct {
	TripType    enum.TripType `json:"tripType"`
	AdultNumber int           `json:"adultNumber"`
	ChildNumber int           `json:"childNumber"`
	Routing     *Routing      `json:"routing"`
}

type SearchRefundRequest struct {
	*SearchRefundParams
	AuthInfo
}

type SearchRefundParams struct {
	DataID  string `json:"dataId"`
	TraceID string `json:"traceId"`
}

type OrderDetailRequest struct {
	OrderNo string `json:"orderNo"`
	AuthInfo
}

type BookingRequest struct {
	*BookingParams
	AuthInfo
}

type BookingParams struct {
	SessionID string        `json:"sessionId"`
	TripType  enum.TripType `json:"tripType"`
	Routing   *Routing      `json:"routing"`
	Passenger []*Passenger  `json:"passengers"`
	Contact   []*Contact    `json:"contacts"`
}

type IssueTicketRequest struct {
	*NotificationParams
}

type CancelBookingRequest struct {
	*NotificationParams
}

type NotificationParams struct {
	EngineSerialNo string           `json:"engineSerialNo"`
	OrderNo        string           `json:"orderNo"`
	TcSerialNo     string           `json:"tcSerialNo"`
	Status         enum.OrderStatus `json:"status"`
	Type           enum.NotiType    `json:"type"`
}
