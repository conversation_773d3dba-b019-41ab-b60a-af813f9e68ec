package entities

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client/enum"

type Response struct {
	ErrorCode string `json:"errorCode"`
	Msg       string `json:"msg"`
	Async     bool   `json:"async"`
	ErrorMsg  string `json:"errorMsg"`
	Status    string `json:"status"`
	IsSuccess bool   `json:"success"`
}

type SearchFlightResponse struct {
	Response
	Routings []*Routing `json:"routings"`
}

type Routing struct {
	AdultPrice        float64               `json:"adultPrice,omitempty"`
	ChildPrice        float64               `json:"childPrice,omitempty"`
	AdultTax          float64               `json:"adultTax,omitempty"`
	ChildTax          float64               `json:"childTax,omitempty"`
	Currency          string                `json:"currency,omitempty"`
	Data              string                `json:"data,omitempty"`
	FareBasis         string                `json:"fareBasis,omitempty"`
	FromSegment       []*Segment            `json:"fromSegments,omitempty"`
	RetSegment        []*Segment            `json:"retSegments,omitempty"`
	Rule              *Rule                 `json:"rule,omitempty"`
	ValidatingCarrier string                `json:"validatingCarrier,omitempty"`
	ResourceCategory  enum.ResourceCategory `json:"resouceCategory,omitempty"`
	ProductType       enum.ProductType      `json:"productType,omitempty"`
	TicketTimeType    enum.TicketTimeType   `json:"ticketTimeType,omitempty"`
}

type Segment struct {
	CabinGrade       enum.CabinGrade `json:"cabinGrade"`
	Cabin            string          `json:"cabin"`
	CabinCount       int32           `json:"cabinCount"`
	DepAirport       string          `json:"depAirport"`
	DepTime          string          `json:"depTime"`
	ArrTime          string          `json:"arrTime"`
	ArrAirport       string          `json:"arrAirport"`
	FlightNumber     string          `json:"flightNumber"`
	FlightDuration   int             `json:"flightDuration"`
	Carrier          string          `json:"carrier"`
	OperatingCarrier string          `json:"operatingCarrier"`
	AircraftCode     string          `json:"aircraftCode"`
	DepTerminal      string          `json:"depTerminal"`
	ArrTerminal      string          `json:"arrTerminal"`
	SegmentIndex     int32           `json:"segmentIndex"`
	SegmentType      int32           `json:"segmentType"`
}

type Rule struct {
	BaggageInfo *BaggageInfo  `json:"baggageInfo"`
	ChangeInfo  []*ChangeInfo `json:"changeInfos"`
	RefundInfo  []*RefundInfo `json:"refundInfos"`
}

type BaggageInfo struct {
	BaggageRules []*BaggageRule `json:"baggageRules"`
}

type BaggageRule struct {
	BaggageAllowance int32              `json:"baggageAllowance"`
	BaggagePieces    int32              `json:"baggagePieces"`
	BaggageType      enum.BaggageType   `json:"baggageType"` // 0: checked baggage, 1: hand baggage
	PassengerType    enum.PassengerType `json:"passengerType"`
	SegmentNum       int32              `json:"segmentNum"`
}

type ChangeInfo struct {
	ChangeStatus          enum.ChangeStatus   `json:"changeStatus"`
	ChangeFee             float64             `json:"changeFee"`
	ChangeNoShow          enum.ChangeNoShow   `json:"chaNoshow"`
	ChangeNoShowFee       float64             `json:"chaNoshowFee"`
	ChangeNoShowCondition int32               `json:"chaNoShowCondition"`
	Currency              string              `json:"currency"`
	ChangeType            enum.ChangeInfoType `json:"changeType"`
	PassengerType         enum.PassengerType  `json:"passengerType"`
}

type RefundInfo struct {
	RefundStatus          enum.RefundStatus   `json:"refundStatus"`
	RefundType            enum.RefundInfoType `json:"refundType"`
	RefundNoShowCondition int32               `json:"refNoShowCondition"`
	RefundFee             float64             `json:"refundFee"`
	RefundNoShow          enum.RefundNoShow   `json:"refNoshow"`
	RefundNoShowFee       float64             `json:"refNoshowFee"`
	Currency              string              `json:"currency"`
	PassengerType         enum.PassengerType  `json:"passengerType"`
}

type VerifyResponse struct {
	Response
	SessionID string   `json:"sessionID"`
	Routing   *Routing `json:"routing"`
	Rule      *Rule    `json:"rule"`
}

type SearchRefundResponse struct {
	Response
	ChangeRules []*ChangeRule `json:"changeRules"`
	RefundRules []*RefundRule `json:"refundRules"`
}

type OrderDetailResponse struct {
	Response
	OrderVO *OrderVO `json:"orderVO"`
}

type BookingResponse struct {
	Response
	MaxSeats   int      `json:"maxSeats"`
	SessionID  string   `json:"sessionID"`
	OrderNo    string   `json:"orderNo"`
	PnrCode    string   `json:"pnrCode"`
	IcsPnrCode string   `json:"icsPnrCode"`
	Routing    *Routing `json:"routing"`
	GmtPayOut  string   `json:"gmtPayOut"`
}

type IssueTicketResponse struct {
	Response
}

type CancelBookingResponse struct {
	Response
}

type OrderVO struct {
	AirplanePnr      string            `json:"airplanePnr"`
	TotalSalePrice   float64           `json:"totalSalePrice"`
	Pnr              string            `json:"pnr"`
	Price            []*Price          `json:"price"`
	Contact          *Contact          `json:"contact"`
	Psis             []*Psis           `json:"psis"`
	RefundChangeRule *RefundChangeRule `json:"refundChangeRule"`
	BaggageInfo      *BaggageVOList    `json:"baggageInfo"`
	SaleCurrency     string            `json:"saleCurrency"`
	Status           enum.Status       `json:"status"`
}

type Price struct {
	PassengerType enum.PassengerType `json:"passengerType"`
	SalePrice     float64            `json:"salePrice"`
	SaleTax       float64            `json:"saleTax"`
	SaleCurrency  string             `json:"saleCurrency"`
}

type Contact struct {
	Name     string `json:"name"`
	Address  string `json:"address,omitempty"`
	PostCode string `json:"postCode,omitempty"`
	Email    string `json:"email"`
	Mobile   string `json:"mobile"`
}

type Psis struct {
	TicketNo  string               `json:"ticketNo"`
	Passenger *Passenger           `json:"passenger"`
	Segment   *DistributionSegment `json:"segment"`
}

type DistributionSegment struct {
	OperatingFlightNo string          `json:"operatingFlightNo"`
	DepTime           string          `json:"depTime"`
	CabinGrade        enum.CabinGrade `json:"cabinGrade"`
	Cabin             string          `json:"cabin"`
	DepAirport        string          `json:"depAirport"`
	FlightNumber      string          `json:"flightNumber"`
	Duration          int32           `json:"duration"`
	Sequence          int32           `json:"sequence"`
	Carrier           string          `json:"carrier"`
	CodeShare         int32           `json:"codeShare"`
	AircraftCode      string          `json:"aircraftCode"`
	ArrAirport        string          `json:"arrAirport"`
	DepTerminal       string          `json:"depTerminal"`
	ArrTerminal       string          `json:"arrTerminal"`
	OperatingCarrier  string          `json:"operatingCarrier"`
	ArrTime           string          `json:"arrTime"`
	SegmentType       int32           `json:"segmentType"`
	StopAirport       string          `json:"stopAirPorts"`
}

type Passenger struct {
	TicketNo       string                 `json:"ticketNo"`
	BirthDay       string                 `json:"birthday"`
	CardIssuePlace string                 `json:"cardIssuePlace"`
	LastName       string                 `json:"lastName"`
	FirstName      string                 `json:"firstName"`
	CardNum        string                 `json:"cardNum"`
	PassengerType  enum.PassengerType     `json:"passengerType,omitempty"`
	Gender         enum.Gender            `json:"gender"`
	Nationality    string                 `json:"nationality"`
	CardExpired    string                 `json:"cardExpired"`
	CardType       enum.PassengerCardType `json:"cardType"`
	PassengerID    string                 `json:"passgenerID,omitempty"`
	AgeType        enum.AgeType           `json:"ageType,omitempty"`
}

type RefundChangeRule struct {
	RefundRule *RefundRule `json:"refundRules"`
	ChangeRule *ChangeRule `json:"changeRules"`
}

type RefundRule struct {
	MultiStageRuleFees []*RefundMultiStageRuleFee `json:"multiStageRuleFees"`
}

type ChangeRule struct {
	MultiStageRuleFees []*ChangeMultiStageRuleFee `json:"multiStageRuleFees"`
}

type MultiStageRuleFee struct {
	IncludeEndPoint   bool                `json:"includeEndPoint"`
	Allowed           enum.Allow          `json:"allowed"`
	EndPointUnit      string              `json:"endPointUnit"`
	StartPoint        string              `json:"startPoint"`
	IncludeStartPoint bool                `json:"includeStartPoint"`
	StartPointUnit    string              `json:"startPointUnit"`
	EndPoint          string              `json:"endPoint"`
	DeparturePoint    enum.DeparturePoint `json:"departurePoint"`
	TaxAllowed        string              `json:"taxAllowed"`
	Currency          string              `json:"currency"`
}

type RefundMultiStageRuleFee struct {
	*MultiStageRuleFee
	Type enum.RefundType `json:"type"`
}

type ChangeMultiStageRuleFee struct {
	*MultiStageRuleFee
	Type enum.ChangeType `json:"type"`
}

type BaggageVOList struct {
	BaggageVOList []*BaggageVO `json:"baggageVOList"`
}

type BaggageVO struct {
	SegmentNum     int32            `json:"segmentNum"`
	BaggageRuleVOs []*BaggageRuleVO `json:"rules"`
}

type BaggageRuleVO struct {
	BaggageAllowance string             `json:"baggageAllowance"`
	BaggagePieces    string             `json:"baggagePieces"`
	BaggageType      enum.BaggageType   `json:"baggageType"`
	PassengerType    enum.PassengerType `json:"passengerType"`
}
