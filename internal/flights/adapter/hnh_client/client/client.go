package client

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"reflect"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type HNHClient interface {
	GetSeatMap(ctx context.Context, req *entities.GetSeatMapRequest, tracingID string, provider enum.FlightProvider) (*entities.GetSeatMapResponse, error)
	SearchFlight(ctx context.Context, req *entities.SearchFlightRequest, tracingID string, provider enum.FlightProvider) (*entities.SearchFlightResponse, error)
	GetBaggage(ctx context.Context, req *entities.GetBaggageRequest, tracingID string, provider enum.FlightProvider) (*entities.GetBaggageResponse, error)
	VerifyFlight(ctx context.Context, req *entities.VerifyFlightRequest, tracingID string, provider enum.FlightProvider) (*entities.VerifyFlightResponse, error)
	BookFlight(ctx context.Context, req *entities.BookFlightRequest, tracingID string, provider enum.FlightProvider) (*entities.BookFlightResponse, error)
	SearchMinFare(ctx context.Context, req *entities.SearchMinFareRequest, tracingID string, provider enum.FlightProvider) (*entities.SearchMinFaresResponse, error)
	IssueTicket(ctx context.Context, req *entities.IssueTicketRequest, tracingID string, provider enum.FlightProvider) (*entities.IssueTicketResponse, error)
	GetFareRules(ctx context.Context, req *entities.GetFareRulesRequest, tracingID string, provider enum.FlightProvider) (*entities.GetFareRulesResponse, error)
	GetAncillary(ctx context.Context, req *entities.GetAncillaryRequest, tracingID string, provider enum.FlightProvider) (*entities.GetAncillaryResponse, error)
	VoidTicket(ctx context.Context, req *entities.VoidTicketRequest, tracingID string, provider enum.FlightProvider) (*entities.VoidTicketResponse, error)
	GetBooking(ctx context.Context, req *entities.GetBookingRequest, tracingID string, provider enum.FlightProvider) (*entities.GetBookingResponse, error)
}

type hnhClient struct {
	BaseURL       string `validate:"required,url"`
	HeaderUser    string `validate:"required"`
	HeaderPass    string `validate:"required"`
	AgentAccount  string `validate:"required"`
	AgentPassword string `validate:"required"`
	ProductKey    string `validate:"required"`
	requestRepo   repositories.RequestRepository
}

func NewHNHClient(cfg *config.Schema, requestRepository repositories.RequestRepository, provider enum.FlightProvider) HNHClient {
	if provider == enum.FlightProviderHNH {
		return &hnhClient{
			BaseURL:       cfg.HNHBaseURL,
			HeaderUser:    cfg.HNHHeaderUser,
			HeaderPass:    cfg.HNHHeaderPass,
			AgentAccount:  cfg.HNHAgentAccount,
			AgentPassword: cfg.HNHAgentPassword,
			ProductKey:    cfg.HNHProductKey,
			requestRepo:   requestRepository,
		}
	}

	return &hnhClient{
		BaseURL:       cfg.HNHHPLBaseURL,
		HeaderUser:    cfg.HNHHPLHeaderUser,
		HeaderPass:    cfg.HNHHPLHeaderPass,
		AgentAccount:  cfg.HNHHPLAgentAccount,
		AgentPassword: cfg.HNHHPLAgentPassword,
		ProductKey:    cfg.HNHHPLProductKey,
		requestRepo:   requestRepository,
	}
}

func (c *hnhClient) do(
	ctx context.Context,
	relativePath string,
	method string,
	body interface{},
	headers map[string]string,
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	response, err := tracingHttp.JSONRequest(ctx, c.BaseURL+relativePath, method, body, headers)

	if err != nil {
		log.Error("Do request from HNH platform error",
			log.Any("error", err),
			log.String("relative path", relativePath),
			log.Any("req", body))
		return nil, 0, duration, err
	}

	bytesBuffer := bytes.Buffer{}

	_, err = io.Copy(&bytesBuffer, response.Body)
	if err != nil {
		log.Error("Read response error ",
			log.Any("error", err),
			log.String("relative path", relativePath))
		return nil, response.StatusCode, duration, err
	}
	defer response.Body.Close()

	resBody := bytesBuffer.Bytes()

	if response.StatusCode != http.StatusOK {
		log.Error("Status code not successful",
			log.Int("Status Code", response.StatusCode))
		return resBody, response.StatusCode, duration, errors.New(response.Status)
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *hnhClient) hideClientInfoRequest(body interface{}) {
	stype := reflect.ValueOf(body).Elem()
	field := stype.FieldByName("HeaderPass")
	if field.IsValid() {
		field.SetString("")
	}
	field = stype.FieldByName("AgentPassword")
	if field.IsValid() {
		field.SetString("")
	}
	field = stype.FieldByName("ProductKey")
	if field.IsValid() {
		field.SetString("")
	}
}

func (c *hnhClient) doRequest(
	ctx context.Context,
	relativePath string,
	method string,
	body interface{},
	headers map[string]string,
	tracingID string,
	provider enum.FlightProvider,
) ([]byte, string, error) {
	requestID := uuid.New().String()

	data, statusCode, duration, err := c.do(ctx, relativePath, method, body, headers)

	go func() {
		fullPath := c.BaseURL + relativePath
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)
		defer cancel()
		c.hideClientInfoRequest(body)

		req := &repositories.Request{
			Provider:   provider,
			RequestID:  requestID,
			Path:       fullPath,
			Method:     method,
			Body:       body,
			Headers:    headers,
			Response:   data,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     relativePath,
			IsJson:     true,
			ErrorMsg:   err,
			TracingID:  tracingID,
		}

		if err = c.requestRepo.Create(bCtx, req); err != nil {
			log.Error("doRequest requestRepo.Create error",
				log.Any("error", err),
				log.Any("req", req),
			)
		}
	}()

	return data, requestID, err
}

func (c *hnhClient) getAuthInfo() *entities.AuthInfo {
	return &entities.AuthInfo{
		HeaderUser:    c.HeaderUser,
		HeaderPass:    c.HeaderPass,
		AgentAccount:  c.AgentAccount,
		AgentPassword: c.AgentPassword,
		ProductKey:    c.ProductKey,
	}
}

func (c *hnhClient) SearchFlight(ctx context.Context, req *entities.SearchFlightRequest, tracingID string, provider enum.FlightProvider) (*entities.SearchFlightResponse, error) {
	cReq := &entities.HNHSearchFlightRequest{
		PassengerQuantity: &entities.PassengerQuantity{
			Adt: req.Adt,
			Chd: req.Chd,
			Inf: req.Inf,
		},
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		ViewMode:   "",
		ListFlight: req.ListFlight,
	}

	resBody, rID, err := c.doRequest(ctx, "/flights/searchflight", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.SearchFlightResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}

	res.InternalRequestID = rID
	return res, nil
}

func (c *hnhClient) GetSeatMap(ctx context.Context, req *entities.GetSeatMapRequest, tracingID string, provider enum.FlightProvider) (*entities.GetSeatMapResponse, error) {
	if req == nil {
		return nil, errors.New("nil input")
	}
	cReq := &entities.HNHGetSeatMapRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			IPRequest: "",
			Currency:  req.Currency,
			Language:  req.Language,
		},
		ListFareData: req.ListFareData,
	}
	resBody, _, err := c.doRequest(ctx, "/flights/getseatmap", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.GetSeatMapResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *hnhClient) SearchMinFare(ctx context.Context, req *entities.SearchMinFareRequest, tracingID string, provider enum.FlightProvider) (*entities.SearchMinFaresResponse, error) {
	if req == nil {
		return nil, errors.New("nil input")
	}
	cReq := &entities.HNHSearchMinFareRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Lang,
			IPRequest: "",
		},
		FlightRequest: &entities.FlightRequest{
			StartPoint: req.StartPoint,
			EndPoint:   req.EndPoint,
			DepartDate: req.DepartDate,
			Airline:    "",
		},
	}
	resBody, _, err := c.doRequest(ctx, "/flights/searchminfare", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		log.Error("SearchMinFare doRequest error", log.Any("error", err), log.Any("req", cReq))
		return nil, err
	}

	var res *entities.SearchMinFaresResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	if !res.Status {
		// if res.ErrorCode == string(enum.ErrNotFoundAnyFlightForItinerary) {
		// 	return res, nil
		// }

		log.Error(
			"SearchMinFare Response error",
			log.Any("error_code", res.ErrorCode),
			log.Any("message", res.Message),
			log.Any("req", req),
		)
		return res, commonErrors.ErrSomethingOccurred
	}
	return res, nil
}

func (c *hnhClient) GetBaggage(ctx context.Context, req *entities.GetBaggageRequest, tracingID string, provider enum.FlightProvider) (*entities.GetBaggageResponse, error) {
	if req == nil {
		return nil, errors.New("nil input")
	}
	cReq := &entities.HNHGetBaggageRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		ListFareData: req.ListFareData,
	}

	resBody, _, err := c.doRequest(ctx, "/flights/getbaggage", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.GetBaggageResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *hnhClient) VerifyFlight(ctx context.Context, req *entities.VerifyFlightRequest, tracingID string, provider enum.FlightProvider) (*entities.VerifyFlightResponse, error) {
	if req == nil {
		return nil, errors.New("nil input")
	}
	cReq := &entities.HNHVerifyFlightRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		ListFareData: req.ListFareData,
	}
	resBody, _, err := c.doRequest(ctx, "/flights/verifyflight", "POST", cReq, nil, tracingID, provider)

	var res *entities.VerifyFlightResponse

	if err != nil {
		return nil, err
	}
	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *hnhClient) BookFlight(ctx context.Context, req *entities.BookFlightRequest, tracingID string, provider enum.FlightProvider) (*entities.BookFlightResponse, error) {
	cReq := &entities.HNHBookFlightRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		BookType:        req.BookType,
		UseAgentContact: req.UseAgentContact,
		Contact:         req.Contact,
		AccountCode:     req.AccountCode,
		Remark:          req.Remark,
		ListPassenger:   req.ListPassenger,
		ListFareData:    req.ListFareData,
	}
	resBody, rID, err := c.doRequest(ctx, "/flights/bookflight", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.BookFlightResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	res.InternalRequestID = rID
	return res, nil
}

func (c *hnhClient) IssueTicket(ctx context.Context, req *entities.IssueTicketRequest, tracingID string, provider enum.FlightProvider) (*entities.IssueTicketResponse, error) {
	cReq := &entities.HNHIssueTicketRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		Airline:          req.Airline,
		BookingCode:      req.BookingCode,
		BookingPcc:       req.BookingPcc,
		System:           req.System,
		TotalPrice:       req.TotalPrice,
		FareQuoteSession: req.FareQuoteSession,
		TourCode:         req.TourCode,
		SendEmail:        req.SendEmail,
		ListPassenger:    req.ListPassenger,
	}
	resBody, _, err := c.doRequest(ctx, "/flights/issueticket", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.IssueTicketResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *hnhClient) GetAncillary(ctx context.Context, req *entities.GetAncillaryRequest, tracingID string, provider enum.FlightProvider) (*entities.GetAncillaryResponse, error) {
	cReq := &entities.HNHGetAncillaryRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		ListFareData: req.ListFareData,
	}
	resBody, _, err := c.doRequest(ctx, "/flights/getancillary", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.GetAncillaryResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c hnhClient) GetFareRules(ctx context.Context, req *entities.GetFareRulesRequest, tracingID string, provider enum.FlightProvider) (*entities.GetFareRulesResponse, error) {
	cReq := &entities.HNHGetFareRulesRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		GetFromGDS:   req.GetFromGDS,
		ListFareData: req.ListFareData,
	}

	resBody, _, err := c.doRequest(ctx, "/flights/getfarerules", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.GetFareRulesResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c hnhClient) VoidTicket(ctx context.Context, req *entities.VoidTicketRequest, tracingID string, provider enum.FlightProvider) (*entities.VoidTicketResponse, error) {
	cReq := &entities.HNHVoidTicketRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		Airline:       req.Airline,
		BookingCode:   req.BookingCode,
		BookingPcc:    req.BookingPcc,
		System:        req.System,
		TicketNumber:  req.TicketNumber,
		CancelBooking: req.CancelBooking,
		VoidAllTicket: req.VoidAllTicket,
	}

	resBody, _, err := c.doRequest(ctx, "/flights/voidticket", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.VoidTicketResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c hnhClient) GetBooking(ctx context.Context, req *entities.GetBookingRequest, tracingID string, provider enum.FlightProvider) (*entities.GetBookingResponse, error) {
	cReq := &entities.HNHGetBookingRequest{
		BaseRequestInfo: &entities.BaseRequestInfo{
			AuthInfo:  c.getAuthInfo(),
			Currency:  req.Currency,
			Language:  req.Language,
			IPRequest: "",
		},
		Airline:         req.Airline,
		System:          req.System,
		BookingCode:     req.BookingCode,
		LastName:        req.LastName,
		GetBookingImage: req.GetBookingImage,
	}

	resBody, _, err := c.doRequest(ctx, "/flights/retrievebooking", "POST", cReq, nil, tracingID, provider)
	if err != nil {
		return nil, err
	}

	var res *entities.GetBookingResponse

	if err = json.Unmarshal(resBody, &res); err != nil {
		return nil, err
	}
	return res, nil
}
