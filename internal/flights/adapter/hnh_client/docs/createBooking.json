{"BookingId": 11609940, "OrderCode": "DC1331211609940", "ListBooking": [{"Status": "OK", "System": "VN", "Source": "API", "Airline": "VN", "BookingCode": "5CM7Q7", "GdsCode": "5CM7Q7", "Flight": "0VN6030YSGNHAN202408050725;VN615LHANBKK202408051245|1VN604JBKKSGN202408201425", "Route": "SGNBKK|BKKSGN", "AutoIssue": false, "ErrorCode": "000", "ErrorMessage": null, "BookingImage": null, "ExpiryDate": "2024-07-29T14:11:14.2859228+07:00", "ExpiryDt": "29072024 14:11:14", "ExpiryTime": 40, "TimePurchase": "2024-07-29T14:11:14.2859228+07:00", "PriceExpiration": "29072024 14:11:14", "ResponseTime": 13.6681832, "Price": 43939500, "Difference": 0, "ListTicket": null, "Warnings": null, "ListPassenger": [{"Index": 0, "NameId": "2", "ParentId": -1, "FirstName": "TRAN", "LastName": "THAN", "Type": "ADT", "Gender": true, "Birthday": "01011970", "PassportNumber": "", "PassportExpiry": "", "Nationality": "", "IssueCountry": "", "Membership": null, "CustLoyalty": {"Airline": "VN", "MembershipID": null, "MembershipType": null, "Status": "HL", "Success": false}, "ListFare": [{"DocType": "TKT", "BaseFare": 11838000, "Fees": 0, "Taxes": 3436000, "VAT": 0}], "ListBaggage": [], "ListSeat": [], "ListService": [{"Session": null, "Airline": "VN", "Code": "INFT", "Type": "OTHER", "Name": "INFT", "Description": null, "Price": 0, "Currency": "VND", "StartPoint": "SGN", "EndPoint": "HAN", "Route": null, "Leg": 0, "StatusCode": "HK", "Confirmed": true}, {"Session": null, "Airline": "VN", "Code": "INFT", "Type": "OTHER", "Name": "INFT", "Description": null, "Price": 0, "Currency": "VND", "StartPoint": "HAN", "EndPoint": "BKK", "Route": null, "Leg": 0, "StatusCode": "HK", "Confirmed": true}, {"Session": null, "Airline": "VN", "Code": "INFT", "Type": "OTHER", "Name": "INFT", "Description": null, "Price": 0, "Currency": "VND", "StartPoint": "BKK", "EndPoint": "SGN", "Route": "BKKSGN", "Leg": 0, "StatusCode": "HK", "Confirmed": true}], "ListCustLoyalty": null, "NewPassenger": null}, {"Index": 1, "NameId": "4", "ParentId": -1, "FirstName": "TRAN", "LastName": "TIN", "Type": "ADT", "Gender": true, "Birthday": "25121999", "PassportNumber": "", "PassportExpiry": "", "Nationality": "", "IssueCountry": "", "Membership": null, "CustLoyalty": {"Airline": "VN", "MembershipID": null, "MembershipType": null, "Status": "HL", "Success": false}, "ListFare": [{"DocType": "TKT", "BaseFare": 11838000, "Fees": 0, "Taxes": 3436000, "VAT": 0}], "ListBaggage": [], "ListSeat": [], "ListService": [], "ListCustLoyalty": null, "NewPassenger": null}, {"Index": 2, "NameId": "5", "ParentId": -1, "FirstName": "VO", "LastName": "TAM", "Type": "CHD", "Gender": false, "Birthday": "10102018", "PassportNumber": "", "PassportExpiry": "", "Nationality": "", "IssueCountry": "", "Membership": null, "CustLoyalty": {"Airline": "VN", "MembershipID": null, "MembershipType": null, "Status": "HL", "Success": false}, "ListFare": [{"DocType": "TKT", "BaseFare": 8885000, "Fees": 0, "Taxes": 3033500, "VAT": 0}], "ListBaggage": [], "ListSeat": [], "ListService": [{"Session": null, "Airline": "VN", "Code": "CHLD", "Type": "OTHER", "Name": "CHLD", "Description": null, "Price": 0, "Currency": "VND", "StartPoint": null, "EndPoint": null, "Route": null, "Leg": 0, "StatusCode": "HK", "Confirmed": true}], "ListCustLoyalty": null, "NewPassenger": null}, {"Index": 3, "NameId": "2", "ParentId": 1, "FirstName": "VO", "LastName": "HUYNH", "Type": "INF", "Gender": false, "Birthday": "16012023", "PassportNumber": "", "PassportExpiry": "", "Nationality": "", "IssueCountry": "", "Membership": null, "CustLoyalty": {"Airline": "VN", "MembershipID": null, "MembershipType": null, "Status": "HL", "Success": false}, "ListFare": [{"DocType": "TKT", "BaseFare": 1197000, "Fees": 0, "Taxes": 276000, "VAT": 0}], "ListBaggage": [], "ListSeat": [], "ListService": [], "ListCustLoyalty": null, "NewPassenger": null}], "ListFareData": [{"FareDataId": 16, "Airline": "VN", "System": "VN", "Itinerary": 1, "Leg": 0, "Promo": false, "FullFare": true, "Currency": "VND", "Tourcode": null, "FareType": "agent", "CacheAge": 0, "Availability": 9, "Adt": 2, "Chd": 1, "Inf": 1, "FareAdt": 3055000, "FareChd": 2292000, "FareInf": 306000, "TaxAdt": 1863000, "TaxChd": 1460500, "TaxInf": 138000, "FeeAdt": 0, "FeeChd": 0, "FeeInf": 0, "VatAdt": 0, "VatChd": 0, "VatInf": 0, "ServiceFeeAdt": 0, "ServiceFeeChd": 0, "ServiceFeeInf": 0, "DiscountAdt": 0, "DiscountChd": 0, "DiscountInf": 0, "TotalNetPrice": 14032500, "TotalServiceFee": 0, "TotalDiscount": 0, "TotalCommission": 0, "TotalPrice": 14032500, "ListFlight": [{"Leg": 0, "FlightId": 0, "Airline": "VN", "Operating": "BL", "StartPoint": "SGN", "EndPoint": "BKK", "StartDate": "2024-08-05T07:25:00", "EndDate": "2024-08-05T14:45:00", "StartDt": "05082024 07:25:00", "EndDt": "05082024 14:45:00", "FlightNumber": "VN6030,VN615", "Duration": 635, "StopNum": 1, "FlightValue": "0VN6030YSGNHAN202408050725;VN615LHANBKK202408051245", "QH_airBookingId": null, "ListSegment": [{"Id": 1, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "BL", "StartPoint": "SGN", "EndPoint": "HAN", "StartTime": "2024-08-05T07:25:00", "StartTimeZoneOffset": "+07:00", "EndTime": "2024-08-05T09:30:00", "EndTimeZoneOffset": "+07:00", "StartTm": "05082024 07:25:00", "EndTm": "05082024 09:30:00", "FlightNumber": "VN6030", "Duration": 0, "Plane": "320", "StartTerminal": "1", "EndTerminal": "1", "HasStop": true, "StopPoint": "HAN", "StopTime": 195, "DayChange": false, "StopOvernight": false, "ChangeStation": true, "ChangeAirport": false, "LastItem": false, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 9, "Cabin": "Economy Classic", "Class": "Y", "FareBasis": "LOXVNF", "HandBaggage": "7kg", "AllowanceBaggage": "1 kiện x 23kg", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}, {"Id": 2, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "HAN", "EndPoint": "BKK", "StartTime": "2024-08-05T12:45:00", "StartTimeZoneOffset": "+07:00", "EndTime": "2024-08-05T14:45:00", "EndTimeZoneOffset": "+07:00", "StartTm": "05082024 12:45:00", "EndTm": "05082024 14:45:00", "FlightNumber": "VN615", "Duration": 440, "Plane": "321", "StartTerminal": "2", "EndTerminal": null, "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 9, "Cabin": "Economy Classic", "Class": "L", "FareBasis": "LOXVNF", "HandBaggage": "7kg", "AllowanceBaggage": "1 kiện x 23kg", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": [{"PaxType": "ADT", "PaxNumb": 2, "Fare": 3055000, "Tax": 1863000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 4918000}, {"PaxType": "CHD", "PaxNumb": 1, "Fare": 2292000, "Tax": 1460500, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 3752500}, {"PaxType": "INF", "PaxNumb": 1, "Fare": 306000, "Tax": 138000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 444000}], "HasDownStop": false, "NoRefund": false, "GroupClass": "Economy Classic", "FareClass": "Y,L", "FareBasis": "LOXVNF", "SeatRemain": 9, "Promo": false}], "ListXmlRulesInfo": null, "LastTicketDate": "2024-07-29T11:22:24.1185793+07:00", "Session": "DC13312SGNBKK050824BKKSGN200824211112208", "AutoIssue": false, "CAcode": "", "VIPText": "", "Remark": null, "AccountCode": null, "VerifyMode": null, "VnaApiInfo": {"Verify": null, "Session": "SGNBKK05082024-BKKSGN20082024_211_290724112209_VNA00017", "AirOptionId": 7, "FareOptionId": 0, "FlightOptionId": 0}}, {"FareDataId": 222, "Airline": "VN", "System": "VN", "Itinerary": 1, "Leg": 1, "Promo": false, "FullFare": true, "Currency": "VND", "Tourcode": null, "FareType": "agent", "CacheAge": 0, "Availability": 9, "Adt": 2, "Chd": 1, "Inf": 1, "FareAdt": ********, "FareChd": 9168000, "FareInf": 1224000, "TaxAdt": 1573000, "TaxChd": 1573000, "TaxInf": 138000, "FeeAdt": 0, "FeeChd": 0, "FeeInf": 0, "VatAdt": 0, "VatChd": 0, "VatInf": 0, "ServiceFeeAdt": 0, "ServiceFeeChd": 0, "ServiceFeeInf": 0, "DiscountAdt": 0, "DiscountChd": 0, "DiscountInf": 0, "TotalNetPrice": ********, "TotalServiceFee": 0, "TotalDiscount": 0, "TotalCommission": 0, "TotalPrice": ********, "ListFlight": [{"Leg": 1, "FlightId": 1, "Airline": "VN", "Operating": "VN", "StartPoint": "BKK", "EndPoint": "SGN", "StartDate": "2024-08-20T14:25:00", "EndDate": "2024-08-20T16:20:00", "StartDt": "20082024 14:25:00", "EndDt": "20082024 16:20:00", "FlightNumber": "VN604", "Duration": 115, "StopNum": 0, "FlightValue": "1VN604JBKKSGN202408201425", "QH_airBookingId": null, "ListSegment": [{"Id": 1, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "BKK", "EndPoint": "SGN", "StartTime": "2024-08-20T14:25:00", "StartTimeZoneOffset": "+07:00", "EndTime": "2024-08-20T16:20:00", "EndTimeZoneOffset": "+07:00", "StartTm": "20082024 14:25:00", "EndTm": "20082024 16:20:00", "FlightNumber": "VN604", "Duration": 115, "Plane": "350", "StartTerminal": null, "EndTerminal": "2", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 9, "Cabin": "Business Flex", "Class": "J", "FareBasis": "JOXASF", "HandBaggage": "2 kiện x 9kg", "AllowanceBaggage": "1 kiện x 32kg", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": [{"PaxType": "ADT", "PaxNumb": 2, "Fare": ********, "Tax": 1573000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 13796000}, {"PaxType": "CHD", "PaxNumb": 1, "Fare": 9168000, "Tax": 1573000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": ********}, {"PaxType": "INF", "PaxNumb": 1, "Fare": 1224000, "Tax": 138000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 1362000}], "HasDownStop": false, "NoRefund": false, "GroupClass": "Business Flex", "FareClass": "J", "FareBasis": "JOXASF", "SeatRemain": 9, "Promo": false}], "ListXmlRulesInfo": null, "LastTicketDate": "2024-07-29T11:22:24.1185793+07:00", "Session": "DC13312SGNBKK050824BKKSGN200824211112208", "AutoIssue": false, "CAcode": "", "VIPText": "", "Remark": null, "AccountCode": null, "VerifyMode": null, "VnaApiInfo": {"Verify": null, "Session": "SGNBKK05082024-BKKSGN20082024_211_290724112209_VNA00017", "AirOptionId": 9, "FareOptionId": 12, "FlightOptionId": 0}}], "Session": "DC13312SGNBKK050824BKKSGN200824211112208", "FareData": {"FareDataId": 0, "Airline": "VN", "System": "VN", "Itinerary": 1, "Leg": 0, "Promo": false, "FullFare": true, "Currency": "VND", "Tourcode": null, "FareType": "agent", "CacheAge": 0, "Availability": 9, "Adt": 2, "Chd": 1, "Inf": 1, "FareAdt": ********, "FareChd": ********, "FareInf": 1530000, "TaxAdt": 3436000, "TaxChd": 3033500, "TaxInf": 276000, "FeeAdt": 0, "FeeChd": 0, "FeeInf": 0, "VatAdt": 0, "VatChd": 0, "VatInf": 0, "ServiceFeeAdt": 0, "ServiceFeeChd": 0, "ServiceFeeInf": 0, "DiscountAdt": 0, "DiscountChd": 0, "DiscountInf": 0, "TotalNetPrice": ********, "TotalServiceFee": 0, "TotalDiscount": 0, "TotalCommission": 0, "TotalPrice": ********, "ListFlight": [{"Leg": 0, "FlightId": 0, "Airline": "VN", "Operating": "BL", "StartPoint": "SGN", "EndPoint": "BKK", "StartDate": "2024-08-05T07:25:00", "EndDate": "2024-08-05T14:45:00", "StartDt": "05082024 07:25:00", "EndDt": "05082024 14:45:00", "FlightNumber": "VN6030,VN615", "Duration": 635, "StopNum": 1, "FlightValue": "0VN6030YSGNHAN202408050725;VN615LHANBKK202408051245", "QH_airBookingId": null, "ListSegment": [{"Id": 1, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "BL", "StartPoint": "SGN", "EndPoint": "HAN", "StartTime": "2024-08-05T07:25:00", "StartTimeZoneOffset": "+07:00", "EndTime": "2024-08-05T09:30:00", "EndTimeZoneOffset": "+07:00", "StartTm": "05082024 07:25:00", "EndTm": "05082024 09:30:00", "FlightNumber": "VN6030", "Duration": 0, "Plane": "320", "StartTerminal": "1", "EndTerminal": "1", "HasStop": true, "StopPoint": "HAN", "StopTime": 195, "DayChange": false, "StopOvernight": false, "ChangeStation": true, "ChangeAirport": false, "LastItem": false, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 9, "Cabin": "Economy Classic", "Class": "Y", "FareBasis": "LOXVNF", "HandBaggage": "7kg", "AllowanceBaggage": "1 kiện x 23kg", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}, {"Id": 2, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "HAN", "EndPoint": "BKK", "StartTime": "2024-08-05T12:45:00", "StartTimeZoneOffset": "+07:00", "EndTime": "2024-08-05T14:45:00", "EndTimeZoneOffset": "+07:00", "StartTm": "05082024 12:45:00", "EndTm": "05082024 14:45:00", "FlightNumber": "VN615", "Duration": 440, "Plane": "321", "StartTerminal": "2", "EndTerminal": null, "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 9, "Cabin": "Economy Classic", "Class": "L", "FareBasis": "LOXVNF", "HandBaggage": "7kg", "AllowanceBaggage": "1 kiện x 23kg", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": [{"PaxType": "ADT", "PaxNumb": 2, "Fare": 3055000, "Tax": 1863000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 4918000}, {"PaxType": "CHD", "PaxNumb": 1, "Fare": 2292000, "Tax": 1460500, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 3752500}, {"PaxType": "INF", "PaxNumb": 1, "Fare": 306000, "Tax": 138000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 444000}], "HasDownStop": false, "NoRefund": false, "GroupClass": "Economy Classic", "FareClass": "Y,L", "FareBasis": "LOXVNF", "SeatRemain": 9, "Promo": false}, {"Leg": 1, "FlightId": 1, "Airline": "VN", "Operating": "VN", "StartPoint": "BKK", "EndPoint": "SGN", "StartDate": "2024-08-20T14:25:00", "EndDate": "2024-08-20T16:20:00", "StartDt": "20082024 14:25:00", "EndDt": "20082024 16:20:00", "FlightNumber": "VN604", "Duration": 115, "StopNum": 0, "FlightValue": "1VN604JBKKSGN202408201425", "QH_airBookingId": null, "ListSegment": [{"Id": 1, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "BKK", "EndPoint": "SGN", "StartTime": "2024-08-20T14:25:00", "StartTimeZoneOffset": "+07:00", "EndTime": "2024-08-20T16:20:00", "EndTimeZoneOffset": "+07:00", "StartTm": "20082024 14:25:00", "EndTm": "20082024 16:20:00", "FlightNumber": "VN604", "Duration": 115, "Plane": "350", "StartTerminal": null, "EndTerminal": "2", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 9, "Cabin": "Business Flex", "Class": "J", "FareBasis": "JOXASF", "HandBaggage": "2 kiện x 9kg", "AllowanceBaggage": "1 kiện x 32kg", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": [{"PaxType": "ADT", "PaxNumb": 2, "Fare": ********, "Tax": 1573000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 13796000}, {"PaxType": "CHD", "PaxNumb": 1, "Fare": 9168000, "Tax": 1573000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": ********}, {"PaxType": "INF", "PaxNumb": 1, "Fare": 1224000, "Tax": 138000, "Fee": 0, "Vat": 0, "Discount": 0, "Total": 1362000}], "HasDownStop": false, "NoRefund": false, "GroupClass": "Business Flex", "FareClass": "J", "FareBasis": "JOXASF", "SeatRemain": 9, "Promo": false}], "ListXmlRulesInfo": null, "LastTicketDate": "2024-07-29T13:31:27.5624472+07:00", "Session": "DC13312SGNBKK050824BKKSGN200824211112208", "AutoIssue": false, "CAcode": "", "VIPText": "", "Remark": null, "AccountCode": null, "VerifyMode": null, "VnaApiInfo": null}}], "Status": true, "ErrorCode": "000", "ErrorValue": "", "ErrorField": "", "Message": "", "Language": "vi"}