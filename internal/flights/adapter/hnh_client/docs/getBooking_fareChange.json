{"Reservation": {"Airline": "VN", "System": "VN", "Source": "API", "BookingCode": "5N7LYU", "GdsCode": "5N7LYU", "BookingStatus": "OK", "BookingImage": "5N7LYU\n1. PHAM B/TRUC LINH A MR\nITINERARY\n1 VN VN655 22AUG P SGNSIN 1420 1725\n2 VN VN654 31AUG P SINSGN 1825 1955\nTIMELIMIT: 10:37 04/08/2024\nPHONES\n+84********* \nEMAILS\n<EMAIL>", "BookingData": null, "BookingDate": "2024-07-31T10:47:03.3568179+07:00", "BookingTime": "31072024 10:47:03", "IssueDate": null, "IssueTime": null, "ExpiryDate": "2024-08-04T10:37:00", "ExpiryTime": "04082024 10:37:00", "TimePurchase": "2024-07-31T23:59:00", "PriceExpiration": "31072024 23:59:00", "TotalPrice": 4933000, "FareData": {"FareDataId": 0, "Airline": "VN", "System": "VN", "Itinerary": 2, "Leg": 0, "Promo": false, "FullFare": true, "Currency": "VND", "Tourcode": null, "FareType": null, "CacheAge": 0, "Availability": 0, "Adt": 1, "Chd": 0, "Inf": 0, "FareAdt": 1095000, "FareChd": 0, "FareInf": 0, "TaxAdt": 3838000, "TaxChd": 0, "TaxInf": 0, "FeeAdt": 0, "FeeChd": 0, "FeeInf": 0, "VatAdt": 0, "VatChd": 0, "VatInf": 0, "ServiceFeeAdt": 0, "ServiceFeeChd": 0, "ServiceFeeInf": 0, "DiscountAdt": 0, "DiscountChd": 0, "DiscountInf": 0, "TotalNetPrice": 4933000, "TotalServiceFee": 0, "TotalDiscount": 0, "TotalCommission": 0, "TotalPrice": 4933000, "ListFlight": [{"Leg": 0, "FlightId": 0, "Airline": "VN", "Operating": "VN", "StartPoint": "SGN", "EndPoint": "SIN", "StartDate": "2024-08-22T14:20:00", "EndDate": "2024-08-22T17:25:00", "StartDt": "******** 14:20:00", "EndDt": "******** 17:25:00", "FlightNumber": "VN655", "Duration": 0, "StopNum": 0, "FlightValue": "0VN655PSGNSIN202408221420", "QH_airBookingId": null, "ListSegment": [{"Id": 2, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "SGN", "EndPoint": "SIN", "StartTime": "2024-08-22T14:20:00", "StartTimeZoneOffset": "", "EndTime": "2024-08-22T17:25:00", "EndTimeZoneOffset": "", "StartTm": "******** 14:20:00", "EndTm": "******** 17:25:00", "FlightNumber": "VN655", "Duration": 0, "Plane": "350", "StartTerminal": "2", "EndTerminal": "4", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 0, "Cabin": null, "Class": "P", "FareBasis": "P1YVNF", "HandBaggage": "1 kiện x 12kg", "AllowanceBaggage": "", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": null, "HasDownStop": false, "NoRefund": false, "GroupClass": "", "FareClass": "P", "FareBasis": "P1YVNF", "SeatRemain": 0, "Promo": false}, {"Leg": 1, "FlightId": 1, "Airline": "VN", "Operating": "VN", "StartPoint": "SIN", "EndPoint": "SGN", "StartDate": "2024-08-31T18:25:00", "EndDate": "2024-08-31T19:55:00", "StartDt": "31082024 18:25:00", "EndDt": "31082024 19:55:00", "FlightNumber": "VN654", "Duration": 0, "StopNum": 0, "FlightValue": "1VN654PSINSGN202408311825", "QH_airBookingId": null, "ListSegment": [{"Id": 1, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "SIN", "EndPoint": "SGN", "StartTime": "2024-08-31T18:25:00", "StartTimeZoneOffset": "", "EndTime": "2024-08-31T19:55:00", "EndTimeZoneOffset": "", "StartTm": "31082024 18:25:00", "EndTm": "31082024 19:55:00", "FlightNumber": "VN654", "Duration": 0, "Plane": "350", "StartTerminal": "4", "EndTerminal": "2", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 0, "Cabin": null, "Class": "P", "FareBasis": "P1YVNF", "HandBaggage": "1 kiện x 12kg", "AllowanceBaggage": "", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": null, "HasDownStop": false, "NoRefund": false, "GroupClass": "", "FareClass": "P", "FareBasis": "P1YVNF", "SeatRemain": 0, "Promo": false}], "ListXmlRulesInfo": null, "LastTicketDate": "2024-07-31T10:47:03.8698668+07:00", "Session": null, "AutoIssue": false, "CAcode": null, "VIPText": null, "Remark": null, "AccountCode": null, "VerifyMode": null, "VnaApiInfo": {"Verify": null, "Session": null, "AirOptionId": 0, "FareOptionId": 0, "FlightOptionId": 0}}, "ListContact": [{"Gender": true, "Title": null, "Name": null, "Area": "+84", "Phone": "*********", "Email": "<EMAIL>", "Address": null, "City": null, "Country": null, "NameInPhone": false, "ReceiveEmail": false}], "ListPassenger": [{"Index": 0, "NameId": "2", "ParentId": -1, "FirstName": "PHAM B", "LastName": "TRUC LINH A", "Type": "ADT", "Gender": true, "Birthday": "", "PassportNumber": "", "PassportExpiry": "", "Nationality": "", "IssueCountry": "", "Membership": null, "CustLoyalty": {"Airline": "VN", "MembershipID": null, "MembershipType": null, "Status": "HL", "Success": false}, "ListFare": [{"DocType": "TKT", "BaseFare": 1095000, "Fees": 0, "Taxes": 3838000, "VAT": 0}], "ListBaggage": [], "ListSeat": [], "ListService": [], "ListCustLoyalty": null, "NewPassenger": null}], "ListTicket": null, "ListWarning": null, "ListTransaction": null}, "NewFormat": false, "Airline": "VN", "BookingCode": "5N7LYU", "BookingImage": "5N7LYU\n1. PHAM B/TRUC LINH A MR\nITINERARY\n1 VN VN655 22AUG P SGNSIN 1420 1725\n2 VN VN654 31AUG P SINSGN 1825 1955\nTIMELIMIT: 10:37 04/08/2024\nPHONES\n+84********* \nEMAILS\n<EMAIL>", "GdsCode": "5N7LYU", "BookingStatus": "OK", "BookingData": null, "System": "VN", "Source": "API", "BookingDate": "2024-07-31T10:47:03.3568179+07:00", "BookingTime": "31072024 10:47:03", "IssueDate": null, "IssueTime": null, "ExpiryDate": "2024-08-04T10:37:00", "Expiration": "04082024 10:37:00", "TotalPrice": 4933000, "Contact": {"Gender": true, "Title": null, "FirstName": null, "LastName": null, "Area": "+84", "Phone": "*********", "Email": "<EMAIL>", "Address": null, "City": null, "Country": null, "AgentArea": null, "AgentPhone": null, "AgentEmail": null, "AgentName": null, "MultiContact": false, "NameInPhone": false}, "FareData": {"FareDataId": 0, "Airline": "VN", "System": "VN", "Itinerary": 2, "Leg": 0, "Promo": false, "FullFare": true, "Currency": "VND", "Tourcode": null, "FareType": null, "CacheAge": 0, "Availability": 0, "Adt": 1, "Chd": 0, "Inf": 0, "FareAdt": 1095000, "FareChd": 0, "FareInf": 0, "TaxAdt": 3838000, "TaxChd": 0, "TaxInf": 0, "FeeAdt": 0, "FeeChd": 0, "FeeInf": 0, "VatAdt": 0, "VatChd": 0, "VatInf": 0, "ServiceFeeAdt": 0, "ServiceFeeChd": 0, "ServiceFeeInf": 0, "DiscountAdt": 0, "DiscountChd": 0, "DiscountInf": 0, "TotalNetPrice": 4933000, "TotalServiceFee": 0, "TotalDiscount": 0, "TotalCommission": 0, "TotalPrice": 4933000, "ListFlight": [{"Leg": 0, "FlightId": 0, "Airline": "VN", "Operating": "VN", "StartPoint": "SGN", "EndPoint": "SIN", "StartDate": "2024-08-22T14:20:00", "EndDate": "2024-08-22T17:25:00", "StartDt": "******** 14:20:00", "EndDt": "******** 17:25:00", "FlightNumber": "VN655", "Duration": 0, "StopNum": 0, "FlightValue": "0VN655PSGNSIN202408221420", "QH_airBookingId": null, "ListSegment": [{"Id": 2, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "SGN", "EndPoint": "SIN", "StartTime": "2024-08-22T14:20:00", "StartTimeZoneOffset": "", "EndTime": "2024-08-22T17:25:00", "EndTimeZoneOffset": "", "StartTm": "******** 14:20:00", "EndTm": "******** 17:25:00", "FlightNumber": "VN655", "Duration": 0, "Plane": "350", "StartTerminal": "2", "EndTerminal": "4", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 0, "Cabin": null, "Class": "P", "FareBasis": "P1YVNF", "HandBaggage": "1 kiện x 12kg", "AllowanceBaggage": "", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": null, "HasDownStop": false, "NoRefund": false, "GroupClass": "", "FareClass": "P", "FareBasis": "P1YVNF", "SeatRemain": 0, "Promo": false}, {"Leg": 1, "FlightId": 1, "Airline": "VN", "Operating": "VN", "StartPoint": "SIN", "EndPoint": "SGN", "StartDate": "2024-08-31T18:25:00", "EndDate": "2024-08-31T19:55:00", "StartDt": "31082024 18:25:00", "EndDt": "31082024 19:55:00", "FlightNumber": "VN654", "Duration": 0, "StopNum": 0, "FlightValue": "1VN654PSINSGN202408311825", "QH_airBookingId": null, "ListSegment": [{"Id": 1, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "SIN", "EndPoint": "SGN", "StartTime": "2024-08-31T18:25:00", "StartTimeZoneOffset": "", "EndTime": "2024-08-31T19:55:00", "EndTimeZoneOffset": "", "StartTm": "31082024 18:25:00", "EndTm": "31082024 19:55:00", "FlightNumber": "VN654", "Duration": 0, "Plane": "350", "StartTerminal": "4", "EndTerminal": "2", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 0, "Cabin": null, "Class": "P", "FareBasis": "P1YVNF", "HandBaggage": "1 kiện x 12kg", "AllowanceBaggage": "", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPrice": null, "HasDownStop": false, "NoRefund": false, "GroupClass": "", "FareClass": "P", "FareBasis": "P1YVNF", "SeatRemain": 0, "Promo": false}], "ListXmlRulesInfo": null, "LastTicketDate": "2024-07-31T10:47:03.8698668+07:00", "Session": null, "AutoIssue": false, "CAcode": null, "VIPText": null, "Remark": null, "AccountCode": null, "VerifyMode": null, "VnaApiInfo": {"Verify": null, "Session": null, "AirOptionId": 0, "FareOptionId": 0, "FlightOptionId": 0}}, "ListTktInfo": null, "ListAirAvail": [{"Id": 2, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "SGN", "EndPoint": "SIN", "StartTime": "2024-08-22T14:20:00", "StartTimeZoneOffset": "", "EndTime": "2024-08-22T17:25:00", "EndTimeZoneOffset": "", "StartTm": "******** 14:20:00", "EndTm": "******** 17:25:00", "FlightNumber": "VN655", "Duration": 0, "Plane": "350", "StartTerminal": "2", "EndTerminal": "4", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 0, "Cabin": null, "Class": "P", "FareBasis": "P1YVNF", "HandBaggage": "1 kiện x 12kg", "AllowanceBaggage": "", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}, {"Id": 1, "Airline": "VN", "MarketingAirline": "VN", "OperatingAirline": "VN", "StartPoint": "SIN", "EndPoint": "SGN", "StartTime": "2024-08-31T18:25:00", "StartTimeZoneOffset": "", "EndTime": "2024-08-31T19:55:00", "EndTimeZoneOffset": "", "StartTm": "31082024 18:25:00", "EndTm": "31082024 19:55:00", "FlightNumber": "VN654", "Duration": 0, "Plane": "350", "StartTerminal": "4", "EndTerminal": "2", "HasStop": false, "StopPoint": null, "StopTime": 0, "DayChange": false, "StopOvernight": false, "ChangeStation": false, "ChangeAirport": false, "LastItem": true, "MarriageGrp": null, "FlightsMiles": 0, "Status": "HK", "Seat": 0, "Cabin": null, "Class": "P", "FareBasis": "P1YVNF", "HandBaggage": "1 kiện x 12kg", "AllowanceBaggage": "", "ListHiddenStop": null, "QHItinerary": null, "QHFareInfo": null}], "ListPassenger": [{"Index": 0, "NameId": "2", "ParentId": -1, "FirstName": "PHAM B", "LastName": "TRUC LINH A", "Type": "ADT", "Gender": true, "Birthday": "", "PassportNumber": "", "PassportExpiry": "", "Nationality": "", "IssueCountry": "", "Membership": null, "CustLoyalty": {"Airline": "VN", "MembershipID": null, "MembershipType": null, "Status": "HL", "Success": false}, "ListFare": [{"DocType": "TKT", "BaseFare": 1095000, "Fees": 0, "Taxes": 3838000, "VAT": 0}], "ListBaggage": [], "ListSeat": [], "ListService": [], "ListCustLoyalty": null, "NewPassenger": null}], "ListTransaction": null, "ListTicket": null, "ListSegment": [{"Airline": "VN", "StartPoint": "SGN", "EndPoint": "SIN", "StartTime": "2024-08-22T14:20:00", "EndTime": "2024-08-22T17:25:00", "FlightNumber": "VN655", "Class": "P", "Plane": "350", "Leg": 2}, {"Airline": "VN", "StartPoint": "SIN", "EndPoint": "SGN", "StartTime": "2024-08-31T18:25:00", "EndTime": "2024-08-31T19:55:00", "FlightNumber": "VN654", "Class": "P", "Plane": "350", "Leg": 1}], "Status": true, "ErrorCode": "000", "ErrorValue": "", "ErrorField": "", "Message": "", "Language": ""}