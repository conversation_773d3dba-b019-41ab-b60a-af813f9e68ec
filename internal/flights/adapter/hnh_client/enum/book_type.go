package enum

import (
	"bytes"
	"fmt"
)

type BookType uint8

const (
	BookTypeNone BookType = iota
	BookTypeNormal
	BookTypeCustom
	BookTypeDirect
)

var BookTypeName = map[BookType]string{
	BookTypeNone:   "",
	BookTypeNormal: "book-normal",
	BookTypeCustom: "book-custom",
	BookTypeDirect: "book-direct",
}

var BookTypeValue = func() map[string]BookType {
	value := map[string]BookType{}
	for k, v := range BookTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e BookType) MarshalJSON() ([]byte, error) {
	v, ok := BookTypeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *BookType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := BookTypeValue[string(data)]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*BookType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range BookTypeName {
		vals = append(vals, name)
	}

	return vals
}
