package enum

import (
	"bytes"
	"fmt"
)

type FlightType uint8

const (
	FlightTypeNone FlightType = iota
	FlightTypeDomestic
	FlightTypeInternational
)

var FlightTypeName = map[FlightType]string{
	FlightTypeNone:          "",
	FlightTypeDomestic:      "domestic",
	FlightTypeInternational: "international",
}

var FlightTypeValue = func() map[string]FlightType {
	value := map[string]FlightType{}
	for k, v := range FlightTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e FlightType) MarshalJSON() ([]byte, error) {
	v, ok := FlightTypeName[e]
	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *FlightType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := FlightTypeValue[string(data)]
	if !ok {
		return fmt.Errorf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions())
	}

	*e = v

	return nil
}

func (*FlightType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range FlightTypeName {
		vals = append(vals, name)
	}

	return vals
}
