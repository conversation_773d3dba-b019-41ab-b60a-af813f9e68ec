package constants

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

var ErrorMap = map[string]error{
	"401":  domain.ErrItinerarySoldOut,               //Không tìm thấy chuyến bay
	"304":  domain.ErrIssueTicketFailed,              //Không thể xuất vé từ hãng hàng không
	"305":  domain.ErrItinerarySoldOut,               //Không tìm thấy chuyến bay để đặt chỗ
	"309":  domain.ErrTicketFareChanged,              //Giá vé đã bị thay đổi
	"316":  domain.ErrItinerarySoldOut,               //Không tìm thấy chuyến bay nào cho hành trình này
	"0006": domain.ErrItinerarySoldOut,               //Giờ khởi hành sát ngày đặt vé, không thể giữ chỗ, vui lòng không đặt lại
	"0015": domain.ErrItinerarySoldOut,               //Chuyến bay đã bán hết vé trước khi hoàn thành đặt chỗ, vui lòng không đặt lại
	"0022": domain.ErrIssueTicketFailed,              //Đặt chỗ thành công nhưng xuất vé lỗi
	"0030": domain.ErrPaxNameExistedInAnotherBooking, //Booking trùng lặp
	"0033": domain.ErrItinerarySoldOut,               //Không tìm thấy chuyến bay và giá vé phù hợp
	"0034": domain.ErrItinerarySoldOut,               //Chuyến bay đã đóng hoặc không tồn tại
	"0035": domain.ErrItinerarySoldOut,               //Không tìm thấy chuyến bay cho hành trình đã chọn
	"0104": domain.ErrTicketIssued,                   //Booking đã được xuất vé
	"219":  domain.ErrInvalidBaggageRequest,          //	Route baggage không hợp lệ
	"220":  domain.ErrInvalidBaggageRequest,          //	Code baggage không hợp lệ
	"221":  domain.ErrInvalidBaggageRequest,          //	Currency baggage không hợp lệ
	"222":  domain.ErrInvalidBaggageRequest,          //	Name baggage không hợp lệ
	"223":  domain.ErrInvalidBaggageRequest,          //	Price baggage không hợp lệ
	"224":  domain.ErrInvalidBaggageRequest,          //	Value baggage không hợp lệ
	"319":  domain.ErrInvalidBaggageRequest,          //	Không tìm thấy danh sách hành lý
	"0012": domain.ErrBookingBaggage,                 //	Lỗi trong tiến trình chọn hành lý
	"0089": domain.ErrInvalidBaggageRequest,          //	Lỗi khi truy cập trang thêm hành lý
	"0018": domain.ErrSeatNotAvailable,               //Lỗi trong tiến trình lựa chọn ghế ngồi
	// Cheat for specific api response code
	"create_booking_300": domain.ErrBookingSeat,
	"verify_300":         domain.ErrItinerarySoldOut,
	"issue_300":          domain.ErrIssueTicketFailed,
}
