package hnh_client

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type hnhAdapter struct {
	cfg       *config.Schema
	client    client.HNHClient
	HPLClient client.HNHClient
}

type HNHAdapter interface {
	IssueTicket(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo, tracingID string, provider enum.FlightProvider) (*domain.IssueTicketServiceResponse, error)
	ConfirmFare(ctx context.Context, ities []*domain.FlightItinerary, tracingID string, provider enum.FlightProvider) (*domain.TotalFareInfo, string, error)
	CreateBooking(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, verifySession string, tracingID string, provider enum.FlightProvider) (*domain.SvcCreateBookingResponse, error)
	SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string, provider enum.FlightProvider) ([]*domain.ResponseFlight, error)
	VerifyBookingFlightData(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo, provider enum.FlightProvider) (hasChanges bool, fareData *domain.TotalFareInfo, err error)
	SearchFlightsV2(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string, provider enum.FlightProvider) ([]*domain.ResponseFlight, error)
	GetSeatMap(ctx context.Context, ities []*domain.FlightItinerary, tracingID string, provider enum.FlightProvider) ([]*domain.SeatSegment, error)
	GetBaggageOption(ctx context.Context, ities []*domain.FlightItinerary, tracingID string, provider enum.FlightProvider) ([]*domain.BaggageOption, error)
	BookingAutoIssue(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, tracingID string, provider enum.FlightProvider) (*domain.IssueTicketServiceResponse, error)
}

func NewHNHAdapter(cfg *config.Schema, requestRepository repositories.RequestRepository) HNHAdapter {
	return &hnhAdapter{
		cfg:       cfg,
		client:    client.NewHNHClient(cfg, requestRepository, enum.FlightProviderHNH),
		HPLClient: client.NewHNHClient(cfg, requestRepository, enum.FlightProviderHNH_HPL),
	}
}

func HandleError(action, code string) error {
	val, ok := constants.ErrorMap[fmt.Sprintf("%s_%s", action, code)]
	if ok {
		return errors.WithMessage(val, code)
	}

	val, ok = constants.ErrorMap[code]
	if ok {
		return errors.WithMessage(val, code)
	}

	return errors.WithMessage(commonErrors.ErrSomethingOccurred, code)
}

func (c *hnhAdapter) GetClient(provider enum.FlightProvider) (client.HNHClient, error) {
	switch provider {
	case enum.FlightProviderHNH:
		return c.client, nil
	case enum.FlightProviderHNH_HPL:
		return c.HPLClient, nil
	default:
		return nil, domain.ErrInvalidValue
	}
}

func (c *hnhAdapter) BookingAutoIssue(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, tracingID string, provider enum.FlightProvider) (*domain.IssueTicketServiceResponse, error) {
	// Check latest price
	fare, session, err := c.ConfirmFare(ctx, booking.Itineraries, tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "c.ConfirmFare")
	}

	if fare.TotalFareAmount > booking.FareDataCf.TotalFareAmount {
		return nil, domain.ErrTicketFareChanged
	}

	var clientReq *entities.BookFlightRequest

	if provider == enum.FlightProviderHNH {
		if booking.OfficeID == pkgConstants.OfficeVMB {
			clientReq, err = converts.ToHNHCreateBookingRequest(booking, pnr, session, c.cfg.VMBEmail, c.cfg.HNHAgentName, c.cfg.HNHAgentPhone, c.cfg.HNHAgentEmail)
		} else {
			clientReq, err = converts.ToHNHCreateBookingRequest(booking, pnr, session, c.cfg.BizitripEmail, c.cfg.HNHAgentNameBiziTrip, c.cfg.HNHAgentPhoneBiziTrip, c.cfg.HNHAgentEmailBiziTrip)
		}
	} else {
		clientReq, err = converts.ToHNHCreateBookingRequest(booking, pnr, session, c.cfg.HPLEmail, c.cfg.HNHHPLAgentName, c.cfg.HNHHPLAgentPhone, c.cfg.HNHHPLAgentEmail)
	}

	if err != nil {
		return nil, errors.Wrap(err, "convert.ToHNHCreateBookingRequest")
	}

	autoIssue := booking.IsVJ24h

	// Auto issue
	for i := 0; i < len(clientReq.ListFareData); i++ {
		clientReq.ListFareData[i].AutoIssue = autoIssue
	}

	client, err := c.GetClient(provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetClient")
	}

	res, err := client.BookFlight(ctx, clientReq, tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.BookFlight")
	}

	if !res.Status {
		log.Error("[CreateBooking] Res error", log.Any("Res", res))
		return nil, HandleError("create_booking", res.ErrorCode)
	}

	if len(res.ListBooking) == 0 {
		log.Error("[BookFlight] Res empty error", log.Any("Res", res))
		return nil, errors.WithMessage(commonErrors.ErrSomethingOccurred, "Res empty error")
	}

	// Map data
	booking.BookingRef = res.ListBooking[0].BookingCode
	booking.AirlineSystem = res.ListBooking[0].System

	// issue
	if !autoIssue {
		issueRes, err := client.IssueTicket(ctx, converts.ToIssueTicketRequest(booking, pnr.ListPax), tracingID, provider)
		if err != nil {
			return nil, errors.Wrap(err, "HNHClient.IssueTicket error")
		}

		if !issueRes.Status {
			log.Error("[IssueTicket] Res error", log.Any("Res", issueRes))
			return nil, HandleError("issue", issueRes.ErrorCode)
		}
	}

	// Get booking
	response, err := client.GetBooking(ctx, converts.ToRetrieveBookingRequest(booking, pnr.ListPax), tracingID, provider)
	if err != nil {
		log.Error("[GetBooking] Res error", log.Any("Res", response))
		return nil, HandleError("get_booking", response.ErrorCode)
	}

	if !response.Status {
		log.Error("[GetBooking] Res error", log.Any("Res", response))
		return nil, HandleError("get_booking", response.ErrorCode)
	}

	if len(response.ListTicket) == 0 {
		log.Error("[GetBooking] Res error", log.Any("Res", response))
		return nil, errors.WithMessage(commonErrors.ErrSomethingOccurred, "ListTicket empty")
	}

	if response.BookingStatus != "TICKETED" {
		return nil, domain.ErrIssueTicketFailed
	}

	eTickets, emdStatus, err := converts.MapETicket(response, booking.Itineraries, pnr.ListPax)
	if err != nil {
		log.Error("[BookingAutoIssue] MapETicket error", log.Any("err", err))
		return nil, err
	}

	hasChanges, fareData, err := c.VerifyBookingFlightData(ctx, booking, pnr.ListPax, provider)
	if err != nil {
		log.Error("[BookingAutoIssue] VerifyBookingFlightData error", log.Any("err", err), log.String("bookingCode", booking.BookingCode))
	} else {
		if fareData != nil && fareData.BaseTotalFareAmount != booking.FareDataCf.BaseTotalFareAmount {
			log.Error("[IMPORTANT] BaseTotalFareAmount changed after issued", log.String("bookingCode", booking.BookingCode))
		}
		if hasChanges {
			log.Error("[IMPORTANT] booking hasChanges true after issued", log.String("bookingCode", booking.BookingCode))
		}
	}

	booking.FareDataIss = fareData

	reservationInfo := []*domain.IssueTicketSvcReservationInfo{}

	for _, iti := range booking.Itineraries {
		reservationInfo = append(reservationInfo, &domain.IssueTicketSvcReservationInfo{
			ItineraryIndex:  iti.Index,
			ReservationCode: booking.BookingRef,
		})
	}

	if !converts.CheckHasAncillary(booking) {
		emdStatus = enum.EMDStatusEmpty
	}

	return &domain.IssueTicketServiceResponse{
		ReservationInfo: reservationInfo,
		Etickets:        eTickets,
		EMDStatus:       emdStatus,
	}, nil
}

func (c *hnhAdapter) VerifyBookingFlightData(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo, provider enum.FlightProvider) (hasChanges bool, fareData *domain.TotalFareInfo, err error) {
	if booking == nil {
		return false, nil, domain.ErrInvalidValue
	}

	client, err := c.GetClient(provider)
	if err != nil {
		return false, nil, errors.Wrap(err, "client.GetClient")
	}

	response, err := client.GetBooking(ctx, converts.ToRetrieveBookingRequest(booking, pnr), booking.BookingRef, provider)
	if err != nil {
		return false, nil, errors.Wrap(err, "HNHClient.GetBooking error")
	}

	if !response.Status {
		log.Error("[GetBooking] Res error", log.Any("Res", response))
		return false, nil, HandleError("get_booking", response.ErrorCode)
	}

	hasChanges, fareData, err = converts.CheckFareChange(response, booking)
	if err != nil {
		return false, fareData, errors.Wrap(err, "converts.CheckFareChange")
	}

	return hasChanges, fareData, nil
}

func (c *hnhAdapter) IssueTicket(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo, tracingID string, provider enum.FlightProvider) (*domain.IssueTicketServiceResponse, error) {
	if booking == nil {
		return nil, fmt.Errorf("IssueTicket: booking == nil")
	}

	client, err := c.GetClient(provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetClient")
	}

	issueRes, err := client.IssueTicket(ctx, converts.ToIssueTicketRequest(booking, pnr), tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "HNHClient.IssueTicket error")
	}

	if !issueRes.Status {
		log.Error("[IssueTicket] Res error", log.Any("Res", issueRes))
		return nil, HandleError("issue", issueRes.ErrorCode)
	}

	response, err := client.GetBooking(ctx, converts.ToRetrieveBookingRequest(booking, pnr), tracingID, provider)
	if err != nil {
		log.Error("[GetBooking] Res error", log.Any("Res", response))
		return nil, HandleError("get_booking", response.ErrorCode)
	}

	if !response.Status {
		log.Error("[GetBooking] Res error", log.Any("Res", response))
		return nil, HandleError("get_booking", response.ErrorCode)
	}

	if len(response.ListTicket) == 0 {
		log.Error("[GetBooking] Res error", log.Any("Res", response))
		return nil, errors.WithMessage(commonErrors.ErrSomethingOccurred, "ListTicket empty")
	}

	if response.BookingStatus != "TICKETED" {
		return nil, domain.ErrIssueTicketFailed
	}

	eTickets, emdStatus, err := converts.MapETicket(response, booking.Itineraries, pnr)
	if err != nil {
		log.Error("[GetBooking] MapETicket error", log.Any("err", err))
		return nil, err
	}

	reservationInfo := []*domain.IssueTicketSvcReservationInfo{}

	for _, iti := range booking.Itineraries {
		reservationInfo = append(reservationInfo, &domain.IssueTicketSvcReservationInfo{
			ItineraryIndex:  iti.Index,
			ReservationCode: booking.BookingRef,
		})
	}

	if !converts.CheckHasAncillary(booking) {
		emdStatus = enum.EMDStatusEmpty
	}

	return &domain.IssueTicketServiceResponse{
		ReservationInfo: reservationInfo,
		Etickets:        eTickets,
		EMDStatus:       emdStatus,
		// Pending:         response.BookingStatus != "TICKETED",
	}, nil
}

func (a *hnhAdapter) ConfirmFare(ctx context.Context, ities []*domain.FlightItinerary, tracingID string, provider enum.FlightProvider) (*domain.TotalFareInfo, string, error) {
	session := ""

	listFareData, err := converts.ToHNHVerifyFlightRequest(ities)
	if err != nil {
		return nil, session, errors.Wrap(err, "convert.ToHNHVerifyFlightRequest")
	}

	clientReq := &entities.VerifyFlightRequest{
		ListFareData: listFareData,
		Language:     "vi",
		Currency:     "VND",
	}

	client, err := a.GetClient(provider)
	if err != nil {
		return nil, session, errors.Wrap(err, "client.GetClient")
	}

	res, err := client.VerifyFlight(ctx, clientReq, tracingID, provider)
	if err != nil {
		return nil, session, errors.Wrap(err, "client.VerifyFlight")
	}

	if len(res.ListFareStatus) == 0 {
		if res.Message == "Not found session data" {
			return nil, session, domain.ErrItinerarySoldOut
		}

		log.Error("[VerifyFlight] Res empty error", log.Any("Res", res))
		return nil, session, errors.WithMessage(commonErrors.ErrSomethingOccurred, "Res empty error")
	}

	isChangePrice := false

	for _, item := range res.ListFareStatus {
		if item.Difference != 0 {
			isChangePrice = true
			break
		}
	}

	if !res.Status && !isChangePrice {
		log.Error("[VerifyFlight] Res error", log.Any("Res", res))
		return nil, session, HandleError("verify", res.ErrorCode)
	}

	fare := &domain.TotalFareInfo{}

	for _, fareStatus := range res.ListFareStatus {
		if fareStatus.Difference != 0 {
			for _, item := range fareStatus.FareData.ListFlight {
				session += fmt.Sprintf("%s|", converts.BuildBookingKey(fareStatus.Session, fareStatus.FareData.FareDataID, item.FlightValue))
			}
		}

		fareData, err := converts.ToDomainTotalFareInfo(&fareStatus.FareData)
		if err != nil {
			return nil, session, errors.Wrap(err, "[VerifyFlight] converts.ToDomainTotalFareInfo")
		}

		fare.Currency = fareData.Currency
		fare.BaseTotalFareAmount += fareData.BaseTotalFareAmount
		fare.TotalFareAmount += fareData.TotalFareAmount
		fare.TotalFareBasic += fareData.TotalFareBasic
		fare.TotalTaxAmount += fareData.TotalTaxAmount
		fare.TotalSeatAmount += fareData.TotalSeatAmount
		fare.TotalBaggageAmount += fareData.TotalBaggageAmount

		if len(fare.TotalPaxFares) > 0 {
			for _, fareInfo := range fare.TotalPaxFares {
				for _, fareInfoTwo := range fareData.TotalPaxFares {
					if fareInfo.PaxType == fareInfoTwo.PaxType {
						fareInfo.FareAmount += fareInfoTwo.FareAmount
						fareInfo.FareBasic += fareInfoTwo.FareBasic
						fareInfo.TaxAmount += fareInfoTwo.TaxAmount
					}
				}
			}
		} else {
			fare.TotalPaxFares = fareData.TotalPaxFares
		}

	}

	if len(session) > 0 {
		session = session[:len(session)-1]
	}

	return fare, session, nil
}

func (a *hnhAdapter) CreateBooking(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, verifySession string, tracingID string, provider enum.FlightProvider) (*domain.SvcCreateBookingResponse, error) {
	var (
		clientReq *entities.BookFlightRequest
		err       error
	)

	if provider == enum.FlightProviderHNH {
		if booking.OfficeID == pkgConstants.OfficeVMB {
			clientReq, err = converts.ToHNHCreateBookingRequest(booking, pnr, verifySession, a.cfg.VMBEmail, a.cfg.HNHAgentName, a.cfg.HNHAgentPhone, a.cfg.HNHAgentEmail)
		} else {
			clientReq, err = converts.ToHNHCreateBookingRequest(booking, pnr, verifySession, a.cfg.BizitripEmail, a.cfg.HNHAgentNameBiziTrip, a.cfg.HNHAgentPhoneBiziTrip, a.cfg.HNHAgentEmailBiziTrip)
		}
	} else {
		clientReq, err = converts.ToHNHCreateBookingRequest(booking, pnr, verifySession, a.cfg.HPLEmail, a.cfg.HNHHPLAgentName, a.cfg.HNHHPLAgentPhone, a.cfg.HNHHPLAgentEmail)
	}

	if err != nil {
		return nil, errors.Wrap(err, "convert.ToHNHCreateBookingRequest")
	}

	client, err := a.GetClient(provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetClient")
	}

	res, err := client.BookFlight(ctx, clientReq, tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.BookFlight")
	}

	if !res.Status {
		log.Error("[CreateBooking] Res error", log.Any("Res", res))
		return nil, HandleError("create_booking", res.ErrorCode)
	}

	if len(res.ListBooking) == 0 {
		log.Error("[BookFlight] Res empty error", log.Any("Res", res))
		return nil, errors.WithMessage(commonErrors.ErrSomethingOccurred, "Res empty error")
	}

	var totalPrice float64

	bookingExpiredAt := res.ListBooking[0].ExpiryDate.Time.UnixMilli()
	for _, v := range res.ListBooking {
		totalPrice += v.Price
		if bookingExpiredAt > v.ExpiryDate.ToUnixMilli(7) {
			bookingExpiredAt = v.ExpiryDate.ToUnixMilli(7)
		}

		if bookingExpiredAt > v.TimePurchase.ToUnixMilli(7) {
			bookingExpiredAt = v.TimePurchase.ToUnixMilli(7)
		}
	}

	// if totalPrice > booking.FareDataCf.TotalFareAmount {
	// 	return nil, domain.ErrTicketFareChanged
	// }

	return &domain.SvcCreateBookingResponse{
		LastTicketingDate: bookingExpiredAt,
		BookingRef:        res.ListBooking[0].BookingCode,
		AirlineSystem:     res.ListBooking[0].System,
	}, nil
}

func (a *hnhAdapter) SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string, provider enum.FlightProvider) ([]*domain.ResponseFlight, error) {
	if len(req.Itineraries) > 2 {
		return []*domain.ResponseFlight{}, nil
	}

	clientReq := converts.FromDomainSearchFlightsRequest(req)

	client, err := a.GetClient(provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetClient")
	}

	res, err := client.SearchFlight(ctx, clientReq, tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.SearchFlight")
	}

	out, err := converts.ToDomainResponseFlights(res, req, a.cfg.HNHEnableVJ, provider)
	if err != nil {
		return nil, errors.Wrap(err, "ToDomainResponseFlights")
	}

	return out, nil
}

func (a *hnhAdapter) SearchFlightsV2(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string, provider enum.FlightProvider) ([]*domain.ResponseFlight, error) {
	clientReq := converts.FromDomainSearchFlightsRequest(req)

	client, err := a.GetClient(provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetClient")
	}

	res, err := client.SearchFlight(ctx, clientReq, tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.SearchFlight")
	}
	out, err := converts.ToDomainResponseFlightsV2(res, req, a.cfg.HNHEnableVJ, provider)
	if err != nil {

		return nil, errors.Wrap(err, "ToDomainResponseFlights")
	}

	return out, nil
}

func (a *hnhAdapter) GetSeatMap(ctx context.Context, ities []*domain.FlightItinerary, tracingID string, provider enum.FlightProvider) ([]*domain.SeatSegment, error) {
	listFareData, err := converts.ToHNHVerifyFlightRequest(ities)
	if err != nil {
		return nil, errors.Wrap(err, "[GetSeatMap] convert.ToHNHVerifyFlightRequest")
	}

	clientReq := &entities.GetSeatMapRequest{
		ListFareData: listFareData,
		Language:     "vi",
		Currency:     "VND",
	}

	client, err := a.GetClient(provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetClient")
	}

	res, err := client.GetSeatMap(ctx, clientReq, tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetSeatMap")
	}

	seatSegs, err := converts.ToDomainSeatSegment(res, ities)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainSeatSegment")
	}

	return seatSegs, nil
}

func (a *hnhAdapter) GetBaggageOption(ctx context.Context, ities []*domain.FlightItinerary, tracingID string, provider enum.FlightProvider) ([]*domain.BaggageOption, error) {
	listFareData, err := converts.ToHNHVerifyFlightRequest(ities)
	if err != nil {
		return nil, errors.Wrap(err, "[GetSeatMap] convert.ToHNHVerifyFlightRequest")
	}

	clientReq := &entities.GetBaggageRequest{
		ListFareData: listFareData,
		Language:     "vi",
		Currency:     "VND",
	}

	client, err := a.GetClient(provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetClient")
	}

	res, err := client.GetBaggage(ctx, clientReq, tracingID, provider)
	if err != nil {
		return nil, errors.Wrap(err, "client.GetBaggage")
	}

	baggages, err := converts.ToDomainBaggageOptions(res, ities)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainBaggageOptions")
	}

	return baggages, nil
}
