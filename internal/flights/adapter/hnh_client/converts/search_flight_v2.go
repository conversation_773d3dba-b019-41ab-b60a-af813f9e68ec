package converts

import (
	"github.com/pkg/errors"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func filterFlight(fare *entities.FareData, isEnableVJ bool) bool {
	if isEnableVJ {
		return fare.System == "1G"
	}

	for _, iti := range fare.ListFlight {
		if iti.Airline == "VJ" {
			return true
		}
	}

	return fare.Airline == "VJ" || fare.System == "VJ" || fare.System == "1G"
}

func ToDomainResponseFlightsV2(in *entities.SearchFlightResponse, searchReq *domain.SearchFlightsRequest, isEnableVJ bool, provider enum.FlightProvider) ([]*domain.ResponseFlight, error) {
	if in == nil || len(in.ListFareData) == 0 {
		return nil, errors.Errorf("empty input ToDomainResponseFlights")
	}

	fares := in.ListFareData

	out := []*domain.ResponseFlight{}

	i := 1
	groupIDSec := map[string]int{}

	for _, fare := range fares {
		if fare.Itinerary != 1 || filterFlight(fare, isEnableVJ) {
			continue
		}

		if groupIDSec[fare.Airline] == 0 {
			groupIDSec[fare.Airline] = i
			i++
		}

		flight, err := toDomainResponseFlight(fare, in.SessionID, searchReq, enum.FlightOptionSingleTrip, groupIDSec[fare.Airline], provider)
		if err != nil {
			return nil, errors.Wrap(err, "toDomainResponseFlight")
		}

		out = append(out, flight)
	}

	return out, nil
}
