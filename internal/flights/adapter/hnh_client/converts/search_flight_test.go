package converts

import "testing"

func TestSanitizeBaggageRawText(t *testing.T) {
	type FuncOutput struct {
		Arg1 int64
		Arg2 string
		Arg3 error
	}

	inputOutputMap := map[string]FuncOutput{
		"7kg": {
			Arg1: 1,
			Arg2: "7kg",
			Arg3: nil,
		},
		"1 kiện x 23kg": {
			Arg1: 1,
			Arg2: "23kg",
			Arg3: nil,
		},
		"0kg": {
			Arg1: 0,
			Arg2: "",
			Arg3: nil,
		},
		"": {
			Arg1: 0,
			Arg2: "",
			Arg3: nil,
		},
	}

	for input, output := range inputOutputMap {
		t.Run(input, func(t *testing.T) {
			quantity, info, err := sanitizeBaggageRawText(input)
			if output.Arg1 != quantity || output.Arg2 != info || output.Arg3 != err {
				t.<PERSON><PERSON><PERSON>("input %s expect %#v has [%v] [%v] [%v]", input, output, quantity, info, err)
			}
			t.Logf("input %s has %d %s", input, quantity, info)
		})
	}
}
