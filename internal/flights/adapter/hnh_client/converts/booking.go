package converts

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	hnhEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

const HNHDDMMYYYFormat = "02012006"

// Format: sessionID,FareDataId,FlightValue
// 		   string,number,string

func getFareDataInfoFromProviderBookingKey(input string) (string, int32, string, error) {
	parts := strings.Split(input, ",")
	if len(parts) != 3 {
		return "", 0, "", errors.New("invalid parts length")
	}

	rawSessionID, rawFareDataID, rawFightValue := parts[0], parts[1], parts[2]

	fareDataID, err := strconv.ParseInt(rawFareDataID, 0, 32)
	if err != nil {
		return "", 0, "", errors.Wrap(err, "strconv.ParseInt of "+rawFareDataID)
	}

	return rawSessionID, int32(fareDataID), rawFightValue, nil
}

type FareDataMapping struct {
	Index int
	Data  *entities.FareDataInfo
}

func ToHNHVerifyFlightRequest(ities []*domain.FlightItinerary) ([]*entities.FareDataInfo, error) {
	out := []*entities.FareDataInfo{}

	mapping := map[string]*FareDataMapping{}

	for idx, iti := range ities {
		session, fareID, fareValue, err := getFareDataInfoFromProviderBookingKey(iti.ProviderBookingKey)
		if err != nil {
			return nil, errors.Wrap(err, "getFareDataInfoFromProviderBookingKey of "+iti.ProviderBookingKey)
		}

		key := fmt.Sprintf("%s%d", session, fareID)
		if mapping[key] == nil {
			mapping[key] = &FareDataMapping{
				Index: idx,
				Data: &entities.FareDataInfo{
					Session:    session,
					FareDataID: fareID,
					ListFlight: []*entities.FlightInfo{},
				},
			}
		}

		mapping[key].Data.ListFlight = append(mapping[key].Data.ListFlight, &entities.FlightInfo{
			FlightValue: fareValue,
		})
	}

	arr := make([]*FareDataMapping, 0, len(mapping))
	for _, value := range mapping {
		arr = append(arr, value)
	}

	sort.Slice(arr, func(i, j int) bool {
		return arr[i].Index < arr[j].Index
	})

	for _, value := range arr {
		out = append(out, value.Data)
	}

	return out, nil
}

func ToDomainTotalFareInfo(in *entities.FareData) (*domain.TotalFareInfo, error) {
	totalPaxFares := []*domain.ItineraryPaxFare{}
	paxFareInfos := []*domain.PaxFareInfo{}

	if in.Adt > 0 {
		paxFareAdt := &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeAdult,
			FareAmount: in.FareAdt + in.TaxAdt + in.FeeAdt + in.ServiceFeeAdt,
			FareBasic:  in.FareAdt,
			TaxAmount:  in.TaxAdt + in.FeeAdt + in.ServiceFeeAdt,
			Currency:   in.Currency,
		}

		for i := 0; i < int(in.Adt); i++ {
			paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
				PaxID:              i,
				PaxType:            enum.PaxTypeAdult,
				PaxFares:           []*domain.PaxFareReport{}, // Skip
				Seat:               []*domain.PassengerSeatInfo{},
				Baggages:           []*domain.PassengerBaggageInfo{},
				TotalSeatAmount:    0,
				TotalBaggageAmount: 0,
				TotalPrice:         paxFareAdt.FareAmount,
				Currency:           in.Currency,
			})
		}

		totalPaxFares = append(totalPaxFares, paxFareAdt)
	}

	if in.Chd > 0 {
		paxFareChd := &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeChildren,
			FareAmount: in.FareChd + in.TaxChd + in.FeeChd + in.ServiceFeeChd,
			FareBasic:  in.FareChd,
			TaxAmount:  in.TaxChd + in.FeeChd + in.ServiceFeeChd,
			Currency:   in.Currency,
		}

		for i := 0; i < int(in.Chd); i++ {
			paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
				PaxID:              int(in.Adt) + i,
				PaxType:            enum.PaxTypeChildren,
				PaxFares:           []*domain.PaxFareReport{}, // Skip
				Seat:               []*domain.PassengerSeatInfo{},
				Baggages:           []*domain.PassengerBaggageInfo{},
				TotalSeatAmount:    0,
				TotalBaggageAmount: 0,
				TotalPrice:         paxFareChd.FareAmount,
				Currency:           in.Currency,
			})
		}

		totalPaxFares = append(totalPaxFares, paxFareChd)
	}

	if in.Inf > 0 {
		paxFareInf := &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeInfant,
			FareAmount: in.FareInf + in.TaxInf + in.FeeInf,
			FareBasic:  in.FareInf,
			TaxAmount:  in.TaxInf + in.FeeInf + in.ServiceFeeInf,
			Currency:   in.Currency,
		}

		for i := 0; i < int(in.Inf); i++ {
			paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
				PaxID:              int(in.Adt) + int(in.Chd) + i,
				PaxType:            enum.PaxTypeInfant,
				PaxFares:           []*domain.PaxFareReport{}, // Skip
				Seat:               []*domain.PassengerSeatInfo{},
				Baggages:           []*domain.PassengerBaggageInfo{},
				TotalSeatAmount:    0,
				TotalBaggageAmount: 0,
				TotalPrice:         paxFareInf.FareAmount,
				Currency:           in.Currency,
			})
		}

		totalPaxFares = append(totalPaxFares, paxFareInf)
	}

	fareBasic := in.FareAdt*float64(in.Adt) + in.FareChd*float64(in.Chd) + in.FareInf*float64(in.Inf)
	return &domain.TotalFareInfo{
		TotalPaxFares:       totalPaxFares,
		BaseTotalFareAmount: in.TotalPrice,
		TotalFareAmount:     in.TotalPrice,
		TotalFareBasic:      fareBasic,
		TotalTaxAmount:      in.TotalPrice - fareBasic,
		TotalSeatAmount:     0,
		TotalBaggageAmount:  0,
		Currency:            in.Currency,
		PaxFareInfos:        paxFareInfos,
	}, nil
}

func genderTypeToClientGender(gender commonEnum.GenderType) bool {
	return gender == commonEnum.GenderTypeMale
}

func ToPassengerOfBookFlight(v *domain.PaxInfo, itineraries []*domain.FlightItinerary, parentID int32) *entities.PassengerOfBookFlightReq {
	if v == nil || len(itineraries) == 0 {
		return nil
	}

	var exDate *string
	var passportNumber *string

	if v.Passport != nil {
		exDate = lo.ToPtr(time.UnixMilli(v.Passport.ExpiryDate).Format(HNHDDMMYYYFormat))
		passportNumber = lo.ToPtr(v.Passport.Number)
	}

	paxParentID := parentID
	if v.Type != enum.PaxTypeInfant {
		paxParentID = int32(v.ID)
	}

	seats := make([]*entities.SeatInfo, 0, len(v.Seats))
	baggage := make([]*entities.Baggage, 0, len(v.Baggages))
	for index, iti := range itineraries {
		var paxFare *domain.ItineraryPax
		for _, pax := range iti.PaxInfo {
			if paxFare != nil {
				continue
			}
			if pax.PaxID == v.ID {
				paxFare = pax
			}
		}

		if paxFare == nil {
			continue
		}

		var segment *domain.ItinerarySegment
		for _, seg := range iti.Segments {
			for _, seat := range paxFare.Seats {
				if segment != nil {
					continue
				}

				if seg.Index == seat.SegmentIndex {
					segment = seg
				}
			}
		}

		for _, seatReq := range paxFare.Seats {
			seatCode := fmt.Sprintf("%s%s", seatReq.RowNumber, seatReq.SeatFacility.SeatCode)
			seats = append(seats, &entities.SeatInfo{
				Airline:    iti.CarrierMarketing,
				Value:      seatCode,
				Code:       seatCode,
				Price:      seatReq.SeatFacility.SeatCharge.TotalAmount,
				Currency:   seatReq.SeatFacility.SeatCharge.Currency,
				Leg:        int32(index),
				Route:      fmt.Sprintf("%s%s", segment.DepartPlace, segment.ArrivalPlace),
				StartPoint: segment.DepartPlace,
				EndPoint:   segment.ArrivalPlace,
			})
		}

		for _, baggageReq := range paxFare.Baggages {
			if baggageReq == nil || baggageReq.BaggageInfo == nil || baggageReq.BaggageInfo.OfferData == nil || baggageReq.BaggageInfo.OfferData.HNHOfferData == nil {
				return nil
			}

			offerData := baggageReq.BaggageInfo.OfferData.HNHOfferData
			baggage = append(baggage, &entities.Baggage{
				Airline:    offerData.Airline,
				Value:      offerData.Value,
				Code:       offerData.Code,
				Currency:   offerData.Currency,
				Route:      offerData.Route,
				StartPoint: offerData.StartPoint,
				EndPoint:   offerData.EndPoint,
				Leg:        offerData.Leg,
				Price:      offerData.Price,
			})
		}
	}

	return &entities.PassengerOfBookFlightReq{
		Index:                  uint32(v.ID),
		ParentID:               paxParentID,
		FirstName:              v.Surname,
		LastName:               v.GivenName,
		Type:                   hnhEnum.PassengerTypeValue[string(v.Type)],
		Gender:                 genderTypeToClientGender(v.Gender),
		Birthday:               parseTimeHNHDDMMYYY(v.DOB),
		Nationality:            lo.ToPtr(v.Nationality),
		PassportNumber:         passportNumber,
		PassportExpirationDate: exDate,
		ListSeat:               seats,
		ListBaggage:            baggage,
		// Membership:             lo.ToPtr(""),
		// Wheelchair:             lo.ToPtr(false),
		// Vegetarian:             lo.ToPtr(false),
	}
}

func parseTimeHNHDDMMYYY(v *int64) string {
	if v == nil {
		return ""
	}

	return time.UnixMilli(*v).Format(HNHDDMMYYYFormat)
}

func ToPassengerOfBookFlightReq(items []*domain.PaxInfo, booking *domain.BookingSession) []*entities.PassengerOfBookFlightReq {
	if len(items) == 0 || booking == nil {
		return nil
	}

	res := make([]*entities.PassengerOfBookFlightReq, 0, len(items))

	parentID := int32(0)
	for _, v := range items {
		b := ToPassengerOfBookFlight(v, booking.Itineraries, parentID)
		if v.Type == enum.PaxTypeInfant {
			parentID++
		}
		res = append(res, b)

	}
	return res
}

func FromDomainBookingFlightFareData(booking *domain.BookingSession, bookingKey string) ([]*entities.FareDataInfoOfBookFlight, error) {
	fareInfo, err := ToHNHVerifyFlightRequest(booking.Itineraries)
	if err != nil {
		return nil, err
	}

	fareData := make([]*entities.FareDataInfoOfBookFlight, 0, len(fareInfo))

	if bookingKey != "" {
		fareKeys := strings.Split(bookingKey, "|")
		fakeFlightItineraries := make([]*domain.FlightItinerary, 0, len(fareKeys))
		for _, fareKey := range fareKeys {
			fakeFlightItineraries = append(fakeFlightItineraries, &domain.FlightItinerary{
				ProviderBookingKey: fareKey,
			})
		}
		fareInfo, err = ToHNHVerifyFlightRequest(fakeFlightItineraries)
		if err != nil {
			return nil, err
		}
	}

	for _, item := range fareInfo {
		session := item.Session
		listFlight := []*entities.FareFlight{}

		for _, flight := range item.ListFlight {
			listFlight = append(listFlight, &entities.FareFlight{
				FlightValue: flight.FlightValue,
			})
		}

		fareData = append(fareData, &entities.FareDataInfoOfBookFlight{
			Session:    session,
			FareDataID: item.FareDataID,
			AutoIssue:  false,
			CACode:     "",
			VIPText:    "",
			ListFlight: listFlight,
		})
	}

	return fareData, nil
}

func ToHNHCreateBookingRequest(booking *domain.BookingSession, pnr *domain.PNR, bookingKey string, contactEmail, agentName, agentPhone, agentEmail string) (*entities.BookFlightRequest, error) {
	if booking == nil || pnr == nil {
		return nil, errors.WithMessage(commonError.ErrInvalidInput, "booking or pnr nil")
	}

	listFareData, err := FromDomainBookingFlightFareData(booking, bookingKey)
	if err != nil {
		return nil, errors.Wrap(err, "FromDomainBookingFlightFareData error")
	}

	return &entities.BookFlightRequest{
		Contact: &entities.Contact{
			Gender:     pnr.ContactInfo.Gender == commonEnum.GenderTypeMale,
			FirstName:  pnr.ContactInfo.Surname,
			LastName:   pnr.ContactInfo.GivenName,
			Area:       fmt.Sprintf("+%s", pnr.ContactInfo.PhoneCode),
			Phone:      pnr.ContactInfo.Phone,
			Email:      contactEmail,
			Address:    "",
			AgentName:  agentName,
			AgentPhone: agentPhone,
			AgentEmail: agentEmail,
		},
		ListFareData:    listFareData,
		ListPassenger:   ToPassengerOfBookFlightReq(pnr.ListPax, booking),
		BookType:        hnhEnum.BookTypeNormal,
		AccountCode:     "",
		Remark:          "",
		Language:        "vi",
		Currency:        "VND",
		UseAgentContact: true,
	}, nil
}
