package converts

import (
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

func ToDomainSeatSegment(in *entities.GetSeatMapResponse, itineraries []*domain.FlightItinerary) ([]*domain.SeatSegment, error) {
	if in == nil {
		return nil, errors.WithMessage(domain.ErrInvalidValue, "[HNHAdapter] ToDomainSeatSegment")
	}

	result := []*domain.SeatSegment{}

	for _, iti := range itineraries {
		for _, segment := range iti.Segments {
			for _, seatInfo := range in.ListSeatMap {
				if seatInfo.StartPoint == segment.DepartPlace &&
					seatInfo.EndPoint == segment.ArrivalPlace {
					options := []*domain.SeatOption{}
					for _, cabin := range seatInfo.ListCabin {
						rows := []*domain.SeatRow{}
						cols := []*domain.ColumnHeader{}

						for _, row := range cabin.ListRow {
							facilities := []*domain.SeatFacility{}
							for idx, seat := range row.ListSeat {
								seatStatus := enum.SeatStatusAvailable
								if !seat.Enabled {
									seatStatus = enum.SeatStatusOccupied
								}

								properties := &domain.SeatProperty{}

								for _, facility := range seat.Facilities {
									switch facility {
									case "Aisle seat":
										properties.Aisle = true
									case "Window seat":
										properties.Window = true
									case "Exit row seat":
										properties.EmergencyExit = true
									case "Seat with bassinet facility":
										properties.Bassinet = true
										seatStatus = enum.SeatStatusBlocked
									}
								}

								if idx > 0 {
									if seat.ColumnCode == "" && seat.SeatType == "aisle" {
										facilities[idx-1].Property.Aisle = true
									}

									if facilities[idx-1].SeatCode == "" && facilities[idx-1].Type == enum.SeatTypeAisle {
										properties.Aisle = true
									}
								}

								facilities = append(facilities, &domain.SeatFacility{
									Type:         enum.SeatType(seat.SeatType),
									SeatCode:     seat.ColumnCode,
									Availability: seatStatus,
									SeatCharge: &domain.SeatCharge{
										BaseAmount:  seat.Fee,
										TaxAmount:   seat.Tax,
										TotalAmount: seat.Price,
										Currency:    seat.Currency,
									},
									Property: properties,
								})
							}
							rows = append(rows, &domain.SeatRow{
								RowNumber:  row.RowNumber,
								Facilities: facilities,
							})
						}

						for _, col := range cabin.ListColumn {
							isNextToAisle := false
							for _, facility := range col.Characteristics {
								if facility == "Aisle seat" {
									isNextToAisle = true
								}

							}

							cols = append(cols, &domain.ColumnHeader{
								Title:         col.ColumnCode,
								IsAisle:       col.ColumnType == "aisle",
								IsNextToAisle: isNextToAisle,
							})
						}

						options = append(options, &domain.SeatOption{
							CabinClass: cabin.CabinClass,
							Rows:       rows,
							Columns:    cols,
						})
					}

					result = append(result, &domain.SeatSegment{
						Key:          segment.GenerateSegmentID(),
						ItineraryID:  iti.ID,
						SegmentIndex: segment.Index,
						SeatOptions:  options,
						ExpiredAt:    time.Now().Add(constants.SeatMapExpireTime).UnixMilli(),
					})
				}
			}
		}

	}

	return result, nil
}
