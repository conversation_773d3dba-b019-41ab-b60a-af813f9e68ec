package converts

import (
	"strconv"
	"strings"
	"unicode"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func ToDomainBaggageOptions(in *entities.GetBaggageResponse, itineraries []*domain.FlightItinerary) ([]*domain.BaggageOption, error) {
	if in == nil {
		return nil, errors.WithMessage(domain.ErrInvalidValue, "[HNHAdapter] ToDomainSeatSegment")
	}

	result := []*domain.BaggageOption{}

	for _, iti := range itineraries {
		for _, segment := range iti.Segments {
			for _, baggageInfo := range in.ListBaggage {
				if baggageInfo.StartPoint == segment.DepartPlace &&
					baggageInfo.EndPoint == segment.ArrivalPlace {

					baggageInfoDetail := []*domain.BaggageOptionDetail{}
					totalWeight := 0
					unit := "kg"
					var weight int
					var err error
					var dimension string

					switch baggageInfo.Airline {
					case "VN":
						var nums []string
						var charIdx []int

						var currentNum string

						for idx, r := range baggageInfo.Name {
							if unicode.IsDigit(r) {
								currentNum += string(r)
							} else if currentNum != "" {
								nums = append(nums, currentNum)
								charIdx = append(charIdx, idx)
								currentNum = ""
							}

						}

						if currentNum != "" {
							nums = append(nums, currentNum)
						}

						if len(nums) == 2 && len(charIdx) == 2 {
							weight, err = strconv.Atoi(nums[0])
							if err != nil {
								log.Error("[ToDomainBaggageOptions] strconv.ParseInt", log.Any("err", err))
								weight = 0
							}
							unit = baggageInfo.Name[charIdx[0] : charIdx[1]-len(nums[0])-1]
							dimension = nums[1] + baggageInfo.Name[charIdx[1]:]
						}
					case "QH":
					case "VJ":
						if baggageInfo.Value == "CABINEXTRA2" {
							continue
						}

						if isContain := strings.Contains(baggageInfo.Value, "OVER"); isContain {
							baggageInfo.Value = strings.Replace(baggageInfo.Value, "OVER", "", -1)
							dimension = "OVER"
						}

						weight, err = strconv.Atoi(baggageInfo.Value)
						if err != nil {
							log.Error("[ToDomainBaggageOptions] strconv.ParseInt", log.Any("err", err))
							weight = 0
						}
					case "VU":
						if baggageInfo.Value == "BGCO" || baggageInfo.Value == "BPUG" || baggageInfo.Value == "PGOF" {
							continue
						}

						if len(baggageInfo.Value) > 2 && baggageInfo.Value[:2] == "BG" {
							weight, err = strconv.Atoi(baggageInfo.Value[2:])
							if err != nil {
								log.Error("[ToDomainBaggageOptions] strconv.ParseInt", log.Any("err", err))
								weight = 0
							}
							unit = "kg"
						} else {
							// Case name
							var nums []string
							var charIdx []int

							var currentNum string

							for idx, r := range baggageInfo.Name {
								if unicode.IsDigit(r) {
									currentNum += string(r)
								} else if currentNum != "" {
									nums = append(nums, currentNum)
									charIdx = append(charIdx, idx)
									currentNum = ""
								}

							}

							if currentNum != "" {
								nums = append(nums, currentNum)
							}

							if len(nums) == 2 && len(charIdx) == 2 {
								weight, err = strconv.Atoi(nums[0])
								if err != nil {
									log.Error("[ToDomainBaggageOptions] strconv.ParseInt", log.Any("err", err))
									weight = 0
								}
								unit = baggageInfo.Name[charIdx[0] : charIdx[1]-len(nums[0])-1]
							}
						}
					}

					baggageInfoDetail = append(baggageInfoDetail, &domain.BaggageOptionDetail{
						Type:      enum.BaggageTypeWeight,
						SubCode:   baggageInfo.Code,
						Quantity:  1,
						Weight:    int(weight),
						MaxWeight: int(weight),
						Unit:      unit,
						Dimension: dimension,
					})

					result = append(result, &domain.BaggageOption{
						OptionID: uuid.NewString(),
						OfferData: &domain.OfferData{
							HNHOfferData: &domain.HNHOfferData{
								Airline:    baggageInfo.Airline,
								Value:      baggageInfo.Value,
								Code:       baggageInfo.Code,
								Currency:   baggageInfo.Currency,
								Route:      baggageInfo.Route,
								StartPoint: baggageInfo.StartPoint,
								EndPoint:   baggageInfo.EndPoint,
								Leg:        baggageInfo.Leg,
								Price:      baggageInfo.Price,
							},
						},
						ItineraryIndex: iti.Index,
						SegmentIndex:   []int{segment.Index},
						BaggageInfo:    baggageInfoDetail,
						TotalWeight:    totalWeight,
						Unit:           unit,
						TotalBaggageCharge: &domain.BaggageCharge{
							BaseAmount:  baggageInfo.Price,
							TaxAmount:   0,
							TotalAmount: baggageInfo.Price,
							Currency:    baggageInfo.Currency,
						},
					})
				}
			}
		}
	}

	return result, nil
}
