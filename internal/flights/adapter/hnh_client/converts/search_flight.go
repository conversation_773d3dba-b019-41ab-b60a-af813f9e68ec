package converts

import (
	"fmt"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	clientConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func FromDomainSearchFlightsRequest(in *domain.SearchFlightsRequest) *entities.SearchFlightRequest {
	out := &entities.SearchFlightRequest{
		Language: "vi",
		Currency: "VND",
		Adt:      int32(in.Passengers.ADT),
		Chd:      int32(in.Passengers.CHD),
		Inf:      int32(in.Passengers.INF),
	}

	for _, iti := range in.Itineraries {
		out.ListFlight = append(out.ListFlight, &entities.FlightRequest{
			StartPoint: iti.DepartPlace,
			EndPoint:   iti.ArrivalPlace,
			DepartDate: time.UnixMilli(iti.DepartDate).Format(clientConstants.DateFormatDDMMYYY),
			// Airline:    "VN",
		})
	}

	return out
}

func ToDomainResponseFlights(in *entities.SearchFlightResponse, searchReq *domain.SearchFlightsRequest, isEnableVJ bool, provider enum.FlightProvider) ([]*domain.ResponseFlight, error) {
	if in == nil || len(in.ListFareData) == 0 {
		return nil, errors.Errorf("empty input ToDomainResponseFlights")
	}

	fares := in.ListFareData

	out := []*domain.ResponseFlight{}

	reqItiLen := len(searchReq.Itineraries)

	outwards, returns := []*entities.FareData{}, []*entities.FareData{}

	for _, fare := range fares {
		if filterFlight(fare, isEnableVJ) {
			continue
		}

		if len(fare.ListFlight) == reqItiLen {
			flight, err := toDomainResponseFlight(fare, in.SessionID, searchReq, enum.FlightOptionTypeRecommend, 0, provider)
			if err != nil {
				return nil, errors.Wrap(err, "toDomainResponseFlight")
			}

			out = append(out, flight)
			continue
		}

		if len(fare.ListFlight) != 1 {
			continue
		}

		if fare.Leg == 0 {
			outwards = append(outwards, fare)
		}
		if fare.Leg == 1 {
			returns = append(returns, fare)
		}
	}

	if len(searchReq.Itineraries) == 1 || len(outwards) == 0 || len(returns) == 0 {
		return out, nil
	}

	outwardIties, returnIties := []*domain.FlightItinerary{}, []*domain.FlightItinerary{}

	seenItiID := map[string]bool{}
	fareByItiD := map[string]*domain.SearchTotalFareInfo{}
	fareRecordByItiKey := map[string][]float64{}

	// Chuan bi data
	for _, outward := range outwards {
		outwardIti, err := toDomainFlightItinerary(outward.ListFlight[0], 1, outward.FareDataID, in.SessionID, searchReq)
		if err != nil {
			return nil, errors.Wrap(err, "toDomainFlightItinerary")

		}

		outwardIti.GenerateItiID()
		outwardIti.GenerateItiKey()

		if seenItiID[outwardIti.ID] {
			continue
		}

		outwardIties = append(outwardIties, outwardIti)
		seenItiID[outwardIti.ID] = true
		outwardFare := toDomainSearchTotalFareInfo(outward)
		fareByItiD[outwardIti.ID] = &outwardFare
		fareRecordByItiKey[outwardIti.Key] = append(fareRecordByItiKey[outwardIti.Key], outwardFare.TotalFareAmount)
	}

	for _, returnItem := range returns {
		returnIti, err := toDomainFlightItinerary(returnItem.ListFlight[0], 2, returnItem.FareDataID, in.SessionID, searchReq)
		if err != nil {
			return nil, errors.Wrap(err, "toDomainFlightItinerary")

		}

		returnIti.GenerateItiID()
		returnIti.GenerateItiKey()

		if seenItiID[returnIti.ID] {
			continue
		}

		returnIties = append(returnIties, returnIti)
		seenItiID[returnIti.ID] = true
		returnFare := toDomainSearchTotalFareInfo(returnItem)
		fareByItiD[returnIti.ID] = &returnFare
		fareRecordByItiKey[returnIti.Key] = append(fareRecordByItiKey[returnIti.Key], returnFare.TotalFareAmount)
	}

	outwardIties2, returnIties2 := []*domain.FlightItinerary{}, []*domain.FlightItinerary{}

	for key, val := range fareRecordByItiKey {
		data := lo.Uniq(val)
		slices.Sort(data)

		fareRecordByItiKey[key] = data
	}

	// Filter chi lay 3 hang moi chuyen bay
	for _, outwardIti := range outwardIties {
		outwardFare := fareByItiD[outwardIti.ID]

		if outwardFare == nil {
			continue
		}

		itiFareRecord := fareRecordByItiKey[outwardIti.Key]
		if !isValidFare(itiFareRecord, outwardFare.TotalFareAmount) {
			continue
		}

		outwardIties2 = append(outwardIties2, outwardIti)
	}

	for _, returnIti := range returnIties {
		returnFare := fareByItiD[returnIti.ID]

		if returnFare == nil {
			continue
		}

		itiFareRecord := fareRecordByItiKey[returnIti.Key]
		if !isValidFare(itiFareRecord, returnFare.TotalFareAmount) {
			continue
		}

		returnIties2 = append(returnIties2, returnIti)
	}

	// Ghep chuyen
	for _, outwardIti := range outwardIties2 {
		for _, returnIti := range returnIties2 {
			if outwardIti.CarrierMarketing != returnIti.CarrierMarketing {
				continue
			}

			outwardFare := fareByItiD[outwardIti.ID]
			returnFare := fareByItiD[returnIti.ID]

			if outwardFare == nil || returnFare == nil {
				continue
			}

			ities := []*domain.FlightItinerary{
				outwardIti,
				returnIti,
			}

			total := domain.SearchTotalFareInfo{}
			total = total.SumTwo(outwardFare, returnFare)

			pairFlight := &domain.ResponseFlight{
				FlightID:            helpers.GenerateFlightID(provider),
				Itineraries:         ities,
				SearchTotalFareInfo: total,
				Provider:            provider,
				VAT:                 true,
				OptionType:          enum.FlightOptionTypeRecommend,
			}

			out = append(out, pairFlight)
		}
	}

	return out, nil
}

func isValidFare(ins []float64, value float64) bool {
	if len(ins) == 0 || len(ins) < 4 {
		return true
	}

	// Lay 3 hang
	if ins[0] == value || ins[len(ins)-1] == value || ins[(len(ins)-1)/2] == value {
		return true
	}

	// Lay them 2 hang giua (total 5 hang)
	// if len(ins) > 4 {
	// 	mid := (len(ins) - 1) / 2
	// 	leftHalfMid := mid / 2
	// 	rightHalfMid := mid + leftHalfMid

	// 	if ins[leftHalfMid] == value || ins[rightHalfMid] == value {
	// 		return true
	// 	}
	// }

	return false
}

func toDomainResponseFlight(in *entities.FareData, sessionID string, searchReq *domain.SearchFlightsRequest, optionType enum.FlightOptionType, groupSec int, provider enum.FlightProvider) (*domain.ResponseFlight, error) {
	if in == nil {
		return nil, errors.Errorf("empty input toDomainResponseFlight")
	}

	ities, err := toDomainFlightItineraries(in.ListFlight, in.FareDataID, sessionID, searchReq)
	if err != nil {
		return nil, errors.Wrap(err, "toDomainFlightItineraries")
	}

	out := &domain.ResponseFlight{
		FlightID:            helpers.GenerateFlightID(provider),
		Itineraries:         ities,
		SearchTotalFareInfo: toDomainSearchTotalFareInfo(in),
		Provider:            enum.FlightProviderHNH,
		VAT:                 true,
		OptionType:          optionType,
	}

	if optionType == enum.FlightOptionSingleTrip {
		out.GroupID = utils.GetFlightGroupID(provider, groupSec)
		out.Leg = int(in.Leg) + 1
	}

	return out, nil
}

func toDomainSearchTotalFareInfo(in *entities.FareData) domain.SearchTotalFareInfo {
	totalPaxFares := []*domain.ItineraryPaxFare{}
	totalFareBasic := float64(0)
	totalTaxAmount := float64(0)

	if in.Adt != 0 {
		taxAmount := in.TaxAdt + in.FeeAdt + in.ServiceFeeAdt
		totalFareBasic += in.FareAdt * float64(in.Adt)
		totalTaxAmount += taxAmount * float64(in.Adt)

		totalPaxFares = append(totalPaxFares, &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeAdult,
			FareAmount: in.FareAdt + taxAmount,
			FareBasic:  in.FareAdt,
			TaxAmount:  taxAmount,
			Currency:   in.Currency,
		})
	}

	if in.Chd != 0 {
		taxAmount := in.TaxChd + in.FeeChd + in.ServiceFeeChd
		totalFareBasic += in.FareChd * float64(in.Chd)
		totalTaxAmount += taxAmount * float64(in.Chd)

		totalPaxFares = append(totalPaxFares, &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeChildren,
			FareAmount: in.FareChd + taxAmount,
			FareBasic:  in.FareChd,
			TaxAmount:  taxAmount,
			Currency:   in.Currency,
		})
	}

	if in.Inf != 0 {
		taxAmount := in.TaxInf + in.FeeInf + in.ServiceFeeInf
		totalFareBasic += in.FareInf * float64(in.Inf)
		totalTaxAmount += taxAmount * float64(in.Inf)

		totalPaxFares = append(totalPaxFares, &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeInfant,
			FareAmount: in.FareInf + taxAmount,
			FareBasic:  in.FareInf,
			TaxAmount:  taxAmount,
			Currency:   in.Currency,
		})
	}

	return domain.SearchTotalFareInfo{
		TotalPaxFares:       totalPaxFares,
		BaseTotalFareAmount: in.TotalPrice,
		TotalFareAmount:     in.TotalPrice,
		TotalFareBasic:      totalFareBasic,
		TotalTaxAmount:      totalTaxAmount,
		Currency:            in.Currency,
	}
}

func toDomainFlightItineraries(ins []*entities.Flight, dataID int32, sessionID string, searchReq *domain.SearchFlightsRequest) ([]*domain.FlightItinerary, error) {
	out := []*domain.FlightItinerary{}

	for idx, flight := range ins {
		recIti, err := toDomainFlightItinerary(flight, idx+1, dataID, sessionID, searchReq)
		if err != nil {
			return nil, errors.Wrap(err, "toDomainFlightItinerary rec")
		}

		out = append(out, recIti)
	}
	return out, nil
}

func BuildBookingKey(sessionID string, dataID int32, flightVal string) string {
	return fmt.Sprintf("%s,%d,%s", sessionID, dataID, flightVal)
}

func toDomainItinerarySegments(ins []*entities.Segment) []*domain.ItinerarySegment {
	out := []*domain.ItinerarySegment{}

	for idx, item := range ins {
		out = append(out, toDomainItinerarySegment(item, idx+1))
	}

	return out
}

func toDomainItinerarySegment(in *entities.Segment, idx int) *domain.ItinerarySegment {
	out := &domain.ItinerarySegment{
		Index:            idx,
		DepartPlace:      in.StartPoint,
		DepartDate:       in.StartTime.ToUTCLocal(),
		DepartTerminal:   in.StartTerminal,
		ArrivalPlace:     in.EndPoint,
		ArrivalDate:      in.EndTime.ToUTCLocal(),
		ArrivalTerminal:  in.EndTerminal,
		CarrierMarketing: in.Airline,
		CarrierOperator:  in.OperatingAirline,
		FlightNumber:     replaceFlightNumber(in.FlightNumber, []string{in.MarketingAirline, in.OperatingAirline}),
		Aircraft:         constants.GetAirCraftName(in.Plane),
		BookingClass:     in.Class,
		CabinClassCode:   in.Cabin,
		FareBasis:        in.FareBasis,
		FlightDuration:   int(in.Duration),
		CabbinClass:      in.Cabin,
	}

	out.DepartDt = helpers.ToUTCDateTime(out.DepartDate)
	out.ArrivalDt = helpers.ToUTCDateTime(out.ArrivalDate)

	return out
}

func toDomainFlightItinerary(in *entities.Flight, idx int, dataID int32, sessionID string, searchReq *domain.SearchFlightsRequest) (*domain.FlightItinerary, error) {
	seatRemain := in.SeatRemain
	if seatRemain > 9 {
		seatRemain = 9
	}

	duration := in.Duration

	freeBags, err := getFreeBaggages(in.ListSegment[0], searchReq)
	if err != nil {
		return nil, errors.Wrap(err, "getFreeBaggages error")
	}

	segs := toDomainItinerarySegments(in.ListSegment)
	firstSeg := segs[0]

	if duration == 0 {
		for _, seg := range segs {
			duration += int32(seg.FlightDuration)
		}
	}

	out := &domain.FlightItinerary{
		Index:              idx,
		FareBasis:          firstSeg.FareBasis,
		CabinClass:         firstSeg.CabbinClass,
		CabinClassCode:     firstSeg.CabinClassCode,
		BookingClass:       firstSeg.BookingClass,
		Availability:       int(seatRemain),
		DepartPlace:        in.StartPoint,
		DepartDate:         in.StartDate.ToUTCLocal(),
		ArrivalPlace:       in.EndPoint,
		ArrivalDate:        in.EndDate.ToUTCLocal(),
		CarrierMarketing:   in.Airline,
		CarrierOperator:    in.Operating,
		FlightNumber:       firstSeg.FlightNumber,
		FlightDuration:     int(duration),
		StopNumber:         int(in.StopNum),
		FreeBaggage:        freeBags,
		ProviderBookingKey: BuildBookingKey(sessionID, dataID, in.FlightValue),
		Segments:           segs,
	}

	return out, nil
}

func replaceFlightNumber(in string, airlines []string) string {
	return lo.Reduce(airlines, func(result string, item string, _ int) string {
		return strings.ReplaceAll(result, item, "")
	}, in)
}

func sanitizeBaggageRawText(rawText string) (int64, string, error) {
	splittedStr := strings.Split(rawText, " ")

	if len(splittedStr) == 0 || strings.TrimSpace(splittedStr[0]) == "" || splittedStr[0][0] == '0' {
		return 0, "", nil
	}

	if len(splittedStr) == 1 {
		return 1, strings.TrimSpace(splittedStr[0]), nil
	}

	rawQuan := strings.TrimSpace(splittedStr[0])

	quantity, err := strconv.ParseInt(rawQuan, 10, 64)
	if err != nil {
		return 0, "", errors.Wrap(err, "strconv.ParseInt")
	}

	separatorPoint := "x"
	sepIndex := strings.Index(rawText, separatorPoint)
	bagInfo := strings.TrimSpace(rawText[sepIndex+1:])

	return quantity, bagInfo, nil
}

func getFreeBaggages(in *entities.Segment, searchReq *domain.SearchFlightsRequest) ([]*domain.BaggageInfo, error) {
	out := []*domain.BaggageInfo{}

	handQuan, handBag, err := sanitizeBaggageRawText(in.HandBaggage)
	if err != nil {
		return nil, errors.Wrap(err, "sanitizeBaggageRawText handBag")
	}

	allowQuan, allowBag, err := sanitizeBaggageRawText(in.AllowanceBaggage)
	if err != nil {
		return nil, errors.Wrap(err, "sanitizeBaggageRawText allowBag")
	}

	if searchReq.Passengers.ADT != 0 {
		if handBag != "" {
			out = append(out, &domain.BaggageInfo{
				Name:          handBag,
				IsHandBaggage: true,
				Quantity:      handQuan,
				PaxType:       enum.PaxTypeAdult,
			})
		}

		if allowBag != "" {
			out = append(out, &domain.BaggageInfo{
				Name:          allowBag,
				IsHandBaggage: false,
				Quantity:      allowQuan,
				PaxType:       enum.PaxTypeAdult,
			})
		}
	}

	if searchReq.Passengers.CHD != 0 {
		if handBag != "" {
			out = append(out, &domain.BaggageInfo{
				Name:          handBag,
				IsHandBaggage: true,
				Quantity:      handQuan,
				PaxType:       enum.PaxTypeChildren,
			})
		}

		if allowBag != "" {
			out = append(out, &domain.BaggageInfo{
				Name:          allowBag,
				IsHandBaggage: false,
				Quantity:      allowQuan,
				PaxType:       enum.PaxTypeChildren,
			})
		}
	}

	return out, nil
}
