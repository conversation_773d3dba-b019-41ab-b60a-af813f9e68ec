package converts

import (
	"fmt"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	hnhEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func ToIssueTicketRequest(booking *domain.BookingSession, pnr []*domain.PaxInfo) *entities.IssueTicketRequest {
	req := entities.IssueTicketRequest{
		Airline:       booking.Itineraries[0].CarrierMarketing,
		BookingCode:   booking.BookingRef,
		System:        booking.AirlineSystem,
		TotalPrice:    booking.FareDataIss.TotalFareAmount,
		ListPassenger: toHNHListPassenger(pnr, booking),
	}
	return &req
}

func ToRetrieveBookingRequest(booking *domain.BookingSession, pnr []*domain.PaxInfo) *entities.GetBookingRequest {
	req := entities.GetBookingRequest{
		Airline:     booking.Itineraries[0].CarrierMarketing,
		BookingCode: booking.BookingRef,
		System:      booking.AirlineSystem,
		LastName:    pnr[0].GivenName,
	}
	return &req
}

func toHNHListPassenger(items []*domain.PaxInfo, booking *domain.BookingSession) []*entities.PassengerOfIssueTicket {
	passengers := make([]*entities.PassengerOfIssueTicket, 0, len(items))
	parentID := int32(0)
	for _, v := range items {
		b := toPassengerOfIssueTicket(v, booking.Itineraries, parentID)
		if v.Type == enum.PaxTypeInfant {
			parentID++
		}
		passengers = append(passengers, b)
	}
	return passengers
}

func toPassengerOfIssueTicket(v *domain.PaxInfo, itineraries []*domain.FlightItinerary, parentID int32) *entities.PassengerOfIssueTicket {
	if v == nil {
		return nil
	}
	var exDate *string
	var passportNumber *string

	if v.Passport != nil {
		exDate = lo.ToPtr(time.UnixMilli(v.Passport.ExpiryDate).Format(HNHDDMMYYYFormat))
		passportNumber = lo.ToPtr(v.Passport.Number)
	}

	paxParentID := parentID
	if v.Type != enum.PaxTypeInfant {
		paxParentID = int32(v.ID)
	}

	seats := make([]*entities.SeatInfo, 0, len(v.Seats))

	for index, iti := range itineraries {
		var paxFare *domain.ItineraryPax
		for _, pax := range iti.PaxInfo {
			if paxFare != nil {
				continue
			}
			if pax.PaxID == v.ID {
				paxFare = pax
			}
		}

		if paxFare == nil {
			continue
		}

		var segment *domain.ItinerarySegment
		for _, seg := range iti.Segments {
			for _, seat := range paxFare.Seats {
				if segment != nil {
					continue
				}

				if seg.Index == seat.SegmentIndex {
					segment = seg
				}
			}
		}

		for _, seatReq := range paxFare.Seats {
			seatCode := fmt.Sprintf("%s%s", seatReq.RowNumber, seatReq.SeatFacility.SeatCode)
			seats = append(seats, &entities.SeatInfo{
				Airline:    iti.CarrierMarketing,
				Value:      seatCode,
				Code:       seatCode,
				Price:      seatReq.SeatFacility.SeatCharge.TotalAmount,
				Currency:   seatReq.SeatFacility.SeatCharge.Currency,
				Leg:        int32(index),
				Route:      fmt.Sprintf("%s%s", segment.DepartPlace, segment.ArrivalPlace),
				StartPoint: segment.DepartPlace,
				EndPoint:   segment.ArrivalPlace,
			})
		}
	}

	return &entities.PassengerOfIssueTicket{
		Index:                  uint32(v.ID),
		ParentID:               paxParentID,
		FirstName:              v.Surname,
		LastName:               v.GivenName,
		Type:                   hnhEnum.PassengerTypeValue[string(v.Type)],
		Gender:                 genderTypeToClientGender(v.Gender),
		Birthday:               parseTimeHNHDDMMYYY(v.DOB),
		Nationality:            lo.ToPtr(v.Nationality),
		PassportNumber:         passportNumber,
		PassportExpirationDate: exDate,
		ListSeat:               seats,
		// Membership:             lo.ToPtr(""),
		// Wheelchair:             lo.ToPtr(false),
		// Vegetarian:             lo.ToPtr(false),
	}
}

func getProviderTitle(gender commonEnum.GenderType, paxType enum.PaxType) string {
	if paxType == enum.PaxTypeAdult {
		if gender == commonEnum.GenderTypeFeMale {
			return "MS"
		}
		return "MR"
	}
	if gender == commonEnum.GenderTypeFeMale {
		return "MISS"
	}

	return "MSTR"
}

func MapETicket(data *entities.GetBookingResponse, itineraries []*domain.FlightItinerary, pnr []*domain.PaxInfo) ([]*domain.ETicketInfo, enum.EMDStatus, error) {
	result := []*domain.ETicketInfo{}
	emdStatus := enum.EMDStatusOK
	if data == nil || data.Reservation == nil || len(data.Reservation.ListTicket) == 0 {
		return nil, enum.EMDStatusEmpty, domain.ErrInvalidValue
	}

	paxTicketMap := map[string][]*entities.Ticket{}

	trueResPaxGenderMap := map[string]commonEnum.GenderType{}

	for _, pax := range data.ListPassenger {
		paxGender := commonEnum.GenderTypeFeMale
		if pax.Gender {
			paxGender = commonEnum.GenderTypeMale
		}

		trueResPaxGenderMap[pax.NameId] = paxGender
	}

	for _, item := range data.Reservation.ListTicket {
		for _, paxInfo := range pnr {
			if item.PassengerName != "" && item.PassengerName == fmt.Sprintf("%s %s %s", strings.ToUpper(paxInfo.Surname), strings.ToUpper(paxInfo.GivenName), getProviderTitle(paxInfo.Gender, paxInfo.Type)) {
				item.FirstName = paxInfo.Surname
				item.LastName = paxInfo.GivenName
			}
		}

		truePaxGender := trueResPaxGenderMap[strconv.Itoa(item.PassengerIndex)]
		key := helpers.GetPaxKey(item.FirstName, item.LastName, truePaxGender)

		if _, ok := paxTicketMap[key]; !ok {
			paxTicketMap[key] = []*entities.Ticket{}
		}

		paxTicketMap[key] = append(paxTicketMap[key], item)
	}

	for _, paxInfo := range pnr {
		paxTickets := paxTicketMap[helpers.GetPaxKey(paxInfo.Surname, paxInfo.GivenName, paxInfo.Gender)]
		var eTicket *domain.ETicketInfo
		emdInfo := []*domain.EMDInfo{}
		for _, paxTicket := range paxTickets {
			if paxTicket != nil && strings.EqualFold(paxTicket.FirstName, paxInfo.Surname) && strings.EqualFold(paxTicket.LastName, paxInfo.GivenName) {
				// skip check emd if one fail
				if emdStatus != enum.EMDStatusOK {
					continue
				}

				if paxTicket.TicketType == "EMD" {
					if paxTicket.ErrorMessage != "" || paxTicket.Status != "EMD" {
						log.Error("MapETicket EMD fail", log.Any("paxTicket", paxTicket))
						emdStatus = enum.EMDStatusFailed
					}

					var emd *domain.EMDInfo
					switch paxTicket.ServiceType {
					case "SEAT":
						if paxTicket.ServiceCode != "" {
							var rowNumber string
							var seatCode string

							for _, r := range paxTicket.ServiceCode {
								if unicode.IsDigit(r) {
									rowNumber += string(r)
								} else {
									seatCode = string(r)
								}
							}

							emd = &domain.EMDInfo{
								EMDTicketNumber: paxTicket.TicketNumber,
								EMDType:         enum.EMDTypeSeat,
								SeatRowNumber:   rowNumber,
								SeatCode:        seatCode,
							}
						} else {
							emd = &domain.EMDInfo{
								EMDTicketNumber: paxTicket.TicketNumber,
								EMDType:         enum.EMDTypeSeat,
							}
						}
					case "BAGGAGE":
						emd = &domain.EMDInfo{
							EMDTicketNumber: paxTicket.TicketNumber,
							EMDType:         enum.EMDTypeBag,
							BaggageCode:     paxTicket.ServiceCode,
						}
					}

					if emd != nil {
						emdInfo = append(emdInfo, emd)
					}
				} else {
					if paxTicket.ErrorMessage != "" || paxTicket.Status != "OPEN" {
						log.Error("MapETicket ticket issue fail", log.Any("paxTicket", paxTicket))
						return nil, emdStatus, domain.ErrIssueTicketFailed
					}

					eTicket = &domain.ETicketInfo{
						PaxID:        paxInfo.ID,
						TicketNumber: paxTicket.TicketNumber,
					}
				}
			}
		}

		if eTicket != nil {
			eTicket.EMDInfos = emdInfo
			for _, itinerary := range itineraries {
				for _, pax := range itinerary.PaxInfo {
					for _, item := range eTicket.EMDInfos {
						if item.EMDType == enum.EMDTypeSeat {
							for _, seat := range pax.Seats {
								if item.SeatCode == seat.SeatFacility.SeatCode && item.SeatRowNumber == seat.RowNumber {
									item.SegmentIndex = seat.SegmentIndex
								}
							}

						}

						if item.EMDType == enum.EMDTypeBag {
							for _, bag := range pax.Baggages {
								if item.BaggageCode == bag.BaggageInfo.OfferData.HNHOfferData.Code {
									item.BaggageOptionID = bag.BaggageInfo.OptionID
								}
							}
						}
					}
				}
				result = append(result, &domain.ETicketInfo{
					PaxID:           eTicket.PaxID,
					PaxName:         eTicket.PaxName,
					ItineraryNumber: itinerary.Index,
					TicketNumber:    eTicket.TicketNumber,
					EMDInfos:        emdInfo,
				})
			}
		}
	}

	return result, emdStatus, nil
}

func ToDomainTotalFareInfoFromReservationFareData(in *entities.ReservationFareData) (*domain.TotalFareInfo, error) {
	totalPaxFares := []*domain.ItineraryPaxFare{}
	paxFareInfos := []*domain.PaxFareInfo{}

	if in.Adt > 0 {
		paxFareAdt := &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeAdult,
			FareAmount: in.FareAdt + in.TaxAdt + in.FeeAdt + in.ServiceFeeAdt,
			FareBasic:  in.FareAdt,
			TaxAmount:  in.TaxAdt + in.FeeAdt + in.ServiceFeeAdt,
			Currency:   in.Currency,
		}

		for i := 0; i < int(in.Adt); i++ {
			paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
				PaxID:              i,
				PaxType:            enum.PaxTypeAdult,
				PaxFares:           []*domain.PaxFareReport{}, // Skip
				Seat:               []*domain.PassengerSeatInfo{},
				Baggages:           []*domain.PassengerBaggageInfo{},
				TotalSeatAmount:    0,
				TotalBaggageAmount: 0,
				TotalPrice:         paxFareAdt.FareAmount,
				Currency:           in.Currency,
			})
		}

		totalPaxFares = append(totalPaxFares, paxFareAdt)
	}

	if in.Chd > 0 {
		paxFareChd := &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeChildren,
			FareAmount: in.FareChd + in.TaxChd + in.FeeChd + in.ServiceFeeChd,
			FareBasic:  in.FareChd,
			TaxAmount:  in.TaxChd + in.FeeChd + in.ServiceFeeChd,
			Currency:   in.Currency,
		}

		for i := 0; i < int(in.Chd); i++ {
			paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
				PaxID:              int(in.Adt) + i,
				PaxType:            enum.PaxTypeChildren,
				PaxFares:           []*domain.PaxFareReport{}, // Skip
				Seat:               []*domain.PassengerSeatInfo{},
				Baggages:           []*domain.PassengerBaggageInfo{},
				TotalSeatAmount:    0,
				TotalBaggageAmount: 0,
				TotalPrice:         paxFareChd.FareAmount,
				Currency:           in.Currency,
			})
		}

		totalPaxFares = append(totalPaxFares, paxFareChd)
	}

	if in.Inf > 0 {
		paxFareInf := &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeInfant,
			FareAmount: in.FareInf + in.TaxInf + in.FeeInf,
			FareBasic:  in.FareInf,
			TaxAmount:  in.TaxInf + in.FeeInf + in.ServiceFeeInf,
			Currency:   in.Currency,
		}

		for i := 0; i < int(in.Inf); i++ {
			paxFareInfos = append(paxFareInfos, &domain.PaxFareInfo{
				PaxID:              int(in.Adt) + int(in.Chd) + i,
				PaxType:            enum.PaxTypeInfant,
				PaxFares:           []*domain.PaxFareReport{}, // Skip
				Seat:               []*domain.PassengerSeatInfo{},
				Baggages:           []*domain.PassengerBaggageInfo{},
				TotalSeatAmount:    0,
				TotalBaggageAmount: 0,
				TotalPrice:         paxFareInf.FareAmount,
				Currency:           in.Currency,
			})
		}

		totalPaxFares = append(totalPaxFares, paxFareInf)
	}

	fareBasic := in.FareAdt + in.FareChd + in.FareInf
	return &domain.TotalFareInfo{
		TotalPaxFares:       totalPaxFares,
		BaseTotalFareAmount: in.TotalPrice,
		TotalFareAmount:     in.TotalPrice,
		TotalFareBasic:      in.FareAdt + in.FareChd + in.FareInf,
		TotalTaxAmount:      in.TotalPrice - fareBasic,
		TotalSeatAmount:     0,
		TotalBaggageAmount:  0,
		Currency:            in.Currency,
		PaxFareInfos:        paxFareInfos,
	}, nil
}

func SyncBooking(pBk *entities.GetBookingResponse, domainBk *domain.BookingSession) (bool, error) {
	hasChanges := false

	flights := pBk.FareData.ListFlight

	pSegIDMap := map[int]*entities.Segment{}

	for _, flight := range flights {
		for _, seg := range flight.ListSegment {
			pSegIDMap[int(seg.ID)] = seg
		}
	}

	for _, iti := range domainBk.Itineraries {
		itiChange := false
		for _, flight := range flights {
			if iti.ArrivalPlace == flight.EndPoint && iti.DepartPlace == flight.StartPoint {
				for _, seg := range iti.Segments {
					for _, pSeg := range flight.ListSegment {
						if seg.ArrivalPlace == pSeg.EndPoint && seg.DepartPlace == pSeg.StartPoint {
							if pSeg.StartTime.ToUTCLocal() != seg.DepartDate || pSeg.EndTime.ToUTCLocal() != seg.ArrivalDate {
								hasChanges = true
								itiChange = true

								seg.DepartDate = pSeg.StartTime.ToUTCLocal()
								seg.ArrivalDate = pSeg.EndTime.ToUTCLocal()
								seg.DepartDt = helpers.ToUTCDateTime(seg.DepartDate)
								seg.ArrivalDt = helpers.ToUTCDateTime(seg.ArrivalDate)
							}
						}
					}
				}
				// Post processing
				if itiChange {
					var err error

					firstSeg := iti.Segments[0]
					lastSeg := iti.Segments[len(iti.Segments)-1]

					iti.DepartDate = firstSeg.DepartDate
					iti.ArrivalDate = lastSeg.ArrivalDate

					iti.DepartDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.DepartDate, "", iti.DepartDt.Location())
					if err != nil {
						return false, err
					}

					iti.ArrivalDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.ArrivalDate, "", iti.ArrivalDt.Location())
					if err != nil {
						return false, err
					}

					iti.FlightDuration = int(iti.ArrivalDt.Sub(iti.DepartDt).Minutes())
				}
			}
		}
	}

	return hasChanges, nil
}

func CheckFareChange(data *entities.GetBookingResponse, booking *domain.BookingSession) (bool, *domain.TotalFareInfo, error) {
	if booking == nil || data == nil || data.FareData == nil || data.Reservation == nil {
		return false, nil, domain.ErrInvalidValue
	}

	fareData, err := ToDomainTotalFareInfoFromReservationFareData(data.FareData)
	if err != nil {
		return false, nil, err
	}

	// Update Seat price to FareDataIss
	totalSeatAmount := float64(0)
	totalBaggageAmount := float64(0)
	for _, fareInfo := range data.Reservation.ListPassenger {
		for _, seat := range fareInfo.ListSeat {
			totalSeatAmount += seat.Price
		}

		for _, baggage := range fareInfo.ListBaggage {
			totalBaggageAmount += baggage.Price
		}
	}

	fareData.TotalBaggageAmount = totalBaggageAmount
	fareData.TotalSeatAmount = totalSeatAmount
	fareData.TotalFareAmount = fareData.BaseTotalFareAmount + fareData.TotalSeatAmount + fareData.TotalBaggageAmount

	// Check ticket price only
	if data.FareData.TotalPrice > booking.FareDataCf.BaseTotalFareAmount {
		return false, fareData, domain.ErrTicketFareChanged
	}

	hasChange, err := SyncBooking(data, booking)

	return hasChange, fareData, err
}

func CheckHasAncillary(booking *domain.BookingSession) bool {
	if booking == nil || len(booking.Itineraries) == 0 {
		return false
	}
	for _, iti := range booking.Itineraries {
		if iti == nil {
			return false
		}

		for _, pax := range iti.PaxInfo {
			if pax == nil {
				return false
			}

			if len(pax.Seats) > 0 || len(pax.Baggages) > 0 {
				return true
			}
		}

	}
	return false
}
