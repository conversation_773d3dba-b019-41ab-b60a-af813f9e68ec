package test

import (
	"encoding/json"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func TestGetBookingFareChange() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/getBooking_fareChange.json")

	response := &entities.GetBookingResponse{}
	err := json.Unmarshal(byteValue, response)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	byteValue, _ = utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/bookingSession_fareChange.json")

	bookingSession := &domain.BookingSession{}
	err = json.Unmarshal(byteValue, bookingSession)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	hasChange, err := converts.SyncBooking(response, bookingSession)
	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	fmt.Println("hasChange", hasChange)
	utils.WriteStructToXMLFile(response, "./logs/hnh_get_booking_fare_change.xml")

	return nil
}
