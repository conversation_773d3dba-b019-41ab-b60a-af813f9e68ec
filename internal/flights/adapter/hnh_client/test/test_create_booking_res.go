package test

import (
	"encoding/json"
	"fmt"

	commonErrors "gitlab.deepgate.io/apps/common/errors"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func TestCreateBookingRes() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/createBooking.json")

	res := &entities.BookFlightResponse{}
	err := json.Unmarshal(byteValue, res)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	utils.WriteStructToJSONFile(res, "./logs/hnh_create_booking.json")

	if !res.Status {
		log.Error("[VerifyFlight] Res error", log.Any("Res", res))
		return errors.WithMessage(commonErrors.ErrSomethingOccurred, res.ErrorCode)
	}

	if len(res.ListBooking) == 0 {
		log.Error("[BookFlight] Res empty error", log.Any("Res", res))
		return errors.WithMessage(commonErrors.ErrSomethingOccurred, "Res empty error")
	}

	var totalPrice float64

	bookingExpiredAt := res.ListBooking[0].ExpiryDate.Time
	for _, v := range res.ListBooking {
		totalPrice += v.Price
		if bookingExpiredAt.Before(v.ExpiryDate.Time) {
			bookingExpiredAt = v.ExpiryDate.Time
		}

		if bookingExpiredAt.Before(v.TimePurchase.Time) {
			bookingExpiredAt = v.TimePurchase.Time
		}
	}

	utils.WriteStructToJSONFile(&domain.SvcCreateBookingResponse{
		LastTicketingDate: bookingExpiredAt.UnixMilli(),
		BookingRef:        res.ListBooking[0].BookingCode,
		AirlineSystem:     res.ListBooking[0].System,
	}, "./logs/hnh_create_booking_res.json")

	return nil
}
