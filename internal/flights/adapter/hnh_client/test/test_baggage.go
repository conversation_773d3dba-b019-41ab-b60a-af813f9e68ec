package test

import (
	"encoding/json"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func TestBaggage() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/getBaggage.json")

	res := &entities.GetBaggageResponse{}
	err := json.Unmarshal(byteValue, res)

	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	byteValue, _ = utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/itineraries_seat_bag.json")

	iti := []*domain.FlightItinerary{}
	err = json.Unmarshal(byteValue, &iti)

	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	seatmap, err := converts.ToDomainBaggageOptions(res, iti)
	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	utils.WriteStructToJSONFile(seatmap, "logs/hnh_baggage.json")

	return nil
}
