package test

import (
	"encoding/json"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	convert "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func TestSeatMap() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/getseatmap.json")

	res := &entities.GetSeatMapResponse{}
	err := json.Unmarshal(byteValue, res)

	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	byteValue, _ = utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/itineraries_seat_bag.json")

	iti := []*domain.FlightItinerary{}
	err = json.Unmarshal(byteValue, &iti)

	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	seatmap, err := converts.ToDomainSeatSegment(res, iti)
	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	utils.WriteStructToJSONFile(seatmap, "logs/hnh_seatmap_raw.json")

	convert.GenerateSeatMapAisle(seatmap)

	utils.WriteStructToJSONFile(seatmap, "logs/hnh_seatmap.json")

	return nil
}
