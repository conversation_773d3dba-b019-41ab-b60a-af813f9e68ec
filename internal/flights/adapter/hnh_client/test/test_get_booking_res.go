package test

import (
	"encoding/json"
	"fmt"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func TestGetBookingRes() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/getBooking.json")

	response := &entities.GetBookingResponse{}
	err := json.Unmarshal(byteValue, response)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	utils.WriteStructToXMLFile(response, "./logs/hnh_get_booking.xml")

	eTicket, _, err := converts.MapETicket(response, []*domain.FlightItinerary{}, []*domain.PaxInfo{
		{
			GivenName: "Thit",
			Surname:   "Boa",
			Gender:    commonEnum.GenderTypeMale,
			Type:      enum.PaxTypeAdult,
		},
	})
	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}
	utils.WriteStructToXMLFile(eTicket, "./logs/hnh_get_booking_eticket.xml")

	return nil
}
