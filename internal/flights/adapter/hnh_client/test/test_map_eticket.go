package test

import (
	"encoding/json"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func TesMapETicket() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/issue_res_seat_bag.json")

	res := &entities.GetBookingResponse{}
	err := json.Unmarshal(byteValue, res)

	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	byteValue, _ = utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/itineraries_seat_bag.json")

	iti := []*domain.FlightItinerary{}
	err = json.Unmarshal(byteValue, &iti)

	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	byteValue, _ = utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/pnr_seat_bag.json")

	pnr := []*domain.PaxInfo{}
	err = json.Unmarshal(byteValue, &pnr)

	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	eTicket, emdStatus, err := converts.MapETicket(res, iti, pnr)
	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	fmt.Println("emdStatus", emdStatus)

	utils.WriteStructToJSONFile(eTicket, "logs/hnh_eticket_seat_bag.json")

	return nil
}
