package test

import (
	"encoding/json"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func TestCreateBookingSeatBaggage() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/pax_info.json")

	paxInfo := &domain.PaxInfo{}
	err := json.Unmarshal(byteValue, paxInfo)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	byteValue, _ = utils.ReadXMLFile("./internal/flights/adapter/hnh_client/docs/flight_iti.json")

	iti := []*domain.FlightItinerary{}
	err = json.Unmarshal(byteValue, &iti)
	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	pax := converts.ToPassengerOfBookFlight(paxInfo, iti, 1)

	utils.WriteStructToJSONFile(pax, "./logs/hnh_create_booking_seat_baggage_req.json")

	return nil
}
