package entities

import (
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/enum"
)

// RESPONSE : Tì<PERSON> kiếm chuyến bay thông thường

type SearchFlightResponse struct {
	*FlightResponse
	ListFareData      []*FareData     `json:"ListFareData"`
	FlightType        enum.FlightType `json:"FlightType"`
	SessionID         string          `json:"Session"`
	ExpiredAt         int64
	Itinerary         int32 `json:"Itinerary"`
	InternalRequestID string
}

// RESPONSE : Tìm giá vé thấp nhất theo ngày
type SearchMinFaresResponse struct {
	*FlightResponse
	Key        string    `json:"Key"`
	DepartDate string    `json:"DepartDate"`
	MinFlight  *FareData `json:"MinFlight"`
}

// RESPONSE : Tìm giá vé thấp nhất theo tháng
type SearchMonthResponse struct {
	*FlightResponse
	ListMinPrice []*MinPrice `json:"ListMinPrice"`
}

// RESPONSE : L<PERSON>y thông tin hành lý ký gửi
type GetBaggageResponse struct {
	*FlightResponse
	ListBaggage []*Baggage `json:"ListBaggage"`
}

// RESPONSE : Lấy thông tin sơ đồ ghế
type GetSeatMapResponse struct {
	// Key string `json:"key"`
	// *FlightResponse
	*FlightResponse
	ListSeatMap []*SeatMap `json:"ListSeatMap"`
}

// RESPONSE : Lấy thông tin dịch vụ kèm thêm
type GetServicesResponse struct {
	*FlightResponse
	ListService []*Service `json:"ListService"`
}

type GetFareRulesResponse struct {
	*FlightResponse
	ListFareRules []*FareRules `json:"ListFareRules"`
}

type AddSeatResponse struct {
	Airline         string      `json:"Airline"`
	BookingCode     string      `json:"BookingCode"`
	BookingImage    string      `json:"BookingImage"`
	NewBookingCode  string      `json:"NewBookingCode"`
	NewBookingImage string      `json:"NewBookingImage"`
	ExpiryDate      time.Time   `json:"ExpiryDate"`
	ListPassenger   []Passenger `json:"ListPassenger"`
	ListTicket      []Ticket    `json:"ListTicket"`
	Warnings        []Warning   `json:"Warnings"`
	Status          bool        `json:"Status"`
	ErrorCode       string      `json:"ErrorCode"`
	ErrorValue      string      `json:"ErrorValue"`
	ErrorField      string      `json:"ErrorField"`
	Message         string      `json:"Message"`
	Language        string      `json:"Language"`
}

type BookFlightResponse struct {
	OrderCode         string     `json:"OrderCode"`
	BookingID         int64      `json:"BookingId"`
	ListBooking       []*Booking `json:"ListBooking"`
	Status            bool       `json:"Status"`
	ErrorCode         string     `json:"ErrorCode"`
	ErrorValue        string     `json:"ErrorValue"`
	ErrorField        string     `json:"ErrorField"`
	Message           string     `json:"Message"`
	Language          string     `json:"Language"`
	InternalRequestID string     `json:"InternalRequestID"`
}

type VerifyFlightResponse struct {
	ListFareStatus []FareStatus `json:"ListFareStatus"`
	Status         bool         `json:"Status"`
	ErrorCode      string       `json:"ErrorCode"`
	ErrorValue     string       `json:"ErrorValue"`
	ErrorField     string       `json:"ErrorField"`
	Message        string       `json:"Message"`
	Language       string       `json:"Language"`
}

type IssueTicketResponse struct {
	*FlightResponse
	ListTicket   []*Ticket `json:"ListTicket"`
	BookingImage string    `json:"BookingImage"`
}

type GetAncillaryResponse struct {
	ListService []*Ancillary `json:"ListService"`
	*FlightResponse
}

type VoidTicketResponse struct {
	BookingImage string    `json:"BookingImage"`
	ListTicket   []*Ticket `json:"ListTicket"`
	*FlightResponse
}

type GetBookingResponse struct {
	Airline         string                  `json:"Airline"`
	BookingCode     string                  `json:"BookingCode"`
	BookingImage    string                  `json:"BookingImage"`
	GdsCode         string                  `json:"GdsCode"`
	BookingStatus   string                  `json:"BookingStatus"`
	BookingData     string                  `json:"BookingData"`
	System          string                  `json:"System"`
	BookingDate     DateTimeUTC             `json:"BookingDate"`
	BookingTime     string                  `json:"BookingTime"`
	IssueDate       DateTimeUTC             `json:"IssueDate"`
	IssueTime       string                  `json:"IssueTime"`
	ExpiryDate      DateTimeUTC             `json:"ExpiryDate"`
	Expiration      string                  `json:"Expiration"`
	TotalPrice      float64                 `json:"TotalPrice"`
	NewFormat       bool                    `json:"NewFormat"`
	Reservation     *Reservation            `json:"Reservation"`
	Contact         *ContactInfo            `json:"Contact"`
	FareData        *ReservationFareData    `json:"FareData"`
	ListTktInfo     []*Ticket               `json:"ListTktInfo"`
	ListAirAvail    []*AirAvail             `json:"ListAirAvail"`
	ListPassenger   []*ReservationPassenger `json:"ListPassenger"`
	ListTransaction []*Transaction          `json:"ListTransaction"`
	ListTicket      []*Ticket               `json:"ListTicket"`
	ListSegment     []*Segment              `json:"ListSegment"`
	*FlightResponse
}
