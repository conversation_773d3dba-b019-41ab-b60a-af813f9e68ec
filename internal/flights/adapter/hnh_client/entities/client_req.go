package entities

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/enum"

// REQUEST : Tìm kiếm chuyến bay thông thường

type SearchFlightRequest struct {
	ListFlight []*FlightRequest
	Language   string
	Currency   string
	Adt        int32
	Chd        int32
	Inf        int32
}

type GetSeatMapRequest struct {
	ListFareData []*FareDataInfo
	Language     string
	Currency     string
}

type GetBaggageRequest struct {
	ListFareData []*FareDataInfo
	Language     string
	Currency     string
}

type VerifyFlightRequest struct {
	ListFareData []*FareDataInfo
	Language     string
	Currency     string
}

type BookFlightItemReq struct{}

type BookFlightRequest struct {
	Contact         *Contact
	ListFareData    []*FareDataInfoOfBookFlight
	ListPassenger   []*PassengerOfBookFlightReq
	Flight          []*BookFlightItemReq
	BookType        enum.BookType
	AccountCode     string // khong bat buoc
	Remark          string // khong bat buoc
	Language        string
	Currency        string
	UseAgentContact bool
}

type SearchMinFareRequest struct {
	StartPoint string
	EndPoint   string
	DepartDate string
	Lang       string
	Currency   string
}

type GetAncillaryRequest struct {
	ListFareData []*FareDataInfo
	Language     string
	Currency     string
}

type IssueTicketRequest struct {
	ListPassenger    []*PassengerOfIssueTicket
	Language         string
	Currency         string
	Airline          string
	BookingCode      string
	BookingPcc       string
	System           string
	TotalPrice       float64
	FareQuoteSession string
	TourCode         string
	SendEmail        bool
}

type GetFareRulesRequest struct {
	GetFromGDS   *bool // Optional
	ListFareData []*FareDataInfo
	Language     string
	Currency     string
}
type VoidTicketRequest struct {
	Airline       string
	BookingCode   string
	BookingPcc    string
	System        string
	TicketNumber  string
	Language      string
	Currency      string
	CancelBooking bool
	VoidAllTicket bool
}

type GetBookingRequest struct {
	Language        string
	Currency        string
	Airline         string
	System          string
	BookingCode     string
	LastName        string
	GetBookingImage bool
}

type HNHSearchFlightRequest struct {
	*PassengerQuantity
	*BaseRequestInfo
	ViewMode   string           `json:"ViewMode"`
	ListFlight []*FlightRequest `json:"ListFlight"`
}

// REQUEST : Tìm giá vé thấp nhất theo ngày
type HNHSearchMinFareRequest struct {
	*BaseRequestInfo
	*FlightRequest `json:"FlightRequest"`
}

// REQUEST : Tìm giá vé thấp nhất theo tháng
type SearchMonthRequest struct {
	*BaseRequestInfo
	StartPoint string `json:"StartPoint"`
	EndPoint   string `json:"EndPoint"`
	Airline    string `json:"Airline"`
	Month      int    `json:"Month"`
	Year       int    `json:"Year"`
}

// REQUEST : Lấy thông tin hành lý ký gửi
type HNHGetBaggageRequest struct {
	*BaseRequestInfo
	ListFareData []*FareDataInfo `json:"ListFareData"`
}

// REQUEST : Lấy thông tin sơ đồ ghế
type HNHGetSeatMapRequest struct {
	*BaseRequestInfo
	ListFareData []*FareDataInfo `json:"ListFareData"`
}

// REQUEST : Lấy thông tin dịch vụ kèm thêm
type GetServicesRequest struct {
	*BaseRequestInfo
	StartPoint string `json:"StartPoint"`
	EndPoint   string `json:"EndPoint"`
}

// REQUEST : Lấy thông tin điều kiện giá vé
type HNHGetFareRulesRequest struct {
	*BaseRequestInfo
	GetFromGDS   *bool           `json:"GetFromGDS"` // Optional
	ListFareData []*FareDataInfo `json:"ListFareData"`
}

// Thêm dịch vụ ghế ngồi
// URL: https://platform.hongngocha.com/flights/addseat
// Giao thức: POST
type AddSeatRequest struct {
	Airline       string      `json:"Airline"`
	BookingCode   string      `json:"BookingCode"`
	AutoIssue     bool        `json:"AutoIssue"`
	ListPassenger []Passenger `json:"ListPassenger"`
	HeaderUser    string      `json:"HeaderUser"`
	HeaderPass    string      `json:"HeaderPass"`
	AgentAccount  string      `json:"AgentAccount"`
	AgentPassword string      `json:"AgentPassword"`
	ProductKey    string      `json:"ProductKey"`
	Currency      string      `json:"Currency"`
	Language      string      `json:"Language"`
	IpRequest     string      `json:"IpRequest"`
}

// Đặt chỗ tiêu chuẩn
// URL: https://platform.hongngocha.com/flights/bookflight
// ▪ Giao thức: POST

type HNHBookFlightRequest struct {
	*BaseRequestInfo
	BookType        enum.BookType               `json:"BookType"`
	UseAgentContact bool                        `json:"UseAgentContact"`
	Contact         *Contact                    `json:"Contact"`
	AccountCode     string                      `json:"AccountCode"`
	Remark          string                      `json:"Remark"`
	ListPassenger   []*PassengerOfBookFlightReq `json:"ListPassenger"`
	ListFareData    []*FareDataInfoOfBookFlight `json:"ListFareData"`
}

// Kiểm tra chuyến bay
// URL: https://platform.hongngocha.com/flights/verifyflight
// ▪ Giao thức: POST

type HNHVerifyFlightRequest struct {
	*BaseRequestInfo
	ListFareData []*FareDataInfo `json:"ListFareData"`
}

type HNHIssueTicketRequest struct {
	*BaseRequestInfo
	Airline          string                    `json:"Airline"`
	BookingCode      string                    `json:"BookingCode"`
	BookingPcc       string                    `json:"BookingPcc"` // ma dai ly, khong bat buoc
	System           string                    `json:"System"`
	TotalPrice       float64                   `json:"TotalPrice"`
	FareQuoteSession string                    `json:"FareQuoteSession"`
	TourCode         string                    `json:"Tourcode"` // ma danh nghiep, khong bat buoc
	SendEmail        bool                      `json:"SendEmail"`
	ListPassenger    []*PassengerOfIssueTicket `json:"ListPassenger"`
}

type HNHGetAncillaryRequest struct {
	*BaseRequestInfo
	ListFareData []*FareDataInfo `json:"ListFareData"`
}

// Hủy vé
// URL: https://platform.hongngocha.com/flights/voidticket
// ▪ Giao thức: POST
type HNHVoidTicketRequest struct {
	*BaseRequestInfo
	Airline       string `json:"Airline"`
	BookingCode   string `json:"BookingCode"`
	BookingPcc    string `json:"BookingPcc"`
	System        string `json:"System"`
	TicketNumber  string `json:"TicketNumber"`
	CancelBooking bool   `json:"CancelBooking"`
	VoidAllTicket bool   `json:"VoidAllTicket"`
}

type HNHGetBookingRequest struct {
	*BaseRequestInfo
	Airline         string `json:"Airline"`
	System          string `json:"System"`
	BookingCode     string `json:"BookingCode"`
	LastName        string `json:"LastName"`
	GetBookingImage bool   `json:"GetBookingImage"`
}
