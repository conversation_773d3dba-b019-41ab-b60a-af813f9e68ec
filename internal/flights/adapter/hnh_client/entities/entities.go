package entities

import (
	"bytes"
	"fmt"
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

type DateTimeUTC struct {
	time.Time
	hasTz bool
}

const dateTimeUTCLayout = "2006-01-02T15:04:05"
const dateRFC3339NanoLayout = "2006-01-02T15:04:05.999Z07:00"

func (s *DateTimeUTC) ToUTCLocal() int64 {
	if s == nil {
		return 0
	}

	if s.hasTz {
		return helpers.ParseRealUTCToFakeTimeUTCLocal(s.Time).UnixMilli()
	}

	return s.UnixMilli()
}

func (s *DateTimeUTC) ToUnixMilli(gmtOffset int64) int64 {
	if s == nil {
		return 0
	}

	if s.hasTz {
		return s.UnixMilli()
	}

	return s.UnixMilli() - time.Hour.Milliseconds()*gmtOffset
}

func (s *DateTimeUTC) HasTz() bool {
	if s == nil {
		return false
	}

	return s.hasTz
}

func (e DateTimeUTC) MarshalJSON() ([]byte, error) {
	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(e.Time.Format(dateTimeUTCLayout))
	buffer.WriteString(`"`)
	return buffer.Bytes(), nil
}

func (e *DateTimeUTC) UnmarshalJSON(data []byte) (err error) {
	data = bytes.Trim(data, "\"")
	layouts := []string{dateTimeUTCLayout, dateRFC3339NanoLayout}
	if string(data) == "null" {
		return nil
	}
	for _, layout := range layouts {
		e.Time, err = time.Parse(layout, string(data))
		if err == nil {
			if layout == dateRFC3339NanoLayout {
				e.hasTz = true
			}

			return nil
		}
	}
	if err != nil {
		return fmt.Errorf("DateTimeUTC Parse %w", err)
	}
	return nil
}

type FlightRequest struct {
	StartPoint string `json:"StartPoint"`
	EndPoint   string `json:"EndPoint"`
	DepartDate string `json:"DepartDate"`
	Airline    string `json:"Airline"`
}

type AuthInfo struct {
	HeaderUser    string `json:"HeaderUser"`
	HeaderPass    string `json:"HeaderPass"`
	AgentAccount  string `json:"AgentAccount"`
	AgentPassword string `json:"AgentPassword"`
	ProductKey    string `json:"ProductKey"`
}
type BaseRequestInfo struct {
	*AuthInfo
	Currency  string `json:"Currency"`
	Language  string `json:"Language"`
	IPRequest string `json:"IpRequest"`
}
type PassengerQuantity struct {
	Adt int32 `json:"Adt"`
	Chd int32 `json:"Chd"`
	Inf int32 `json:"Inf"`
}

type FlightResponse struct {
	Status     bool   `json:"Status"`
	ErrorCode  string `json:"ErrorCode"`
	ErrorValue string `json:"ErrorValue"`
	ErrorField string `json:"ErrorField"`
	Message    string `json:"Message"`
	Language   string `json:"Language"`
}

type FlightCost struct {
	FareAdt         float64 `json:"FareAdt"`
	FareChd         float64 `json:"FareChd"`
	FareInf         float64 `json:"FareInf"`
	TaxAdt          float64 `json:"TaxAdt"`
	TaxChd          float64 `json:"TaxChd"`
	TaxInf          float64 `json:"TaxInf"`
	FeeAdt          float64 `json:"FeeAdt"`
	FeeChd          float64 `json:"FeeChd"`
	FeeInf          float64 `json:"FeeInf"`
	VatAdt          float64 `json:"VatAdt"`
	VatChd          float64 `json:"VatChd"`
	VatInf          float64 `json:"VatInf"`
	ServiceFeeAdt   float64 `json:"ServiceFeeAdt"`
	ServiceFeeChd   float64 `json:"ServiceFeeChd"`
	ServiceFeeInf   float64 `json:"ServiceFeeInf"`
	TotalNetPrice   float64 `json:"TotalNetPrice"`
	TotalServiceFee float64 `json:"TotalServiceFee"`
	TotalDiscount   float64 `json:"TotalDiscount"`
	TotalCommission float64 `json:"TotalCommission"`
	TotalPrice      float64 `json:"TotalPrice"`
	DiscountAdt     float64 `json:"DiscountAdt"`
	DiscountChd     float64 `json:"DiscountChd"`
	DiscountInf     float64 `json:"DiscountInf"`
}
type Flight struct {
	StartDate    DateTimeUTC `json:"StartDate"`
	EndDate      DateTimeUTC `json:"EndDate"`
	ListSegment  []*Segment  `json:"ListSegment"`
	HasDownStop  bool        `json:"HasDownStop"`
	NoRefund     bool        `json:"NoRefund"`
	Promo        bool        `json:"Promo"`
	Airline      string      `json:"Airline"`
	Operating    string      `json:"Operating"`
	StartPoint   string      `json:"StartPoint"`
	EndPoint     string      `json:"EndPoint"`
	StartDt      string      `json:"StartDt"`
	EndDt        string      `json:"EndDt"`
	FlightNumber string      `json:"FlightNumber"`
	GroupClass   string      `json:"GroupClass"`
	FareClass    string      `json:"FareClass"`
	FareBasis    string      `json:"FareBasis"`
	FlightValue  string      `json:"FlightValue"`
	FlightID     int32       `json:"FlightId"`
	Leg          int32       `json:"Leg"`
	StopNum      int32       `json:"StopNum"`
	Duration     int32       `json:"Duration"`
	SeatRemain   int32       `json:"SeatRemain"`
	ID           string      `json:"Id"`
	LinkIDs      []string    `json:"LinkIDs"`
}

type FareData struct {
	*PassengerQuantity
	*FlightCost
	ListFlight   []*Flight `json:"ListFlight"`
	Promo        bool      `json:"Promo"`
	Airline      string    `json:"Airline"`
	Currency     string    `json:"Currency"`
	System       string    `json:"System"`
	FareType     string    `json:"FareType"`
	CacheAge     float64   `json:"CacheAge"`
	Availability int32     `json:"Availability"`
	Itinerary    int32     `json:"Itinerary"`
	FareDataID   int32     `json:"FareDataId"`
	Leg          int32     `json:"Leg"`
	ID           string
}

type Segment struct {
	StartTime           DateTimeUTC `json:"StartTime"`
	EndTime             DateTimeUTC `json:"EndTime"`
	HasStop             bool        `json:"HasStop"`
	DayChange           bool        `json:"DayChange"`
	StopOvernight       bool        `json:"StopOvernight"`
	ChangeStation       bool        `json:"ChangeStation"`
	ChangeAirport       bool        `json:"ChangeAirport"`
	LastItem            bool        `json:"LastItem"`
	Airline             string      `json:"Airline"`
	MarketingAirline    string      `json:"MarketingAirline"`
	OperatingAirline    string      `json:"OperatingAirline"`
	StartPoint          string      `json:"StartPoint"`
	EndPoint            string      `json:"EndPoint"`
	StartTm             string      `json:"StartTm"`
	EndTm               string      `json:"EndTm"`
	FlightNumber        string      `json:"FlightNumber"`
	Class               string      `json:"Class"`
	Cabin               string      `json:"Cabin"`
	FareBasis           string      `json:"FareBasis"`
	Plane               string      `json:"Plane"`
	StartTimeZoneOffset string      `json:"StartTimeZoneOffset"`
	EndTimeZoneOffset   string      `json:"EndTimeZoneOffset"`
	StartTerminal       string      `json:"StartTerminal"`
	EndTerminal         string      `json:"EndTerminal"`
	HandBaggage         string      `json:"HandBaggage"`
	AllowanceBaggage    string      `json:"AllowanceBaggage"`
	StopPoint           string      `json:"StopPoint"`
	StopTime            float64     `json:"StopTime"`
	ID                  int32       `json:"Id"`
	Duration            int32       `json:"Duration"`
	Seat                int32       `json:"Seat"`
}

type MinPrice struct {
	DepartDate   string      `json:"DepartDate"`
	ListFareData []*FareData `json:"ListFareData"`
}

type FareDataInfo struct {
	Session    string        `json:"Session"`
	FareDataID int32         `json:"FareDataId"`
	ListFlight []*FlightInfo `json:"ListFlight"`
}

type FareDataInfoOfBookFlight struct {
	Session    string        `json:"Session"`
	FareDataID int32         `json:"FareDataId"`
	AutoIssue  bool          `json:"AutoIssue"`
	CACode     string        `json:"CAcode"`
	VIPText    string        `json:"VIPText"`
	ListFlight []*FareFlight `json:"ListFlight"`
}

type FlightInfo struct {
	FlightValue string `json:"FlightValue"`
}

type Baggage struct {
	Confirmed  bool    `json:"Confirmed,omitempty"`
	Airline    string  `json:"Airline"`
	Value      string  `json:"Value"`
	Code       string  `json:"Code"`
	Name       string  `json:"Name"`
	Currency   string  `json:"Currency"`
	Route      string  `json:"Route"`
	StartPoint string  `json:"StartPoint"`
	EndPoint   string  `json:"EndPoint"`
	StatusCode string  `json:"StatusCode,omitempty"`
	Leg        int32   `json:"Leg"`
	Price      float64 `json:"Price"`
}

type SeatMap struct {
	StartPoint   string   `json:"StartPoint"`
	EndPoint     string   `json:"EndPoint"`
	FlightNumber string   `json:"FlightNumber"`
	Equipment    string   `json:"Equipment"`
	Operating    string   `json:"Operating"`
	Marketing    string   `json:"Marketing"`
	DepartDate   string   `json:"DepartDate"`
	ListCabin    []*Cabin `json:"ListCabin"`
}

type Cabin struct {
	FirstRow      string    `json:"FirstRow"`
	LastRow       string    `json:"LastRow"`
	CabinClass    string    `json:"CabinClass"`
	Wing          *Wing     `json:"Wing"`
	ClassLocation []string  `json:"ClassLocation"`
	ListColumn    []*Column `json:"ListColumn"`
	ListRow       []*Row    `json:"ListRow"`
}

type Wing struct {
	FirstRow string `json:"FirstRow"`
	LastRow  string `json:"LastRow"`
}

type Column struct {
	ColumnIndex     int64    `json:"ColumnIndex"`
	ColumnCode      string   `json:"ColumnCode"`
	ColumnType      string   `json:"ColumnType"`
	Characteristics []string `json:"Characteristics"`
}

type Row struct {
	RowNumber string  `json:"RowNumber"`
	ListSeat  []*Seat `json:"ListSeat"`
}

type Seat struct {
	ID          string   `json:"ID"`
	SeatNumber  string   `json:"SeatNumber"`
	SeatType    string   `json:"SeatType"`
	RowNumber   string   `json:"RowNumber"`
	ColumnCode  string   `json:"ColumnCode"`
	ColumnIndex int      `json:"ColumnIndex"`
	Fee         float64  `json:"Fee"`
	Tax         float64  `json:"Tax"`
	Price       float64  `json:"Price"`
	Currency    string   `json:"Currency"`
	Enabled     bool     `json:"Enabled"`
	Locations   []string `json:"Location"`
	Limitations []string `json:"Limitations"`
	Facilities  []string `json:"Facilities"`
}

type Service struct {
	Confirmed   bool    `json:"Confirmed"`
	Airline     string  `json:"Airline"`
	Code        string  `json:"Code"`
	Type        string  `json:"Type"`
	Name        string  `json:"Name"`
	Description string  `json:"Description"`
	Currency    string  `json:"Currency"`
	Route       string  `json:"Route"`
	StartPoint  string  `json:"StartPoint"`
	EndPoint    string  `json:"EndPoint"`
	StatusCode  string  `json:"StatusCode"`
	Price       float64 `json:"Price"`
	Leg         int32   `json:"Leg"`
}

type FareRules struct {
	Route          string            `json:"Route"`
	FareBasis      string            `json:"FareBasis"`
	ListRulesGroup []*ListRulesGroup `json:"ListRulesGroup"`
	FareDataInfo   FareDataInfo      `json:"FareDataInfo"`
}

type ListRulesGroup struct {
	RulesTitle    string   `json:"RulesTitle"`
	ListRulesText []string `json:"ListRulesText"`
}

type Fare struct {
	DocType  string  `json:"DocType"`
	BaseFare float64 `json:"BaseFare"`
	Fees     float64 `json:"Fees"`
	Taxes    float64 `json:"Taxes"`
}
type Passenger struct {
	ListBaggage            []*Baggage       `json:"ListBaggage"`
	ListSeat               []*Seat          `json:"ListSeat"`
	ListFare               []*Fare          `json:"ListFare"`
	CustLoyalty            *CustomerLoyalty `json:"CustLoyalty"`
	ListService            []*Service       `json:"ListService"`
	Wheelchair             bool             `json:"Wheelchair"`
	Vegetarian             bool             `json:"Vegetarian"`
	Gender                 bool             `json:"Gender"`
	NameID                 string           `json:"NameId"`
	FirstName              string           `json:"FirstName"`
	LastName               string           `json:"LastName"`
	Type                   string           `json:"Type"`
	Birthday               string           `json:"Birthday"`
	Nationality            string           `json:"Nationality"`
	PassportNumber         string           `json:"PassportNumber"`
	PassportExpirationDate string           `json:"PassportExpirationDate"`
	Membership             string           `json:"Membership"`
	ParentID               int              `json:"ParentId"`
	Index                  int              `json:"Index"`
}

type PassengerOfBookFlightReq PassengerOfBookFlight

type PassengerOfBookFlightRes struct {
	*PassengerOfBookFlight
	CustLoyalty *CustomerLoyalty `json:"CustLoyalty,omitempty"`
}
type PassengerOfBookFlight struct {
	CustLoyalty            *CustomerLoyalty   `json:"CustLoyalty"`
	Nationality            *string            `json:"Nationality"`
	PassportNumber         *string            `json:"PassportNumber"`
	PassportExpirationDate *string            `json:"PassportExpirationDate"`
	Membership             *string            `json:"Membership"`
	Wheelchair             *bool              `json:"Wheelchair"`
	Vegetarian             *bool              `json:"Vegetarian"`
	ListBaggage            []*Baggage         `json:"ListBaggage"`
	ListSeat               []*SeatInfo        `json:"ListSeat"`
	ListFare               []*Fare            `json:"ListFare"`
	ListService            []*Service         `json:"ListService"`
	Gender                 bool               `json:"Gender"`
	Type                   enum.PassengerType `json:"Type"`
	NameID                 string             `json:"NameId"`
	FirstName              string             `json:"FirstName"`
	LastName               string             `json:"LastName"`
	Birthday               string             `json:"Birthday"`
	Index                  uint32             `json:"Index"`
	ParentID               int32              `json:"ParentId"`
}

type CustomerLoyalty struct {
	Airline        string `json:"Airline"`
	MembershipID   string `json:"MembershipID"`
	MembershipType string `json:"MembershipType"`
	Status         string `json:"Status"`
	Success        bool   `json:"Success"`
}

type Ticket struct {
	Index          int         `json:"Index"`
	Airline        string      `json:"Airline"`
	BookingCode    string      `json:"BookingCode"`
	ConjTktNum     string      `json:"ConjTktNum"`
	TicketNumber   string      `json:"TicketNumber"`
	TicketType     string      `json:"TicketType"`
	TicketRelated  string      `json:"TicketRelated"`
	RelatedType    string      `json:"RelatedType"`
	ServiceType    string      `json:"ServiceType"`
	ServiceCode    string      `json:"ServiceCode"`
	IssueDate      DateTimeUTC `json:"IssueDate"`
	PassengerIndex int         `json:"PassengerIndex"`
	PassengerName  string      `json:"PassengerName"`
	PassengerType  string      `json:"PassengerType"`
	FirstName      string      `json:"FirstName"`
	LastName       string      `json:"LastName"`
	Gender         bool        `json:"Gender"`
	Fare           float64     `json:"Fare"`
	Tax            float64     `json:"Tax"`
	Fee            float64     `json:"Fee"`
	Penalty        float64     `json:"Penalty"`
	Amount         float64     `json:"Amount"`
	Price          float64     `json:"Price"`
	TotalPrice     float64     `json:"TotalPrice"`
	Currency       string      `json:"Currency"`
	EquivCurrency  string      `json:"EquivCurrency"`
	Sequence       int         `json:"Sequence"`
	AgentCode      string      `json:"AgentCode"`
	MasterSignIn   string      `json:"MasterSignIn"`
	SignIn         string      `json:"SignIn"`
	Remark         string      `json:"Remark"`
	Status         string      `json:"Status"`
	ErrorMessage   string      `json:"ErrorMessage"`
	TicketDetails  string      `json:"TicketDetails"`
	TicketImage    string      `json:"TicketImage"`
	BookingFile    string      `json:"BookingFile"`
	ListSegment    []*Segment  `json:"ListSegment"`
}
type Warning struct {
	Type    string `json:"Type"`
	Code    string `json:"Code"`
	Message string `json:"Message"`
}
type FareFlight struct {
	FlightValue string `json:"FlightValue"`
	Leg         int32  `json:"Leg,omitempty"`
}
type Contact struct {
	Gender     bool   `json:"Gender"`
	FirstName  string `json:"FirstName"`
	LastName   string `json:"LastName"`
	Area       string `json:"Area"`
	Phone      string `json:"Phone"`
	Email      string `json:"Email"`
	Address    string `json:"Address"`
	AgentName  string `json:"AgentName"`
	AgentEmail string `json:"AgentEmail"`
	AgentPhone string `json:"AgentPhone"`
}
type Booking struct {
	Source        string                      `json:"Source"`
	Status        string                      `json:"Status"`
	AutoIssue     bool                        `json:"AutoIssue"`
	Airline       string                      `json:"Airline"`
	BookingCode   string                      `json:"BookingCode"`
	GDSCode       string                      `json:"GdsCode"`
	Flight        string                      `json:"Flight"`
	Route         string                      `json:"Route"`
	ErrorCode     string                      `json:"ErrorCode"`
	ErrorMessage  string                      `json:"ErrorMessage"`
	BookingImage  string                      `json:"BookingImage"`
	ExpiryDate    DateTimeUTC                 `json:"ExpiryDate"`
	TimePurchase  DateTimeUTC                 `json:"TimePurchase"`
	ExpiryDt      string                      `json:"ExpiryDt"`
	ExpiryTime    int64                       `json:"ExpiryTime"`
	ResponseTime  float64                     `json:"ResponseTime"`
	System        string                      `json:"System"`
	Price         float64                     `json:"Price"`
	Difference    float64                     `json:"Difference"`
	ListTicket    []*Ticket                   `json:"ListTicket"`
	Warning       *Warning                    `json:"Warnings"`
	ListPassenger []*PassengerOfBookFlightRes `json:"ListPassenger"`
	Session       string                      `json:"Session"`
	FareData      *FareData                   `json:"FareData"`
}
type FareStatus struct {
	Status     bool     `json:"Status"`
	Remark     string   `json:"Remark"`
	Price      float64  `json:"Price"`
	Difference float64  `json:"Difference"`
	Session    string   `json:"Session"`
	FareData   FareData `json:"FareData"`
}

type SeatInfo struct {
	Airline    string  `json:"Airline"`
	Value      string  `json:"Value"`
	Code       string  `json:"Code"`
	Name       string  `json:"Name"`
	Price      float64 `json:"Price"`
	Currency   string  `json:"Currency"`
	Leg        int32   `json:"Leg"`
	Route      string  `json:"Route"`
	StartPoint string  `json:"StartPoint"`
	EndPoint   string  `json:"EndPoint"`
}

type PassengerOfIssueTicket struct {
	Index                  uint32             `json:"Index"`
	ParentID               int32              `json:"ParentId"`
	FirstName              string             `json:"FirstName"`
	LastName               string             `json:"LastName"`
	Type                   enum.PassengerType `json:"Type"`
	Gender                 bool               `json:"Gender"`
	Birthday               string             `json:"Birthday"`
	Nationality            *string            `json:"Nationality"`
	PassportNumber         *string            `json:"PassportNumber"`
	PassportExpirationDate *string            `json:"PassportExpirationDate"`
	Membership             *string            `json:"Membership"`
	Wheelchair             *bool              `json:"Wheelchair"`
	Vegetarian             *bool              `json:"Vegetarian"`
	ListBaggage            []*Baggage         `json:"ListBaggage"`
	ListSeat               []*SeatInfo        `json:"ListSeat"`
	ListFare               []*Fare            `json:"ListFare"`
	CustLoyalty            *CustomerLoyalty   `json:"CustLoyalty"`
	NameId                 string             `json:"NameId"`
}

type Ancillary struct {
	Airline     string  `json:"Airline"`
	Code        string  `json:"Code"`
	Type        string  `json:"Type"`
	Name        string  `json:"Name"`
	Description string  `json:"Description"`
	Price       float64 `json:"Price"`
	Currency    string  `json:"Currency"`
	Leg         int32   `json:"Leg"`
	Route       string  `json:"Route"`
	StartPoint  string  `json:"StartPoint"`
	EndPoint    string  `json:"EndPoint"`
	StatusCode  string  `json:"StatusCode"`
	Confirmed   bool    `json:"Confirmed"`
}

type Reservation struct {
	Airline         string                  `json:"Airline"`
	System          string                  `json:"System"`
	BookingCode     string                  `json:"BookingCode"`
	GdsCode         string                  `json:"GdsCode"`
	BookingStatus   string                  `json:"BookingStatus"`
	BookingImage    string                  `json:"BookingImage"`
	BookingData     string                  `json:"BookingData"`
	BookingDate     DateTimeUTC             `json:"BookingDate"`
	BookingTime     string                  `json:"BookingTime"`
	IssueDate       DateTimeUTC             `json:"IssueDate"`
	IssueTime       string                  `json:"IssueTime"`
	ExpiryDate      DateTimeUTC             `json:"ExpiryDate"`
	ExpiryTime      string                  `json:"ExpiryTime"`
	TimePurchase    DateTimeUTC             `json:"TimePurchase"`
	PriceExpiration string                  `json:"PriceExpiration"`
	TotalPrice      float64                 `json:"TotalPrice"`
	ListContact     []*ContactInfo          `json:"ListContact"`
	FareData        *ReservationFareData    `json:"FareData"`
	ListPassenger   []*ReservationPassenger `json:"ListPassenger"`
	ListTicket      []*Ticket               `json:"ListTicket"`
	ListWarning     []*Warning              `json:"ListWarning"`
	ListTransaction []*Transaction          `json:"ListTransaction"`
}

type ReservationFareData struct {
	LastTicketDate DateTimeUTC `json:"LastTicketDate"`
	*PassengerQuantity
	*FlightCost
	ListFlight       []*ReservationFlight `json:"ListFlight"`
	ListXMLRulesInfo []*string            `json:"ListXmlRulesInfo"`
	Promo            bool                 `json:"Promo"`
	AutoIssue        bool                 `json:"AutoIssue"`
	Airline          string               `json:"Airline"`
	Currency         string               `json:"Currency"`
	Tourcode         string               `json:"Tourcode"`
	System           string               `json:"System"`
	FareType         string               `json:"FareType"`
	Session          string               `json:"Session"`
	CAcode           string               `json:"CAcode"`
	VIPText          string               `json:"VIPText"`
	Remark           string               `json:"Remark"`
	AccountCode      string               `json:"AccountCode"`
	VerifyMode       string               `json:"VerifyMode"`
	CacheAge         float64              `json:"CacheAge"`
	DiscountAdt      float64              `json:"DiscountAdt"`
	DiscountChd      float64              `json:"DiscountChd"`
	DiscountInf      float64              `json:"DiscountInf"`
	Availability     int32                `json:"Availability"`
	Itinerary        int32                `json:"Itinerary"`
	FareDataID       int32                `json:"FareDataId"`
	Leg              int32                `json:"Leg"`
}

type ReservationFlight struct {
	*Flight
	*FlightPaxPrice
}

type FlightPaxPrice struct {
	PaxType  string  `json:"PaxType"`
	Fare     float64 `json:"Fare"`
	Tax      float64 `json:"Tax"`
	Fee      float64 `json:"Fee"`
	Vat      float64 `json:"Vat"`
	Discount float64 `json:"Discount"`
	Total    float64 `json:"Total"`
	PaxNumb  int32   `json:"PaxNumb"`
}

type ContactInfo struct {
	Gender       bool   `json:"Gender"`
	MultiContact bool   `json:"MultiContact"`
	NameInPhone  bool   `json:"NameInPhone"`
	Title        string `json:"Title"`
	FirstName    string `json:"FirstName"`
	LastName     string `json:"LastName"`
	Area         string `json:"Area"`
	Phone        string `json:"Phone"`
	Email        string `json:"Email"`
	Address      string `json:"Address"`
	City         string `json:"City"`
	Country      string `json:"Country"`
	AgentArea    string `json:"AgentArea"`
	AgentPhone   string `json:"AgentPhone"`
	AgentEmail   string `json:"AgentEmail"`
	AgentName    string `json:"AgentName"`
}

type ReservationPassenger struct {
	*PassengerOfIssueTicket
	ListService     []*Service         `json:"ListService"`
	ListCustLoyalty []*CustomerLoyalty `json:"ListCustLoyalty"`
}

type Transaction struct {
	CreateDate    DateTimeUTC `json:"CreateDate"`
	Amount        float64     `json:"Amount"`
	Currency      string      `json:"Currency"`
	TransactionID string      `json:"TransactionId"`
	StatusCode    string      `json:"StatusCode"`
	Remark        string      `json:"Remark"`
}

type AirAvail struct {
	*Segment
}

type TicketInfo struct {
	Index         int    `json:"Index"`
	Airline       string `json:"Airline"`
	BookingCode   string `json:"BookingCode"`
	TicketNumber  string `json:"TicketNumber"`
	TicketStatus  string `json:"TicketStatus"`
	PassengerName string `json:"PassengerName"`
}

type SegmentInfo struct {
	Airline      string      `json:"Airline"`
	StartPoint   string      `json:"StartPoint"`
	EndPoint     string      `json:"EndPoint"`
	StartTime    DateTimeUTC `json:"StartTime"`
	EndTime      DateTimeUTC `json:"EndTime"`
	FlightNumber string      `json:"FlightNumber"`
	Class        string      `json:"Class"`
	Plane        string      `json:"Plane"`
	Leg          int         `json:"Leg"`
}
