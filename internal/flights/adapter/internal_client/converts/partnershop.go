package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/partner"
	commonEnums "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	bPartnerPb "gitlab.deepgate.io/apps/api/gen/go/partner/backend"
)

func ToDomainProviderConfigs(ins []*partner.ProviderConfig) []*domain.ProviderConfig {
	out := []*domain.ProviderConfig{}

	for _, item := range ins {
		out = append(out, &domain.ProviderConfig{
			Provider: item.Value,
			Enabled:  item.Enabled,
		})
	}

	return out
}

func ToDomainPartnerDCPs(in *partner.DCPs) *domain.PartnerDCPs {
	if in == nil {
		return nil
	}

	return &domain.PartnerDCPs{
		DCPsAmadeus:         toDomainDCPAmadeus(in.DcpsAmadeus),
		DCPsTravelFusion:    toDomainDCPTravelFusion(in.DcpsTravelFusion),
		DCPsVietnamAirlines: toDomainDCPVietnamAirline(in.DcpsVietnamAirlines),
		DCPsVietjetAir:      toDomainDCPVietjet(in.DcpsVietjetAir),
		DCPsEV:              toDomainDCPEV(in.DcpsEv),
		DCPsInternationalEV: toDomainDCPInternationalEV(in.DcpsInternationalEv),
	}
}

func toDomainDCPAmadeus(info []*partner.DCPAmadeus) []*domain.DCPAmadeus {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.DCPAmadeus, len(info))
	for index, dcp := range info {
		res[index] = &domain.DCPAmadeus{
			UserName:       dcp.UserName,
			Password:       dcp.Password,
			PseudoCityCode: dcp.PseudoCityCode,
			AgentDutyCode:  dcp.AgentDutyCode,
			RequestorType:  dcp.RequestorType,
			POSType:        dcp.PosType,
			BaseURL:        dcp.BaseUrl,
			Level:          commonEnums.DCPLevel(dcp.Level),
		}
	}

	return res
}

func toDomainDCPTravelFusion(info []*partner.DCPTravelFusion) []*domain.DCPTravelFusion {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.DCPTravelFusion, len(info))
	for index, dcp := range info {
		res[index] = &domain.DCPTravelFusion{
			UserName: dcp.UserName,
			Password: dcp.Password,
			BaseURL:  dcp.BaseUrl,
			Level:    commonEnums.DCPLevel(dcp.Level),
		}
	}

	return res
}

func toDomainDCPVietnamAirline(info []*partner.DCPVietnamAirlines) []*domain.DCPVietnamAirlines {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.DCPVietnamAirlines, len(info))
	for index, dcp := range info {
		res[index] = &domain.DCPVietnamAirlines{
			AccountNumber:  dcp.AccountNumber,
			UserName:       dcp.UserName,
			Password:       dcp.Password,
			StationNumber:  dcp.StationNumber,
			PCC:            dcp.Pcc,
			PrinterLniata:  dcp.PrinterLniata,
			PrinterCountry: dcp.PrinterCountry,
			Domain:         dcp.Domain,
			Organization:   dcp.Organization,
			BaseURL:        dcp.BaseUrl,
			Level:          commonEnums.DCPLevel(dcp.Level),
		}
	}

	return res
}

func toDomainDCPVietjet(info []*partner.DCPVietjetAir) []*domain.DCPVietjetAir {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.DCPVietjetAir, len(info))
	for index, dcp := range info {
		res[index] = &domain.DCPVietjetAir{
			UserName: dcp.UserName,
			Password: dcp.Password,
			BaseURL:  dcp.BaseUrl,
			Level:    commonEnums.DCPLevel(dcp.Level),
		}
	}

	return res
}

func toDomainDCPEV(info []*partner.DCPEV) []*domain.DCPEV {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.DCPEV, len(info))
	for index, dcp := range info {
		res[index] = &domain.DCPEV{
			Username: dcp.Username,
			Password: dcp.Password,
			BaseURL:  dcp.BaseUrl,
			Level:    commonEnums.DCPLevel(dcp.Level),
		}
	}

	return res
}

func toDomainDCPInternationalEV(info []*partner.DCPInternationalEV) []*domain.DCPInternationalEV {
	if len(info) == 0 {
		return nil
	}

	res := make([]*domain.DCPInternationalEV, len(info))
	for index, dcp := range info {
		res[index] = &domain.DCPInternationalEV{
			BaseURL:      dcp.BaseUrl,
			LoginURL:     dcp.LoginUrl,
			Username:     dcp.Username,
			Password:     dcp.Password,
			ClientSecret: dcp.ClientSecret,
			ClientID:     dcp.ClientId,
			GrantType:    dcp.GrantType,
			Level:        commonEnums.DCPLevel(dcp.Level),
		}
	}

	return res
}

func ToDomainPartnerShops(pbs []*partner.PartnerShopInfo) []*domain.PartnerShopInfo {
	if pbs == nil {
		return nil
	}

	if len(pbs) == 0 {
		return nil
	}

	domains := []*domain.PartnerShopInfo{}
	for _, pb := range pbs {
		if pb != nil {
			domains = append(domains, &domain.PartnerShopInfo{
				Code:     pb.Code,
				ID:       pb.Id,
				OfficeID: pb.OfficeId,
			})
		}
	}

	return domains
}

func ToDomainShopsBySaleCodes(pbs *bPartnerPb.GetShopsBySaleCodesRes) []*domain.ShopsBySaleCode {
	if pbs == nil || pbs.Items == nil {
		return nil
	}

	if len(pbs.Items) == 0 {
		return nil
	}

	domains := []*domain.ShopsBySaleCode{}
	for _, item := range pbs.Items {
		if item != nil {
			data := &domain.ShopsBySaleCode{
				Shops: ToDomainPartnerShops(item.Shops),
			}
			data.SaleCode = ""
			if item.SaleCode != nil {
				data.SaleCode = *item.SaleCode
			}

			domains = append(domains, data)
		}
	}

	return domains
}
