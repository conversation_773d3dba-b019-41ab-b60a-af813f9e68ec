package internal_client

import (
	"context"
	"fmt"

	bPartnerPb "gitlab.deepgate.io/apps/api/gen/go/partner/backend"

	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/server"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
)

type InternalClient interface {
	AuthByOffice(ctx context.Context, req *domain.LoginReq) (*domain.PartnerUser, error)
	GetOfficeInfo(ctx context.Context, partnershipID, officeID string) (*domain.PartnerShopInfo, error)
	GetShopsByManagerID(ctx context.Context, pID, managerID string) ([]*domain.PartnerShopInfo, error)
	GetShopsBySaleCodes(ctx context.Context, pID string, saleCodes []string) ([]*domain.ShopsBySaleCode, error)
}

type internalClient struct {
	cfg         *config.Schema
	partnerConn *grpc.ClientConn
}

func NewInternalClient(cfg *config.Schema, partnerConn *grpc.ClientConn) InternalClient {
	return &internalClient{cfg, partnerConn}
}

// GetOfficeInfo implements InternalClient.
func (c *internalClient) GetOfficeInfo(ctx context.Context, partnershipID, officeID string) (*domain.PartnerShopInfo, error) {
	client := bPartnerPb.NewPartnerShopServiceClient(c.partnerConn)
	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.getEffectivePartnershipID(partnershipID))
	grpcCtx := metadata.NewOutgoingContext(ctx, md)

	res, err := client.GetByOfficeID(grpcCtx, &bPartnerPb.GetByOfficeIDReq{OfficeId: officeID})
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if res.Info == nil {
		return nil, fmt.Errorf("res info nil")
	}

	info := res.Info

	providerCf := []*domain.ProviderConfig{}

	for _, item := range info.ProviderConfigs {
		provider := &domain.ProviderConfig{
			Provider: item.Value,
			Enabled:  item.Enabled,
		}

		if len(item.FilterConfig) > 0 {
			provider.FilterConfigs = make([]*domain.FilterConfig, 0, len(item.FilterConfig))

			for _, filter := range item.FilterConfig {
				provider.FilterConfigs = append(provider.FilterConfigs, &domain.FilterConfig{
					TripType:   enum.TripType(filter.TripType),
					IsEnable:   filter.IsEnable,
					Airlines:   filter.Airlines,
					FilterMode: enum.FilterMode(filter.FilterMode),
				})
			}
		}

		providerCf = append(providerCf, provider)
	}

	out := &domain.PartnerShopInfo{
		ID:              info.Id,
		Name:            info.Name,
		OwnerID:         info.Owner,
		PartnerType:     info.PartnerType,
		Code:            info.Code,
		OfficeID:        info.OfficeId,
		DCPs:            converts.ToDomainPartnerDCPs(info.Dcps),
		ProviderConfigs: providerCf,
	}

	if info.Webhook != nil {
		webhookURLCfg := domain.WebhookURLCfg{
			Transaction: "",
			LastTktDate: "",
		}

		if info.Webhook.UrlConfig != nil {
			webhookURLCfg.Transaction = info.Webhook.UrlConfig.Transaction
			webhookURLCfg.LastTktDate = info.Webhook.UrlConfig.LastTktDate
		}

		out.WebhookCfg = &domain.WebhookCfg{
			WebhookKey:    info.Webhook.Key,
			WebhookURLCfg: webhookURLCfg,
		}
	}

	return out, nil
}

func (c *internalClient) AuthByOffice(ctx context.Context, req *domain.LoginReq) (*domain.PartnerUser, error) {
	client := bPartnerPb.NewAuthServiceClient(c.partnerConn)
	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.getEffectivePartnershipID(req.PartnershipID))
	grpcCtx := metadata.NewOutgoingContext(ctx, md)

	res, err := client.AuthenByOfficeID(grpcCtx, &bPartnerPb.AuthenByOfficeIDReq{
		OfficeId: req.OfficeID,
		ApiKey:   req.APIKey,
	})
	if err != nil {
		log.Error("internalClient AuthByOffice AuthenByOfficeID error", log.Any("error", err), log.Any("req", req))
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if !res.IsSuccess {
		log.Error("internalClient AuthByOffice AuthenByOfficeID response error", log.Any("res", res), log.Any("req", req))
		return nil, commonError.New(commonError.BadRequest, res.ErrorCode)
	}

	if res.Data == nil {
		log.Error("partner.UserInfo nil error", log.Any("res", res), log.Any("req", req))
		return nil, commonError.ErrSomethingOccurred
	}

	out := &domain.PartnerUser{
		ID:            res.Data.Id,
		CreatedAt:     res.Data.CreatedAt,
		UpdatedAt:     res.Data.UpdatedAt,
		CreatedBy:     res.Data.CreatedBy,
		UpdatedBy:     res.Data.UpdatedBy,
		Email:         res.Data.Email,
		Name:          res.Data.Name,
		PartnershipID: res.Data.PartnershipId,
		PartnerShopID: res.Data.PartnerShopId,
	}

	if res.WebhookCfg != nil {
		webhookURLCfg := domain.WebhookURLCfg{
			Transaction: "",
			LastTktDate: "",
		}

		if res.Data.WebhookUrlConfig != nil {
			webhookURLCfg.Transaction = res.Data.WebhookUrlConfig.Transaction
			webhookURLCfg.LastTktDate = res.Data.WebhookUrlConfig.LastTktDate
		}

		out.WebhookCfg = domain.WebhookCfg{
			WebhookKey:    res.WebhookCfg.Key,
			WebhookURLCfg: webhookURLCfg,
		}
	}

	return out, nil
}

func (c *internalClient) GetShopsByManagerID(ctx context.Context, pID, managerID string) ([]*domain.PartnerShopInfo, error) {
	md := server.WithMetadataInternalPartner(c.cfg.InternalSecretToken, c.getEffectivePartnershipID(pID), "")
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := bPartnerPb.NewPartnerShopServiceClient(c.partnerConn)

	res, err := client.GetShopsByManagerID(newCtx, &bPartnerPb.GetShopsByManagerIDReq{
		ManagerId: managerID,
	})
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	out := []*domain.PartnerShopInfo{}

	if len(res.Items) == 0 {
		return out, nil
	}

	for _, item := range res.Items {
		out = append(out, &domain.PartnerShopInfo{
			Name:     item.Name,
			Code:     item.Code,
			OfficeID: item.OfficeId,
		})
	}

	return out, nil
}

func (c *internalClient) getEffectivePartnershipID(partnershipID string) string {
	if partnershipID != "" {
		return partnershipID
	}
	return c.cfg.HubPartnershipID
}

func (c *internalClient) GetShopsBySaleCodes(ctx context.Context, pID string, saleCodes []string) ([]*domain.ShopsBySaleCode, error) {
	md := server.WithMetadataInternalPartner(c.cfg.InternalSecretToken, pID, "")
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := bPartnerPb.NewPartnerShopServiceClient(c.partnerConn)

	res, err := client.GetShopsBySaleCodes(newCtx, &bPartnerPb.GetShopsBySaleCodesReq{SaleCodes: saleCodes})
	if err != nil {
		log.Error("GetShopsBySaleCode", log.Any("Error:", err))
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}
	if res == nil {
		log.Error("GetShopsBySaleCode", log.String("Error:", "res is nil"))
		return nil, nil
	}

	return converts.ToDomainShopsBySaleCodes(res), nil
}
