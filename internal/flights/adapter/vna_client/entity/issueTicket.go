package entity

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type ETicketInfo struct {
	Surname       string
	GivenName     string
	Email         string
	TicketNumbers []string   `json:"ticket_number,omitempty"`
	EMDInfos      []*EMDInfo `json:"emd_ticket_number,omitempty"`
}

type EMDInfo struct {
	EMDTicketNumber string
	EMDType         enum.EMDType
	SegmentIndex    int
	SeatRowNumber   string
	SeatCode        string
	BaggageSubCode  string
}
