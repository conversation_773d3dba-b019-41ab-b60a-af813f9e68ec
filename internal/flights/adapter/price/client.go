package price

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	basePb "gitlab.deepgate.io/apps/api/gen/go/base"
	pricePb "gitlab.deepgate.io/apps/api/gen/go/price"
	backendPb "gitlab.deepgate.io/apps/api/gen/go/price/backend"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"google.golang.org/grpc/metadata"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

// Error constants
var (
	ErrPricingRequestNil = errors.New("pricing request is nil")
	ErrBatchRequestNil   = errors.New("batch request is nil")
	ErrBatchRequestEmpty = errors.New("batch request is empty")
)

type PriceClient interface {
	CalculateFlightPricing(ctx context.Context, req *domain.PricingCalculationRequest) (*domain.PricingCalculationResponse, error)
	CalculateFlightPricingBatch(ctx context.Context, req *domain.PricingBatchServiceRequest) (*domain.PricingBatchServiceResponse, error)
	CalculatePenaltyPricing(ctx context.Context, req *domain.CalculatePenaltyPricingRequest) ([]*domain.MiniRuleFlight, error)
}

type priceClient struct {
	cfg *config.Schema
}

func NewPriceClient(cfg *config.Schema) PriceClient {
	return &priceClient{cfg}
}

// CalculateFlightPricing handles single pricing calculation.
func (c *priceClient) CalculateFlightPricing(ctx context.Context, req *domain.PricingCalculationRequest) (*domain.PricingCalculationResponse, error) {
	if req == nil {
		return nil, ErrPricingRequestNil
	}
	// Convert domain request to proto request
	protoReq, err := c.mapDomainToProtoRequest(req)
	if err != nil {
		return nil, fmt.Errorf("failed to map domain request to proto: %w", err)
	}

	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PriceServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close() // nolint: errcheck

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)
	client := backendPb.NewFlightPricingServiceClient(conn)

	protoRes, err := client.CalculateFlightPricing(newCtx, protoReq)
	if err != nil {
		return nil, fmt.Errorf("failed to call pricing service: %w", err)
	}

	// Convert proto response to domain response
	domainRes, err := c.mapProtoResponseToDomain(protoRes)
	if err != nil {
		return nil, fmt.Errorf("failed to map proto response to domain: %w", err)
	}

	log.Info("External price service call completed successfully",
		log.String("request_id", req.FlightID),
		log.Bool("success", domainRes.Success))

	return domainRes, nil
}

// CalculateFlightPricingBatch handles batch pricing calculation.
func (c *priceClient) CalculateFlightPricingBatch(ctx context.Context, req *domain.PricingBatchServiceRequest) (*domain.PricingBatchServiceResponse, error) {
	if req == nil {
		return nil, ErrBatchRequestNil
	}

	if len(req.Requests) == 0 {
		return nil, ErrBatchRequestEmpty
	}

	// Convert domain batch request to proto batch request
	protoBatchReq, err := c.mapDomainToBatchProtoRequest(req)
	if err != nil {
		return nil, fmt.Errorf("failed to map domain batch request to proto: %w", err)
	}

	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PriceServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)
	client := backendPb.NewFlightPricingServiceClient(conn)

	protoBatchRes, err := client.CalculateFlightPricingBatch(newCtx, protoBatchReq)
	if err != nil {
		return nil, fmt.Errorf("failed to call batch pricing service: %w", err)
	}

	// Convert proto batch response to domain service batch response
	return c.mapProtoBatchResponseToServiceResponse(protoBatchRes)
}

func (c *priceClient) CalculatePenaltyPricing(ctx context.Context, req *domain.CalculatePenaltyPricingRequest) ([]*domain.MiniRuleFlight, error) {
	if req == nil {
		return nil, ErrPricingRequestNil
	}

	protoReq := toCalculatePenaltyHiddenFeeBatchRequest(req)
	if len(protoReq.Flights) == 0 {
		return req.Flights, nil
	}

	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PriceServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close() // nolint: errcheck

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)
	client := backendPb.NewPenaltyPricingServiceClient(conn)

	protoRes, err := client.CalculatePenaltyPricingBatch(newCtx, protoReq)
	if err != nil {
		return nil, fmt.Errorf("failed to call pricing service: %w", err)
	}

	if protoRes == nil {
		return nil, fmt.Errorf("failed to call pricing service: res nil")
	}

	protoResMap := map[string]*backendPb.CalculatePenaltyPricingBatchResponse_Flight{}
	for _, flight := range protoRes.Flights {
		protoResMap[flight.FlightId] = flight
	}

	for _, flight := range req.Flights {
		if _, ok := protoResMap[flight.FlightID]; !ok {
			continue
		}

		filteMiniRule(protoResMap[flight.FlightID].MiniRules, flight.MiniRules)
	}

	return req.Flights, nil
}

// mapDomainToProtoRequest converts domain pricing request to proto request
func (c *priceClient) mapDomainToProtoRequest(req *domain.PricingCalculationRequest) (*backendPb.CalculateFlightPricingRequest, error) {
	if req == nil {
		return nil, ErrPricingRequestNil
	}

	// Map flight itineraries
	protoItineraries := make([]*pricePb.FlightPricingItinerary, 0, len(req.FlightData.Itineraries))
	for _, itinerary := range req.FlightData.Itineraries {
		protoItinerary, err := c.mapFlightItineraryToProto(itinerary)
		if err != nil {
			return nil, fmt.Errorf("failed to map itinerary: %w", err)
		}
		protoItineraries = append(protoItineraries, protoItinerary)
	}

	// Map passenger quantity
	passengerQuantity := &pricePb.PassengerQuantity{
		Adt: int32(req.Passengers.ADT),
		Chd: int32(req.Passengers.CHD),
		Inf: int32(req.Passengers.INF),
	}

	// Map passenger fares from SearchTotalFareInfo
	passengerFares := c.convertTotalFareInfoToProto(&req.FlightData.SearchTotalFareInfo)

	provider := int32(req.FlightData.Provider)

	protoReq := &backendPb.CalculateFlightPricingRequest{
		PartnershipId:     req.PartnershipID,
		OfficeId:          req.OfficeID,
		FlightId:          req.FlightID,
		FlightType:        pricePb.FlightType(req.FlightData.FlightType),
		PassengerFares:    passengerFares,
		Itineraries:       protoItineraries,
		PassengerQuantity: passengerQuantity,
		Currency:          req.Currency,
		Provider:          provider,
	}

	return protoReq, nil
}

func (c *priceClient) convertTotalFareInfoToProto(fareInfo *domain.SearchTotalFareInfo) []*pricePb.PassengerFare {
	if fareInfo == nil || len(fareInfo.TotalPaxFares) == 0 {
		return nil
	}

	protoFares := make([]*pricePb.PassengerFare, 0, len(fareInfo.TotalPaxFares))
	for _, fare := range fareInfo.TotalPaxFares {
		protoFare := &pricePb.PassengerFare{
			PaxType:    c.mapPaxTypeToProto(fare.PaxType),
			FareAmount: fare.FareAmount,
			FareBasic:  fare.FareBasic,
			TaxAmount:  fare.TaxAmount,
		}
		protoFares = append(protoFares, protoFare)
	}

	return protoFares
}

func (c *priceClient) mapPaxTypeToProto(paxType enum.PaxType) basePb.PassengerType {
	switch paxType {
	case enum.PaxTypeAdult:
		return basePb.PassengerType_ADT
	case enum.PaxTypeChildren:
		return basePb.PassengerType_CHD
	case enum.PaxTypeInfant:
		return basePb.PassengerType_INF
	default:
		return basePb.PassengerType_Passenger_Type_None
	}
}

// mapDomainToBatchProtoRequest converts domain batch request to proto batch request
func (c *priceClient) mapDomainToBatchProtoRequest(req *domain.PricingBatchServiceRequest) (*backendPb.CalculateFlightPricingBatchRequest, error) {
	if req == nil {
		return nil, ErrBatchRequestNil
	}

	if len(req.Requests) == 0 {
		return nil, ErrBatchRequestEmpty
	}

	// Convert each individual service request to proto request
	protoRequests := make([]*backendPb.CalculateFlightPricingRequest, 0, len(req.Requests))
	for _, serviceReq := range req.Requests {
		// Convert service request to calculation request first
		calcReq := c.mapServiceRequestToCalculationRequest(serviceReq, req.PartnershipID, req.OfficeID)

		protoReq, err := c.mapDomainToProtoRequest(calcReq)
		if err != nil {
			return nil, fmt.Errorf("failed to map individual request: %w", err)
		}
		protoRequests = append(protoRequests, protoReq)
	}

	protoBatchReq := &backendPb.CalculateFlightPricingBatchRequest{
		Requests: protoRequests,
	}

	return protoBatchReq, nil
}

// mapServiceRequestToCalculationRequest converts PricingServiceRequest to PricingCalculationRequest
func (c *priceClient) mapServiceRequestToCalculationRequest(serviceReq *domain.PricingServiceRequest, partnershipID, officeID string) *domain.PricingCalculationRequest {
	// Use provided IDs from batch request, fallback to individual request IDs
	finalPartnershipID := partnershipID
	if finalPartnershipID == "" {
		finalPartnershipID = serviceReq.PartnershipID
	}

	finalOfficeID := officeID
	if finalOfficeID == "" {
		finalOfficeID = serviceReq.OfficeID
	}

	return &domain.PricingCalculationRequest{
		FlightID:      serviceReq.FlightID, // Use FlightID as RequestID for tracking
		PartnershipID: finalPartnershipID,
		OfficeID:      finalOfficeID,
		FlightData:    serviceReq.FlightData,
		Passengers:    serviceReq.Passengers,
		Currency:      serviceReq.Currency,
	}
}

// mapFlightItineraryToProto converts domain flight itinerary to proto
func (c *priceClient) mapFlightItineraryToProto(itinerary *domain.FlightItinerary) (*pricePb.FlightPricingItinerary, error) {
	if itinerary == nil {
		return nil, errors.New("itinerary is nil")
	}

	route := fmt.Sprintf("%s-%s", itinerary.DepartCountry, itinerary.ArrivalCountry)

	protoItinerary := &pricePb.FlightPricingItinerary{
		ItineraryId:      itinerary.ID,
		CarrierMarketing: itinerary.CarrierMarketing,
		DepartCountry:    itinerary.DepartCountry,
		ArrivalCountry:   itinerary.ArrivalCountry,
		BookingClass:     itinerary.BookingClass,
		CabinClass:       itinerary.CabinClass,
		Route:            route,
		DepartPlace:      itinerary.DepartPlace,
		ArrivalPlace:     itinerary.ArrivalPlace,
	}

	return protoItinerary, nil
}

// mapProtoResponseToDomain converts proto response to domain response
func (c *priceClient) mapProtoResponseToDomain(protoRes *backendPb.CalculateFlightPricingResponse) (*domain.PricingCalculationResponse, error) {
	if protoRes == nil {
		return nil, errors.New("proto response is nil")
	}

	// Map itinerary results
	itineraryResults := make([]*domain.ItineraryPricingResult, 0, len(protoRes.ItineraryResults))
	for _, protoItinerary := range protoRes.ItineraryResults {
		domainItinerary, err := c.mapProtoItineraryResultToDomain(protoItinerary)
		if err != nil {
			log.Warn("Failed to map itinerary result", log.Any("error", err))
			continue
		}
		itineraryResults = append(itineraryResults, domainItinerary)
	}

	// Map passenger results
	passengerResults := make([]*domain.PassengerPricingResult, 0, len(protoRes.PassengerResults))
	for _, protoPassenger := range protoRes.PassengerResults {
		domainPassenger := c.mapProtoPassengerResultToDomain(protoPassenger)
		passengerResults = append(passengerResults, domainPassenger)
	}

	// Calculate totals from passenger results
	var originalTotalFareAmount, finalTotalFareAmount, originalTotalFareBasic, finalTotalFareBasic float64
	for _, passenger := range passengerResults {
		originalTotalFareAmount += passenger.OriginalFareAmount * float64(passenger.Quantity)
		finalTotalFareAmount += passenger.FinalFareAmount * float64(passenger.Quantity)
		originalTotalFareBasic += passenger.OriginalFareBasic * float64(passenger.Quantity)
		finalTotalFareBasic += passenger.FinalFareBasic * float64(passenger.Quantity)
	}

	// Create pricing result
	pricingResult := &domain.FlightPricingResult{
		IsSuccess:               protoRes.IsSuccess,
		ErrorCode:               protoRes.ErrorCode,
		FlightID:                protoRes.FlightId,
		ItineraryResults:        itineraryResults,
		PassengerResults:        passengerResults,
		OriginalTotalFareAmount: originalTotalFareAmount,
		FinalTotalFareAmount:    finalTotalFareAmount,
		OriginalTotalFareBasic:  originalTotalFareBasic,
		FinalTotalFareBasic:     finalTotalFareBasic,
		TotalHiddenFeeAmount:    protoRes.TotalHiddenFeeAmount,
		TotalDiscountAmount:     protoRes.TotalDiscountAmount,
	}

	domainRes := &domain.PricingCalculationResponse{
		Success:     protoRes.IsSuccess,
		PricingData: pricingResult,
		ErrorCode:   protoRes.ErrorCode,
	}

	return domainRes, nil
}

// mapProtoPassengerResultToDomain converts proto passenger result to domain
func (c *priceClient) mapProtoPassengerResultToDomain(protoPassenger *pricePb.PassengerPricingResult) *domain.PassengerPricingResult {
	if protoPassenger == nil {
		return nil
	}

	// Convert passenger type from proto to domain enum
	passengerType := c.mapProtoPassengerTypeToDomain(protoPassenger.PassengerType)

	return &domain.PassengerPricingResult{
		PassengerType:      passengerType,
		Quantity:           protoPassenger.Quantity,
		OriginalFareAmount: protoPassenger.OriginalFareAmount,
		OriginalFareBasic:  protoPassenger.OriginalFareBasic,
		TaxAmount:          protoPassenger.TaxAmount,
		FinalFareAmount:    protoPassenger.FinalFareAmount,
		FinalFareBasic:     protoPassenger.FinalFareBasic,
		HiddenFeeAmount:    protoPassenger.HiddenFeeAmount,
		DiscountAmount:     protoPassenger.DiscountAmount,
	}
}

// mapProtoPassengerTypeToDomain converts proto passenger type to domain enum
func (c *priceClient) mapProtoPassengerTypeToDomain(protoType basePb.PassengerType) enum.PaxType {
	switch protoType {
	case basePb.PassengerType_ADT:
		return enum.PaxTypeAdult
	case basePb.PassengerType_CHD:
		return enum.PaxTypeChildren
	case basePb.PassengerType_INF:
		return enum.PaxTypeInfant
	default:
		return enum.PaxTypeAdult // Default to adult
	}
}

// mapProtoItineraryResultToDomain converts proto itinerary result to domain
func (c *priceClient) mapProtoItineraryResultToDomain(protoItinerary *backendPb.ItineraryPricingResult) (*domain.ItineraryPricingResult, error) {
	if protoItinerary == nil {
		return nil, errors.New("proto itinerary is nil")
	}

	// Map hidden fee config
	var hiddenFeeConfig *domain.FlightHiddenFeeConfig
	if protoItinerary.HiddenFeeConfig != nil {
		hiddenFeeConfig = &domain.FlightHiddenFeeConfig{
			ID:           protoItinerary.HiddenFeeConfig.Id,
			Type:         protoItinerary.HiddenFeeConfig.Type.String(),
			Amount:       protoItinerary.HiddenFeeConfig.Amount,
			VAT:          protoItinerary.HiddenFeeConfig.Vat,
			AirlineCode:  protoItinerary.HiddenFeeConfig.AirlineCode,
			Route:        protoItinerary.HiddenFeeConfig.Route,
			BookingClass: protoItinerary.HiddenFeeConfig.BookingClass,
			Percent:      protoItinerary.HiddenFeeConfig.Percent,
			Provider:     protoItinerary.HiddenFeeConfig.Provider.String(),
		}
	}

	// Map discount config
	var discountConfig *domain.FlightDiscountConfig
	if protoItinerary.DiscountConfig != nil {
		discountConfig = &domain.FlightDiscountConfig{
			ID:           protoItinerary.DiscountConfig.Id,
			Type:         protoItinerary.DiscountConfig.Type.String(),
			Amount:       protoItinerary.DiscountConfig.Amount,
			VAT:          protoItinerary.DiscountConfig.Vat,
			AirlineCode:  protoItinerary.DiscountConfig.AirlineCode,
			Route:        protoItinerary.DiscountConfig.Route,
			BookingClass: protoItinerary.DiscountConfig.BookingClass,
			Percent:      protoItinerary.DiscountConfig.Percent,
			Provider:     protoItinerary.DiscountConfig.Provider.String(),
		}
	}

	domainItinerary := &domain.ItineraryPricingResult{
		ItineraryID:      protoItinerary.ItineraryId,
		CarrierMarketing: protoItinerary.CarrierMarketing,
		DepartCountry:    protoItinerary.DepartCountry,
		ArrivalCountry:   protoItinerary.ArrivalCountry,
		BookingClass:     protoItinerary.BookingClass,
		CabinClass:       protoItinerary.CabinClass,
		Route:            protoItinerary.Route,
		HiddenFeeConfig:  hiddenFeeConfig,
		DiscountConfig:   discountConfig,
	}

	return domainItinerary, nil
}

// mapProtoBatchResponseToServiceResponse converts proto batch response to service batch response
func (c *priceClient) mapProtoBatchResponseToServiceResponse(protoBatchRes *backendPb.CalculateFlightPricingBatchResponse) (*domain.PricingBatchServiceResponse, error) {
	if protoBatchRes == nil {
		return nil, errors.New("proto batch response is nil")
	}

	// Map individual responses
	serviceResponses := make([]*domain.PricingServiceResponse, 0, len(protoBatchRes.Responses))
	successCount := 0

	for _, protoRes := range protoBatchRes.Responses {
		// Convert to calculation response first
		calcRes, err := c.mapProtoResponseToDomain(protoRes)
		if err != nil {
			log.Warn("Failed to map individual response", log.Any("error", err))
			continue
		}

		// Convert to service response
		serviceRes := &domain.PricingServiceResponse{
			Success:     calcRes.Success,
			PricingData: calcRes.PricingData,
			ErrorCode:   calcRes.ErrorCode,
			FlightID:    calcRes.PricingData.FlightID,
		}

		if serviceRes.Success {
			successCount++
		}

		serviceResponses = append(serviceResponses, serviceRes)
	}

	return &domain.PricingBatchServiceResponse{
		Success:         protoBatchRes.IsSuccess,
		Responses:       serviceResponses,
		TotalRequests:   len(serviceResponses),
		SuccessfulCount: successCount,
		FailedCount:     len(serviceResponses) - successCount,
	}, nil
}

func toCalculatePenaltyHiddenFeeBatchRequest(in *domain.CalculatePenaltyPricingRequest) *backendPb.CalculatePenaltyPricingBatchRequest {
	if in == nil {
		return nil
	}

	flights := []*backendPb.CalculatePenaltyPricingRequest{}
	for _, flight := range in.Flights {
		if len(flight.MiniRules) == 0 {
			continue
		}

		flights = append(flights, &backendPb.CalculatePenaltyPricingRequest{
			FlightId:      flight.FlightID,
			PartnershipId: in.PartnershipID,
			OfficeId:      in.OfficeID,
			FlightType:    pricePb.FlightType(flight.Type),
			Provider:      int32(flight.Provider),
			Airline:       flight.Airline,
			BookingClass:  flight.BookingClass,
			MiniRules:     toMiniRulesProto(flight.MiniRules),
		})
	}

	return &backendPb.CalculatePenaltyPricingBatchRequest{
		Flights: flights,
	}
}

func toMiniRuleProto(in *domain.MiniRule) *pricePb.MiniRule {
	if in == nil {
		return nil
	}

	return &pricePb.MiniRule{
		PaxType:      basePb.PassengerType(enum.PaxReverseMap[in.PaxType]),
		PenaltyRules: toPenaltyRulesProto(in.PenaltyRules),
	}
}

func toMiniRulesProto(ins []*domain.MiniRule) []*pricePb.MiniRule {
	outs := []*pricePb.MiniRule{}
	for _, in := range ins {
		outs = append(outs, toMiniRuleProto(in))
	}

	return outs
}

func toPenaltyRuleProto(in *domain.PenaltyRule) *pricePb.PenaltyRule {
	if in == nil {
		return nil
	}

	var amount, percent float64
	if in.Amount != nil {
		amount = *in.Amount
	}

	if in.Percent != nil {
		percent = *in.Percent
	}

	return &pricePb.PenaltyRule{
		PenaltyType: pricePb.PenaltyType(commonEnum.PenaltyReverseMap[commonEnum.PenaltyType(in.PenaltyType)]),
		IsPermitted: pricePb.MiniRulePermitted(enum.PermittedReverseMap[in.IsPermitted]),
		Amount:      amount,
		Percent:     percent,
		Currency:    in.Currency,
	}
}

func toPenaltyRulesProto(ins []*domain.PenaltyRule) []*pricePb.PenaltyRule {
	outs := []*pricePb.PenaltyRule{}
	for _, in := range ins {
		outs = append(outs, toPenaltyRuleProto(in))
	}

	return outs
}

func filteMiniRule(ins []*pricePb.MiniRule, outs []*domain.MiniRule) {
	if len(ins) != len(outs) {
		log.Error("Error while filteMiniRule", log.Any("ins", ins), log.Any("outs", outs))
		return
	}

	for i, out := range outs {
		if len(out.PenaltyRules) != len(ins[i].PenaltyRules) {
			log.Error("Error while filteMiniRule", log.Any("ins", ins), log.Any("outs", outs))
			continue
		}

		for j, penaltyRule := range out.PenaltyRules {
			penaltyRule.Amount = &ins[i].PenaltyRules[j].Amount
		}
	}
}

func fromPenaltyRuleProto(in *pricePb.PenaltyRule) *domain.PenaltyRule {
	if in == nil {
		return nil
	}

	return &domain.PenaltyRule{
		PenaltyType: enum.PenaltyTypeMap[int(in.PenaltyType)],
		IsPermitted: enum.PermittedMap[int(in.IsPermitted)],
		Amount:      &in.Amount,
		Percent:     &in.Percent,
		Currency:    in.Currency,
	}
}

func fromPenaltyRulesProto(ins []*pricePb.PenaltyRule) []*domain.PenaltyRule {
	outs := make([]*domain.PenaltyRule, 0, len(ins))
	for _, in := range ins {
		outs = append(outs, fromPenaltyRuleProto(in))
	}
	return outs
}

func fromMiniRuleProto(in *pricePb.MiniRule) *domain.MiniRule {
	if in == nil {
		return nil
	}

	paxType := enum.PaxType(enum.PaxTypeMap[int(in.PaxType)])

	return &domain.MiniRule{
		PaxType:      paxType,
		PenaltyRules: fromPenaltyRulesProto(in.PenaltyRules),
	}
}

func fromMiniRulesProto(ins []*pricePb.MiniRule) []*domain.MiniRule {
	outs := make([]*domain.MiniRule, 0, len(ins))
	for _, in := range ins {
		outs = append(outs, fromMiniRuleProto(in))
	}
	return outs
}

func fromAppliedHiddenFeeProto(in *backendPb.AppliedPenaltyHiddenFee) *domain.PenaltyHiddenFee {
	if in == nil {
		return nil
	}

	return &domain.PenaltyHiddenFee{
		ID:            in.HiddenFee.Id,
		Amount:        in.HiddenFee.Amount,
		Percent:       in.HiddenFee.Percent,
		PenaltyType:   commonEnum.PenaltyTypeMap[int(in.HiddenFee.PenaltyType)],
		AppliedAmount: in.AppliedAmount,
	}
}

func fromAppliedHiddenFeesProto(ins []*backendPb.AppliedPenaltyHiddenFee) []*domain.PenaltyHiddenFee {
	outs := make([]*domain.PenaltyHiddenFee, 0, len(ins))
	for _, in := range ins {
		outs = append(outs, fromAppliedHiddenFeeProto(in))
	}

	return outs
}
