package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/order"
	backendPb "gitlab.deepgate.io/apps/api/gen/go/order/backend"
	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func FromDomainCustomerInfo(info *domain.CustomerInfo) *backendPb.OrderCustomerInfo {
	if info != nil {
		return nil
	}
	idType := order.IdType(info.IDType)

	return &backendPb.OrderCustomerInfo{
		Salutation:     info.Salutation,
		Name:           info.Name,
		FirstName:      info.FirstName,
		LastName:       info.LastName,
		IdType:         &idType,
		IdNumber:       &info.IDNumber,
		Email:          info.Email,
		PhoneCode:      info.PhoneCode,
		PhoneNumber:    info.PhoneNumber,
		IsChildren:     &info.IsChildren,
		IdGrantCountry: info.IDGrantCountry,
		IdExpiredAt:    info.IDExpiredAt,
		Nationality:    info.Nationality,
		Dob:            info.DOB,
		Gender:         base.GENDER(info.DOB),
	}
}

func FromDomainCustomersInfo(items []*domain.CustomerInfo) []*backendPb.OrderCustomerInfo {
	result := make([]*backendPb.OrderCustomerInfo, 0, len(items))
	for _, v := range items {
		result = append(result, FromDomainCustomerInfo(v))
	}

	return result
}

func FromDomainCurrency(info *domain.OrderCurrencyInfo) *backendPb.OrderCurrencyInfo {
	if info == nil {
		return nil
	}
	return &backendPb.OrderCurrencyInfo{
		Id:   info.Id,
		Name: info.Name,
		Code: info.Code,
		Icon: info.Icon,
	}
}

func FromDomainFlightOrder(info *domain.FlightOrder) *backendPb.FlightOrder {
	if info == nil {
		return nil
	}
	return &backendPb.FlightOrder{
		UserId:      info.UserID,
		BookingId:   info.BookingID,
		Service:     info.Service,
		Currency:    FromDomainCurrency(info.Currency),
		TotalAmount: info.TotalPrice,
		Customer:    FromDomainCustomerInfo(info.CustomerInfo),
		Passengers:  FromDomainCustomersInfo(info.PassengersInfo),
		Note:        info.Note,
	}
}

func ToDomainTransactionInfo(info *backendPb.PlaceOrderRes) *domain.TransactionInfo {
	if info == nil || info.TransactionInfo == nil {
		return nil
	}

	return &domain.TransactionInfo{
		ID:        info.TransactionInfo.Id,
		Type:      enum.TransactionType(info.TransactionInfo.Type),
		Amount:    info.TransactionInfo.Amount,
		CreatedAt: info.TransactionInfo.CreatedAt,
	}
}
