package order

import (
	"context"

	"github.com/pkg/errors"
	backendPb "gitlab.deepgate.io/apps/api/gen/go/order/backend"
	"gitlab.deepgate.io/apps/common/enum"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/order/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"google.golang.org/grpc/metadata"
)

type orderServiceClient struct {
	cfg *config.Schema
}

type OrderServiceClient interface {
	CreateFlightOrder(ctx context.Context, order *domain.FlightOrder) (string, error)
	PlaceFlightOrder(ctx context.Context, orderID string, shopInfo *domain.PartnerShopInfo, provider string, bookingDetails *domain.BookingSession) (*domain.TransactionInfo, error)
}

func NewOrderServiceClient(cfg *config.Schema) OrderServiceClient {
	return &orderServiceClient{
		cfg: cfg,
	}
}

func (s *orderServiceClient) CreateFlightOrder(ctx context.Context, order *domain.FlightOrder) (string, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.OrderServiceEndpoint)
	if err != nil {
		return "", errors.Wrap(err, "Cannot connect order service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, s.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := backendPb.NewOrderServiceClient(conn)
	res, err := client.CreateFlightOrder(
		newCtx,
		&backendPb.CreateFlightOrderReq{
			Order: converts.FromDomainFlightOrder(order),
		},
	)
	if err != nil {
		return "", errors.Wrap(err, "CreateFlightOrder failed")
	}

	return res.Id, nil
}

func (s *orderServiceClient) PlaceFlightOrder(ctx context.Context, orderID string, shopInfo *domain.PartnerShopInfo, provider string, bookingDetails *domain.BookingSession) (*domain.TransactionInfo, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.OrderServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "Cannot connect order service")
	}
	defer conn.Close()

	if shopInfo == nil {
		return nil, errors.Wrap(commonError.ErrInvalidInput, "shopInfo nil")
	}

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, s.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := backendPb.NewOrderServiceClient(conn)

	// Prepare PlaceOrderReq with enhanced data
	placeOrderReq := s.buildPlaceOrderRequest(orderID, shopInfo, provider, bookingDetails)

	res, err := client.PlaceOrder(newCtx, placeOrderReq)

	if err != nil {
		return nil, errors.Wrap(err, "PlaceFlightOrder failed")
	}

	if !res.IsSuccess {
		if res.ErrorCode == "INSUFFICIENT_FUNDS" {
			return nil, domain.ErrInsufficientBalance
		}

		return nil, errors.New(res.ErrorCode)
	}

	return converts.ToDomainTransactionInfo(res), nil
}

// buildPlaceOrderRequest builds PlaceOrderReq with current and future fields
func (s *orderServiceClient) buildPlaceOrderRequest(orderID string, shopInfo *domain.PartnerShopInfo, provider string, bookingDetails *domain.BookingSession) *backendPb.PlaceOrderReq {
	req := &backendPb.PlaceOrderReq{
		OrderId:      orderID,
		CustomerId:   shopInfo.OwnerID,
		CustomerName: shopInfo.Code,
		AdditionData: &backendPb.AdditionData{
			HubMetadata: &backendPb.HubMetadata{
				AgentCode: shopInfo.Code,
				AgentName: shopInfo.Name,
				Provider:  provider,
			},
		},
	}

	req.AdditionData.PriceBreakdown = s.createBreakdown(bookingDetails)
	req.AdditionData.ReferenceCode = bookingDetails.BookingCode
	req.AdditionData.Source = int64(enum.TransactionSourceHubFlight)
	req.AdditionData.SourceRef = bookingDetails.ID
	req.AdditionData.PartnerShopId = shopInfo.ID
	// req.AdditionData.SenderInfo = s.createSenderInfo(shopInfo, bookingDetails)

	return req
}

func (s *orderServiceClient) createBreakdown(bookingDetails *domain.BookingSession) *backendPb.PriceBreakdown {
	if bookingDetails == nil || bookingDetails.FareDataCf == nil || bookingDetails.FareDataCf.PricingTracking == nil {
		return nil
	}

	breakdown := &backendPb.PriceBreakdown{
		BasePrice: bookingDetails.FareDataCf.PricingTracking.OriginalTotalFareAmount,
		HiddenFee: bookingDetails.FareDataCf.PricingTracking.TotalHiddenFeeAmount,
		Discount:  bookingDetails.FareDataCf.PricingTracking.TotalDiscountAmount,
	}

	return breakdown
}
