package payment

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"

	"gitlab.deepgate.io/apps/api/gen/go/payment"
	paymentPb "gitlab.deepgate.io/apps/api/gen/go/payment/backend"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"google.golang.org/grpc/metadata"
)

type PaymentClient interface {
	GetPaymentMethod(ctx context.Context, method commonEnum.PaymentMethod) (*domain.PaymentMethod, error)
}

type paymentClient struct {
	cfg *config.Schema
}

func NewPaymentClient(cfg *config.Schema) PaymentClient {
	return &paymentClient{
		cfg: cfg,
	}
}

func (s paymentClient) GetPaymentMethod(ctx context.Context, method commonEnum.PaymentMethod) (*domain.PaymentMethod, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.PaymentServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, s.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)
	client := paymentPb.NewPaymentServiceClient(conn)

	res, err := client.GetPaymentMethod(newCtx, &paymentPb.GetPaymentMethodReq{Method: payment.PaymentMethod(method)})
	if err != nil {
		return nil, errors.Wrap(err, "GetPaymentMethod")
	}

	return &domain.PaymentMethod{
		ID:         res.PaymentMethod.Id,
		Code:       res.PaymentMethod.Code,
		Name:       res.PaymentMethod.Name,
		Method:     commonEnum.PaymentMethod(res.PaymentMethod.Method),
		Fee:        res.PaymentMethod.Fee,
		DisplayFee: res.PaymentMethod.DisplayFee,
		Icon:       res.PaymentMethod.Icon,
	}, nil
}
