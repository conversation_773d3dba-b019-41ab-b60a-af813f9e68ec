package utils

import (
	"fmt"
	"strconv"
	"time"
)

const FullDateLayout = "2006-01-02T15:04:05"
const DateLayout = "2006-01-02"
const ServiceDateFormat = "02Jan06"
const ServiceDateTimeFormat = "02Jan/1504"

func TimeToStringWithFormat(t int64, format string) string {
	return time.UnixMilli(t).UTC().Format(format)
}

func TimeToString(t int64) string {
	return time.UnixMilli(t).UTC().Format(FullDateLayout)
}

func TimeToStringForSearch(t int64) string {
	return fmt.Sprintf("%sT00:00:00", time.UnixMilli(t).UTC().Format(DateLayout))
}

func ParseTime(date string) (*time.Time, error) {
	timer, err := time.Parse(FullDateLayout, date)
	if err != nil {
		timer, err = time.Parse(DateLayout, date)
		if err != nil {
			return nil, err
		}
	}

	return &timer, nil
}

func ParseTimeWithOffset(date string, offset string) (*time.Time, *time.Time, error) {
	iOffset, err := strconv.ParseFloat(offset, 64)
	if err != nil {
		return nil, nil, err
	}

	// with timezone
	timeWithOffset, err := time.Parse(FullDateLayout, date)
	if err != nil {
		return nil, nil, err
	}

	timezoneOffset := time.Duration(float64(time.Hour) * iOffset)
	timeAtUTC := timeWithOffset.Add(-timezoneOffset)

	return &timeAtUTC, &timeWithOffset, nil
}
