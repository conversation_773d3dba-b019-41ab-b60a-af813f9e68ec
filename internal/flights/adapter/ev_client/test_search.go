package ev_client

import (
	"encoding/json"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/SearchFlight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
)

func TestSearchFlightMapping() error {
	byteValue, _ := utils.ReadXMLFile("./internal/flights/adapter/ev_client/docs/search_ev_res.json")

	response := &SearchFlight.Response{}

	err := json.Unmarshal(byteValue, response)

	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	byteValue, _ = utils.ReadXMLFile("./internal/flights/adapter/ev_client/docs/booking_session.json")
	bookingSession := &domain.BookingSession{}
	err = json.Unmarshal(byteValue, bookingSession)
	if err != nil {
		fmt.Println("Error parse", err)

		return err
	}

	// bookingSession, err := mongoDbConvert.ToDomainBookingSession(bookingModel)
	// if err != nil {
	// 	fmt.Println("Error parse", err)

	// 	return err
	// }

	_, itis, err := converts.ToConfirmFare(bookingSession, response)
	if err != nil {
		fmt.Println("Error converts", err)
		return err
	}
	utils.WriteStructToXMLFile(itis, "./logs/ev_iti_parse.xml")
	return nil
}
