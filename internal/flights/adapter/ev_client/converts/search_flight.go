package converts

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"

	evConstants "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/constants"
	evEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/SearchFlight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToSearchFlightReq(req *domain.SearchFlightsRequest) (*SearchFlight.Request, error) {
	if req == nil || len(req.Itineraries) == 0 {
		return nil, fmt.Errorf("ToSearchFlightReq: req == nil || len(req.Itineraries) == 0")
	}

	if !req.IsRoundTrip() && len(req.Itineraries) > 1 {
		return nil, fmt.Errorf("multi-stop flight")
	}

	searchType := evEnum.RouteTypeOneWay
	if req.IsRoundTrip() {
		searchType = evEnum.RouteTypeRoundTrip
	}

	routes := []*SearchFlight.Route{}

	for idx, itinerary := range req.Itineraries {
		routes = append(routes, &SearchFlight.Route{
			RouteNo:       idx + 1,
			DepartureCode: itinerary.DepartPlace,
			ArrivalCode:   itinerary.ArrivalPlace,
			DepartureDate: utils.TimeToStringWithFormat(itinerary.DepartDate, utils.DateLayout),
		})
	}

	result := &SearchFlight.Request{
		SearchType:              searchType,
		NumberOfAdult:           req.Passengers.ADT,
		NumberOfChildren:        req.Passengers.CHD,
		NumberOfInfant:          req.Passengers.INF,
		Routes:                  routes,
		IncludeConnectingFlight: false,
	}

	return result, nil
}

func getListItiByLegMap(data *SearchFlight.Response, paxRequest *domain.PaxRequest) (map[int][]*domain.FlightItinerary, error) {
	itineraries := map[int][]*domain.FlightItinerary{}

	for _, route := range data.Routes {
		itineraries[route.RouteNo] = []*domain.FlightItinerary{}
		for _, flight := range route.Flights {
			departTime, err := utils.ParseTime(flight.DepartureDate[:19])
			if err != nil {
				return nil, err
			}

			arrivalTime, err := utils.ParseTime(flight.ArrivalDate[:19])
			if err != nil {
				return nil, err
			}

			flightNumber := flight.FlightCode[2:]

			segments := []*domain.ItinerarySegment{}
			for idx, stopInfo := range flight.StopInfos {
				departTime, err := utils.ParseTime(stopInfo.DepartureDate[:19])
				if err != nil {
					return nil, err
				}

				arrivalTime, err := utils.ParseTime(stopInfo.ArrivalDate[:19])
				if err != nil {
					return nil, err
				}

				flightNumber := stopInfo.FlightCode[2:]
				carrierMarketing := stopInfo.FlightCode
				if len(stopInfo.FlightCode) > 2 {
					carrierMarketing = stopInfo.FlightCode[:2]
				}

				duration := stopInfo.Duration

				if duration == 0 {
					duration = int(arrivalTime.UnixMilli()-departTime.UnixMilli()) / int(time.Minute/time.Millisecond)
				}

				segments = append(segments, &domain.ItinerarySegment{
					Index:            idx + 1,
					DepartPlace:      stopInfo.DepartureCode,
					ArrivalPlace:     stopInfo.ArrivalCode,
					DepartDate:       departTime.UnixMilli(),
					ArrivalDate:      arrivalTime.UnixMilli(),
					ArrivalDt:        helpers.ToUTCDateTime(arrivalTime.UnixMilli()),
					DepartDt:         helpers.ToUTCDateTime(departTime.UnixMilli()),
					CarrierMarketing: carrierMarketing,
					CarrierOperator:  evConstants.ToDomainCarrierOperator(stopInfo.FlightAirline),
					Aircraft:         constants.GetAirCraftName(stopInfo.AirPlaneModel),
					FlightDuration:   duration,
					FlightNumber:     flightNumber,
				})
			}

			duration := flight.TotalDuration

			if duration == 0 {
				duration = int(arrivalTime.UnixMilli()-departTime.UnixMilli()) / int(time.Minute/time.Millisecond)
			}

			if len(segments) == 0 {
				carrierMarketing := flight.FlightCode
				if len(flight.FlightCode) > 2 {
					carrierMarketing = flight.FlightCode[:2]
				}
				segments = append(segments, &domain.ItinerarySegment{
					Index:            1,
					DepartPlace:      flight.DepartureCode,
					ArrivalPlace:     flight.ArrivalCode,
					DepartDate:       departTime.UnixMilli(),
					ArrivalDate:      arrivalTime.UnixMilli(),
					ArrivalDt:        helpers.ToUTCDateTime(arrivalTime.UnixMilli()),
					DepartDt:         helpers.ToUTCDateTime(departTime.UnixMilli()),
					CarrierMarketing: carrierMarketing,
					CarrierOperator:  evConstants.ToDomainCarrierOperator(flight.FlightAirline),
					Aircraft:         constants.GetAirCraftName(flight.FlightType),
					FlightDuration:   duration,
					FlightNumber:     flightNumber,
				})
			}

			for _, class := range flight.Inventories {
				if class.TotalPrice > 0 {
					paxFare := []*domain.ItineraryPaxFare{}

					if paxRequest.ADT > 0 && class.AdultFare != nil && class.AdultFare.Price > 0 {
						paxFare = append(paxFare, &domain.ItineraryPaxFare{
							PaxType:    enum.PaxTypeAdult,
							FareAmount: float64(class.AdultFare.Price),
							FareBasic:  float64(class.AdultFare.Fare),
							TaxAmount:  float64(class.AdultFare.Charge),
							Currency:   class.Currency,
						})
					}

					if paxRequest.CHD > 0 && class.ChildFare != nil && class.ChildFare.Price > 0 {
						paxFare = append(paxFare, &domain.ItineraryPaxFare{
							PaxType:    enum.PaxTypeChildren,
							FareAmount: float64(class.ChildFare.Price),
							FareBasic:  float64(class.ChildFare.Fare),
							TaxAmount:  float64(class.ChildFare.Charge),
							Currency:   class.Currency,
						})
					}

					if paxRequest.INF > 0 && class.InfantFare != nil && class.InfantFare.Price > 0 {
						paxFare = append(paxFare, &domain.ItineraryPaxFare{
							PaxType:    enum.PaxTypeInfant,
							FareAmount: float64(class.InfantFare.Price),
							FareBasic:  float64(class.InfantFare.Fare),
							TaxAmount:  float64(class.InfantFare.Charge),
							Currency:   class.Currency,
						})
					}

					itineraryID := uuid.New().String()
					classSegments := []*domain.ItinerarySegment{}

					for _, segment := range segments {
						segment.ItineraryID = itineraryID
						segment.FareBasis = class.FareBasisCode
						segment.CabinClassCode = class.FareType
						segment.BookingClass = class.SeatClass

						segmentClone := &domain.ItinerarySegment{}
						*segmentClone = *segment
						classSegments = append(classSegments, segmentClone)
					}

					carrierMarketing := flight.FlightCode

					if len(flight.FlightCode) > 2 {
						carrierMarketing = flight.FlightCode[:2]
					}
					carrierOperator := evConstants.ToDomainCarrierOperator(flight.FlightAirline)

					freeBaggage := mapFreeBaggage(flight.DepartureCode, flight.ArrivalCode, class.FareType, class.SeatClass, carrierOperator, carrierMarketing, paxRequest)
					itineraries[route.RouteNo] = append(itineraries[route.RouteNo], &domain.FlightItinerary{
						ID:               itineraryID,
						Index:            route.RouteNo,
						FareBasis:        class.FareBasisCode,
						CabinClass:       class.FareType,
						CabinClassCode:   class.FareType,
						BookingClass:     class.SeatClass,
						Availability:     class.Available,
						DepartPlace:      flight.DepartureCode,
						ArrivalPlace:     flight.ArrivalCode,
						DepartDate:       departTime.UnixMilli(),
						ArrivalDate:      arrivalTime.UnixMilli(),
						CarrierMarketing: carrierMarketing,
						CarrierOperator:  carrierOperator,
						FlightDuration:   duration,
						FlightNumber:     flightNumber,
						Segments:         classSegments,
						PaxFares:         paxFare,
						FreeBaggage:      freeBaggage,
						FareBasic:        float64(class.TotalFare),
						FareAmount:       float64(class.TotalPrice),
						TaxAmount:        float64(class.TotalCharge),
						StopNumber:       flight.StopNo,
						Currency:         class.Currency,
					})
				}
			}
		}
	}

	return itineraries, nil
}

func ToSearchFlightRes(data *SearchFlight.Response, airline string, paxRequest *domain.PaxRequest, flightType string) ([]*domain.ResponseFlight, error) {
	if data == nil {
		return []*domain.ResponseFlight{}, nil
	}

	if paxRequest == nil {
		return nil, fmt.Errorf("ToSearchFlightRes: paxRequest == nil")
	}

	response := []*domain.ResponseFlight{}

	itineraries, err := getListItiByLegMap(data, paxRequest)
	if err != nil {
		return nil, errors.Wrap(err, "getListItiByLegMap")
	}

	if value, ok := itineraries[1]; ok && len(value) > 0 {
		idx := 1
		for _, iti := range value {
			if valueTwo, ok := itineraries[2]; ok && len(valueTwo) > 0 {
				for _, itiTwo := range valueTwo {
					if itiTwo.DepartDate-iti.ArrivalDate >= 3*time.Hour.Milliseconds() {
						paxFares := []*domain.ItineraryPaxFare{}
						if len(iti.PaxFares) != len(itiTwo.PaxFares) {
							continue
						}

						for idx, paxFare := range iti.PaxFares {
							paxFares = append(paxFares, &domain.ItineraryPaxFare{
								PaxType:    paxFare.PaxType,
								FareAmount: paxFare.FareAmount + itiTwo.PaxFares[idx].FareAmount,
								FareBasic:  paxFare.FareBasic + itiTwo.PaxFares[idx].FareBasic,
								TaxAmount:  paxFare.TaxAmount + itiTwo.PaxFares[idx].TaxAmount,
								Currency:   paxFare.Currency,
							})
						}

						response = append(response, &domain.ResponseFlight{
							Index:       idx,
							FlightID:    helpers.GenerateFlightID(enum.FlightProviderEV),
							Provider:    enum.FlightProviderEV,
							Itineraries: []*domain.FlightItinerary{iti, itiTwo},
							SearchTotalFareInfo: domain.SearchTotalFareInfo{
								TotalPaxFares:       paxFares,
								BaseTotalFareAmount: itiTwo.FareAmount + iti.FareAmount,
								TotalFareBasic:      itiTwo.FareBasic + iti.FareBasic,
								TotalTaxAmount:      iti.TaxAmount + itiTwo.TaxAmount,
								Currency:            iti.Currency,
							},
							AirlineSystem: airline,
							OptionType:    enum.FlightOptionTypeRecommend,
						})

						idx++
					}
				}
			} else if flightType == evEnum.RouteTypeOneWay {
				response = append(response, &domain.ResponseFlight{
					FlightID:    helpers.GenerateFlightID(enum.FlightProviderEV),
					Index:       idx,
					Provider:    enum.FlightProviderEV,
					Itineraries: []*domain.FlightItinerary{iti},
					SearchTotalFareInfo: domain.SearchTotalFareInfo{
						TotalPaxFares:       iti.PaxFares,
						BaseTotalFareAmount: iti.FareAmount,
						TotalFareBasic:      iti.FareBasic,
						TotalTaxAmount:      iti.TaxAmount,
						Currency:            iti.Currency,
					},
					AirlineSystem: airline,
					OptionType:    enum.FlightOptionTypeRecommend,
				})

				idx++
			}
		}
	}

	return response, nil
}

func mapFreeBaggage(departPlace, arrivalPlace, cabinClass, bookingClass, carrierOperator, carrierMarketing string, paxRequest *domain.PaxRequest) []*domain.BaggageInfo {
	var freeBaggage []*domain.BaggageInfo

	listPaxType := make([]enum.PaxType, 0)
	if paxRequest.ADT > 0 {
		listPaxType = append(listPaxType, enum.PaxTypeAdult)
	}
	if paxRequest.CHD > 0 {
		listPaxType = append(listPaxType, enum.PaxTypeChildren)
	}

	hasStopoverInVCS := departPlace == "VCS" || arrivalPlace == "VCS"
	switch carrierMarketing {
	case evConstants.VNAirline:
		freeBaggage = evConstants.GetVNABaggage(carrierOperator, bookingClass, listPaxType)
	case evConstants.VJAirline:
		freeBaggage = evConstants.GetVJBaggage(cabinClass, listPaxType)
	case evConstants.BamBooAirways:
		freeBaggage = evConstants.GetQHBaggage(cabinClass, hasStopoverInVCS, listPaxType)
	case evConstants.VietravelAirline:
		freeBaggage = evConstants.GetVUBaggage(listPaxType)
	default:
		return nil
	}

	return freeBaggage
}
