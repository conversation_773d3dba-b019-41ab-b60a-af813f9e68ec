package converts

import (
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/IssueTicket"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func MapETicket(data *IssueTicket.GetBookingRes, itineraries []*domain.FlightItinerary, pnr []*domain.PaxInfo) ([]*domain.ETicketInfo, error) {
	result := []*domain.ETicketInfo{}

	if data == nil || len(data.Tickets) == 0 {
		return nil, nil
	}

	paxTicketMap := map[string]*IssueTicket.TicketInfo{}

	for _, item := range data.Tickets {
		paxTicketMap[helpers.GetPaxKey(item.LastName, item.FirstName, 0)] = item
	}

	for _, paxInfo := range pnr {
		paxTicket := paxTicketMap[helpers.GetPaxKey(paxInfo.Surname, paxInfo.GivenName, 0)]

		if paxTicket != nil && strings.EqualFold(paxTicket.FirstName, paxInfo.GivenName) && strings.EqualFold(paxTicket.LastName, paxInfo.Surname) {
			for _, itinerary := range itineraries {
				result = append(result, &domain.ETicketInfo{
					PaxID:           paxInfo.ID,
					ItineraryNumber: itinerary.Index,
					TicketNumber:    paxTicket.TicketNumber,
				})
			}
		}
	}

	return result, nil
}
