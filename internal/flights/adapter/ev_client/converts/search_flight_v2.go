package converts

import (
	"strings"

	"github.com/pkg/errors"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/SearchFlight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func ToSearchFlightResV2(data *SearchFlight.Response, airline string, paxRequest *domain.PaxRequest, flightType string) ([]*domain.ResponseFlight, error) {
	if data == nil {
		return []*domain.ResponseFlight{}, nil
	}

	if paxRequest == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	response := []*domain.ResponseFlight{}

	groupSecMap := map[string]int{
		"VNA":       1,
		"BAMBOO":    2,
		"VIETRAVEL": 3,
		"VIETJET":   4,
		"VJ":        4,
	}

	groupSec := groupSecMap[strings.ToUpper(airline)]

	itinerariesMap, err := getListItiByLegMap(data, paxRequest)
	if err != nil {
		return nil, errors.Wrap(err, "getListItiByLegMap")
	}

	for leg, ities := range itinerariesMap {
		if len(ities) == 0 {
			return []*domain.ResponseFlight{}, nil
		}

		for _, iti := range ities {
			paxFares := []*domain.ItineraryPaxFare{}

			for _, paxFare := range iti.PaxFares {
				paxFares = append(paxFares, &domain.ItineraryPaxFare{
					PaxType:    paxFare.PaxType,
					FareAmount: paxFare.FareAmount,
					FareBasic:  paxFare.FareBasic,
					TaxAmount:  paxFare.TaxAmount,
					Currency:   paxFare.Currency,
				})
			}

			rf := &domain.ResponseFlight{
				FlightID:    helpers.GenerateFlightID(enum.FlightProviderEV),
				Provider:    enum.FlightProviderEV,
				Itineraries: []*domain.FlightItinerary{iti},
				SearchTotalFareInfo: domain.SearchTotalFareInfo{
					TotalPaxFares:       paxFares,
					BaseTotalFareAmount: iti.FareAmount,
					TotalFareBasic:      iti.FareBasic,
					TotalTaxAmount:      iti.TaxAmount,
					Currency:            iti.Currency,
				},
				AirlineSystem: airline,
				OptionType:    enum.FlightOptionSingleTrip,
				GroupID:       utils.GetFlightGroupID(enum.FlightProviderEV, groupSec),
				Leg:           leg,
			}

			response = append(response, rf)
		}
	}

	return response, nil
}
