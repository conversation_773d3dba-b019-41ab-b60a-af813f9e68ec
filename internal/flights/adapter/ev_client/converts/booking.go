package converts

import (
	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/IssueTicket"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

func SyncBooking(pBk *IssueTicket.GetBookingRes, dBk *domain.BookingSession) (bool, error) {
	hasChanges := false

	pSegIDMap := map[int]*IssueTicket.FlightInfo{}

	for _, flight := range pBk.Flights {
		pSegIDMap[flight.RouteNo] = flight
	}

	step := 0
	for _, iti := range dBk.Itineraries {
		itiChanges := false

		for _, seg := range iti.Segments {
			segID := step + seg.Index

			pSeg := pSegIDMap[segID]

			pSegDepartDate, err := utils.ParseTime(pSeg.DepartureDate)
			if err != nil {
				return false, errors.Wrap(err, "time.Parse depart")
			}

			pSegArrivalDate, err := utils.ParseTime(pSeg.ArrivalDate)
			if err != nil {
				return false, errors.Wrap(err, "time.Parse depart")
			}

			if pSegDepartDate.UnixMilli() != seg.DepartDate || pSegArrivalDate.UnixMilli() != seg.ArrivalDate {
				hasChanges = true
				itiChanges = true

				seg.DepartDate = pSegDepartDate.UnixMilli()
				seg.ArrivalDate = pSegArrivalDate.UnixMilli()
				seg.DepartDt = helpers.ToUTCDateTime(seg.DepartDate)
				seg.ArrivalDt = helpers.ToUTCDateTime(seg.ArrivalDate)
			}
		}
		// Post processing
		if itiChanges {
			var err error

			firstSeg := iti.Segments[0]
			lastSeg := iti.Segments[len(iti.Segments)-1]

			iti.DepartDate = firstSeg.DepartDate
			iti.ArrivalDate = lastSeg.ArrivalDate

			iti.DepartDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.DepartDate, "", iti.DepartDt.Location())
			if err != nil {
				return false, err
			}

			iti.ArrivalDt, err = helpers.ParseFakeUTCToRealTimeWithTz(iti.ArrivalDate, "", iti.ArrivalDt.Location())
			if err != nil {
				return false, err
			}

			iti.FlightDuration = int(iti.ArrivalDt.Sub(iti.DepartDt).Minutes())
		}
		step += len(iti.Segments)
	}

	return hasChanges, nil
}
