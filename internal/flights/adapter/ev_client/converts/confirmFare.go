package converts

import (
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/SearchFlight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	domainEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func ToConfirmFare(booking *domain.BookingSession, data *SearchFlight.Response) (*domain.TotalFareInfo, []*domain.FlightItinerary, error) {
	if booking == nil || data == nil || booking.SearchRequest == nil {
		return nil, nil, fmt.Errorf("ToConfirmFare:  booking == nil || data == nil || booking.SearchRequest == nil")
	}

	itiFares := []*domain.TotalFareInfo{}
	ities := []*domain.FlightItinerary{}

	searchReq := booking.SearchRequest

	for idx, route := range data.Routes {
		for _, flight := range route.Flights {
			if idx != len(itiFares) {
				break
			}

			if len(flight.DepartureDate) < 19 {
				log.Error("flight.DepartureDate invalid", log.String("flight.DepartureDate", flight.DepartureDate))
				return nil, nil, fmt.Errorf("ToConfirmFare:  len(flight.DepartureDate) < 19")
			}

			departTime, err := utils.ParseTime(flight.DepartureDate[:19])
			if err != nil {
				log.Error("Parse time error flight.DepartureDate", log.Any("err", err))
				return nil, nil, err
			}

			if len(flight.ArrivalDate) < 19 {
				log.Error("flight.ArrivalDate invalid", log.String("flight.ArrivalDate", flight.ArrivalDate))
				return nil, nil, fmt.Errorf("ToConfirmFare:  len(flight.ArrivalDate) < 19 ")
			}

			arrivalTime, err := utils.ParseTime(flight.ArrivalDate[:19])
			if err != nil {
				log.Error("Parse time error flight.ArrivalDate", log.Any("err", err))
				return nil, nil, err
			}

			flightNumber := flight.FlightCode[2:]

			for _, iti := range booking.Itineraries {
				if departTime.UnixMilli() == iti.DepartDate && arrivalTime.UnixMilli() == iti.ArrivalDate && flightNumber == iti.FlightNumber {
					for _, class := range flight.Inventories {
						if class.TotalPrice > 0 {
							if iti.BookingClass == class.SeatClass {
								paxFare := []*domain.ItineraryPaxFare{}
								totalFareAmount := 0.0
								totalFareBasic := 0.0
								totalTaxAmount := 0.0
								// itiData := &domain.FlightItinerary{
								// 	Index:    idx + 1,
								// 	PaxFares: []*domain.ItineraryPaxFare{},
								// }

								if searchReq.Passengers.ADT > 0 && class.AdultFare != nil && class.AdultFare.Price > 0 {
									fareData := &domain.ItineraryPaxFare{
										PaxType:    domainEnum.PaxTypeAdult,
										FareAmount: float64(class.AdultFare.Price),
										FareBasic:  float64(class.AdultFare.Fare),
										TaxAmount:  float64(class.AdultFare.Charge),
										Currency:   class.Currency,
									}

									paxFare = append(paxFare, fareData)
									// itiData.PaxFares = append(itiData.PaxFares, fareData)
									totalFareAmount += fareData.FareAmount * float64(searchReq.Passengers.ADT)
									totalFareBasic += fareData.FareBasic * float64(searchReq.Passengers.ADT)
									totalTaxAmount += fareData.TaxAmount * float64(searchReq.Passengers.ADT)
								}

								if searchReq.Passengers.CHD > 0 && class.ChildFare != nil && class.ChildFare.Price > 0 {
									fareData := &domain.ItineraryPaxFare{
										PaxType:    domainEnum.PaxTypeChildren,
										FareAmount: float64(class.ChildFare.Price),
										FareBasic:  float64(class.ChildFare.Fare),
										TaxAmount:  float64(class.ChildFare.Charge),
										Currency:   class.Currency,
									}

									paxFare = append(paxFare, fareData)
									// itiData.PaxFares = append(itiData.PaxFares, fareData)
									totalFareAmount += fareData.FareAmount * float64(searchReq.Passengers.CHD)
									totalFareBasic += fareData.FareBasic * float64(searchReq.Passengers.CHD)
									totalTaxAmount += fareData.TaxAmount * float64(searchReq.Passengers.CHD)
								}

								if searchReq.Passengers.INF > 0 && class.InfantFare != nil && class.InfantFare.Price > 0 {
									fareData := &domain.ItineraryPaxFare{
										PaxType:    domainEnum.PaxTypeInfant,
										FareAmount: float64(class.InfantFare.Price),
										FareBasic:  float64(class.InfantFare.Fare),
										TaxAmount:  float64(class.InfantFare.Charge),
										Currency:   class.Currency,
									}

									paxFare = append(paxFare, fareData)
									// itiData.PaxFares = append(itiData.PaxFares, fareData)
									totalFareAmount += fareData.FareAmount * float64(searchReq.Passengers.INF)
									totalFareBasic += fareData.FareBasic * float64(searchReq.Passengers.INF)
									totalTaxAmount += fareData.TaxAmount * float64(searchReq.Passengers.INF)
								}

								itiFares = append(itiFares, &domain.TotalFareInfo{
									TotalPaxFares:       paxFare,
									BaseTotalFareAmount: totalFareAmount,
									TotalFareBasic:      totalFareBasic,
									TotalTaxAmount:      totalTaxAmount,
								})
								ities = append(ities, &domain.FlightItinerary{
									Index:    idx + 1,
									PaxFares: paxFare,
								})
								break
							}
						}
					}
				}
			}
		}
	}

	result := &domain.TotalFareInfo{
		TotalPaxFares: []*domain.ItineraryPaxFare{},
	}

	for _, fare := range itiFares {
		result.BaseTotalFareAmount += fare.BaseTotalFareAmount
		result.TotalFareBasic += fare.TotalFareBasic
		result.TotalTaxAmount += fare.TotalTaxAmount

		if len(result.TotalPaxFares) == 0 {
			for _, paxFare := range fare.TotalPaxFares {
				result.TotalPaxFares = append(result.TotalPaxFares, &domain.ItineraryPaxFare{
					PaxType:    paxFare.PaxType,
					FareAmount: paxFare.FareAmount,
					FareBasic:  paxFare.FareBasic,
					TaxAmount:  paxFare.TaxAmount,
					Currency:   paxFare.Currency,
				})
			}
		} else {
			for _, paxFare := range fare.TotalPaxFares {
				if len(result.TotalPaxFares) == 0 {

				} else {
					for _, resultPaxFare := range result.TotalPaxFares {
						if paxFare.PaxType == resultPaxFare.PaxType {
							resultPaxFare.FareAmount += paxFare.FareAmount
							resultPaxFare.FareBasic += paxFare.FareBasic
							resultPaxFare.TaxAmount += paxFare.TaxAmount
						}
					}
				}
			}
		}
	}

	return result, ities, nil
}
