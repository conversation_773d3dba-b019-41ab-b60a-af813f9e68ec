package converts

import (
	"fmt"

	commonEnum "gitlab.deepgate.io/apps/common/enum"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client/utils"
	domainEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/CreateBooking"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToCreateBookingReq(data *domain.BookingDetails, searchReq *domain.SearchFlightsRequest, ities []*domain.FlightItinerary, mailReceiver string) (*CreateBooking.Request, error) {
	titleMap := map[commonEnum.GenderType]string{
		commonEnum.GenderTypeFeMale: "MRS",
		commonEnum.GenderTypeMale:   "MR",
	}

	if data == nil || len(data.Itineraries) == 0 || len(ities) == 0 || len(data.Itineraries) != len(ities) {
		return nil, fmt.Errorf("ToCreateBookingReq: data == nil || len(data.Itineraries) == 0 || len(ities) == 0 || len(data.Itineraries) != len(ities)")
	}

	if !searchReq.IsRoundTrip() && len(data.Itineraries) > 1 {
		return nil, fmt.Errorf("ToCreateBookingReq: Multi-stop flight")
	}

	searchType := enum.RouteTypeOneWay
	if searchReq.IsRoundTrip() {
		searchType = enum.RouteTypeRoundTrip
	}

	request := &CreateBooking.Request{
		BookType:    searchType,
		IsTicketing: false,
		SendEmailBy: "SYSTEMEMAIL",
		Passengers:  []*CreateBooking.PaxInfo{},
		Email:       mailReceiver,
	}

	if data.ContactInfo != nil {
		if data.ContactInfo.Phone != "" {
			request.Phone = fmt.Sprintf("%s%s", data.ContactInfo.PhoneCode, data.ContactInfo.Phone)
		}
	}

	for _, paxInfo := range data.ListPax {
		paxType := string(paxInfo.Type)
		if paxInfo.Type == domainEnum.PaxTypeChildren {
			paxType = "CNN"
		}

		baggages := make([]*CreateBooking.Baggage, 0, len(data.Itineraries))

		for index := range baggages {
			baggages[index] = &CreateBooking.Baggage{
				RouteNo: index + 1,
				Value:   0,
			}
		}

		request.Passengers = append(request.Passengers, &CreateBooking.PaxInfo{
			Type:        paxType,
			Title:       titleMap[paxInfo.Gender],
			FirstName:   paxInfo.GivenName,
			LastName:    paxInfo.Surname,
			DateOfBirth: utils.TimeToStringWithFormat(paxInfo.DOB, utils.DateLayout),
			Baggages:    baggages,
		})
	}

	for idx, itinerary := range data.Itineraries {
		fareInfo := &CreateBooking.FareInfo{
			SeatClass: itinerary.BookingClass,
		}

		for _, paxFare := range ities[idx].PaxFares {
			if paxFare.PaxType == domainEnum.PaxTypeAdult {
				fareInfo.AdultFare = &CreateBooking.FareDetail{
					Fare:   paxFare.FareBasic,
					Charge: paxFare.TaxAmount,
					Price:  paxFare.FareAmount,
				}
			}

			if paxFare.PaxType == domainEnum.PaxTypeChildren {
				fareInfo.ChildFare = &CreateBooking.FareDetail{
					Fare:   paxFare.FareBasic,
					Charge: paxFare.TaxAmount,
					Price:  paxFare.FareAmount,
				}
			}

			if paxFare.PaxType == domainEnum.PaxTypeInfant {
				fareInfo.InfantFare = &CreateBooking.FareDetail{
					Fare:   paxFare.FareBasic,
					Charge: paxFare.TaxAmount,
					Price:  paxFare.FareAmount,
				}
			}
		}

		request.Flights = append(request.Flights, &CreateBooking.Flight{
			RouteNo:       itinerary.Index,
			DepartureCode: itinerary.DepartPlace,
			ArrivalCode:   itinerary.ArrivalPlace,
			FlightCode:    itinerary.CarrierMarketing + itinerary.FlightNumber,
			DepartureDate: utils.TimeToString(itinerary.DepartDate),
			ArrivalDate:   utils.TimeToString(itinerary.ArrivalDate),
			AirlineSystem: data.AirlineSystem,
			FlightAirline: itinerary.CarrierOperator,
			FareInfo:      fareInfo,
		})
	}

	return request, nil
}
