package IssueTicket

type TicketInfo struct {
	TicketNumber string `json:"TicketNumber"`
	FirstName    string `json:"FirstName"`
	LastName     string `json:"LastName"`
	Status       string `json:"Status"`
}

type GetBookingRes struct {
	RecordLocation string        `json:"RecordLocation"`
	RecordStatus   string        `json:"RecordStatus"`
	TicketFace     string        `json:"TicketFace"`
	Phone          string        `json:"Phone"`
	CreatedDate    string        `json:"CreatedDate"`
	HoldExpireDate string        `json:"HoldExpireDate"`
	TicketedDate   string        `json:"TicketedDate"`
	Tickets        []*TicketInfo `json:"Tickets"`
	TotalAmount    float64       `json:"TotalAmount"`
	Result         bool          `json:"Result"`
	Messages       []string      `json:"Messages"`
	ExtendData     struct {
		PaymentOnlineRetriction bool `json:"PaymentOnlineRetriction"`
	} `json:"ExtendData"`
	Flights []*FlightInfo `json:"Flights"`
}

type IssueTicketRes struct {
	Result     bool     `json:"Result"`
	TicketFace string   `json:"TicketFace"`
	Messages   []string `json:"Messages"`
}

type FlightInfo struct {
	RouteNo       int    `json:"RouteNo"`
	DepartureCode string `json:"DepartureCode"`
	ArrivalCode   string `json:"ArrivalCode"`
	FlightCode    string `json:"FlightCode"`
	DepartureDate string `json:"DepartureDate"`
	ArrivalDate   string `json:"ArrivalDate"`
	AirlineSystem string `json:"AirlineSystem"`
	// FlightAirline interface{} `json:"FlightAirline"`
	FareInfo *FareInfo `json:"FareInfo"`
	// StopInfos     interface{} `json:"StopInfos"`
}

type FareInfo struct {
	SeatClass     string   `json:"SeatClass"`
	FareBasisCode string   `json:"FareBasisCode"`
	AdultFare     *PaxFare `json:"AdultFare"`
	ChildFare     *PaxFare `json:"ChildFare"`
	InfantFare    *PaxFare `json:"InfantFare"`
}

type PaxFare struct {
	Fare     float64 `json:"Fare"`
	Charge   float64 `json:"Charge"`
	Price    float64 `json:"Price"`
	Discount float64 `json:"Discount"`
	// TaxDetails interface{} `json:"TaxDetails"`
}
