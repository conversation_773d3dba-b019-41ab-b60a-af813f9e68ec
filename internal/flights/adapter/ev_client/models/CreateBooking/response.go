package CreateBooking

type RecordLocator struct {
	RouteNo        int    `json:"RouteNo"`
	RecordLocation string `json:"RecordLocation"`
	RecordStatus   string `json:"RecordStatus"`
	HoldExpireDate string `json:"HoldExpireDate"`
	TicketedDate   string `json:"TicketedDate"`
}

type Response struct {
	RecordLocations []*RecordLocator `json:"RecordLocations"`
	TicketFace      string           `json:"TicketFace"`
	TotalAmount     float64          `json:"TotalAmount"`
	Result          bool             `json:"Result"`
	Messages        []string         `json:"Messages"`
}
