package CreateBooking

type Baggage struct {
	RouteNo int `json:"RouteNo"`
	Value   int `json:"Value"`
}

type PaxInfo struct {
	Type        string     `json:"Type"`
	Title       string     `json:"Title"`
	FirstName   string     `json:"FirstName"`
	LastName    string     `json:"LastName"`
	DateOfBirth string     `json:"DateOfBirth"`
	Baggages    []*Baggage `json:"Baggages"`
}

type FareDetail struct {
	Fare   float64 `json:"Fare"`
	Charge float64 `json:"Charge"`
	Price  float64 `json:"Price"`
}

type FareInfo struct {
	SeatClass  string    `json:"SeatClass"`
	AdultFare  *FareDetail `json:"AdultFare"`
	ChildFare  *FareDetail `json:"ChildFare"`
	InfantFare *FareDetail `json:"InfantFare"`
}

type Flight struct {
	RouteNo       int       `json:"RouteNo"`
	DepartureCode string    `json:"DepartureCode"`
	ArrivalCode   string    `json:"ArrivalCode"`
	FlightCode    string    `json:"FlightCode"`
	DepartureDate string    `json:"DepartureDate"`
	ArrivalDate   string    `json:"ArrivalDate"`
	AirlineSystem string    `json:"AirlineSystem"`
	FlightAirline string    `json:"FlightAirline"`
	FareInfo      *FareInfo `json:"FareInfo"`
}

type Request struct {
	BookType      string     `json:"BookType"`
	IsTicketing   bool       `json:"IsTicketing"`
	Phone         string     `json:"Phone"`
	PhoneRemark   string     `json:"PhoneRemark"`
	Email         string     `json:"Email"`
	Remark        string     `json:"Remark"`
	SendEmailBy   string     `json:"SendEmailBy"`
	Passengers    []*PaxInfo `json:"Passengers"`
	Flights       []*Flight  `json:"Flights"`
	PromotionCode string     `json:"PromotionCode"`
}
