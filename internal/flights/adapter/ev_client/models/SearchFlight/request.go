package SearchFlight

type Route struct {
	RouteNo       int    `json:"RouteNo"`
	DepartureCode string `json:"DepartureCode"`
	ArrivalCode   string `json:"ArrivalCode"`
	DepartureDate string `json:"DepartureDate"`
}

type Request struct {
	SearchType              string   `json:"SearchType"`
	AirlineSystem           string   `json:"AirlineSystem"`
	NumberOfAdult           int      `json:"NumberOfAdult"`
	NumberOfChildren        int      `json:"NumberOfChildren"`
	NumberOfInfant          int      `json:"NumberOfInfant"`
	Routes                  []*Route `json:"Routes"`
	IncludeConnectingFlight bool     `json:"IncludeConnectingFlight"`
}
