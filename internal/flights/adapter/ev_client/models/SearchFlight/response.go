package SearchFlight

type TaxDetail struct {
	TaxCode          string  `json:"TaxCode"`
	Description      string  `json:"Description"`
	Amount           float64 `json:"Amount"`
	CommissionAmount float64 `json:"CommissionAmount"`
}

type StopInfo struct {
	RouteNo           int    `json:"RouteNo"`
	DepartureCode     string `json:"DepartureCode"`
	ArrivalCode       string `json:"ArrivalCode"`
	AirlineSystem     string `json:"AirlineSystem"`
	FlightAirline     string `json:"FlightAirline"`
	AirlineSystemText string `json:"AirlineSystemText"`
	FlightAirlineText string `json:"FlightAirlineText"`
	DepartureDate     string `json:"DepartureDate"`
	ArrivalDate       string `json:"ArrivalDate"`
	LayoverDuration   int    `json:"LayoverDuration"`
	Duration          int    `json:"Duration"`
	AirPlaneModel     string `json:"AirPlaneModel"`
	FlightCode        string `json:"FlightCode"`
}

type FareInfo struct {
	Fare       float64      `json:"Fare"`
	Charge     float64      `json:"Charge"`
	Price      float64      `json:"Price"`
	Discount   float64      `json:"Discount"`
	TaxDetails []*TaxDetail `json:"TaxDetails"`
}

type Inventory struct {
	SeatClass     string    `json:"SeatClass"`
	FareType      string    `json:"FareType"`
	FareBasisCode string    `json:"FareBasisCode"`
	Available     int       `json:"Available"`
	AdultFare     *FareInfo `json:"AdultFare"`
	ChildFare     *FareInfo `json:"ChildFare"`
	InfantFare    *FareInfo `json:"InfantFare"`
	TotalFare     float64   `json:"TotalFare"`
	TotalCharge   float64   `json:"TotalCharge"`
	TotalPrice    float64   `json:"TotalPrice"`
	Currency      string    `json:"Currency"`
}

type Flight struct {
	DepartureCode     string       `json:"DepartureCode"`
	ArrivalCode       string       `json:"ArrivalCode"`
	FlightCode        string       `json:"FlightCode"`
	DepartureDate     string       `json:"DepartureDate"`
	ArrivalDate       string       `json:"ArrivalDate"`
	TotalDuration     int          `json:"TotalDuration"`
	StopNo            int          `json:"StopNo"`
	AirlineSystem     string       `json:"AirlineSystem"`
	FlightAirline     string       `json:"FlightAirline"`
	AirlineSystemText string       `json:"AirlineSystemText"`
	FlightAirlineText string       `json:"FlightAirlineText"`
	FlightType        string       `json:"FlightType"`
	Inventories       []*Inventory `json:"Inventories"`
	StopInfos         []*StopInfo  `json:"StopInfos"`
}

type Response struct {
	IsMergeFlight bool `json:"IsMergeFlight"`
	Routes        []struct {
		RouteNo int       `json:"RouteNo"`
		Flights []*Flight `json:"Flights"`
	} `json:"Routes"`
	Result   bool     `json:"Result"`
	Messages []string `json:"Messages"`
}
