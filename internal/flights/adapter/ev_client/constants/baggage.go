package constants

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func GetVJBaggage(cabinClass string, listPaxType []enum.PaxType) []*domain.BaggageInfo {
	res := make([]*domain.BaggageInfo, 0)

	switch cabinClass {
	case "Eco":
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	case "Deluxe":
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
				{
					Name:          "20 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	case "Skyboss":
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "10 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
				{
					Name:          "30 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	case "Skyboss-Biz":
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "18 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
				{
					Name:          "40 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	default:
		return res
	}
}

func GetVNABaggage(operatingAirlines, bookingClass string, listPaxType []enum.PaxType) []*domain.BaggageInfo {
	res := make([]*domain.BaggageInfo, 0)
	switch operatingAirlines {
	case "VN":
		switch bookingClass {
		case "A", "P", "G":
			for _, paxType := range listPaxType {
				tempBaggage := []*domain.BaggageInfo{
					{
						Name:          "12 kg",
						IsHandBaggage: true,
						Quantity:      1,
						PaxType:       paxType,
					},
				}
				res = append(res, tempBaggage...)
			}

			return res
		case "Q", "N", "R", "T", "E", "S", "H", "K", "L", "Y", "B", "M", "Z", "U":
			for _, paxType := range listPaxType {
				tempBaggage := []*domain.BaggageInfo{
					{
						Name:          "12 kg",
						IsHandBaggage: true,
						Quantity:      1,
						PaxType:       paxType,
					},
					{
						Name:          "23 kg",
						IsHandBaggage: false,
						Quantity:      1,
						PaxType:       paxType,
					},
				}
				res = append(res, tempBaggage...)
			}

			return res
		case "W", "D", "I", "J", "C":
			for _, paxType := range listPaxType {
				tempBaggage := []*domain.BaggageInfo{
					{
						Name:          "18 kg",
						IsHandBaggage: true,
						Quantity:      1,
						PaxType:       paxType,
					},
					{
						Name:          "32 kg",
						IsHandBaggage: false,
						Quantity:      1,
						PaxType:       paxType,
					},
				}
				res = append(res, tempBaggage...)
			}

			return res
		default:
			return nil
		}
	case "BL":
		switch bookingClass {
		case "A", "P", "G":
			for _, paxType := range listPaxType {
				tempBaggage := []*domain.BaggageInfo{
					{
						Name:          "7 kg",
						IsHandBaggage: true,
						Quantity:      1,
						PaxType:       paxType,
					},
				}
				res = append(res, tempBaggage...)
			}

			return res
		case "Q", "N", "R", "T", "E", "S", "H", "K", "L", "Y", "B", "M", "Z", "U":
			for _, paxType := range listPaxType {
				tempBaggage := []*domain.BaggageInfo{
					{
						Name:          "7 kg",
						IsHandBaggage: true,
						Quantity:      1,
						PaxType:       paxType,
					},
					{
						Name:          "23 kg",
						IsHandBaggage: false,
						Quantity:      1,
						PaxType:       paxType,
					},
				}
				res = append(res, tempBaggage...)
			}

			return res
		case "W", "D", "I", "J", "C":
			for _, paxType := range listPaxType {
				tempBaggage := []*domain.BaggageInfo{
					{
						Name:          "7 kg",
						IsHandBaggage: true,
						Quantity:      1,
						PaxType:       paxType,
					},
					{
						Name:          "32 kg",
						IsHandBaggage: false,
						Quantity:      1,
						PaxType:       paxType,
					},
				}
				res = append(res, tempBaggage...)
			}

			return res

		default:
			return res
		}
	default:
		return res
	}
}

func GetQHBaggage(cabinClass string, hasStopoverInVCS bool, listPaxType []enum.PaxType) []*domain.BaggageInfo {
	res := make([]*domain.BaggageInfo, 0)
	switch {
	case cabinClass == "EcoSaverMax" || cabinClass == "EconomySmart" || cabinClass == "EconomySaverMax" || cabinClass == "EconomySmart" || cabinClass == "Economy Saver Max" || cabinClass == "Economy Smart":
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	case (cabinClass == "EconomySaver" || cabinClass == "EconomyFlex"):
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
				{
					Name:          "20 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	// case (cabinClass == "EconomySaver" || cabinClass == "EconomySmart" || cabinClass == "EconomyFlex") && hasStopoverInVCS:
	// 	for _, paxType := range listPaxType {
	// 		tempBaggage := []*domain.BaggageInfo{
	// 			{
	// 				Name:          "07 kg",
	// 				IsHandBaggage: true,
	// 				Quantity:      1,
	// 				PaxType:       paxType,
	// 			},
	// 			{
	// 				Name:          "15 kg",
	// 				IsHandBaggage: false,
	// 				Quantity:      1,
	// 				PaxType:       paxType,
	// 			},
	// 		}
	// 		res = append(res, tempBaggage...)
	// 	}

	// 	return res
	case (cabinClass == "BusinessSmart" || cabinClass == "BusinessFlex") && !hasStopoverInVCS:
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      2,
					PaxType:       paxType,
				},
				{
					Name:          "40 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	case (cabinClass == "BusinessSmart" || cabinClass == "BusinessFlex") && hasStopoverInVCS:
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      2,
					PaxType:       paxType,
				},
				{
					Name:          "25 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	case (cabinClass == "PremiumSmart" || cabinClass == "PremiumFlex") && !hasStopoverInVCS:
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
				{
					Name:          "30 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	case (cabinClass == "PremiumSmart" || cabinClass == "PremiumFlex") && hasStopoverInVCS:
		for _, paxType := range listPaxType {
			tempBaggage := []*domain.BaggageInfo{
				{
					Name:          "07 kg",
					IsHandBaggage: true,
					Quantity:      1,
					PaxType:       paxType,
				},
				{
					Name:          "15 kg",
					IsHandBaggage: false,
					Quantity:      1,
					PaxType:       paxType,
				},
			}
			res = append(res, tempBaggage...)
		}

		return res
	default:
		return res
	}
}

func GetVUBaggage(listPaxType []enum.PaxType) []*domain.BaggageInfo {
	res := make([]*domain.BaggageInfo, 0)

	for _, paxType := range listPaxType {
		tempBaggage := []*domain.BaggageInfo{
			{
				Name:          "07 kg",
				IsHandBaggage: true,
				Quantity:      1,
				PaxType:       paxType,
			},
		}
		res = append(res, tempBaggage...)
	}

	return res
}
