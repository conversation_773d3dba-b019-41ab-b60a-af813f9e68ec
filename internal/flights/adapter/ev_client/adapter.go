package ev_client

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/converts"
	clientEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/SearchFlight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type EVAdapter interface {
	SearchFlight(ctx context.Context, req *domain.SearchFlightsRequest, version enum.SearchVersion, tracingID string) (*domain.SearchFlightsResponse, error)
	CreateBooking(ctx context.Context, req *domain.BookingDetails, searchReq *domain.SearchFlightsRequest, ities []*domain.FlightItinerary, tracingID string) (*domain.SvcCreateBookingResponse, error)
	ConfirmFare(ctx context.Context, booking *domain.BookingSession) (*domain.TotalFareInfo, []*domain.FlightItinerary, error)
	IssueTicket(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo, tracingID string) (*domain.IssueTicketServiceResponse, error)
	// Also verify flight fare total amount before issuing
	VerifyBookingFlightData(ctx context.Context, bk *domain.BookingSession) (bool, error)
	FetchEticketInfo(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo) ([]*domain.ETicketInfo, bool, error)
}

type evAdapter struct {
	evClient client.EvClient
	cfg      *config.Schema
}

var DefaultProviderSearch = []string{string(clientEnum.AirlineVNA), string(clientEnum.AirlineBAMBOO), string(clientEnum.AirlineVJ), string(clientEnum.AirlineVietTravel)}

func NewEVAdapter(cfg *config.Schema, redis redis.IRedis, requestRepo repositories.RequestRepository) EVAdapter {
	return &evAdapter{
		evClient: client.NewEvClient(cfg, redis, requestRepo),
		cfg:      cfg,
	}
}

// Return etickets, isPassed, error
func (c *evAdapter) FetchEticketInfo(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo) ([]*domain.ETicketInfo, bool, error) {
	if booking == nil {
		return nil, false, fmt.Errorf("IssueTicket: booking == nil")
	}

	token, err := c.evClient.GetToken(ctx, booking.BookingRef, true)
	if err != nil {
		log.Error("a.evClient.GetToken error", log.Any("error", err), log.Any("tracingID", booking.BookingRef))
		return nil, false, errors.Wrap(err, "get Token error")
	}

	response, err := c.evClient.GetBookingTicket(ctx, booking.BookingRef, booking.AirlineSystem, token, booking.BookingRef)
	if err != nil {
		return nil, false, errors.Wrap(err, "c.evClient.GetBookingTicket")
	}

	const statusTickted = "CONFIRMED"

	if response.RecordStatus != statusTickted {
		return nil, false, errors.Wrap(err, "c.evClient.GetBookingTicket")
	}

	eTickets, err := converts.MapETicket(response, booking.Itineraries, pnr)
	if err != nil {
		return nil, false, errors.Wrap(err, "converts.MapETicket error")
	}

	return eTickets, true, nil
}

func (a *evAdapter) VerifyBookingFlightData(ctx context.Context, bk *domain.BookingSession) (bool, error) {
	token, err := a.evClient.GetToken(ctx, bk.BookingRef, false)
	if err != nil {
		log.Error("a.evClient.GetToken error", log.Any("error", err), log.Any("tracingID", bk.BookingRef))
		return false, errors.Wrap(commonErrors.ErrSomethingOccurred, "Get Token error")
	}

	pnr, err := a.evClient.GetBookingTicket(ctx, bk.BookingRef, bk.AirlineSystem, token, bk.BookingRef)
	if err != nil {
		return false, errors.Wrap(err, "evClient.GetBookingTicket")
	}

	if pnr.TotalAmount > bk.FareDataCf.TotalFareAmount {
		return false, domain.ErrTicketFareChanged
	}

	return converts.SyncBooking(pnr, bk)
}

func (a *evAdapter) SearchFlight(ctx context.Context, req *domain.SearchFlightsRequest, version enum.SearchVersion, tracingID string) (*domain.SearchFlightsResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("SearchFlight: req == nil")
	}

	token, err := a.evClient.GetToken(ctx, tracingID, false)
	if err != nil {
		log.Error("a.evClient.GetToken error", log.Any("error", err), log.Any("tracingID", tracingID))
		return nil, errors.Wrap(commonErrors.ErrSomethingOccurred, "Get Token error")
	}

	searchReq, err := converts.ToSearchFlightReq(req)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToSearchFlightReq error")
	}

	data := map[string][]*domain.ResponseFlight{}
	var (
		wg    sync.WaitGroup
		mutex sync.Mutex
	)

	providerSearch := strings.Split(a.cfg.EvEnableAirline, ",")

	providerSearch = lo.Filter[string](providerSearch, func(item string, index int) bool {
		return item != ""
	})

	if len(providerSearch) == 0 {
		providerSearch = DefaultProviderSearch
	}

	for _, airline := range providerSearch {
		airline := airline
		wg.Add(1)
		go func() {
			defer wg.Done()
			searchReqAirline := new(SearchFlight.Request)
			*searchReqAirline = *searchReq
			searchReqAirline.AirlineSystem = airline

			ctxTimeout := time.Second * time.Duration(a.cfg.SearchFlightCtxTimeout)
			searchCtx, cc := context.WithTimeout(ctx, ctxTimeout)
			defer cc()

			response, err := a.evClient.SearchFlight(searchCtx, searchReqAirline, token, airline, tracingID)
			if err != nil {
				log.Error("a.evClient.SearchFlight error", log.String("airline", airline), log.Any("err", err))
				return
			}

			var listFlights []*domain.ResponseFlight

			if version == enum.SearchVersionV2 {
				listFlights, err = converts.ToSearchFlightResV2(response, airline, &req.Passengers, searchReq.SearchType)
				if err != nil {
					log.Error("converts.ToSearchFlightRes error", log.String("airline", airline), log.Any("err", err))
					return
				}
			} else { // Default
				listFlights, err = converts.ToSearchFlightRes(response, airline, &req.Passengers, searchReq.SearchType)
				if err != nil {
					log.Error("converts.ToSearchFlightRes error", log.String("airline", airline), log.Any("err", err))
					return
				}
			}

			mutex.Lock()
			data[airline] = listFlights
			mutex.Unlock()
		}()
	}

	wg.Wait()

	length := 0
	for _, value := range data {
		length += len(value)
	}

	listFlights := make([]*domain.ResponseFlight, 0, length)

	for _, value := range data {
		listFlights = append(listFlights, value...)
	}

	return &domain.SearchFlightsResponse{
		ListFlights: listFlights,
	}, nil
}

func (a *evAdapter) ConfirmFare(ctx context.Context, booking *domain.BookingSession) (*domain.TotalFareInfo, []*domain.FlightItinerary, error) {
	if booking == nil {
		return nil, nil, fmt.Errorf("ConfirmFare: booking == nil")
	}

	token, err := a.evClient.GetToken(ctx, booking.SessionID, false)
	if err != nil {
		log.Error("a.evClient.GetToken error", log.Any("error", err), log.Any("tracingID", booking.SessionID))
		return nil, nil, errors.Wrap(commonErrors.ErrSomethingOccurred, "Get Token error")
	}

	searchReq, err := converts.ToSearchFlightReq(booking.SearchRequest)
	if err != nil {
		return nil, nil, errors.Wrap(err, "converts.ToSearchFlightReq error")
	}

	searchReq.AirlineSystem = booking.AirlineSystem

	ctxTimeout := time.Second * time.Duration(a.cfg.SearchFlightCtxTimeout)
	searchCtx, cc := context.WithTimeout(ctx, ctxTimeout)
	defer cc()

	response, err := a.evClient.SearchFlight(searchCtx, searchReq, token, booking.AirlineSystem, booking.SessionID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "SearchFlight error")
	}

	result, ities, err := converts.ToConfirmFare(booking, response)
	if err != nil {
		return nil, nil, errors.Wrap(err, "converts.ToConfirmFare error")
	}

	return result, ities, nil
}

func (a *evAdapter) CreateBooking(ctx context.Context, req *domain.BookingDetails, searchReq *domain.SearchFlightsRequest, ities []*domain.FlightItinerary, tracingID string) (*domain.SvcCreateBookingResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("CreateBooking: req == nil")
	}

	token, err := a.evClient.GetToken(ctx, tracingID, false)
	if err != nil {
		log.Error("a.evClient.GetToken error", log.Any("error", err), log.Any("tracingID", tracingID))
		return nil, errors.Wrap(commonErrors.ErrSomethingOccurred, "Get Token error")
	}

	bookingReq, err := converts.ToCreateBookingReq(req, searchReq, ities, a.cfg.EvMailReceiver)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToCreateBookingReq error")
	}

	response, err := a.evClient.CreateBooking(ctx, bookingReq, token, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "a.evClient.CreateBooking error")
	}

	if len(response.RecordLocations) > 0 && response.RecordLocations[0] != nil {
		lastTicketDate := int64(0)

		if response.RecordLocations[0].HoldExpireDate != "" {
			loc, err := time.LoadLocation(constants.HCMTimezone)
			if err != nil {
				return nil, fmt.Errorf(fmt.Sprintf("CreateBooking: invalid time zone %s", constants.HCMTimezone))
			}
			lastTicket, err := time.ParseInLocation(utils.FullDateLayout, response.RecordLocations[0].HoldExpireDate, loc)
			if err != nil {
				return nil, errors.Wrap(err, "ParseTime HoldExpireDate error")
			}

			lastTicketDate = lastTicket.UnixMilli()

			if time.Until(lastTicket) < 0 {
				lastTicketDate = 0
			}
		}

		return &domain.SvcCreateBookingResponse{
			BookingRef:        response.RecordLocations[0].RecordLocation,
			ExpectedPrice:     &domain.ExpectedPrice{Amount: response.TotalAmount, Currency: "VND"},
			LastTicketingDate: lastTicketDate,
		}, nil
	}

	return nil, domain.ErrItinerarySoldOut
}

func (c *evAdapter) IssueTicket(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo, tracingID string) (*domain.IssueTicketServiceResponse, error) {
	if booking == nil {
		return nil, fmt.Errorf("IssueTicket: booking == nil")
	}

	token, err := c.evClient.GetToken(ctx, tracingID, true)
	if err != nil {
		log.Error("a.evClient.GetToken error", log.Any("error", err), log.Any("tracingID", tracingID))
		return nil, errors.Wrap(commonErrors.ErrSomethingOccurred, "Get Token error")
	}

	_, err = c.evClient.IssueTicket(ctx, booking.BookingRef, booking.AirlineSystem, token, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "a.evClient.IssueTicket error")
	}

	pending := false

	response, err := c.evClient.GetBookingTicket(ctx, booking.BookingRef, booking.AirlineSystem, token, tracingID)
	if err != nil {
		// Exception log
		log.Error("[EXCEPTION LOG] evClient.GetBookingTicket error", log.Any("err", err), log.String("bkRef", booking.BookingRef), log.String("airlineSystem", booking.AirlineSystem))
		pending = true
	}

	eTickets, err := converts.MapETicket(response, booking.Itineraries, pnr)
	if err != nil {
		return nil, errors.Wrap(err, "converts.MapETicket error")
	}

	reservationInfo := []*domain.IssueTicketSvcReservationInfo{}

	for _, iti := range booking.Itineraries {
		reservationInfo = append(reservationInfo, &domain.IssueTicketSvcReservationInfo{
			ItineraryIndex:  iti.Index,
			ReservationCode: booking.BookingRef,
		})
	}

	return &domain.IssueTicketServiceResponse{
		ReservationInfo: reservationInfo,
		Etickets:        eTickets,
		Pending:         pending,
	}, nil

}
