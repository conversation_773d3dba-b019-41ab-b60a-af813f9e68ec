package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	hubEnum "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

	clientConst "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/CreateBooking"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/CreateToken"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/IssueTicket"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client/models/SearchFlight"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	contextbinding "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/context_binding"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const (
	successfulCode        = 200
	methodPost            = "POST"
	defaultRequestTimeout = 60

	radius = 1000

	actionLogin = "login"
	loginPath   = "/token"

	actionSearchFlight  = "search_flight"
	searchFlightPath    = "/api/Flights/Search"
	actionCreateBooking = "create_booking"
	createBookingPath   = "/api/BookingManage/CreateBooking"

	actionIssueTicket = "issue_ticket"
	issueTicketPath   = "/api/BookingManage/Ticketing"

	actionGetBooking = "get_booking"
	getBookingPath   = "/api/BookingManage/GetBooking"
)

var ErrSomeThingWrong = "Something went wrong"
var ErrsFlightNotFound = []string{"Invalid flight connection", "Không tìm được chuyến bay", "NO FLIGHT FOR THIS CITY PAIR"}

type EvClient interface {
	GetToken(ctx context.Context, tracingID string, issue bool) (string, error)
	SearchFlight(ctx context.Context, req *SearchFlight.Request, token, airline, tracingID string) (*SearchFlight.Response, error)
	CreateBooking(ctx context.Context, req *CreateBooking.Request, token, tracingID string) (*CreateBooking.Response, error)
	IssueTicket(ctx context.Context, pnr, airlineSystem, token, tracingID string) (*IssueTicket.IssueTicketRes, error)
	GetBookingTicket(ctx context.Context, pnr, airlineSystem, token, tracingID string) (*IssueTicket.GetBookingRes, error)
}

type evClient struct {
	baseUrl     string
	requestRepo repositories.RequestRepository
	redis       commonRedis.IRedis
	username    string
	password    string
}

func NewEvClient(cfg *config.Schema, redis commonRedis.IRedis, requestRepo repositories.RequestRepository) EvClient {
	return &evClient{
		baseUrl:     cfg.EvURL,
		requestRepo: requestRepo,
		redis:       redis,
		username:    cfg.EvUsername,
		password:    cfg.EvPassword,
	}
}

func (c *evClient) getCtxDCP(ctx context.Context, issue bool) *domain.DCPEV {
	dcps, isOK := ctx.Value(contextbinding.ContextDCPsKey{}).(*domain.PartnerDCPs)
	if isOK {
		for _, dcp := range dcps.DCPsEV {
			if helpers.CanUseDCP(dcp.Level, issue) {
				return dcp
			}
		}
	}

	return &domain.DCPEV{
		BaseURL:  c.baseUrl,
		Username: c.username,
		Password: c.password,
		Level:    enum.DCPLevelBookingAndTicketing,
	}
}

func (c *evClient) Login(ctx context.Context, pID string, tracingID string, dcp *domain.DCPEV, issue bool) (*CreateToken.Response, error) {
	req := &CreateToken.Request{
		Username:  dcp.Username,
		Password:  dcp.Password,
		GrantType: "password",
		ClientID:  "api",
	}

	response := &CreateToken.Response{}
	err := c.doRequest(ctx, "", actionLogin, c.getLoginHeader(), req, loginPath, tracingID, response)
	if err != nil {
		return nil, err
	}

	return response, errors.Wrap(err, "doRequest")
}

func (c *evClient) GetToken(ctx context.Context, tracingID string, issue bool) (string, error) {
	dcp := c.getCtxDCP(ctx, issue)

	key := fmt.Sprintf("ev_token_%s", dcp.Username)
	rdVal := c.redis.CMD().Get(ctx, key)

	err := rdVal.Err()
	if err == nil {
		return rdVal.Val(), nil
	}

	if !errors.Is(err, redis.Nil) {
		return "", errors.Wrap(err, "rdVal.Err()")
	}

	res, err := c.Login(ctx, "", tracingID, dcp, issue)
	if err != nil {
		return "", errors.Wrap(err, "c.Login")
	}

	expired := time.Second * time.Duration(math.Round(0.9*float64(res.ExpiresIn)))

	if err := c.redis.CMD().Set(ctx, key, res.AccessToken, expired).Err(); err != nil {
		return "", errors.Wrap(err, "redis.CMD().Set.Err")
	}

	return res.AccessToken, nil
}

func (c *evClient) SearchFlight(ctx context.Context, req *SearchFlight.Request, token, airline, tracingID string) (*SearchFlight.Response, error) {
	response := &SearchFlight.Response{}

	err := retry.Do(func() error {
		err := c.doRequest(ctx, "", actionSearchFlight, c.getHeader(token), req, searchFlightPath, tracingID, response)
		if err != nil {
			return errors.Wrap(err, "doRequest")
		}

		if response != nil && !response.Result && len(response.Messages) > 0 {
			message := response.Messages[0]
			for _, evExceptionMessage := range ErrsFlightNotFound {
				if strings.Contains(message, evExceptionMessage) {
					return nil
				}
			}

			return errors.New(message)
		}

		return nil
	}, retry.Attempts(2), retry.Delay(time.Second), retry.LastErrorOnly(true))
	if err != nil {
		return nil, err
	}

	return response, nil
}

func (c *evClient) CreateBooking(ctx context.Context, req *CreateBooking.Request, token, tracingID string) (*CreateBooking.Response, error) {
	response := &CreateBooking.Response{}

	err := c.doRequest(ctx, "", actionCreateBooking, c.getHeader(token), req, createBookingPath, tracingID, response)

	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if response != nil && !response.Result && len(response.Messages) > 0 {
		if strings.Contains(response.Messages[0], clientConst.ErrItinerarySoldOut) {
			return nil, domain.ErrItinerarySoldOut
		}

		if strings.Contains(response.Messages[0], clientConst.ErrNameLong) {
			return nil, domain.ErrFullNameTooLong
		}

		return nil, errors.New(response.Messages[0])
	}

	return response, nil
}

func (c *evClient) IssueTicket(ctx context.Context, pnr, airlineSystem, token, tracingID string) (*IssueTicket.IssueTicketRes, error) {
	response := &IssueTicket.IssueTicketRes{}

	err := c.doRequest(ctx, "", actionIssueTicket, c.getHeader(token), &IssueTicket.Request{
		RecordLocation: pnr,
		AirlineSystem:  airlineSystem,
	}, issueTicketPath, tracingID, response)

	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if response != nil && !response.Result && len(response.Messages) > 0 {
		return nil, errors.New(response.Messages[0])
	}

	return response, nil
}

func (c *evClient) GetBookingTicket(ctx context.Context, pnr, airlineSystem, token, tracingID string) (*IssueTicket.GetBookingRes, error) {
	response := &IssueTicket.GetBookingRes{}

	err := c.doRequest(ctx, "", actionGetBooking, c.getHeader(token), &IssueTicket.Request{
		RecordLocation: pnr,
		AirlineSystem:  airlineSystem,
	}, getBookingPath, tracingID, response)

	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if response != nil && !response.Result && len(response.Messages) > 0 {
		return nil, errors.New(response.Messages[0])
	}

	return response, nil
}

func (c *evClient) doRequest(
	ctx context.Context,
	partnershipID string,
	action string,
	header map[string]string,
	body interface{},
	apiPath string,
	tracingID string,
	responseData interface{},
) error {
	var err error

	dcp := c.getCtxDCP(ctx, false)

	response, statusCode, duration, err := c.do(ctx, header, apiPath, methodPost, action, body, dcp.BaseURL)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := dcp.BaseURL + apiPath
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     action,
			Provider:   hubEnum.FlightProviderEV,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("EV requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", action),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if err != nil {
		return errors.Wrap(err, "c.do")
	}

	if err := json.Unmarshal(response, responseData); err != nil {
		return errors.Wrap(err, "Unmarshal")
	}

	return nil
}

func (c *evClient) do(
	ctx context.Context,
	header map[string]string,
	relativePath string,
	method string,
	action string,
	body interface{},
	baseURL string,
) ([]byte, int, int64, error) {
	var duration int64
	beginAt := time.Now()
	fullPath := baseURL + relativePath
	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("ev Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))
		return nil, 0, duration, err
	}

	var response *http.Response

	if relativePath == loginPath {
		loginReq := &CreateToken.Request{}
		err := json.Unmarshal(jsonData, loginReq)
		if err != nil {
			log.Error("ev UnMarshal error",
				log.Any("error", err),
				log.String("relativePath", fullPath),
				log.String("action", action),
				log.Any("req", body))
			return nil, 0, duration, err
		}

		formData := url.Values{}
		formData.Set("username", loginReq.Username)
		formData.Set("password", loginReq.Password)
		formData.Set("grant_type", loginReq.GrantType)
		formData.Set("client_id", loginReq.ClientID)

		response, err = tracingHttp.RawRequest(ctx, fullPath, method, strings.NewReader(formData.Encode()), header)
		if err != nil {
			duration = time.Since(beginAt).Milliseconds()
			return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
		}
	} else {
		payload := bytes.NewBuffer(jsonData)
		response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
		if err != nil {
			duration = time.Since(beginAt).Milliseconds()
			return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
		}
	}
	duration = time.Since(beginAt).Milliseconds()

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != successfulCode {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *evClient) getHeader(token string) map[string]string {
	return map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + token,
	}
}

func (c *evClient) getLoginHeader() map[string]string {
	return map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}
}
