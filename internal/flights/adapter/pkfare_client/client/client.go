package client

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	contextbinding "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/context_binding"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const (
	Currency   = "VN"
	Language   = "VN"
	methodPost = "POST"
)

type PkfareClient interface {
	ShoppingV8(ctx context.Context, req *entities.ShoppingV8Req, tracingID string) (*entities.ShoppingV8Res, error)
	PrecisePricingV10(ctx context.Context, req *entities.PrecisePricingV10Req, tracingID string) (*entities.PrecisePricingV10Res, error)
	PreciseBookingV5(ctx context.Context, req *entities.PreciseBookingV5Req, tracingID string) (*entities.PreciseBookingV5Res, error)
	Ticketing(ctx context.Context, req *entities.TicketingReq, tracingID string) (*entities.TicketingRes, error)
	OrderPricingV4(ctx context.Context, req *entities.OrderPricingV4Req, tracingID string) (*entities.OrderPricingV4Res, error)
	OrderDetailV8(ctx context.Context, req *entities.OrderDetailV8Req, tracingID string) (*entities.OrderDetailV8Res, error)
	CancelOrder(ctx context.Context, req *entities.CancelOrderReq, tracingID string) (*entities.CancelOrderRes, error)
	PenaltyV3(ctx context.Context, req *entities.PenaltyV3Req, tracingID string) (*entities.PenaltyV3Res, error)
}

type pkfareClient struct {
	requestRepo repositories.RequestRepository
	PartnerID   string
	PartnerKey  string
	BaseURL     string
}

func NewPkfareClient(cfg *config.Schema, requestRepo repositories.RequestRepository) PkfareClient {
	return &pkfareClient{
		requestRepo: requestRepo,
		PartnerID:   cfg.PkfarePartnerID,
		PartnerKey:  cfg.PkfarePartnerKey,
		BaseURL:     cfg.PkfareBaseURL,
	}
}

func (c *pkfareClient) getHeader() map[string]string {
	return map[string]string{
		"Content-Type": "application/json; charset=utf-8",
	}
}

func (a *pkfareClient) do(
	ctx context.Context,
	dcp *domain.DCPPkfare,
	action,
	method string,
	body []byte,
	headers map[string]string,
) ([]byte, int, time.Duration, error) {
	var duration time.Duration
	beginAt := time.Now()

	apiURL, err := url.JoinPath(dcp.BaseURL, action)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "Parse base URL")
	}

	response, err := tracingHttp.RawRequest(ctx, apiURL, method, bytes.NewBuffer(body), headers)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	duration = time.Since(beginAt)

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != http.StatusOK {
		err := fmt.Errorf("status %v", response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *pkfareClient) doRequest(
	ctx context.Context,
	action string,
	body []byte,
	tracingID string,
	dcp *domain.DCPPkfare,
) ([]byte, error) {
	var err error
	headers := c.getHeader()
	response, statusCode, duration, err := c.do(ctx, dcp, action, methodPost, body, headers)

	headerClone := map[string]string{}
	for key, val := range headers {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := dcp.BaseURL
		bCtx, cancel := context.WithTimeout(context.Background(), pkgConstants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		bodyToLog := c.hideClientInfoRequest(body)

		logReq := &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       string(bodyToLog),
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Provider:   enum.FlightProviderPkfare,
			Duration:   duration.Milliseconds(),
			Action:     action,
			TracingID:  tracingID,
			IsJson:     true,
		}

		if err = c.requestRepo.Create(bCtx, logReq); err != nil {
			log.Error("requestRepo.Create error", log.Any("error", err), log.Any("logReq", logReq))
		}
	}(headerClone)
	if err != nil {
		return nil, errors.Wrap(err, "a.do")
	}

	return response, nil
}

func (c *pkfareClient) hideClientInfoRequest(body []byte) []byte {
	var data map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return body
	}

	delete(data, "authentication")

	newBody, err := json.Marshal(data)
	if err != nil {
		return body
	}

	return newBody
}

func (c *pkfareClient) PreciseBookingV5(ctx context.Context, req *entities.PreciseBookingV5Req, tracingID string) (*entities.PreciseBookingV5Res, error) {
	dcp := c.GetCtxDCP(ctx, false)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APIPreciseBookingV5, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.PreciseBookingV5Res{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}

func (c *pkfareClient) ShoppingV8(ctx context.Context, req *entities.ShoppingV8Req, tracingID string) (*entities.ShoppingV8Res, error) {
	dcp := c.GetCtxDCP(ctx, false)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APIShoppingV8, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.ShoppingV8Res{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}

func (c *pkfareClient) PrecisePricingV10(ctx context.Context, req *entities.PrecisePricingV10Req, tracingID string) (*entities.PrecisePricingV10Res, error) {
	dcp := c.GetCtxDCP(ctx, false)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APIPrecisePricingV10, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.PrecisePricingV10Res{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}

func (c *pkfareClient) Ticketing(ctx context.Context, req *entities.TicketingReq, tracingID string) (*entities.TicketingRes, error) {
	dcp := c.GetCtxDCP(ctx, true)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APITicketing, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.TicketingRes{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}

func (c *pkfareClient) OrderPricingV4(ctx context.Context, req *entities.OrderPricingV4Req, tracingID string) (*entities.OrderPricingV4Res, error) {
	dcp := c.GetCtxDCP(ctx, false)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APIOrderPricingV4, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.OrderPricingV4Res{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}

func (c *pkfareClient) OrderDetailV8(ctx context.Context, req *entities.OrderDetailV8Req, tracingID string) (*entities.OrderDetailV8Res, error) {
	dcp := c.GetCtxDCP(ctx, false)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APIOrderDetailV8, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.OrderDetailV8Res{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}

func (c *pkfareClient) CancelOrder(ctx context.Context, req *entities.CancelOrderReq, tracingID string) (*entities.CancelOrderRes, error) {
	dcp := c.GetCtxDCP(ctx, false)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APICancelOrder, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.CancelOrderRes{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}

func (c *pkfareClient) handleErr(err *entities.ErrorResponse) error {
	if lo.Contains(constants.SoldOutCode, err.ErrorCode) {
		return domain.ErrItinerarySoldOut
	}

	if lo.Contains(constants.FullNameLongCode, err.ErrorCode) {
		return domain.ErrFullNameTooLong
	}

	if lo.Contains(constants.InvalidDOBCode, err.ErrorCode) {
		return domain.ErrInvalidDOB
	}

	if lo.Contains(constants.FareChangeCode, err.ErrorCode) {
		return domain.ErrTicketFareChanged
	}

	switch err.ErrorCode {
	case constants.ErrCodeInvalidParam:
		return domain.ErrInvalidValue
	case constants.ErrCodeDuplicateBooking:
		return domain.ErrPaxNameExistedInAnotherBooking
	default:
		return errors.New(err.ErrorMsg)
	}
}

func (c *pkfareClient) getAuthentication(dcp *domain.DCPPkfare) *entities.Authentication {
	sign := fmt.Sprintf("%x", md5.Sum([]byte(dcp.PartnerID+dcp.PartnerKey)))

	return &entities.Authentication{
		PartnerID: dcp.PartnerID,
		Sign:      sign,
	}
}

func (c *pkfareClient) GetCtxDCP(ctx context.Context, issue bool) *domain.DCPPkfare {
	dcps, isOK := ctx.Value(contextbinding.ContextDCPsKey{}).(*domain.PartnerDCPs)
	if isOK {
		for _, dcp := range dcps.DCPsPkfare {
			if helpers.CanUseDCP(dcp.Level, issue) {
				return dcp
			}
		}
	}

	return &domain.DCPPkfare{
		PartnerID:  c.PartnerID,
		PartnerKey: c.PartnerKey,
		BaseURL:    c.BaseURL,
	}
}

func (c *pkfareClient) PenaltyV3(ctx context.Context, req *entities.PenaltyV3Req, tracingID string) (*entities.PenaltyV3Res, error) {
	dcp := c.GetCtxDCP(ctx, false)
	req.Authentication = c.getAuthentication(dcp)

	bReq, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "Marshal")
	}

	res, err := c.doRequest(ctx, constants.APIPenaltyV3, bReq, tracingID, dcp)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.PenaltyV3Res{}

	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "Unmarshal")
	}

	if out.ErrorCode != constants.SuccessCode {
		return nil, c.handleErr(&out.ErrorResponse)
	}

	return out, nil
}
