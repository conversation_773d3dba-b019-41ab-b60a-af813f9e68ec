package client

import (
	"context"
	"testing"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/entities"
)

func TestShoppingV8(t *testing.T) {
	// Create a new instance of pkfareClient
	client := getTestInstance()

	// Create a new ShoppingV8Req object
	req := &entities.ShoppingV8Req{
		SearchParams: &entities.SearchParams{
			Adults:         1,
			Children:       1,
			Infants:        0,
			Nonstop:        0,
			Airline:        "",
			Solutions:      0,
			Tag:            "",
			ReturnTagPrice: "Y",
			SearchAirLegs: []entities.SearchAirLeg{
				{
					CabinClass:    "",
					DepartureDate: "2025-08-02",
					Destination:   "LAX",
					Origin:        "NRT",
					Airplane:      "SQ",
				},
				{
					CabinClass:    "",
					DepartureDate: "2025-08-05",
					Destination:   "NRT",
					Origin:        "LAX",
					Airplane:      "SQ",
				},
			},
		},
	}

	// Call the ShoppingV8 function
	res, err := client.ShoppingV8(context.Background(), req, "tracingID")
	if err != nil {
		t.Errorf("Error calling ShoppingV8: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.ErrorCode != constants.SuccessCode {
		t.Errorf("Expected ErrorCode to be %s, but got %s", constants.SuccessCode, res.ErrorCode)
	}

}

func getTestInstance() *pkfareClient {
	return &pkfareClient{
		requestRepo: nil,
		PartnerID:   "CUaFd8pNZLxX88i4XyXNMT/ipsw=",
		PartnerKey:  "YjJhOGJiYThiNmNkZDFkMzg4YjgxZDZhYzViZGU0MzE=",
		BaseURL:     "https://api.pkfare.com",
	}
}

func TestPrecisePricingV10(t *testing.T) {
	// Create a new instance of pkfareClient
	client := getTestInstance()

	// Create a new PrecisePricingV10Req object
	req := &entities.PrecisePricingV10Req{
		PricingRequest: &entities.PricingRequest{
			Journeys: &entities.JourneyMapRequest{
				Journey0: []entities.JourneyDetails{
					{
						Airline:       "UO",
						FlightNum:     "700",
						Arrival:       "BKK",
						ArrivalDate:   "2025-05-26",
						ArrivalTime:   "15:30",
						Departure:     "HKG",
						DepartureDate: "2025-05-26",
						DepartureTime: "13:30",
						BookingCode:   "W",
					},
				},
				Journey1: []entities.JourneyDetails{
					{
						Airline:       "UO",
						FlightNum:     "709",
						Arrival:       "HKG",
						ArrivalDate:   "2025-05-26",
						ArrivalTime:   "22:55",
						Departure:     "BKK",
						DepartureDate: "2025-05-26",
						DepartureTime: "18:50",
						BookingCode:   "V",
					},
				},
			},
			Adults:     1,
			Children:   1,
			Infants:    0,
			SolutionID: "8KQk0tuwDCVXdcJfgG2zwYjLEbVVU2bXuFty9GgqqJYv8mS3hUuNQTiyK9IDDnQIh4/ZAjcuq3KgJmmy33myZHuetOUY4T9c7O9B6vifOogfzwLKX0Bb7qEe5Rwxr/9F60tRk50NIeX1av12q5D0MlDFskcLIU51iXzUmhBPmKhEF4Q0dmMcAiO3hX+4wWxeKf1WdcfkLhSf33O0wTXUXLnIicZl64XqvXNvswvtlkLXfDGItj7Nxy7WNDJcOtWg0mQjYqHHUNdvZtlIkUZEAerx3nUne30aV33H1EMD39yklbzGy6zyTZI7tPgPvz5aUICOc/057DXy1FsPxwMegVuVgAG93QByHMYsgnPq3vO4DJOacn06rzEd5GNZbIiSgmeeOqcqjH7q9ns0exU9ug==",
		},
	}

	// Call the PrecisePricingV10 function
	res, err := client.PrecisePricingV10(context.Background(), req, "tracingID")
	if err != nil {
		t.Errorf("Error calling PrecisePricingV10: %v", err)
	}

	log.Info("info", log.Any("data", res))

	if res == nil {
		t.Errorf("Error calling PrecisePricingV10 nil: %v", err)
		return
	}

	// Check the response
	if res.ErrorCode != constants.SuccessCode {
		t.Errorf("Expected ErrorCode to be %s, but got %s", constants.SuccessCode, res.ErrorCode)
	}

	// Print the response

}

func TestPenaltyV3(t *testing.T) {
	// Create a new instance of pkfareClient
	client := getTestInstance()

	// Create a new PenaltyV3Req object
	req := &entities.PenaltyV3Req{
		Penalty: &entities.PenaltyReqData{
			SolutionID: "hcWNhkowmx8ryssA3v+2vzq6jP/HhAr5FnQE2gfGeua8VZHArJ7ZBjHWsL796ZyVBoKh1oEXA9k4H8cR//XWUxKKdY2P0qP2sMnGkjbt8/KQoZOWHX4gpuw+C8Gq7AThX7W6ahyBZ+pKjmYAMFhGoveNbEaUEBwZapwztClyNcz9i7H94NZlcElJMt7ktO2zA0H71t3PWiGJEX3Q0oSNfFyN0UZbwdE8NTNtaAMywiY3+7zYu01uoc1FDE4WYI3l4YMsgq5w3YGpo9JjZwnf9iW4LRMmEowNtw7Rvjz2JlqeKyS78jMvpj3OHaJDV0fWYarMgTJzFL0vZ94exw22UKEJXsiDP4niIvvQYd6XNqoYMxMEDWSYSqAUjAmfl+jQFlQ4VtYE2wjpIq6X4kDIHA==",
			Journeys: &entities.JourneyMapRequest{
				Journey0: []entities.JourneyDetails{
					{
						Airline:       "5J",
						FlightNum:     "113",
						Arrival:       "MNL",
						ArrivalDate:   "2025-05-26",
						ArrivalTime:   "21:55",
						Departure:     "HKG",
						DepartureDate: "2025-05-26",
						DepartureTime: "19:25",
						BookingCode:   "Z",
					},
					{
						Airline:       "5J",
						FlightNum:     "929",
						Arrival:       "BKK",
						ArrivalDate:   "2025-05-27",
						ArrivalTime:   "10:05",
						Departure:     "MNL",
						DepartureDate: "2025-05-27",
						DepartureTime: "07:25",
						BookingCode:   "Z",
					},
				},
			},
		},
	}

	// Call the.PenaltyV3 function
	res, err := client.PenaltyV3(context.Background(), req, "tracingID")
	if err != nil {
		t.Errorf("Error calling.PenaltyV3: %v", err)
	}

	log.Info("info", log.Any("data", res))

	// Check the response
	if res.ErrorCode != constants.SuccessCode {
		t.Errorf("Expected ErrorCode to be %s, but got %s", constants.SuccessCode, res.ErrorCode)
	}

}
