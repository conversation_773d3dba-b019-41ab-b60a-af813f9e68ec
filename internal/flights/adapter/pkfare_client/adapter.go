package pkfare_client

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/entities"
	pkfareutils "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/pkfare_utils"

	commonConstants "gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const ()

type pkfareAdapter struct {
	cfg    *config.Schema
	client client.PkfareClient
}

type PkfareAdapter interface {
	SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error)
	ConfirmFare(ctx context.Context, booking *domain.BookingSession, tracingID string) (*domain.TotalFareInfo, error)
	CreateBooking(ctx context.Context, bookingDetails *domain.BookingDetails, tracingID string) (*domain.SvcCreateBookingResponse, error)
	CancelBooking(ctx context.Context, bookingRef, orderNum string) error
	IssueTicket(ctx context.Context, bkSession *domain.BookingSession, paxInfo []*domain.PaxInfo, tracingID string) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, enum.EMDStatus, bool, int64, error)
	VerifyBookingFlightData(ctx context.Context, bkFlight *domain.BookingSession) (bool, *domain.TotalFareInfo, error)
	CheckFare(ctx context.Context, req *domain.SearchFlightsRequest, ities []*domain.FlightItinerary, tracingID string) (*domain.CheckFareInfo, error)
	FetchEticketInfo(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, bool, error)
}

func NewPkfareAdapter(cfg *config.Schema, requestRepo repositories.RequestRepository) PkfareAdapter {
	{
		return &pkfareAdapter{
			cfg:    cfg,
			client: client.NewPkfareClient(cfg, requestRepo),
		}
	}
}

func (a *pkfareAdapter) FetchEticketInfo(ctx context.Context, booking *domain.BookingSession, pnr []*domain.PaxInfo) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, bool, error) {
	// QA/Dev testing hook to simulate provider responses without calling the actual API.
	// This logic is completely skipped in production environments.
	if a.cfg.Env != commonConstants.EnvProduction {
		if strings.HasPrefix(booking.BookingRef, "TEST_STATUS_") {
			statusToTest := strings.TrimPrefix(booking.BookingRef, "TEST_STATUS_")
			failedStatuses := []string{
				constants.OrderStatusCancelled,
				constants.OrderStatusCancelledTBR,
				constants.OrderStatusCancelledReim,
			}
			if lo.Contains(failedStatuses, statusToTest) {
				return nil, nil, false, domain.ErrIssueTicketFailed
			}
		}
	}

	orderDetailReq := &entities.OrderDetailV8Req{
		Data: &entities.OrderDetailDataReq{
			OrderNum:      booking.OrderNumRef,
			IncludeFields: constants.OrderDetailsFields,
		},
	}

	orderDetail, err := a.client.OrderDetailV8(ctx, orderDetailReq, booking.BookingRef)
	if err != nil {
		return nil, nil, false, errors.Wrap(err, "client.OrderDetailV8")
	}

	issueFailedStatuses := []string{
		constants.OrderStatusCancelled,
		constants.OrderStatusCancelledTBR,
		constants.OrderStatusCancelledReim,
	}
	if lo.Contains(issueFailedStatuses, orderDetail.Data.OrderStatus) {
		return nil, nil, false, domain.ErrIssueTicketFailed
	}

	issueOKStatuses := []string{
		constants.OrderStatusPending,
		constants.OrderStatusTicketed,
	}

	if !lo.Contains(issueOKStatuses, orderDetail.Data.OrderStatus) {
		return nil, nil, false, errors.New("invalid order status " + orderDetail.Data.OrderStatus)
	}

	reservationInfos, ticketInfos, err := converts.ToDomainReservationInfos(&orderDetail.Data, booking, pnr)
	if err != nil {
		return nil, nil, false, errors.Wrap(err, "converts.ToDomainReservationInfos")
	}

	pass := true

	if orderDetail.Data.OrderStatus == constants.OrderStatusPending {
		pass = false
	}

	return reservationInfos, ticketInfos, pass, nil
}

func (a *pkfareAdapter) CheckFare(ctx context.Context, req *domain.SearchFlightsRequest, ities []*domain.FlightItinerary, tracingID string) (*domain.CheckFareInfo, error) {
	clientReq := converts.ToPricingRequest(ities, req)

	res, err := a.client.PrecisePricingV10(ctx, clientReq, tracingID)

	if err != nil {
		return nil, errors.Wrap(err, "client.PrecisePricingV10")
	}

	if res == nil {
		return nil, errors.Wrap(err, "client.PrecisePricingV10")
	}

	out := &domain.CheckFareInfo{}

	newSolID := res.Solution.SolutionID
	firstIti := ities[0]

	if newSolID != firstIti.ProviderBookingKey {
		return nil, domain.ErrItinerarySoldOut
	}

	newIties, err := converts.ToDomainIties(res.Solution, res.Flights, res.Segments)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainResponseFlightV2")
	}

	if len(newIties) != len(ities) {
		return nil, errors.New("newIties != ities")
	}

	isFareChanged := false

	for i := 0; i < len(newIties); i++ {
		oldIti := ities[i]
		newIti := newIties[i]

		if len(oldIti.Segments) != len(newIti.Segments) {
			return nil, domain.ErrItinerarySoldOut
		}

		for j := 0; j < len(newIties[i].Segments); j++ {
			oldSeg := oldIti.Segments[j]
			newSeg := newIti.Segments[j]

			if oldSeg.DepartPlace != newSeg.DepartPlace ||
				oldSeg.ArrivalPlace != newSeg.ArrivalPlace ||
				oldSeg.CarrierMarketing != newSeg.CarrierMarketing ||
				oldSeg.FlightNumber != newSeg.FlightNumber ||
				oldSeg.DepartDate != newSeg.DepartDate ||
				oldSeg.ArrivalDate != newSeg.ArrivalDate ||
				oldSeg.Aircraft != "" && newSeg.Aircraft != "" && oldSeg.Aircraft != newSeg.Aircraft ||
				oldSeg.CabinClassCode != newSeg.CabinClassCode {
				return nil, domain.ErrItinerarySoldOut
			}

			if oldSeg.BookingClass != newSeg.BookingClass || oldSeg.FareBasis != newSeg.FareBasis {
				isFareChanged = true
			}

			oldSeg.FareBasis = newSeg.FareBasis
			oldSeg.BookingClass = newSeg.BookingClass
			oldSeg.CarrierOperator = newSeg.CarrierOperator
		}

		oldIti.FareBasis = oldIti.Segments[0].FareBasis
		oldIti.BookingClass = oldIti.Segments[0].BookingClass
		oldIti.CarrierOperator = oldIti.Segments[0].CarrierOperator
		oldIti.FreeBaggage = newIties[i].FreeBaggage

	}

	miniRules, err := a.CheckPenalty(ctx, res.Solution, ities, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "a.CheckPenalty")
	}

	out.MiniRules = miniRules

	if isFareChanged {
		return out, domain.ErrFareBasisChanged
	}

	return out, nil
}

func (a *pkfareAdapter) CheckPenalty(ctx context.Context, solution entities.Solution, ities []*domain.FlightItinerary, tracingID string) ([]*domain.MiniRule, error) {
	miniRuleSegmentMap := converts.GetMiniRuleSegementMap(ities)
	miniRules := converts.ToDomainMiniRules(solution, miniRuleSegmentMap)

	penaltyV3Req := converts.ToPenaltyRequest(ities, &entities.PenaltyRequest{
		SolutionID: solution.SolutionID,
	})

	penalty, err := a.client.PenaltyV3(ctx, penaltyV3Req, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "client.PenaltyV3")
	}

	penaltyText := ""
	if len(penalty.Penalties) == 1 {
		penaltyText = penalty.Penalties[0]
	} else {
		penaltyText = strings.Join(penalty.Penalties, ";")
	}

	for _, miniRule := range miniRules {
		miniRule.PenaltyText = penaltyText
	}

	return miniRules, nil
}

func (a *pkfareAdapter) VerifyBookingFlightData(ctx context.Context, bkFlight *domain.BookingSession) (hasChanges bool, fareDataIss *domain.TotalFareInfo, err error) {

	orderPricingReq := &entities.OrderPricingV4Req{
		OrderPricing: bkFlight.OrderNumRef,
	}

	pricingRes, err := a.client.OrderPricingV4(ctx, orderPricingReq, bkFlight.BookingRef)
	if err != nil {
		return false, nil, errors.Wrap(err, "client.OrderPricingV4")
	}

	if pricingRes == nil {
		return false, nil, errors.New("pricingRes is nil")
	}

	lastestFare := converts.ToDomainSearchTotalFareInfo(pricingRes.Data.PricingResult.Solution)
	fareDataIss = lastestFare.ConvertToTotal()

	if lastestFare.TotalFareAmount > bkFlight.OriginFareData.TotalFareAmount {
		return false, fareDataIss, domain.ErrTicketFareChanged
	}

	hasChanges, err = converts.SyncBooking(&pricingRes.Data.PricingResult, bkFlight)
	if err != nil {
		return false, nil, errors.Wrap(err, "convert.SyncBooking")
	}

	return hasChanges, fareDataIss, nil
}

func (a *pkfareAdapter) IssueTicket(ctx context.Context, bkSession *domain.BookingSession, paxInfo []*domain.PaxInfo, tracingID string) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, enum.EMDStatus, bool, int64, error) {
	clientIssueReq := &entities.TicketingReq{
		Data: &entities.TicketingDataReq{
			OrderNum: bkSession.OrderNumRef,
			PNR:      bkSession.BookingRef,
		},
	}

	ticketingRes, err := a.client.Ticketing(ctx, clientIssueReq, tracingID)
	if err != nil {
		return nil, nil, enum.EMDStatusEmpty, false, 0, errors.Wrap(err, "client.Ticketing")
	}

	if ticketingRes != nil {
		bkSession.ExpectedPrice = &domain.ExpectedPrice{
			Amount:   ticketingRes.Data.PayAmount,
			Currency: "USD",
		}
	}

	orderDetailReq := &entities.OrderDetailV8Req{
		Data: &entities.OrderDetailDataReq{
			OrderNum:      bkSession.OrderNumRef,
			IncludeFields: constants.OrderDetailsFields,
		},
	}

	orderDetail, err := a.client.OrderDetailV8(ctx, orderDetailReq, tracingID)
	if err != nil {
		return nil, nil, enum.EMDStatusEmpty, false, 0, errors.Wrap(err, "client.OrderDetailV8")
	}

	issueOKStatuses := []string{
		constants.OrderStatusPending,
		constants.OrderStatusTicketed,
	}

	if !lo.Contains(issueOKStatuses, orderDetail.Data.OrderStatus) {
		return nil, nil, enum.EMDStatusEmpty, false, 0, errors.New("invalid order status " + orderDetail.Data.OrderStatus)
	}

	reserInfos, ticketInfos, err := converts.ToDomainReservationInfos(&orderDetail.Data, bkSession, paxInfo)
	if err != nil {
		return nil, nil, enum.EMDStatusEmpty, false, 0, errors.Wrap(err, "converts.ToDomainReservationInfos")
	}

	isPending := false
	pendingDl := int64(0)

	if orderDetail.Data.OrderStatus == constants.OrderStatusPending {
		isPending = true
		pendingDl = time.Now().Add(120 * time.Minute).UnixMilli()
	}

	return reserInfos, ticketInfos, enum.EMDStatusEmpty, isPending, pendingDl, nil
}

func (a *pkfareAdapter) CancelBooking(ctx context.Context, bookingRef, orderNum string) error {

	clientReq := &entities.CancelOrderReq{
		Cancel: &entities.CancelOrderDataReq{
			OrderNum:   orderNum,
			VirtualPNR: bookingRef,
		},
	}

	_, err := a.client.CancelOrder(ctx, clientReq, bookingRef)
	return err
}

func (a *pkfareAdapter) CreateBooking(ctx context.Context, bookingDetails *domain.BookingDetails, tracingID string) (*domain.SvcCreateBookingResponse, error) {
	if bookingDetails.OriginFareData == nil {
		return nil, errors.New("bookingDetails.OriginFareData is nil")
	}

	clientReq := &entities.PreciseBookingV5Req{
		Booking: converts.ToBookingRequestClient(bookingDetails),
	}

	res, err := a.client.PreciseBookingV5(ctx, clientReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "client.BookFlight")
	}

	// For mock test lastTktTime
	if a.cfg.Env != commonConstants.EnvProduction && res != nil && bookingDetails != nil {
		itineraries := bookingDetails.Itineraries
		flights := res.BookingData.Flights

		if len(itineraries) == 2 {
			iti0 := itineraries[0]
			iti1 := itineraries[1]

			isRoundTripHKG_MNL :=
				(iti0.DepartPlace == "HKG" && iti0.ArrivalPlace == "MNL" && iti1.DepartPlace == "MNL" && iti1.ArrivalPlace == "HKG") ||
					(iti1.DepartPlace == "HKG" && iti1.ArrivalPlace == "MNL" && iti0.DepartPlace == "MNL" && iti0.ArrivalPlace == "HKG")

			isRoundTripSGN_TPE :=
				(iti0.DepartPlace == "SGN" && iti0.ArrivalPlace == "TPE" && iti1.DepartPlace == "TPE" && iti1.ArrivalPlace == "SGN") ||
					(iti1.DepartPlace == "SGN" && iti1.ArrivalPlace == "TPE" && iti0.DepartPlace == "TPE" && iti0.ArrivalPlace == "SGN")

			// Lấy departureTime thực từ segments
			firstSegments := res.BookingData.Segments[0]
			departureTime := time.UnixMilli(firstSegments.DepartureDate).UTC()
			now := time.Now().UTC()

			durationUntilDeparture := departureTime.Sub(now)
			if durationUntilDeparture <= 0 {
				departureTime = now
				durationUntilDeparture = 0
			}

			mid1 := now.Add(durationUntilDeparture / 2)
			mid2 := now.Add(durationUntilDeparture / 4)
			mid3 := now.Add(durationUntilDeparture / 8)

			for i := range flights {
				if (isRoundTripHKG_MNL && i > 0) || (!isRoundTripSGN_TPE && !isRoundTripHKG_MNL) {
					break
				}

				var t time.Time
				switch i {
				case 0:
					t = mid1
				case 1:
					t = mid2
				default:
					t = mid3.Add(time.Duration(i-2) * 15 * time.Minute)
				}

				flights[i].LastTktTime = t.Format(pkfareutils.DateFmtSec)
			}
		}
	}

	// Lastticketdate là 60 phút sau khi tạo booking
	// https://www.notion.so/deeptechjsc/Mapping-PKFare-Flight-HUB-93ea5e62a78743b6b067a70ffbe84684?pvs=4
	lastTicketingDate := time.Now().Add(time.Minute * 60)

	return &domain.SvcCreateBookingResponse{
		LastTicketingDate: lastTicketingDate.UnixMilli(),
		BookingRef:        res.BookingData.PNR,
		OrderNumRef:       res.BookingData.OrderNum,
	}, nil
}

func (a *pkfareAdapter) ConfirmFare(ctx context.Context, booking *domain.BookingSession, tracingID string) (*domain.TotalFareInfo, error) {
	clientReq := converts.ToPricingRequest(booking.Itineraries, booking.SearchRequest)

	res, err := a.client.PrecisePricingV10(ctx, clientReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "client.PrecisePricingV6")
	}

	searchFare := converts.ToDomainSearchTotalFareInfo(res.Solution)

	return searchFare.ConvertToTotal(), nil
}

func (a *pkfareAdapter) SearchFlights(ctx context.Context, req *domain.SearchFlightsRequest, tracingID string) ([]*domain.ResponseFlight, error) {
	if len(req.Itineraries) > 2 || req.FlightType == enum.FlightTypeDomestic {
		return nil, nil
	}

	out := []*domain.ResponseFlight{}
	var (
		wg      sync.WaitGroup
		mu      sync.Mutex
		errChan = make(chan error, len(constants.CabinClasses))
	)

	for _, class := range constants.CabinClasses {
		wg.Add(1)
		go func() {
			defer wg.Done()

			clientReq := &entities.ShoppingV8Req{
				SearchParams: &entities.SearchParams{
					Adults:    req.Passengers.ADT,
					Children:  req.Passengers.CHD,
					Infants:   req.Passengers.INF,
					Nonstop:   0,
					Airline:   "",
					Solutions: 0,
				},
			}

			for _, iti := range req.Itineraries {
				clientReq.SearchParams.SearchAirLegs = append(clientReq.SearchParams.SearchAirLegs, entities.SearchAirLeg{
					CabinClass:    class,
					DepartureDate: pkfareutils.UnixMlToDateString(iti.DepartDate),
					Origin:        iti.DepartPlace,
					Destination:   iti.ArrivalPlace,
				})
			}

			data, err := a.client.ShoppingV8(ctx, clientReq, tracingID)
			if err != nil {
				errChan <- errors.Wrap(err, "client.SearchFlight")
				return
			}

			log.Info("SearchFlights data here")

			// If env = QA, then random fare basis
			if a.cfg.Env == commonConstants.EnvQA || a.cfg.Env == commonConstants.EnvSandbox {
				log.Info("Case change farebasic")
				for i := range data.SearchDataRes.Segments {
					if (data.SearchDataRes.Segments[i].Airline == "AK" && data.SearchDataRes.Segments[i].BookingCode == "E" &&
						(data.SearchDataRes.Segments[i].Arrival == "SGN" || data.SearchDataRes.Segments[i].Departure == "SIN" ||
							data.SearchDataRes.Segments[i].Arrival == "SIN" || data.SearchDataRes.Segments[i].Departure == "SGN")) ||
						(data.SearchDataRes.Segments[i].Airline == "CI" && data.SearchDataRes.Segments[i].BookingCode == "H" &&
							(data.SearchDataRes.Segments[i].Arrival == "HKG" || data.SearchDataRes.Segments[i].Departure == "TPE" ||
								data.SearchDataRes.Segments[i].Arrival == "TPE" || data.SearchDataRes.Segments[i].Departure == "HKG")) {
						data.SearchDataRes.Segments[i].FareBasis = helpers.GenRandomCapitalLetter(3)
					}
				}
			}

			res, err := converts.ToDomainResponseFlights(data, req)
			if err != nil {
				errChan <- errors.Wrap(err, "ToDomainResponseFlights")
				return
			}

			mu.Lock()
			out = append(out, res...)
			mu.Unlock()
		}()
	}

	wg.Wait()

	close(errChan)

	for err := range errChan {
		return nil, err
	}

	return out, nil
}
