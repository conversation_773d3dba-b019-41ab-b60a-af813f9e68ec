package pkfareutils

import (
	"time"

	"github.com/pkg/errors"
)

const (
	DateFmtDefault = "2006-01-02 15:04"
	DateFmtSec     = "2006-01-02 15:04:05"
)

func UnixMlToDateString(unixMl int64) string {
	return time.UnixMilli(unixMl).UTC().Format("2006-01-02")
}

func PUnixMlToDateString(unixMl *int64) string {
	if unixMl == nil {
		return ""
	}

	return time.UnixMilli(*unixMl).UTC().Format("2006-01-02")
}

func UnixMlToTimeString(unixMl int64) string {
	return time.UnixMilli(unixMl).UTC().Format("15:04")
}

// 2024-11-15 11:30 => 1731670200000
func  DateStringToUnixMl(date string, format string) (int64, error) {
	if format == "" {
		format = DateFmtDefault
	}

	zoneUTC := time.FixedZone("UTC", 0)

	t, err := time.ParseInLocation(format, date, zoneUTC)
	if err != nil {
		return 0, errors.Wrap(err, "time.Parse")
	}
	return t.UnixMilli(), nil
}
