package entities

// type ShoppingV4Res struct {
// 	ErrorResponse
// 	SearchDataRes `json:"data"`
// }

// type ShoppingV4Req struct {
// 	SearchParams   *SearchParams   `json:"search"`
// 	Authentication *Authentication `json:"authentication"`
// }

// type SearchParams struct {
// 	Adults        int            `json:"adults"`
// 	Children      int            `json:"children"`
// 	Infants       int            `json:"infants"`
// 	Nonstop       int            `json:"nonstop"`
// 	Airline       string         `json:"airline"`
// 	Solutions     int            `json:"solutions"`
// 	SearchAirLegs []SearchAirLeg `json:"searchAirLegs"`
// }

// type SearchAirLeg struct {
// 	CabinClass    string `json:"cabinClass"`
// 	DepartureDate string `json:"departureDate"`
// 	Origin        string `json:"origin"`
// 	Destination   string `json:"destination"`
// }

type SearchDataRes struct {
	// SearchKey string `json:"searchKey"`
	// ShoppingKey string            `json:"shoppingKey"`
	Solutions []Solution        `json:"solutions"`
	Flights   []FlightSearchRes `json:"flights"`
	Segments  []Segment         `json:"segments"`
}

type JourneyMap struct {
	Journey0 []string `json:"journey_0"`
	Journey1 []string `json:"journey_1"`
}

type FlightOrderPricing struct {
	FlightID string `json:"flightId"`
	// JourneyTime   int    `json:"journeyTime"`
	TransferCount int `json:"transferCount"`
	// LastTktTime   string   `json:"lastTktTime"`
	SegmentIDs []string `json:"segmengtIds"`
}

type FlightSearchRes struct {
	FlightID string `json:"flightId"`
	// JourneyTime   int      `json:"journeyTime"`
	TransferCount int `json:"transferCount"`
	// LastTktTime   string   `json:"lastTktTime"`
	SegmentIDs []string `json:"segmengtIds"`
}
