package entities

type PrecisePricingV10Req struct {
	Authentication *Authentication `json:"authentication"`
	PricingRequest *PricingRequest `json:"pricing"`
}

type PrecisePricingV10Res struct {
	ErrorResponse
	PrecisePricingDataRes `json:"data"`
}

type PrecisePricingDataRes struct {
	Solution Solution  `json:"solution"`
	Flights  []Flight  `json:"flights"`
	Segments []Segment `json:"segments"`
	// AncillaryAvailability AncillaryAvailability `json:"ancillaryAvailability"`
}

type Baggage struct {
	CarryOn string `json:"carryOn"`
	Baggage string `json:"baggage"`
}

type Flight struct {
	FlightID string `json:"flightId"`
	// JourneyTime   int      `json:"journeyTime"`
	LastTktTime   string   `json:"lastTktTime"`
	TransferCount int      `json:"transferCount"`
	SegmentIds    []string `json:"segmentIds"`
}

type AncillaryAvailability struct {
	PaidBag  bool `json:"paidBag"`
	PaidSeat bool `json:"paidSeat"`
}

//

type PricingRequest struct {
	Journeys    *JourneyMapRequest `json:"journeys"`
	Adults      int                `json:"adults"`
	Children    int                `json:"children"`
	Infants     int                `json:"infants"`
	SolutionID  string             `json:"solutionId"`
	Cabin       string             `json:"cabin"`
	SolutionKey string             `json:"solutionKey"`
	Tag         string             `json:"tag"`
}

type JourneyMapRequest struct {
	Journey0 []JourneyDetails `json:"journey_0"`
	Journey1 []JourneyDetails `json:"journey_1,omitempty"`
}

type JourneyDetails struct {
	Airline       string `json:"airline"`
	FlightNum     string `json:"flightNum"`
	BookingCode   string `json:"bookingCode"`
	Departure     string `json:"departure"`
	Arrival       string `json:"arrival"`
	DepartureDate string `json:"departureDate"`
	DepartureTime string `json:"departureTime"`
	ArrivalDate   string `json:"arrivalDate"`
	ArrivalTime   string `json:"arrivalTime"`
}
