package entities

type Solution struct {
	// SolutionKey        string             `json:"solutionKey"`
	SolutionID string `json:"solutionId"`
	// FareType           string             `json:"fareType"`
	Currency           string     `json:"currency"`
	AdtFare            float64    `json:"adtFare"`
	AdtTax             float64    `json:"adtTax"`
	ChdFare            float64    `json:"chdFare"`
	ChdTax             float64    `json:"chdTax"`
	InfFare            float64    `json:"infFare"`
	InfTax             float64    `json:"infTax"`
	QCharge            float64    `json:"qCharge"`
	TktFee             float64    `json:"tktFee"`
	PlatformServiceFee float64    `json:"platformServiceFee"`
	Journeys           JourneyMap `json:"journeys"`
	// PlatingCarrier     string             `json:"platingCarrier"`
	MerchantFee float64     `json:"merchantFee"`
	Adults      int         `json:"adults"`
	Children    int         `json:"children"`
	Infants     int         `json:"infants"`
	BaggageMap  BaggageMap  `json:"baggageMap"`
	MiniRuleMap MiniRuleMap `json:"miniRuleMap"`
	// Baggages           map[string]Baggage `json:"baggages"`
	// BookingWithoutCard int                `json:"bookingWithoutCard"`
}

type MiniRuleMap struct {
	ADT []MiniRuleDetails `json:"ADT"`
	CHD []MiniRuleDetails `json:"CHD"`
	INF []MiniRuleDetails `json:"INF"`
}

type MiniRuleDetails struct {
	SegmentIndex []int      `json:"segmentIndex"`
	MiniRules    []MiniRule `json:"miniRules"`
}

type MiniRule struct {
	PenaltyType    int      `json:"penaltyType"`
	IsPermited     int      `json:"isPermited"`
	When           int      `json:"when"`
	NoShowTime     string   `json:"noShowTime"`
	NoShowTimeUnit string   `json:"noShowTimeUnit"`
	Amount         *float64 `json:"amount"`
	CurrencyCode   string   `json:"currencyCode"`
	Percent        *float64 `json:"percent"`
	BaseType       *int     `json:"baseType"`
}
