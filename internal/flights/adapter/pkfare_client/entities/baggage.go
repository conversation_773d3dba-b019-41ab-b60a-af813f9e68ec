package entities

type BaggageDetails struct {
	SegmentIndexList []int  `json:"segmentIndexList"`
	BaggageAmount    string `json:"baggageAmount"`
	BaggageWeight    string `json:"baggageWeight"`
	CarryOnAmount    string `json:"carryOnAmount"`
	CarryOnWeight    string `json:"carryOnWeight"`
	// CarryOnSize      string `json:"carryOnSize"`
}

type BaggageMap struct {
	ADT []BaggageDetails `json:"ADT"`
	CHD []BaggageDetails `json:"CHD"`
	INF []BaggageDetails `json:"INF"`
}
