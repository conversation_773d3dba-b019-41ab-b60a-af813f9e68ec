package entities

type TicketingReq struct {
	Authentication *Authentication   `json:"authentication"`
	Data           *TicketingDataReq `json:"ticketing"`
}

type TicketingDataReq struct {
	OrderNum string `json:"orderNum"`
	PNR      string `json:"PNR"`
}

type TicketingDataRes struct {
	// OrderNum string `json:"orderNum"`
	// OrderAmount        float64 `json:"orderAmount"`
	// TransactionExpense float64 `json:"transactionExpense"`
	PayAmount float64 `json:"payAmount"`
}

type TicketingRes struct {
	ErrorResponse
	Data TicketingDataRes `json:"data"`
}
