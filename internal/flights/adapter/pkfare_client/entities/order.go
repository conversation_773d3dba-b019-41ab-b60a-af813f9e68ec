package entities

type OrderPricingV4Req struct {
	Authentication *Authentication `json:"authentication"`
	OrderPricing   string          `json:"orderPricing"`
}

type OrderPricingV4Res struct {
	ErrorResponse
	Data OrderPricingDataRes `json:"data"`
}

type OrderPricingDataRes struct {
	OrderResult   OrderResultData      `json:"orderResult"`
	PricingResult PricingResultDataRes `json:"pricingResult"`
}

type PricingResultDataRes struct {
	Solution Solution             `json:"solution"`
	Flights  []FlightOrderPricing `json:"flights"`
	Segments []Segment            `json:"segments"`
}

type OrderResultData struct {
	OrderNum string `json:"orderNum"`
	PNR      string `json:"pnr"`
}

type OrderDetailV8Req struct {
	Authentication *Authentication     `json:"authentication"`
	Data           *OrderDetailDataReq `json:"data"`
}

type OrderDetailDataReq struct {
	OrderNum      string `json:"orderNum"`
	IncludeFields string `json:"includeFields"`
}

type OrderDetailV8Res struct {
	ErrorResponse
	Data OrderDetailDataRes `json:"data"`
}

type OrderDetailDataRes struct {
	OrderStatus string `json:"orderStatus"`
	// OrderNum    string `json:"orderNum"`
	// RefOrderNum string `json:"refOrderNum"`
	// OrderType string `json:"orderType"`
	// CreatedTime    string `json:"createdTime"`
	// PNR string `json:"pnr"`
	AirPNR string `json:"airPnr"`
	// PlatingCarrier string `json:"platingCarrier"`
	// PayGate        string          `json:"payGate"`
	// PaySerialNum   string          `json:"paySerialNum"`
	Passengers []Passenger `json:"passengers"`
	// Journeys   []OrderJourney `json:"journeys"`
	// Solutions  []OrderSolution `json:"solutions"`
	// CheckInInfos   []CheckInInfo   `json:"checkInInfos"`
	LastTktTime string `json:"lastTktTime"`
	// PermitVoid  int    `json:"permitVoid"`
	// AncillaryItems []interface{} `json:"ancillaryItems"`
	PNRList []PNRList `json:"pnrList"`
}

type PNRList struct {
	SegmentNo   int         `json:"segmentNo"`
	Departure   string      `json:"departure"`
	Arrival     string      `json:"arrival"`
	FlightNum   string      `json:"flightNum"`
	PNR         string      `json:"pnr"`
	AirPNR      string      `json:"airPnr"`
	CabinClass  string      `json:"cabinClass"`
	BookingCode string      `json:"bookingCode"`
	TicketNums  []TicketNum `json:"ticketNums"`
}

type TicketNum struct {
	TicketNum      string `json:"ticketNum"`
	PassengerIndex int    `json:"passengerIndex"`
}

// type OrderJourney struct {
// 	Segments      []OrderDetailSegment `json:"segments"`
// 	DepartureTime string               `json:"departureTime"`
// 	ArrivalTime   string               `json:"arrivalTime"`
// 	DepartureDate string               `json:"departureDate"`
// 	ArrivalDate   string               `json:"arrivalDate"`
// 	JourneyTime   int                  `json:"journeyTime"`
// 	TransferCount int                  `json:"transferCount"`
// }

// type CheckInInfo struct {
// 	FirstName      string `json:"firstName"`
// 	LastName       string `json:"lastName"`
// 	TicketNum      string `json:"ticketNum"`
// 	PNR            string `json:"pnr"`
// 	AirPNR         string `json:"airPnr"`
// 	Airline        string `json:"airline"`
// 	MailAccount    string `json:"mailAccount"`
// 	MailPassword   string `json:"mailPassword"`
// 	AirlineWebsite string `json:"airlineWebsite"`
// }

// type OrderSolution struct {
// 	Currency    string  `json:"currency"`
// 	AdtFare     float64 `json:"adtFare"`
// 	AdtTax      float64 `json:"adtTax"`
// 	ChdFare     float64 `json:"chdFare"`
// 	ChdTax      float64 `json:"chdTax"`
// 	InfFare     float64 `json:"infFare"`
// 	InfTax      float64 `json:"infTax"`
// 	TktFee      float64 `json:"tktFee"`
// 	MerchantFee float64 `json:"merchantFee"`
// 	DistCost    float64 `json:"distCost"`
// 	Coupon      float64 `json:"coupon"`
// 	BuyerAmount float64 `json:"buyerAmount"`
// }

type CancelOrderReq struct {
	Authentication *Authentication     `json:"authentication"`
	Cancel         *CancelOrderDataReq `json:"cancel"`
}

type CancelOrderDataReq struct {
	OrderNum   string `json:"orderNum"`
	VirtualPNR string `json:"virtualPnr"`
}

type CancelOrderRes struct {
	ErrorResponse
}
