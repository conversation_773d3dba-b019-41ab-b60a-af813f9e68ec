package entities

type PenaltyRequest struct {
	SolutionID string `json:"solutionId"`
}

type PenaltyV3Req struct {
	Authentication *Authentication `json:"authentication"`
	Penalty        *PenaltyReqData `json:"penalty"`
}

type PenaltyReqData struct {
	SolutionID string             `json:"solutionId"`
	Journeys   *JourneyMapRequest `json:"journeys"`
	Tag        string             `json:"tag,omitempty"`
}

type PenaltyV3Res struct {
	ErrorResponse
	PenaltyDataRes `json:"data"`
}

type PenaltyDataRes struct {
	Penalties []string `json:"penalties"`
	Remark    string   `json:"remark"`
}
