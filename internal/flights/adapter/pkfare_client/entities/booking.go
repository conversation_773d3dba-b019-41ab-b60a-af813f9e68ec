package entities

type PreciseBookingV5Req struct {
	Authentication *Authentication `json:"authentication"`
	Booking        *BookingRequest `json:"booking"`
}

type PreciseBookingV5Res struct {
	ErrorResponse
	BookingData BookingDataRes `json:"data"`
}

type BookingDataRes struct {
	OrderNum string `json:"orderNum"`
	PNR      string `json:"pnr"`
	// Solution Solution `json:"solution"`
	Flights  []Flight  `json:"flights"`
	Segments []Segment `json:"segments"`
}

type Contact struct {
	Name    string `json:"name"`
	Email   string `json:"email"`
	TelCode string `json:"telCode"`
	Mobile  string `json:"mobile"`
}

type BookingRequest struct {
	Passengers []Passenger     `json:"passengers"`
	Solution   SolutionRequest `json:"solution"`
	Contact    Contact         `json:"contact"`
}

type SolutionRequest struct {
	SolutionID string             `json:"solutionId"`
	AdtFare    float64            `json:"adtFare"`
	AdtTax     float64            `json:"adtTax"`
	ChdFare    float64            `json:"chdFare"`
	ChdTax     float64            `json:"chdTax"`
	InfFare    float64            `json:"infFare"`
	InfTax     float64            `json:"infTax"`
	TktFee     float64            `json:"tktFee"`
	Journeys   *JourneyMapRequest `json:"journeys"`
}

type Passenger struct {
	PassengerIndex           int    `json:"passengerIndex"`
	Birthday                 string `json:"birthday"`
	FirstName                string `json:"firstName"`
	LastName                 string `json:"lastName"`
	Nationality              string `json:"nationality"`
	PsgType                  string `json:"psgType"`
	Sex                      string `json:"sex"`
	CardType                 string `json:"cardType"`
	CardNum                  string `json:"cardNum"`
	CardExpiredDate          string `json:"cardExpiredDate"`
	AssociatedPassengerIndex int    `json:"associatedPassengerIndex,omitempty"`
	TicketNum                string `json:"ticketNum,omitempty"`
}
