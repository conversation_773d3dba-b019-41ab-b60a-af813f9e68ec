package entities

type Segment struct {
	SegmentID         string `json:"segmentId"`
	Airline           string `json:"airline"`
	FlightNum         string `json:"flightNum"`
	Equipment         string `json:"equipment"`
	CabinClass        string `json:"cabinClass"`
	BookingCode       string `json:"bookingCode"`
	AvailabilityCount int    `json:"availabilityCount"`
	Departure         string `json:"departure"`
	Arrival           string `json:"arrival"`
	DepartureTerminal string `json:"departureTerminal"`
	ArrivalTerminal   string `json:"arrivalTerminal"`
	DepartureDate     int64  `json:"departureDate"`
	ArrivalDate       int64  `json:"arrivalDate"`
	FlightTime        int    `json:"flightTime"`
	// StayTime     int    `json:"stayTime"`
	// CodeShare    string `json:"codeShare"`
	// OpFltNo      string `json:"opFltNo"`
	OpFltAirline     string `json:"opFltAirline"`
	FareBasis        string `json:"fareBasis"`
	StrDepartureDate string `json:"strDepartureDate"`
	StrDepartureTime string `json:"strDepartureTime"`
	StrArrivalDate   string `json:"strArrivalDate"`
	StrArrivalTime   string `json:"strArrivalTime"`
}

// type OrderDetailSegment struct {
// 	Airline           string `json:"airline"`
// 	FlightNum         string `json:"flightNum"`
// 	Equipment         string `json:"equipment"`
// 	CabinClass        string `json:"cabinClass"`
// 	BookingCode       string `json:"bookingCode"`
// 	Departure         string `json:"departure"`
// 	Arrival           string `json:"arrival"`
// 	DepartureTerminal string `json:"departureTerminal"`
// 	ArrivalTerminal   string `json:"arrivalTerminal"`
// 	DepartureDate     string `json:"departureDate"`
// 	ArrivalDate       string `json:"arrivalDate"`
// 	DepartureTime     string `json:"departureTime"`
// 	ArrivalTime       string `json:"arrivalTime"`
// 	FlightTime        int    `json:"flightTime"`
// 	CodeShare         string `json:"codeShare"`
// 	OpFltNo           string `json:"opFltNo"`
// 	OpFltAirline      string `json:"opFltAirline"`
// }
