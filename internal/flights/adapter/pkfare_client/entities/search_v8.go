package entities

type ShoppingV8Res struct {
	ErrorResponse
	SearchDataRes `json:"data"`
}

type ShoppingV8Req struct {
	SearchParams   *SearchParams   `json:"search"`
	Authentication *Authentication `json:"authentication"`
}

type SearchParams struct {
	Adults         int            `json:"adults"`
	Children       int            `json:"children"`
	Infants        int            `json:"infants"`
	Nonstop        int            `json:"nonstop"`
	Airline        string         `json:"airline"`
	Solutions      int            `json:"solutions"`
	Tag            string         `json:"tag"`
	ReturnTagPrice string         `json:"returnTagPrice"`
	SearchAirLegs  []SearchAirLeg `json:"searchAirLegs"`
}

type SearchAirLeg struct {
	CabinClass    string `json:"cabinClass"`
	DepartureDate string `json:"departureDate"`
	Origin        string `json:"origin"`
	Destination   string `json:"destination"`
	Airplane      string `json:"airplane"`
}
