package converts

import (
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/spf13/cast"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/entities"
	pkfareutils "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/pkfare_utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

const maximumItiLen = 2

// var PkFareIgnoreAirline = []string{
// 	"VN",
// 	"QH",
// 	"VU",
// }

// func filterFlight(flight *domain.FlightItinerary) bool {
// 	return commonHelper.Contains(PkFareIgnoreAirline, flight.CarrierMarketing) ||
// 		commonHelper.Contains(PkFareIgnoreAirline, flight.CarrierOperator)
// }

func getFlightsFromJourney(journey entities.JourneyMap, flightMap map[string]entities.FlightSearchRes) ([]entities.FlightSearchRes, error) {
	out := []entities.FlightSearchRes{}

	// J1
	for _, flightID := range journey.Journey0 {
		flight := flightMap[flightID]

		if flight.FlightID == "" {
			return nil, errors.New("can not map flight flight from journey 1 " + flightID)
		}

		out = append(out, flight)
	}
	// J2
	for _, flightID := range journey.Journey1 {
		flight := flightMap[flightID]

		if flight.FlightID == "" {
			return nil, errors.New("can not map flight flight from journey 2 " + flightID)
		}

		out = append(out, flight)
	}

	if len(out) > maximumItiLen {
		return nil, errors.New("maximum itinerary length exceeded")
	}

	return out, nil
}

func ToDomainResponseFlights(in *entities.ShoppingV8Res, searchReq *domain.SearchFlightsRequest) ([]*domain.ResponseFlight, error) {
	out := []*domain.ResponseFlight{}

	flightsMap := getFlightMap(in.SearchDataRes.Flights)
	segmentsMap := getSegmentMap(in.SearchDataRes.Segments)

	requestAirports := lo.Map(searchReq.Itineraries, func(itinerary *domain.ItineraryRequest, _ int) string {
		return fmt.Sprintf("%s-%s", itinerary.DepartPlace, itinerary.ArrivalPlace)
	})

	for _, fare := range in.SearchDataRes.Solutions {
		flights, err := getFlightsFromJourney(fare.Journeys, flightsMap)
		if err != nil {
			return nil, errors.Wrap(err, "getFlightsFromJourney")
		}

		recFlight, err := ToDomainResponseFlight(fare, flights, segmentsMap, fare.BaggageMap)
		if err != nil {
			return nil, err
		}

		shouldSkip := false

		for _, iti := range recFlight.Itineraries {
			if !lo.Contains(requestAirports, fmt.Sprintf("%s-%s", iti.DepartPlace, iti.ArrivalPlace)) || iti.FlightNumber == "" || iti.FlightNumber == "null" {
				shouldSkip = true
				break
			}
		}

		if shouldSkip {
			continue
		}

		out = append(out, recFlight)
	}

	return out, nil
}

func ToDomainSearchTotalFareInfo(fare entities.Solution) domain.SearchTotalFareInfo {
	var totalFareAmount, totalFareBasic, totalTaxAmount float64
	paxes := []*domain.ItineraryPaxFare{}

	totalADTFees := fare.PlatformServiceFee + fare.MerchantFee + fare.QCharge

	feePerAdt := totalADTFees / float64(fare.Adults)
	fare.AdtTax += feePerAdt + fare.TktFee

	if fare.Adults != 0 {
		paxes = append(paxes, &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeAdult,
			FareAmount: fare.AdtFare + fare.AdtTax,
			FareBasic:  fare.AdtFare,
			TaxAmount:  fare.AdtTax,
			Currency:   fare.Currency,
		})

		totalFareAmount += (fare.AdtFare + fare.AdtTax) * float64(fare.Adults)
		totalFareBasic += fare.AdtFare * float64(fare.Adults)
		totalTaxAmount += fare.AdtTax * float64(fare.Adults)
	}

	if fare.Children != 0 {
		fare.ChdTax += fare.TktFee

		paxes = append(paxes, &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeChildren,
			FareAmount: fare.ChdFare + fare.ChdTax,
			FareBasic:  fare.ChdFare,
			TaxAmount:  fare.ChdTax,
			Currency:   fare.Currency,
		})

		totalFareAmount += (fare.ChdFare + fare.ChdTax) * float64(fare.Children)
		totalFareBasic += fare.ChdFare * float64(fare.Children)
		totalTaxAmount += fare.ChdTax * float64(fare.Children)
	}

	if fare.Infants != 0 {
		fare.InfTax += fare.TktFee

		paxes = append(paxes, &domain.ItineraryPaxFare{
			PaxType:    enum.PaxTypeInfant,
			FareAmount: float64(fare.InfFare) + fare.InfTax,
			FareBasic:  fare.InfFare,
			TaxAmount:  fare.InfTax,
			Currency:   fare.Currency,
		})

		totalFareAmount += (fare.InfFare + fare.InfTax) * float64(fare.Infants)
		totalFareBasic += fare.InfFare * float64(fare.Infants)
		totalTaxAmount += fare.InfTax * float64(fare.Infants)
	}

	return domain.SearchTotalFareInfo{
		TotalPaxFares:       paxes,
		BaseTotalFareAmount: totalFareAmount,
		TotalFareAmount:     totalFareAmount,
		TotalFareBasic:      totalFareBasic,
		TotalTaxAmount:      totalTaxAmount,
		Currency:            fare.Currency,
	}
}

func ToFlightsFromSearchFlights(in []entities.FlightOrderPricing) []entities.Flight {
	out := []entities.Flight{}

	for _, flight := range in {
		out = append(out, entities.Flight{
			FlightID:      flight.FlightID,
			TransferCount: flight.TransferCount,
			SegmentIds:    flight.SegmentIDs,
		})
	}

	return out
}

func ToSearchFlightsFromFlights(ins []entities.Flight) []entities.FlightSearchRes {
	out := []entities.FlightSearchRes{}

	for _, in := range ins {
		out = append(out, entities.FlightSearchRes{
			FlightID:      in.FlightID,
			TransferCount: in.TransferCount,
			SegmentIDs:    in.SegmentIds,
		})
	}

	return out
}

func ToDomainIties(fare entities.Solution, inputFlights []entities.Flight, segments []entities.Segment) ([]*domain.FlightItinerary, error) {
	segmentMap := getSegmentMap(segments)

	flights := ToSearchFlightsFromFlights(inputFlights)

	flightMap := getFlightMap(flights)

	fJours, err := getFlightsFromJourney(fare.Journeys, flightMap)
	if err != nil {
		return nil, errors.Wrap(err, "getFlightsFromJourney")
	}

	ities := []*domain.FlightItinerary{}

	lastSegmentIdx := 0
	for idx, flight := range fJours {
		iti, err := toDomainFlightItinerary(flight, segmentMap, fare.SolutionID, idx+1, fare.BaggageMap, lastSegmentIdx)
		if err != nil {
			return nil, err
		}

		// if filterFlight(iti) {
		// 	continue
		// }

		ities = append(ities, iti)

		lastSegmentIdx += len(flight.SegmentIDs)
	}

	return ities, nil
}

func GetMiniRuleSegementMap(itineraries []*domain.FlightItinerary) map[int]*domain.MiniRuleSegment {
	segmentMap := make(map[int]*domain.MiniRuleSegment)

	index := 1
	for _, iti := range itineraries {
		for _, seg := range iti.Segments {
			segmentMap[index] = &domain.MiniRuleSegment{
				ItineraryIndex: iti.Index,
				SegmentIndex:   seg.Index,
			}
			index++
		}
	}

	return segmentMap
}

func ToDomainResponseFlight(fare entities.Solution, flights []entities.FlightSearchRes, segmentMap map[string]entities.Segment, bagsMap entities.BaggageMap) (*domain.ResponseFlight, error) {
	out := &domain.ResponseFlight{
		FlightID:            helpers.GenerateFlightID(enum.FlightProviderPkfare),
		SearchTotalFareInfo: ToDomainSearchTotalFareInfo(fare),
		Provider:            enum.FlightProviderPkfare,
		Metadata: []*domain.Metadata{
			{
				Key:   "solution",
				Value: fare,
			},
		},
		OptionType: enum.FlightOptionTypeRecommend,
	}

	ities := []*domain.FlightItinerary{}

	lastSegmentIdx := 0
	for idx, flight := range flights {
		iti, err := toDomainFlightItinerary(flight, segmentMap, fare.SolutionID, idx+1, bagsMap, lastSegmentIdx)
		if err != nil {
			return nil, err
		}

		// if filterFlight(iti) {
		// 	continue
		// }

		ities = append(ities, iti)

		lastSegmentIdx += len(flight.SegmentIDs)
	}

	out.Itineraries = ities

	miniRuleSegmentMap := GetMiniRuleSegementMap(ities)

	out.MiniRules = ToDomainMiniRules(fare, miniRuleSegmentMap)

	return out, nil
}

func ToDomainMiniRules(fare entities.Solution, miniRuleSegmentMap map[int]*domain.MiniRuleSegment) []*domain.MiniRule {
	miniRuleMap := fare.MiniRuleMap

	out := make([]*domain.MiniRule, 0)

	out = append(out, mapMiniRuleDetails(enum.PaxTypeAdult, miniRuleMap.ADT, miniRuleSegmentMap)...)
	out = append(out, mapMiniRuleDetails(enum.PaxTypeChildren, miniRuleMap.CHD, miniRuleSegmentMap)...)
	out = append(out, mapMiniRuleDetails(enum.PaxTypeInfant, miniRuleMap.INF, miniRuleSegmentMap)...)

	return out
}

func removeDuplicatePenaltyRules(rules []*domain.PenaltyRule) []*domain.PenaltyRule {
	seen := make(map[string]bool)
	unique := make([]*domain.PenaltyRule, 0, len(rules))

	for _, rule := range rules {
		hash, err := json.Marshal(rule)
		if err != nil {
			continue
		}
		key := string(hash)

		if seen[key] {
			continue
		}
		seen[key] = true
		unique = append(unique, rule)
	}

	return unique
}

func mapMiniRuleDetails(paxType enum.PaxType, details []entities.MiniRuleDetails, segmentMap map[int]*domain.MiniRuleSegment) []*domain.MiniRule {
	// map miniruleSegments to key string => PenaltyRule List
	groupedRules := make(map[string][]*domain.PenaltyRule)

	for _, detail := range details {
		segments := mapSegments(detail.SegmentIndex, segmentMap)
		segmentKey := segmentsKey(segments)

		for _, miniRule := range detail.MiniRules {
			penaltyRule := &domain.PenaltyRule{
				PenaltyType:    enum.PenaltyTypeMap[miniRule.PenaltyType],
				IsPermitted:    enum.PermittedMap[miniRule.IsPermited],
				Situation:      enum.SituationMap[miniRule.When],
				Amount:         miniRule.Amount,
				Percent:        miniRule.Percent,
				Currency:       miniRule.CurrencyCode,
				NoShowTime:     formatNoShowTime(miniRule.NoShowTime, miniRule.NoShowTimeUnit),
				NoShowTimeUnit: miniRule.NoShowTimeUnit,
			}

			if miniRule.BaseType != nil {
				penaltyRule.BaseType = enum.BaseTypeMap[*miniRule.BaseType]
			} else {
				penaltyRule.BaseType = enum.BaseTypeMap[enum.BaseTypeFareNoneInt]
			}

			groupedRules[segmentKey] = append(groupedRules[segmentKey], penaltyRule)
		}
	}

	miniRules := make([]*domain.MiniRule, 0, len(groupedRules))
	for segmentKey, penaltyRules := range groupedRules {
		segments := parseSegmentKey(segmentKey)

		if len(penaltyRules) == 0 {
			continue
		}

		uniquePenaltyRules := removeDuplicatePenaltyRules(penaltyRules)

		miniRules = append(miniRules, &domain.MiniRule{
			PaxType:      paxType,
			Segments:     segments,
			PenaltyRules: uniquePenaltyRules,
		})
	}

	return miniRules
}

func mapSegments(segmentIndexs []int, segmentMap map[int]*domain.MiniRuleSegment) []*domain.MiniRuleSegment {
	segments := make([]*domain.MiniRuleSegment, 0, len(segmentIndexs))
	for _, idx := range segmentIndexs {
		if seg, ok := segmentMap[idx]; ok {
			segments = append(segments, seg)
		}
	}
	return segments
}

func formatNoShowTime(noShowTime string, noShowTimeUnit string) string {
	if noShowTime == "" {
		return ""
	}

	// Convert noShowTime to int
	timeValue, err := strconv.Atoi(noShowTime)
	if err != nil {
		return noShowTime
	}

	if timeValue == 0 || timeValue == -1 {
		timeValue = 0
	}

	isNegative := timeValue < 0
	absTime := int(math.Abs(float64(timeValue)))

	if noShowTimeUnit == constants.Hour {
		absTime += 6
	} else if noShowTimeUnit == constants.Minute {
		absTime += 360
	}

	if isNegative {
		return "-" + strconv.Itoa(absTime)
	}
	return strconv.Itoa(absTime)
}

func segmentsKey(segments []*domain.MiniRuleSegment) string {
	if len(segments) == 0 {
		return ""
	}

	keys := make([]string, len(segments))
	for i, seg := range segments {
		keys[i] = fmt.Sprintf("%d-%d", seg.ItineraryIndex, seg.SegmentIndex)
	}

	sort.Strings(keys)
	return strings.Join(keys, ",")
}

func parseSegmentKey(key string) []*domain.MiniRuleSegment {
	if key == "" {
		return nil
	}

	parts := strings.Split(key, ",")
	result := make([]*domain.MiniRuleSegment, 0, len(parts))

	for _, part := range parts {
		tokens := strings.Split(strings.TrimSpace(part), "-")
		if len(tokens) != 2 {
			continue
		}

		itineraryIndex, err := strconv.Atoi(tokens[0])
		if err != nil {
			log.Error("invalid itinerary index", log.String("token", tokens[0]), log.Any("err", err))
			continue
		}

		segmentIndex, err := strconv.Atoi(tokens[1])
		if err != nil {
			log.Error("invalid segment index", log.String("token", tokens[1]), log.Any("err", err))
			continue
		}

		result = append(result, &domain.MiniRuleSegment{
			ItineraryIndex: itineraryIndex,
			SegmentIndex:   segmentIndex,
		})
	}

	return result
}

func toDomainItinerarySegment(in entities.Segment, idx int) (*domain.ItinerarySegment, error) {
	strDepartDt := fmt.Sprintf("%s %s", in.StrDepartureDate, in.StrDepartureTime)
	strArrivalDt := fmt.Sprintf("%s %s", in.StrArrivalDate, in.StrArrivalTime)

	departDt, err := pkfareutils.DateStringToUnixMl(strDepartDt, "")
	if err != nil {
		return nil, errors.Wrap(err, "invalid depart date "+strDepartDt)
	}

	arrivalDt, err := pkfareutils.DateStringToUnixMl(strArrivalDt, "")
	if err != nil {
		return nil, errors.Wrap(err, "invalid depart arrival "+strArrivalDt)
	}

	out := &domain.ItinerarySegment{
		Index:            idx,
		DepartPlace:      in.Departure,
		DepartDate:       departDt,
		DepartTerminal:   in.DepartureTerminal,
		ArrivalPlace:     in.Arrival,
		ArrivalDate:      arrivalDt,
		ArrivalTerminal:  in.ArrivalTerminal,
		CarrierMarketing: in.Airline,
		CarrierOperator:  in.OpFltAirline,
		FlightNumber:     in.FlightNum,
		Aircraft:         constants.GetAirCraftName(in.Equipment),
		BookingClass:     in.BookingCode,
		CabinClassCode:   in.CabinClass,
		FareBasis:        in.FareBasis,
		FlightDuration:   in.FlightTime,
		Availability:     in.AvailabilityCount,
		CabbinClass:      in.CabinClass,
	}

	out.DepartDt = helpers.ToUTCDateTime(departDt)
	out.ArrivalDt = helpers.ToUTCDateTime(arrivalDt)

	return out, nil
}

func getBagQuantity(input string) int64 {
	if strings.HasSuffix(input, "PC") {
		return cast.ToInt64(strings.TrimSuffix(input, "PC"))
	}

	return 0
}

func getBagWeight(input string, quan int64) string {
	if input == "" {
		if quan == 1 {
			return "Piece"
		} else {
			return "Pieces"
		}
	} else {
		return strings.ToLower(input)
	}
}

func toDomainBaggageInfo(in entities.BaggageDetails, paxType enum.PaxType) []*domain.BaggageInfo {
	out := []*domain.BaggageInfo{}

	bagQuan := getBagQuantity(in.BaggageAmount)
	if in.BaggageWeight != "" || bagQuan != 0 {

		out = append(out, &domain.BaggageInfo{
			Name:          getBagWeight(in.BaggageWeight, bagQuan),
			IsHandBaggage: false,
			Quantity:      bagQuan,
			PaxType:       paxType,
		})
	}

	caryQuan := getBagQuantity(in.CarryOnAmount)
	if in.CarryOnWeight != "" || caryQuan != 0 {

		out = append(out, &domain.BaggageInfo{
			Name:          getBagWeight(in.CarryOnWeight, caryQuan),
			IsHandBaggage: true,
			Quantity:      caryQuan,
			PaxType:       paxType,
		})
	}

	return out
}

func getItiFreeBaggs(bagsMap entities.BaggageMap, lastSegIdx int) []*domain.BaggageInfo {
	out := []*domain.BaggageInfo{}
	currentSeg := lastSegIdx + 1

	if len(bagsMap.ADT) != 0 {
		itiBag, ok := lo.Find(bagsMap.ADT, func(bag entities.BaggageDetails) bool {
			return lo.Contains(bag.SegmentIndexList, currentSeg)
		})

		if ok {
			bags := toDomainBaggageInfo(itiBag, enum.PaxTypeAdult)
			out = append(out, bags...)
		}
	}

	if len(bagsMap.CHD) != 0 {
		itiBag, ok := lo.Find(bagsMap.CHD, func(bag entities.BaggageDetails) bool {
			return lo.Contains(bag.SegmentIndexList, currentSeg)
		})

		if ok {
			bags := toDomainBaggageInfo(itiBag, enum.PaxTypeChildren)
			out = append(out, bags...)
		}
	}

	if len(bagsMap.INF) != 0 {
		itiBag, ok := lo.Find(bagsMap.INF, func(bag entities.BaggageDetails) bool {
			return lo.Contains(bag.SegmentIndexList, currentSeg)
		})

		if ok {
			bags := toDomainBaggageInfo(itiBag, enum.PaxTypeInfant)
			out = append(out, bags...)
		}
	}

	return out
}

func toDomainFlightItinerary(flight entities.FlightSearchRes, segmentMap map[string]entities.Segment, bookingKey string, idx int, bagsMap entities.BaggageMap, lastSegIdx int) (*domain.FlightItinerary, error) {
	segments := []*domain.ItinerarySegment{}

	for index, segID := range flight.SegmentIDs {
		seg := segmentMap[segID]
		itiSeg, err := toDomainItinerarySegment(seg, index+1)
		if err != nil {
			return nil, err
		}

		segments = append(segments, itiSeg)
	}

	firstSeg := segments[0]
	lastSeg := segments[len(segments)-1]

	avai := firstSeg.Availability
	if avai > 9 {
		avai = 9
	}

	out := &domain.FlightItinerary{
		Index:            idx,
		FareBasis:        firstSeg.FareBasis,
		CabinClass:       firstSeg.CabbinClass,
		CabinClassCode:   firstSeg.CabinClassCode,
		BookingClass:     firstSeg.BookingClass,
		Availability:     avai,
		DepartPlace:      firstSeg.DepartPlace,
		DepartDate:       firstSeg.DepartDate,
		ArrivalPlace:     lastSeg.ArrivalPlace,
		ArrivalDate:      lastSeg.ArrivalDate,
		CarrierMarketing: firstSeg.CarrierMarketing,
		CarrierOperator:  firstSeg.CarrierOperator,
		FlightNumber:     firstSeg.FlightNumber,
		// FlightDuration:     flight.JourneyTime, Chi tra tg bay, thieu tg dung. Tinh lai o query layer
		StopNumber:         flight.TransferCount,
		FreeBaggage:        getItiFreeBaggs(bagsMap, lastSegIdx),
		ProviderBookingKey: bookingKey,
		Segments:           segments,
	}

	out.GenerateItiID()

	return out, nil
}

func getSegmentMap(segs []entities.Segment) map[string]entities.Segment {
	out := map[string]entities.Segment{}

	for _, seg := range segs {
		out[seg.SegmentID] = seg
	}

	return out
}

func getFlightMap(flights []entities.FlightSearchRes) map[string]entities.FlightSearchRes {
	out := map[string]entities.FlightSearchRes{}

	for _, flight := range flights {
		out[flight.FlightID] = flight
	}

	return out
}
