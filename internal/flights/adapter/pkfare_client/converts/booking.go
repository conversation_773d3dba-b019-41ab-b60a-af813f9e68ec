package converts

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/entities"
	pkfareutils "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/pkfare_utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func ToBookingRequestClient(booking *domain.BookingDetails) *entities.BookingRequest {
	firstIti := booking.Itineraries[0]
	originFare := booking.OriginFareData

	var adtFare, adtTax, chdFare, chdTax, infFare, infTax float64

	for _, paxFare := range originFare.TotalPaxFares {
		switch paxFare.PaxType {
		case enum.PaxTypeAdult:
			adtFare = paxFare.FareBasic
			adtTax = paxFare.TaxAmount
		case enum.PaxTypeChildren:
			chdFare = paxFare.FareBasic
			chdTax = paxFare.TaxAmount
		case enum.PaxTypeInfant:
			infFare = paxFare.FareBasic
			infTax = paxFare.TaxAmount
		}
	}

	paxes := ToBookingPassengers(booking.ListPax)

	paxes = assignINF(paxes)

	contact := booking.ContactInfo

	return &entities.BookingRequest{
		Contact: entities.Contact{
			Name:    contact.GivenName + " " + contact.Surname,
			Email:   "<EMAIL>",
			TelCode: "+" + contact.PhoneCode,
			Mobile:  contact.Phone,
		},
		Passengers: paxes,
		Solution: entities.SolutionRequest{
			SolutionID: firstIti.ProviderBookingKey,
			AdtFare:    adtFare,
			AdtTax:     adtTax,
			ChdFare:    chdFare,
			ChdTax:     chdTax,
			InfFare:    infFare,
			InfTax:     infTax,
			TktFee:     0,
			Journeys:   ToJourneyMapRequest(booking.Itineraries),
		},
	}
}

func assignINF(paxes []entities.Passenger) []entities.Passenger {
	avaiPaxID := []int{}

	for _, pax := range paxes {
		if pax.PsgType == "ADT" {
			avaiPaxID = append(avaiPaxID, pax.PassengerIndex)
		}
	}

	avaiIdx := 0

	for i, pax := range paxes {
		if pax.PsgType == "INF" {
			if avaiIdx < len(avaiPaxID) {
				paxes[i].AssociatedPassengerIndex = avaiPaxID[avaiIdx]
				avaiIdx++
			}
		}
	}

	return paxes
}

func ToBookingPassengers(ins []*domain.PaxInfo) []entities.Passenger {
	out := make([]entities.Passenger, 0, len(ins))

	for idx, reqPax := range ins {
		out = append(out, ToBookingPassenger(reqPax, idx+1))
	}

	return out
}

func getPaxSex(in commonEnum.GenderType) string {
	if in == commonEnum.GenderTypeMale {
		return "M"
	}

	return "F"
}

func ToBookingPassenger(in *domain.PaxInfo, idx int) entities.Passenger {
	out := entities.Passenger{
		PassengerIndex:  idx,
		Birthday:        pkfareutils.PUnixMlToDateString(in.DOB),
		FirstName:       in.GivenName,
		LastName:        in.Surname,
		Nationality:     in.Nationality,
		PsgType:         string(in.Type),
		Sex:             getPaxSex(in.Gender),
		CardType:        "",
		CardNum:         "",
		CardExpiredDate: "",
	}

	if in.Passport != nil {
		out.CardType = "P"
		out.CardNum = in.Passport.Number
		out.CardExpiredDate = pkfareutils.UnixMlToDateString(in.Passport.ExpiryDate)
	}

	return out
}
