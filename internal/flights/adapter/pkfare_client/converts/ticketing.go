package converts

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
)

func parseAirPNR(input string) string {
	items := strings.Split(input, "/")

	if len(items) < 2 || items[1] == "" {
		return input
	}

	return items[1]
}

func itiETicketMapKey(itiIdx, paxId int) string {
	return fmt.Sprintf("%d-%d", itiIdx, paxId)
}

func getItiTicketKey(paxIndex, itiIndex, segIndex int) string {
	return fmt.Sprintf("%d-%d-%d", paxIndex, itiIndex, segIndex)
}

func mappingTicketsV2(passengers []entities.Passenger, itineraries []*domain.FlightItinerary) ([]*domain.ETicketInfo, error) {
	out := []*domain.ETicketInfo{}

	seenItiTicket := map[string]bool{}

	for _, pax := range passengers {
		ticketNums := strings.Split(pax.TicketNum, "/")
		for index, ticketNum := range ticketNums {

			itiIndex := helpers.GetItiIndexByLinearSegmentIdx(itineraries, index+1)

			uniKey := getItiTicketKey(pax.PassengerIndex, itiIndex, index+1)
			if seenItiTicket[uniKey] {
				continue
			}

			out = append(out, &domain.ETicketInfo{
				PaxID:           pax.PassengerIndex,
				PaxName:         pax.FirstName + " " + pax.LastName,
				ItineraryNumber: itiIndex,
				TicketNumber:    ticketNum,
				SegmentIndex:    index + 1,
			})

			seenItiTicket[uniKey] = true
		}
	}

	return out, nil
}

func ToDomainReservationInfos(in *entities.OrderDetailDataRes, bkSession *domain.BookingSession, paxInfo []*domain.PaxInfo) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, error) {
	reserInfo := []*domain.IssueTicketSvcReservationInfo{}
	airPNR := parseAirPNR(in.AirPNR)

	if len(in.PNRList) == 0 {

		for _, iti := range bkSession.Itineraries {
			reserInfo = append(reserInfo, &domain.IssueTicketSvcReservationInfo{
				ItineraryIndex:  iti.Index,
				ReservationCode: airPNR,
			})
		}

		etickets, err := mappingTicketsV2(in.Passengers, bkSession.Itineraries)
		if err != nil {
			return nil, nil, errors.Wrap(err, "mappingTicketsV2")
		}

		return reserInfo, etickets, nil
	}

	etickets := []*domain.ETicketInfo{}

	pPaxByIndex := map[int]entities.Passenger{}
	paxByKey := map[string]*domain.PaxInfo{}

	for _, pax := range paxInfo {
		paxByKey[helpers.GetPaxKey(pax.GivenName, pax.Surname, pax.Gender)] = pax
	}

	for _, pPax := range in.Passengers {
		pPaxByIndex[pPax.PassengerIndex] = pPax
	}

	itiReserInfoMap := map[int]*domain.IssueTicketSvcReservationInfo{}

	itiETicketMap := map[string]*domain.ETicketInfo{}

	for _, pnrInfo := range in.PNRList {
		itiIndex := helpers.GetItiIndexByLinearSegmentIdx(bkSession.Itineraries, pnrInfo.SegmentNo)

		pnr := parseAirPNR(pnrInfo.AirPNR)
		if pnr == "" {
			pnr = airPNR
		}

		if itiReserInfoMap[itiIndex] == nil {
			itiReserInfoMap[itiIndex] = &domain.IssueTicketSvcReservationInfo{
				ItineraryIndex:  itiIndex,
				ReservationCode: pnr,
			}
		}

		for _, ticket := range pnrInfo.TicketNums {
			pPax := pPaxByIndex[ticket.PassengerIndex]

			if pPax.PassengerIndex == 0 {
				log.Error("[ISSUE_TICKET] pPax.PassengerIndex == 0", log.Int("pPax.PassengerIndex", ticket.PassengerIndex))
				continue
			}

			pax := paxByKey[helpers.GetPaxKey(pPax.FirstName, pPax.LastName, getPaxGender(pPax.Sex))]

			if pax == nil {
				log.Error("[ISSUE_TICKET] pax == nil", log.String("pax", helpers.GetPaxKey(pPax.FirstName, pPax.LastName, getPaxGender(pPax.Sex))))
				continue
			}

			key := itiETicketMapKey(itiIndex, pax.ID)

			if itiETicketMap[key] == nil {
				itiETicketMap[key] = &domain.ETicketInfo{
					PaxID:           pax.ID,
					PaxName:         fmt.Sprintf("%s %s", pax.Surname, pax.GivenName),
					ItineraryNumber: itiIndex,
					TicketNumber:    ticket.TicketNum,
					SegmentIndex:    pnrInfo.SegmentNo,
					// EMDInfos:        []*domain.EMDInfo{},
				}
			} else if itiETicketMap[key].TicketNumber != ticket.TicketNum {
				if pnrInfo.SegmentNo < itiETicketMap[key].SegmentIndex {
					itiETicketMap[key].TicketNumber = ticket.TicketNum + "/" + itiETicketMap[key].TicketNumber
				} else {
					itiETicketMap[key].TicketNumber += "/" + ticket.TicketNum
				}
			}
		}
	}

	for _, iti := range bkSession.Itineraries {
		info := itiReserInfoMap[iti.Index]
		if info != nil {
			reserInfo = append(reserInfo, info)
		} else {
			reserInfo = append(reserInfo, &domain.IssueTicketSvcReservationInfo{
				ItineraryIndex:  iti.Index,
				ReservationCode: airPNR,
			})
		}
	}

	for _, value := range itiETicketMap {
		etickets = append(etickets, value)
	}

	return reserInfo, etickets, nil
}

func getPaxGender(sex string) commonEnum.GenderType {
	if sex == "M" {
		return commonEnum.GenderTypeMale
	}

	return commonEnum.GenderTypeFeMale
}

func SyncBooking(in *entities.PricingResultDataRes, bkFlight *domain.BookingSession) (bool, error) {
	hasChange := false

	trueFlight := ToFlightsFromSearchFlights(in.Flights)

	newIties, err := ToDomainIties(in.Solution, trueFlight, in.Segments)
	if err != nil {
		return false, errors.Wrap(err, "ToDomainIties")
	}

	ities := bkFlight.Itineraries

	for i := 0; i < len(newIties); i++ {
		bookingIti := ities[i]
		itiChange := false

		if len(bookingIti.Segments) != len(newIties[i].Segments) {
			return true, nil
		}

		for j := 0; j < len(newIties[i].Segments); j++ {
			bookingSegment := bookingIti.Segments[j]
			newSeg := newIties[i].Segments[j]

			if bookingSegment.DepartDate != newSeg.DepartDate || bookingSegment.ArrivalDate != newSeg.ArrivalDate {
				bookingSegment.DepartDate = newSeg.DepartDate
				bookingSegment.ArrivalDate = newSeg.ArrivalDate
				bookingSegment.DepartDt = helpers.ToUTCDateTime(bookingSegment.DepartDate)
				bookingSegment.ArrivalDt = helpers.ToUTCDateTime(bookingSegment.ArrivalDate)
				itiChange = true
				hasChange = true
			}
		}

		if itiChange {
			var err error

			firstSeg := bookingIti.Segments[0]
			lastSeg := bookingIti.Segments[len(bookingIti.Segments)-1]

			bookingIti.DepartDate = firstSeg.DepartDate
			bookingIti.ArrivalDate = lastSeg.ArrivalDate

			bookingIti.DepartDt, err = helpers.ParseFakeUTCToRealTimeWithTz(bookingIti.DepartDate, "", bookingIti.DepartDt.Location())
			if err != nil {
				return false, err
			}

			bookingIti.ArrivalDt, err = helpers.ParseFakeUTCToRealTimeWithTz(bookingIti.ArrivalDate, "", bookingIti.ArrivalDt.Location())
			if err != nil {
				return false, err
			}

			bookingIti.FlightDuration = newIties[i].FlightDuration
		}
	}

	return hasChange, nil
}
