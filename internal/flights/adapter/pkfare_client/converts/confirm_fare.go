package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/entities"
	pkfareutils "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client/pkfare_utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

func ToPricingRequest(ities []*domain.FlightItinerary, searchReq *domain.SearchFlightsRequest) *entities.PrecisePricingV10Req {
	firstIti := ities[0]

	return &entities.PrecisePricingV10Req{
		PricingRequest: &entities.PricingRequest{
			Journeys:   ToJourneyMapRequest(ities),
			Adults:     searchReq.Passengers.ADT,
			Children:   searchReq.Passengers.CHD,
			Infants:    searchReq.Passengers.INF,
			SolutionID: firstIti.ProviderBookingKey,
		},
	}
}

func ToPenaltyRequest(ities []*domain.FlightItinerary, req *entities.PenaltyRequest) *entities.PenaltyV3Req {
	return &entities.PenaltyV3Req{
		Penalty: &entities.PenaltyReqData{
			SolutionID: req.SolutionID,
			Journeys:   ToJourneyMapRequest(ities),
		},
	}
}

func ToJourneyMapRequest(ities []*domain.FlightItinerary) *entities.JourneyMapRequest {
	out := &entities.JourneyMapRequest{}

	out.Journey0 = ToListJourneyDetails(ities[0])

	if len(ities) == 2 {
		out.Journey1 = ToListJourneyDetails(ities[1])
	}

	return out
}

func ToListJourneyDetails(iti *domain.FlightItinerary) []entities.JourneyDetails {
	out := []entities.JourneyDetails{}

	for _, seg := range iti.Segments {
		out = append(out, entities.JourneyDetails{
			Airline:       seg.CarrierMarketing,
			FlightNum:     seg.FlightNumber,
			BookingCode:   seg.BookingClass,
			Departure:     seg.DepartPlace,
			Arrival:       seg.ArrivalPlace,
			DepartureDate: pkfareutils.UnixMlToDateString(seg.DepartDate),
			DepartureTime: pkfareutils.UnixMlToTimeString(seg.DepartDate),
			ArrivalDate:   pkfareutils.UnixMlToDateString(seg.ArrivalDate),
			ArrivalTime:   pkfareutils.UnixMlToTimeString(seg.ArrivalDate),
		})
	}

	return out
}
