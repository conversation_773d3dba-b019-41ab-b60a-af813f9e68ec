package telegram

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

type IssueEMDRepository interface {
	SendMessage(ctx context.Context, message string) error
}

type issueEMDRepository struct {
	cfg *config.Schema
}

func NewIssueEMDRepository(cfg *config.Schema) IssueEMDRepository {
	return &issueEMDRepository{
		cfg: cfg,
	}
}

func (tg *issueEMDRepository) SendMessage(ctx context.Context, message string) error {
	requestURL := tg.cfg.TelegramURL + pathBot + tg.cfg.TelegramIssueEMDBotToken + pathSendMessage
	payloadSend := payload{
		ChatID:    tg.cfg.TelegramIssueEMDChatID,
		Text:      message,
		ParseMode: "markdown",
	}

	payloadJSON, err := json.Marshal(payloadSend)
	if err != nil {
		log.Error("[Telegram][SendMessage] marshal failed", log.String("url", requestURL))
		return err
	}

	res, err := http.Post(requestURL, "application/json", bytes.NewBuffer(payloadJSON))
	if err != nil {
		log.Error("[Telegram][SendMessage] request failed",
			log.String("url", requestURL), log.String("message", message))
		return err
	}
	if res.StatusCode != 200 {
		log.Error("[Telegram][SendMessage] request failed",
			log.String("url", requestURL))
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("[Telegram][SendMessage] Response Unmarshal failed",
			log.String("url", requestURL),
			log.Any("response: ", body))
		return err
	}

	return nil
}
