package telegram

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"

	"gitlab.deepgate.io/apps/common/log"
)

func BuildRequestURL(baseURL, token string) string {
	return baseURL + pathBot + token + pathSendMessage
}

func SendMessage(ctx context.Context, requestURL, chatID, message string) error {
	payloadSend := payload{
		ChatID:    chatID,
		Text:      message,
		ParseMode: "markdown",
	}

	payloadJSON, err := json.Marshal(payloadSend)
	if err != nil {
		log.Error("[Telegram][SendMessage] marshal failed", log.String("url", requestURL), log.Any("err", err))
		return err
	}

	res, err := http.Post(requestURL, "application/json", bytes.NewBuffer(payloadJSON))
	if err != nil {
		log.Error("[Telegram][SendMessage] send request failed",
			log.String("url", requestURL), log.String("message", message), log.Any("err", err))
		return err
	}

	if res.StatusCode != 200 {
		log.Error("[Telegram][SendMessage] request failed",
			log.String("url", requestURL), log.Any("payload", payloadSend), log.Any("res", res))
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("[Telegram][SendMessage] Response Unmarshal failed",
			log.String("url", requestURL),
			log.Any("response: ", body),
			log.Any("err", err))
		return err
	}

	return nil
}
