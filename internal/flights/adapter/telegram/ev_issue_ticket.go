package telegram

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
)

const pathSendMessage = "/sendMessage"
const pathBot = "/bot"

type EVIssueTicketTelegramBotRepository interface {
	SendMessage(ctx context.Context, message string, isPkfare bool) error
}

type evIssueTicketTelegramRepository struct {
	cfg *config.Schema
}

func NewEVIssueTicketTelegramRepository(cfg *config.Schema) EVIssueTicketTelegramBotRepository {
	return &evIssueTicketTelegramRepository{
		cfg: cfg,
	}
}

type payload struct {
	ChatID    string `json:"chat_id"`
	Text      string `json:"text"`
	ParseMode string `json:"parse_mode"`
}

func (tg *evIssueTicketTelegramRepository) SendMessage(ctx context.Context, message string, isPkfare bool) error {
	group1ChatID := tg.cfg.TelegramEVInternationalChatID

	if isPkfare {
		group1ChatID = tg.cfg.TelegramPkfareIssuingChatID
	}

	if group1ChatID == "" {
		return errors.New("no chat id found")
	}

	requestURL := tg.cfg.TelegramURL + pathBot + tg.cfg.TelegramEVInternationalBotToken + pathSendMessage
	payloadSend := payload{
		ChatID:    group1ChatID,
		Text:      message,
		ParseMode: "MarkdownV2",
	}

	payloadJSON, err := json.Marshal(payloadSend)
	if err != nil {
		log.Error("[Telegram][SendMessage] marshal failed", log.String("url", requestURL))
		return err
	}

	res, err := http.Post(requestURL, "application/json", bytes.NewBuffer(payloadJSON))
	if err != nil {
		log.Error("[Telegram][SendMessage] request failed", log.Any("err", err),
			log.String("url", requestURL), log.String("message", message))
		return err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("[Telegram][SendMessage] Response Unmarshal failed",
			log.String("url", requestURL),
			log.Any("response: ", body))
		return err
	}

	if res.StatusCode != 200 {
		log.Error("[Telegram][SendMessage] StatusCode != 200",
			log.String("url", requestURL), log.Any("payload", payloadSend), log.String("body", string(body)))
	}

	return nil
}
