package domain

import (
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

// FreeBagge
type BaggageInfo struct {
	Name          string       `json:"name,omitempty"`
	Code          string       `json:"code,omitempty"`
	Price         float64      `json:"price,omitempty"`
	Currency      string       `json:"currency,omitempty"`
	IsHandBaggage bool         `json:"is_hand_baggage"`
	Quantity      int64        `json:"quantity"`
	PaxType       enum.PaxType `json:"pax_type"`
}

// baggageOption
type BaggageOptionDetail struct {
	Type      enum.BaggageType `json:"type"`
	SubCode   string           `json:"sub_code,omitempty"`
	Quantity  int              `json:"quantity"`
	Weight    int              `json:"weight,omitempty"`
	MaxWeight int              `json:"max_weight,omitempty"`
	Unit      string           `json:"unit,omitempty"`
	Dimension string           `json:"dimension,omitempty"`
}

type VNAOfferData struct {
	OfferID           string
	SubCode           string
	SSRCode           string
	OwningCarrierCode string
	Vendor            string
	CommercialName    string
	Group             string
	RFICode           string
}

type TFOfferData struct {
	ParameterName  string
	ParameterValue string
}

type AmadeusOfferData struct {
	RFICode        string
	RFISCode       string
	ProviderCode   string
	CustomerRefIDs []int
	Parameters     []*AmadeusBaggageParameter
}

type AmadeusBaggageParameter struct {
	Name  string
	Value string
}

type HNHOfferData struct {
	Airline    string
	Value      string
	Code       string
	Currency   string
	Route      string
	StartPoint string
	EndPoint   string
	Leg        int32
	Price      float64
}

type OfferData struct {
	TFOfferData  *TFOfferData
	VNAOfferData *VNAOfferData
	AMAOfferData *AmadeusOfferData
	HNHOfferData *HNHOfferData
}

type BaggageOption struct {
	OptionID           string                 `json:"option_id"`
	OfferData          *OfferData             `json:"-"`
	ItineraryIndex     int                    `json:"itinerary_index"`
	SegmentIndex       []int                  `json:"segment_index"`
	BaggageInfo        []*BaggageOptionDetail `json:"baggage_info"`
	TotalWeight        int                    `json:"total_weight,omitempty"`
	Unit               string                 `json:"unit,omitempty"`
	TotalBaggageCharge *BaggageCharge         `json:"total_baggage_charge"`
}

type BaggageCharge struct {
	BaseAmount  float64 `json:"base_amount"`
	TaxAmount   float64 `json:"tax_amount"`
	TotalAmount float64 `json:"total_amount"`
	Currency    string  `json:"currency"`
}

type GetBaggageOptionsRequest struct {
	SearchKey   string   `json:"search_key"`
	FlightID    string   `json:"flight_id"`
	FlightIds   []string `json:"flight_ids"`
	ItineraryID string   `json:"itinerary_id"`
	OfficeID    string   `json:"-"`
}

type GetBaggageOptionsResponse struct {
	ErrorRes
	Baggages []*BaggageOption `json:"baggage_options"`
}

type BaggageOptionCache struct {
	FlightID       string
	ItineraryID    string
	BaggageOptions []*BaggageOption
	ExpiredAt      int64
}
