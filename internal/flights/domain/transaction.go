package domain

import (
	"gitlab.deepgate.io/apps/common/enum"
)

type Agent struct {
	AgentCode string
	AgentName string
	OwnerID   string
}
type TransactionInfo struct {
	ID        string
	Type      enum.TransactionType
	Amount    float64
	CreatedAt int64
}

type Transaction struct {
	TransactionID string               `json:"transaction_id"`
	Type          enum.TransactionType `json:"transaction_type"`
	CreatedAt     int64                `json:"created_at"`
	BookingCode   string               `json:"booking_code"`
	PaymentMethod *PaymentMethod       `json:"payment_method"`
	Amount        float64              `json:"amount"`
	Currency      string
	OfficeID      string `json:"-"`
}
