package domain

import "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"

type LoginReq struct {
	OfficeID      string `json:"office_id"`
	APIKey        string `json:"api_key"`
	PartnershipID string `json:"partnership_id,omitempty"`
}

type PartnerUser struct {
	ID            string
	CreatedAt     int64
	UpdatedAt     int64
	CreatedBy     string
	UpdatedBy     string
	Email         string
	Name          string
	PartnershipID string
	PartnerShopID string
	WebhookCfg    WebhookCfg
}

type PartnerDCPs struct {
	DCPsAmadeus         []*DCPAmadeus
	DCPsTravelFusion    []*DCPTravelFusion
	DCPsVietnamAirlines []*DCPVietnamAirlines
	DCPsVietjetAir      []*DCPVietjetAir
	DCPsEV              []*DCPEV
	DCPsInternationalEV []*DCPInternationalEV
	DCPsVNA1A           []*DCPVNA1A
	DCPsPkfare          []*DCPPkfare
}

type ProviderConfig struct {
	Provider      int64
	Enabled       bool
	FilterConfigs []*FilterConfig
}

type FilterConfig struct {
	TripType   enum.TripType
	IsEnable   bool
	Airlines   []string
	FilterMode enum.FilterMode
}

type PartnerShopInfo struct {
	ID              string
	Name            string
	OwnerID         string
	PartnerType     int64
	Code            string
	OfficeID        string
	ProviderConfigs []*ProviderConfig
	WebhookCfg      *WebhookCfg
	DCPs            *PartnerDCPs
}

type LoginResult struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TTL          uint64 `json:"ttl"`
}

type LoginRes struct {
	*ErrorRes
	Data *LoginResult `json:"data"`
}

type WebhookURLCfg struct {
	Transaction string
	LastTktDate string
}

type WebhookCfg struct {
	WebhookURLCfg WebhookURLCfg
	WebhookKey    string
}

type ShopsBySaleCode struct {
	SaleCode string `json:"sale_code"`
	Shops    []*PartnerShopInfo
}
