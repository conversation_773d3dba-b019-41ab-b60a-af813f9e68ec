package domain

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

// ===== CORE PRICING STRUCTS (Proto-aligned) =====

// PricingCalculationRequest maps to backendPb.CalculateFlightPricingRequest
type PricingCalculationRequest struct {
	FlightID      string             `json:"flight_id"`
	PartnershipID string             `json:"partnership_id"`
	OfficeID      string             `json:"office_id"`
	FlightData    *FlightPricingData `json:"flight_data"`
	Passengers    *PaxRequest        `json:"passengers"`
	Currency      string             `json:"currency,omitempty"`
}

// PricingCalculationResponse maps to backendPb.CalculateFlightPricingResponse
type PricingCalculationResponse struct {
	Success     bool                 `json:"success"`
	PricingData *FlightPricingResult `json:"pricing_data,omitempty"`
	ErrorCode   string               `json:"error_code,omitempty"`
}

// FlightPricingData represents flight data for pricing calculation
type FlightPricingData struct {
	Itineraries         []*FlightItinerary  `json:"itineraries"`
	SearchTotalFareInfo                     // Embedded for backward compatibility
	Provider            enum.FlightProvider `json:"provider"`
	FlightType          enum.FlightType     `json:"flight_type,omitempty"`
}

// FlightPricingResult maps to backendPb.CalculateFlightPricingResponse
type FlightPricingResult struct {
	IsSuccess               bool                      `json:"is_success"`
	ErrorCode               string                    `json:"error_code,omitempty"`
	FlightID                string                    `json:"flight_id,omitempty"`
	ItineraryResults        []*ItineraryPricingResult `json:"itinerary_results"`
	PassengerResults        []*PassengerPricingResult `json:"passenger_results"`
	OriginalTotalFareAmount float64
	FinalTotalFareAmount    float64
	OriginalTotalFareBasic  float64
	FinalTotalFareBasic     float64
	TotalHiddenFeeAmount    float64
	TotalDiscountAmount     float64
}

// ItineraryPricingResult maps to backendPb.ItineraryPricingResult
type ItineraryPricingResult struct {
	ItineraryID      string                 `json:"itinerary_id"`
	CarrierMarketing string                 `json:"carrier_marketing"`
	DepartCountry    string                 `json:"depart_country"`
	ArrivalCountry   string                 `json:"arrival_country"`
	BookingClass     string                 `json:"booking_class"`
	CabinClass       string                 `json:"cabin_class"`
	Route            string                 `json:"route"`
	HiddenFeeConfig  *FlightHiddenFeeConfig `json:"hidden_fee_config,omitempty"`
	DiscountConfig   *FlightDiscountConfig  `json:"discount_config,omitempty"`
}

// PassengerPricingResult maps to pricePb.PassengerPricingResult
type PassengerPricingResult struct {
	PassengerType      enum.PaxType `json:"passenger_type"`
	Quantity           uint32       `json:"quantity"`
	OriginalFareAmount float64      `json:"original_fare_amount"`
	OriginalFareBasic  float64      `json:"original_fare_basic"`
	TaxAmount          float64      `json:"tax_amount"`
	FinalFareAmount    float64      `json:"final_fare_amount"`
	FinalFareBasic     float64      `json:"final_fare_basic"`
	HiddenFeeAmount    float64      `json:"hidden_fee_amount"`
	DiscountAmount     float64      `json:"discount_amount"`
}

// ItineraryPricingDetail maps to pricePb.ItineraryPricingDetail
type ItineraryPricingDetail struct {
	ItineraryID      string `json:"itinerary_id"`
	CarrierMarketing string `json:"carrier_marketing"`
	DepartCountry    string `json:"depart_country"`
	ArrivalCountry   string `json:"arrival_country"`
	BookingClass     string `json:"booking_class"`
	CabinClass       string `json:"cabin_class"`
	Route            string `json:"route"`
}

// FlightHiddenFeeConfig maps to pricePb.FlightHiddenFeeConfig
type FlightHiddenFeeConfig struct {
	ID           string  `json:"id"`
	Type         string  `json:"type"`
	Amount       float64 `json:"amount"`
	VAT          float64 `json:"vat"`
	AirlineCode  string  `json:"airline_code"`
	Route        string  `json:"route"`
	BookingClass string  `json:"booking_class"`
	Percent      float64 `json:"percent"`
	Provider     string  `json:"provider"`
}

// FlightDiscountConfig maps to pricePb.FlightDiscountConfig
type FlightDiscountConfig struct {
	ID           string  `json:"id"`
	Type         string  `json:"type"`
	Amount       float64 `json:"amount"`
	VAT          float64 `json:"vat"`
	AirlineCode  string  `json:"airline_code"`
	Route        string  `json:"route"`
	BookingClass string  `json:"booking_class"`
	Percent      float64 `json:"percent"`
	Provider     string  `json:"provider"`
}

// ===== SERVICE LAYER STRUCTS =====

// PricingServiceRequest represents service layer request for pricing
type PricingServiceRequest struct {
	FlightData    *FlightPricingData `json:"flight_data"`
	Passengers    *PaxRequest        `json:"passengers"`
	OfficeID      string             `json:"office_id"`
	PartnershipID string             `json:"partnership_id"`
	Currency      string             `json:"currency,omitempty"`
	FlightID      string             `json:"flight_id,omitempty"`
}

// PricingServiceResponse represents service layer response for pricing
type PricingServiceResponse struct {
	Success     bool                 `json:"success"`
	PricingData *FlightPricingResult `json:"pricing_data,omitempty"`
	ErrorCode   string               `json:"error_code,omitempty"`
	FlightID    string               `json:"flight_id,omitempty"`
}

// PricingBatchServiceRequest represents service layer batch request for pricing
type PricingBatchServiceRequest struct {
	Requests      []*PricingServiceRequest `json:"requests"`
	OfficeID      string                   `json:"office_id"`
	PartnershipID string                   `json:"partnership_id"`
}

// PricingBatchServiceResponse represents service layer batch response for pricing
type PricingBatchServiceResponse struct {
	Success         bool                      `json:"success"`
	Responses       []*PricingServiceResponse `json:"responses"`
	TotalRequests   int                       `json:"total_requests"`
	SuccessfulCount int                       `json:"successful_count"`
	FailedCount     int                       `json:"failed_count"`
}

type PenaltyHiddenFee struct {
	ID            string
	Amount        float64                `json:"amount" validate:"min=0"`
	Percent       float64                `json:"percent" validate:"min=0,max=1"`
	PenaltyType   commonEnum.PenaltyType `json:"penalty_type"`
	AppliedAmount float64
}

type MiniRuleFlight struct {
	FlightID         string              `json:"flight_id,omitempty"`
	Type             enum.FlightType     `json:"type,omitempty"`
	Provider         enum.FlightProvider `json:"provider,omitempty"`
	BookingClass     string              `json:"booking_class,omitempty"`
	Airline          string              `json:"airline,omitempty"`
	MiniRules        []*MiniRule         `json:"mini_rules,omitempty"`
	AppliedHiddenFee []*PenaltyHiddenFee `json:"applied_hidden_fee,omitempty"`
}

type CalculatePenaltyPricingRequest struct {
	PartnershipID string            `json:"partnership_id,omitempty"`
	OfficeID      string            `json:"office_id,omitempty"`
	Flights       []*MiniRuleFlight `json:"flights,omitempty"`
}
