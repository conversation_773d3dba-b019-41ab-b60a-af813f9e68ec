package domain

import (
	"time"

	"gitlab.deepgate.io/apps/common/auth"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/errors"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

type PrepareBookingRequest struct {
	OfficeID    string `json:"-"`
	BookingCode string `json:"booking_code" validate:"required"`
	// PaymentMethod string `json:"payment_method" validate:"required"`
	AuthUser auth.User
}

type PrepareBookingResponse struct {
	BookingDetails
	ErrorRes
}

type BookingSession struct {
	ID                  string                `json:"-"`
	SessionID           string                `json:"session_id"`
	BookingCode         string                `json:"booking_code,omitempty"`
	BookingRef          string                `json:"-"`
	FlightType          enum.FlightType       `json:"flight_type"`
	OfficeID            string                `json:"-"`
	Itineraries         []*FlightItinerary    `json:"itineraries,omitempty"`
	FareDataRaw         *TotalFareInfo        `json:"-"`
	FareData            *TotalFareInfo        `json:"fare_data,omitempty"`
	FareDataCfRaw       *TotalFareInfo        `json:"-"`
	FareDataCf          *TotalFareInfo        `json:"fare_data_cf,omitempty"`
	FareDataIss         *TotalFareInfo        `json:"-"`
	PassengerInfo       *PaxRequest           `json:"-"`
	Provider            enum.FlightProvider   `json:"flight_provider"`
	Status              enum.BookingStatus    `json:"status"`
	LastTicketingDate   int64                 `json:"last_ticketing_date"`
	FareExpiredDate     int64                 `json:"-"`
	TicketExpiredDate   int64                 `json:"-"`
	SearchRequest       *SearchFlightsRequest `json:"-"`
	SearchKey           string                `json:"-"`
	FlightID            string                `json:"-"`
	OrderID             string                `json:"-"`
	LastTransactionID   string                `json:"-"`
	TicketStatus        enum.TicketStatus     `json:"ticket_status,omitempty"`
	PendingDeadline     int64                 `json:"pending_deadline,omitempty"`
	EMDStatus           enum.EMDStatus        `json:"emd_status,omitempty"`
	OriginFareData      *TotalFareInfo        `json:"-"` // Original fare from provider (currency dependent)
	ExpectedPrice       *ExpectedPrice        `json:"-"`
	EndUserIPAddress    string
	EndUserBrowserAgent string
	EndUserCountryCode  string
	CommissionRate      *float64
	AirlineSystem       string `json:"-"` // use for ev_client
	CreatedAt           int64  `json:"-"`
	VAT                 bool
	IsTransferred       bool        `json:"is_transferred"`
	Confirmed           bool        `json:"confirmed"`
	Notified            bool        // Send notification via cs portal
	ManualIssuing       bool        `json:"-"`
	OrderNumRef         string      `json:"-"`
	InternalBooking     bool        `json:"-"` //  Booking from internal system, not send to provdier
	IsVJ24h             bool        `json:"-"`
	Metadata            []*Metadata `json:"-"`
}

func (b *BookingSession) GetVJProviderKey() string {
	if b == nil || len(b.Itineraries) == 0 {
		return ""
	}

	return b.Itineraries[0].ProviderBookingKey
}

type BookingDetails struct {
	OfficeID          string             `json:"-"`
	OrderID           string             `json:"-"`
	TicketStatus      enum.TicketStatus  `json:"ticket_status,omitempty"`
	EMDStatus         enum.EMDStatus     `json:"emd_status,omitempty"`
	Status            enum.BookingStatus `json:"status"`
	BookingCode       string             `json:"booking_code,omitempty"`
	FlightType        enum.FlightType    `json:"flight_type,omitempty"`
	Itineraries       []*FlightItinerary `json:"itineraries,omitempty"`
	FareDataCf        *TotalFareInfo     `json:"fare_data_cf,omitempty"`
	ListPax           []*PaxInfo         `json:"list_pax"`
	ContactInfo       *Contact           `json:"contact_info"`
	LastTicketingDate int64              `json:"last_ticketing_date"`
	AirlineSystem     string             `json:"-"`
	VAT               bool               `json:"vat"`
	Transferable      bool               `json:"transferable"`
	IsTransferred     bool               `json:"is_transferred"`
	PendingDeadline   int64              `json:"pending_deadline,omitempty"`
	PartnerName       string             `json:"-"`
	OriginFareData    *TotalFareInfo     `json:"-"`
	Tag               enum.Tag           `json:"tag"`
	Metadata          []*Metadata        `json:"-"`
}

type UpdateBookingRepoRequest struct {
	Status            enum.BookingStatus // Required
	LastTicketingDate int64              // Required
	BookingCode       string             // Optional
	BookingRef        string             // Optional
	OrderID           string             // Optional
	TicketStatus      enum.TicketStatus  // Optional
	ExpectedPrice     *ExpectedPrice     // Optional
	CommissionRate    *float64           // Optional
	IsTransferred     *bool              // Optional
	Confirmed         *bool              // Optional
	FareExpiredDate   *int64
	TicketExpiredDate *int64
	AirlineSystem     string
	ReservationCode   *string // Optional
	OrderNumRef       string  // Optional
	InternalBooking   *bool   // Optional
	IsVJ24h           *bool
}

type UpdateBookingTransferStatusRequest struct {
	BookingCode string `json:"booking_code"`
	Transferred bool   `json:"transferred"`
}

type UpdateBookingTransferStatusResponse struct {
	ErrorRes
	ReservationCode string `json:"reservation_code"`
}

type ListUpcomingBookingsReq struct {
	Text        string
	NotiStatus  enum.NotificationStatus
	Pagi        *commonDomain.Pagination
	MaximumDays int64
}

type SvcCreateBookingResponse struct {
	ExpectedPrice          *ExpectedPrice
	LastTicketingDate      int64
	FareExpiredDate        int64
	TicketExpiredDate      int64
	SkipDefaultLastTktDate bool
	BookingRef             string
	CommRate               *float64 // Phan biet nil va zero
	SeatError              error
	AirlineSystem          string
	OrderNumRef            string
}

func (bk *BookingSession) MapReservationCode(code string) {
	if bk != nil {
		for _, item := range bk.Itineraries {
			item.ReservationCode = code
		}
	}
}

func (bk *BookingSession) MapReservationCodeV2(data []*IssueTicketSvcReservationInfo) {
	if data == nil {
		return
	}

	for _, iti := range bk.Itineraries {
		for _, info := range data {
			if iti.Index == info.ItineraryIndex {
				iti.ReservationCode = info.ReservationCode
			}
		}
	}
}

func (bk *BookingSession) MapEtickets(tickets []*ETicketInfo) {
	if bk == nil || len(tickets) == 0 {
		return
	}

	itiIndexMap := map[int][]*ETicketInfo{}

	for _, item := range tickets {
		itiIndexMap[item.ItineraryNumber] = append(itiIndexMap[item.ItineraryNumber], item)
	}

	for _, iti := range bk.Itineraries {
		tickets := itiIndexMap[iti.Index]
		paxIndexMap := map[int]*ItineraryPax{}

		for _, paxInfo := range iti.PaxInfo {
			paxIndexMap[paxInfo.PaxID] = paxInfo
		}

		if len(tickets) > 0 {
			for _, ticket := range tickets {
				if ticket != nil {
					if paxIndexMap[ticket.PaxID] != nil {
						paxIndexMap[ticket.PaxID].TicketNumber = ticket.TicketNumber
						for _, emd := range ticket.EMDInfos {
							if emd.EMDType == enum.EMDTypeSeat {
								for _, seat := range paxIndexMap[ticket.PaxID].Seats {
									if seat.EMDNumber != "" {
										continue
									}
									if emd.SeatRowNumber == seat.RowNumber && emd.SeatCode == seat.SeatFacility.SeatCode {
										seat.EMDNumber = emd.EMDTicketNumber
									}
								}
							}

							if emd.EMDType == enum.EMDTypeBag {
								for _, bag := range paxIndexMap[ticket.PaxID].Baggages {
									if bag.EMDNumber != "" {
										continue
									}

									if emd.BaggageOptionID == bag.BaggageInfo.OptionID {
										bag.EMDNumber = emd.EMDTicketNumber
									}
								}
							}
						}
					} else {
						iti.PaxInfo = append(iti.PaxInfo, &ItineraryPax{
							PaxID:        ticket.PaxID,
							TicketNumber: ticket.TicketNumber,
						})
					}
				}
			}
		}
	}
}

func (bk *BookingSession) CanIssue() error {
	if bk == nil {
		return errors.ErrNotFound
	}

	if bk.TicketStatus == enum.TicketStatusPending {
		return ErrTicketPendingIssuance
	}

	if bk.TicketStatus == enum.TicketStatusOK {
		return ErrTicketIssued
	}

	if bk.Status == enum.BookingStatusCancelled {
		return ErrBookingCancelled
	}

	if bk.IsTransferred {
		return ErrCanNotIssueWithTransferBooking
	}

	if !bk.Confirmed {
		return ErrCheckFlightTime
	}

	return nil
}

func (bk *BookingSession) IsExpired() bool {
	if bk == nil {
		return false
	}

	timeNow := time.Now().UnixMilli()

	return bk.Status == enum.BookingStatusOK && (bk.LastTicketingDate != 0 && bk.LastTicketingDate < timeNow)
}

func (bk *BookingSession) IsCompleteLastTktDate() bool {
	return bk.LastTicketingDate > 0 || (bk.LastTicketingDate == 0 && time.Now().Sub(time.UnixMilli(bk.CreatedAt)).Minutes() > 30)
}

func (bk *BookingSession) IsTransferable() bool {
	if bk == nil {
		return false
	}

	return bk.Status == enum.BookingStatusOK && !bk.HasEMD() &&
		bk.LastTicketingDate != 0 &&
		// time.Until(time.UnixMilli(bk.LastTicketingDate).UTC()) > 30*time.Minute && // TODO Remove : https://www.notion.so/deeptechjsc/SkyHub-Xu-t-h-9846b8329d34456194d1cd6d2db07127?pvs=4#6f77d98c44e24707822c4da994072770
		bk.Provider == enum.FlightProviderAmadeus
}

// Sync seat & baggage fee to totalFareAmount of fare data & fare data confirm
func (bk *BookingSession) SyncFareData() {
	seatAmount := float64(0)
	baggageAmount := float64(0)

	for _, iti := range bk.Itineraries {
		for _, pax := range iti.PaxInfo {
			seatAmount += pax.calculatePaxSeatsAmount()
			baggageAmount += pax.calculatePaxBaggagesAmount()
		}
	}

	if bk.FareData != nil {
		bk.FareData.TotalSeatAmount = seatAmount
		bk.FareData.TotalBaggageAmount = baggageAmount
		bk.FareData.TotalFareAmount = bk.sumTotalFareAmount(bk.FareData.BaseTotalFareAmount, seatAmount, baggageAmount)
	}

	if bk.FareDataCf != nil {
		bk.FareDataCf.TotalSeatAmount = seatAmount
		bk.FareDataCf.TotalBaggageAmount = baggageAmount
		bk.FareDataCf.TotalFareAmount = bk.sumTotalFareAmount(bk.FareDataCf.BaseTotalFareAmount, seatAmount, baggageAmount)
	}
}

func (bk *BookingSession) sumTotalFareAmount(baseTotalAmount, seatAmount float64, baggageAmount float64) float64 {
	return baseTotalAmount + seatAmount + baggageAmount
}

func (bk *BookingSession) HasEMD() bool {
	if bk == nil {
		return false
	}

	for _, iti := range bk.Itineraries {
		for _, pax := range iti.PaxInfo {
			if len(pax.Seats) > 0 || len(pax.Baggages) > 0 { // HACK: update khi imle luggage
				return true
			}
		}
	}

	return false
}

func (bk *BookingSession) HasEMDSeat() bool {
	if bk == nil {
		return false
	}

	for _, iti := range bk.Itineraries {
		for _, pax := range iti.PaxInfo {
			if len(pax.Seats) > 0 {
				return true
			}
		}
	}

	return false
}
