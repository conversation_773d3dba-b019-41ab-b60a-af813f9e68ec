package domain

import (
	"fmt"
	"sort"
	"time"

	"github.com/samber/lo"
	commonHelper "gitlab.deepgate.io/apps/common/helpers"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type SearchFlightsRequest struct {
	OfficeID            string              `json:"-"`
	EndUserIPAddress    string              `json:"-"`
	EndUserBrowserAgent string              `json:"-"`
	Itineraries         []*ItineraryRequest `json:"itineraries" validate:"min=1,dive"`
	Passengers          PaxRequest          `json:"passengers"`
	ProvidersConfig     []*ProviderConfig   `json:"-"`
	FlightType          enum.FlightType
	AirportMap          map[string]*Airport `json:"-"`
}

func (s *SearchFlightsRequest) DetermineFlightType() enum.FlightType {
	if len(s.Itineraries) > 0 {
		var listAirport []string
		for _, v := range s.Itineraries {
			listAirport = append(listAirport, v.ArrivalPlace, v.<PERSON>)
		}
		listAirport = lo.Uniq[string](listAirport)

		for _, v := range listAirport {
			if !commonHelper.Contains[string](constants.AirportCodes, v) {
				return enum.FlightTypeInternational
			}
		}
	}

	return enum.FlightTypeDomestic
}

func (s *SearchFlightsRequest) IsRoundTrip() bool {
	const roundTripLength = 2
	if len(s.Itineraries) != roundTripLength {
		return false
	}

	flights := s.Itineraries

	sort.Slice(flights, func(i, j int) bool {
		return flights[i].DepartDate < flights[j].DepartDate
	})

	if flights[0].DepartPlace == flights[1].ArrivalPlace &&
		flights[0].ArrivalPlace == flights[1].DepartPlace {
		return true
	}

	return false
}

func (s *SearchFlightsRequest) IsMultiItinerary() bool {
	return len(s.Itineraries) > 2 || len(s.Itineraries) == 2 && s.IsRoundTrip()
}

func (s *SearchFlightsRequest) GenKey() string {
	if s == nil {
		return ""
	}

	key := fmt.Sprintf("%d%d%d/", s.Passengers.ADT, s.Passengers.CHD, s.Passengers.INF)

	for _, iti := range s.Itineraries {
		key += fmt.Sprintf("%s-%s-%d/", iti.DepartPlace, iti.ArrivalPlace, iti.DepartDate)
	}

	return key
}

type SearchFlightsResponse struct {
	SearchFlightsResBasicInfo
	ListFlights []*ResponseFlight `json:"list_flights"`
	ErrorRes
	// Pagination *commonDomain.Pagination `json:"pagination"`
}

type SearchFlightsResBasicInfo struct {
	Key        string          `json:"key"`
	FlightType enum.FlightType `json:"flight_type"`
}

type ItineraryRequest struct {
	DepartPlace   string `json:"depart_place" validate:"len=3"`
	DepartDate    int64  `json:"depart_date" validate:"required_if=DepartDateStr ''"`
	DepartDateStr string `json:"depart_date_str" validate:"required_if=DepartDate 0"`
	ArrivalPlace  string `json:"arrival_place" validate:"len=3"`
}

type ResponseFlight struct {
	FlightID    string             `json:"id"`
	Leg         int                `json:"leg,omitempty"`
	Index       int                `json:"index"`
	Itineraries []*FlightItinerary `json:"itineraries"`
	SearchTotalFareInfo
	Provider              enum.FlightProvider   `json:"-"`
	Dispose               bool                  `json:"-"`
	IsDeleted             bool                  `json:"-"`
	AirlineSystem         string                `json:"-"` // Use for EV client
	RecordID              string                `json:"-"`
	ExpiredAt             int64                 `json:"-"` // Use for job clean data
	TotalFareInfoSnapshot *SearchTotalFareInfo  `json:"-"`
	Metadata              []*Metadata           `json:"-"`
	SoldOut               bool                  `json:"-"`
	VAT                   bool                  `json:"vat"`
	MiniRules             []*MiniRule           `json:"mini_rules,omitempty"`
	OptionType            enum.FlightOptionType `json:"-"`
	GroupID               string                `json:"group_id,omitempty"`
}

func (i *ResponseFlight) SumTotalFareInfo(input *SearchTotalFareInfo) {
	if i == nil || input == nil {
		return
	}

	i.BaseTotalFareAmount += input.BaseTotalFareAmount
	i.TotalFareAmount += input.TotalFareAmount
	i.TotalFareBasic += input.TotalFareBasic
	i.TotalTaxAmount += input.TotalTaxAmount

	if i.Currency == "" {
		i.Currency = input.Currency
	}
}

func (i *ResponseFlight) SumPaxFares(ins []*ItineraryPaxFare) {
	rootPaxMap := map[enum.PaxType]*ItineraryPaxFare{}
	out := []*ItineraryPaxFare{}

	for _, item := range i.TotalPaxFares {
		rootPaxMap[item.PaxType] = item
	}

	for _, item := range ins {
		rootPax := rootPaxMap[item.PaxType]
		if rootPax != nil {
			out = append(out, i.sumPaxFare(rootPax, item))
		} else {
			out = append(out, item)
		}
	}

	i.TotalPaxFares = out
}

func (i *ResponseFlight) sumPaxFare(origin *ItineraryPaxFare, target *ItineraryPaxFare) *ItineraryPaxFare {
	if origin == nil {
		return &ItineraryPaxFare{
			PaxType:    target.PaxType,
			FareAmount: target.FareAmount,
			FareBasic:  target.FareBasic,
			TaxAmount:  target.TaxAmount,
			Currency:   target.Currency,
		}
	}

	return &ItineraryPaxFare{
		PaxType:    origin.PaxType,
		FareAmount: origin.FareAmount + target.FareAmount,
		FareBasic:  origin.FareBasic + target.FareBasic,
		TaxAmount:  origin.TaxAmount + target.TaxAmount,
		Currency:   origin.Currency,
	}
}

// Per itinerary sum pax fares
func (i *ResponseFlight) Commit() {
	i.Currency = ""
	i.BaseTotalFareAmount = 0
	i.TotalFareBasic = 0
	i.TotalTaxAmount = 0

	mapPaxFare := map[enum.PaxType]*ItineraryPaxFare{}

	for _, v := range i.Itineraries {
		for _, paxFare := range v.PaxFares {
			mapPaxFare[paxFare.PaxType] = i.sumPaxFare(mapPaxFare[paxFare.PaxType], paxFare)
		}

		i.BaseTotalFareAmount += v.FareAmount
		i.TotalTaxAmount += v.TaxAmount
		i.TotalFareBasic += v.FareBasic
		i.Currency = v.Currency
	}

	for _, val := range mapPaxFare {
		i.TotalPaxFares = append(i.TotalPaxFares, val)
	}
}

// Per passenger numbers (TF only)
func (i *ResponseFlight) CommitV2(req *SearchFlightsRequest) {
	i.Currency = ""
	i.BaseTotalFareAmount = 0
	i.TotalFareBasic = 0
	i.TotalTaxAmount = 0

	mapPaxFare := map[enum.PaxType]*ItineraryPaxFare{}
	mapPaxQuan := map[enum.PaxType]int{
		enum.PaxTypeAdult:    req.Passengers.ADT,
		enum.PaxTypeChildren: req.Passengers.CHD,
		enum.PaxTypeInfant:   req.Passengers.INF,
	}

	for _, v := range i.Itineraries {
		var itiTotalTax, itiTotalAmount float64

		for _, paxFare := range v.PaxFares {
			mapPaxFare[paxFare.PaxType] = i.sumPaxFare(mapPaxFare[paxFare.PaxType], paxFare)

			itiTotalAmount += paxFare.FareAmount * float64(mapPaxQuan[paxFare.PaxType])
			itiTotalTax += paxFare.TaxAmount * float64(mapPaxQuan[paxFare.PaxType])
		}

		i.BaseTotalFareAmount += itiTotalAmount
		i.TotalTaxAmount += itiTotalTax
		i.Currency = v.Currency
	}

	i.TotalFareBasic = i.BaseTotalFareAmount - i.TotalTaxAmount

	for _, val := range mapPaxFare {
		i.TotalPaxFares = append(i.TotalPaxFares, val)
	}
}

// Per passenger numbers
func (i *ResponseFlight) CanBookBaggage() bool {
	if i == nil || len(i.Itineraries) == 0 {
		return false
	}
	now := time.Now().UTC()

	return i.Itineraries[0].DepartDt.In(time.UTC).Sub(now) >= constants.BaggageBookingDeadlineTime
}

type SFAdditionalCacheInfo struct {
	SearchKey string
}

type SearchFlightsCachedRecord struct {
	ID         string
	Key        string
	HashKey    string
	Index      int
	Provider   enum.FlightProvider
	ExpiredAt  int64
	Request    *SearchFlightsRequest
	FlightType enum.FlightType
	Version    enum.SearchVersion
}
