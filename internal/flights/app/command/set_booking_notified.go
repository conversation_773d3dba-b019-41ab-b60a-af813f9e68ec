package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
)

type SetBookingNotifiedHandler interface {
	Handle(ctx context.Context, bookingCode string) error
}

type setBookingNotifiedHandler struct {
	bookingRepo repositories.BookingRepository
}

func NewSetBookingNotifiedHandler(
	bookingRepo repositories.BookingRepository,
) SetBookingNotifiedHandler {
	return &setBookingNotifiedHandler{
		bookingRepo,
	}
}

func (h *setBookingNotifiedHandler) Handle(ctx context.Context, bookingCode string) error {
	booking, err := h.bookingRepo.FindOneByBookingCodeV2(ctx, bookingCode)
	if err != nil {
		log.Error("setBookingNotifiedHandler FindOneByBookingCodeV2 err", log.Any("error", err), log.String("bookingCode", bookingCode))
		return errors.ErrSomethingOccurred
	}

	if booking == nil {
		return errors.ErrNotFound
	}

	booking.Notified = true

	if err = h.bookingRepo.UpdateOne(ctx, booking.ID, booking); err != nil {
		log.Error("bookingRepo.UpdateOne error", log.Any("error", err), log.Any("bk", booking))
		return err
	}

	return nil
}
