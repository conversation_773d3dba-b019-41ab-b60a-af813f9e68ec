package command

import (
	"context"
	"errors"

	"github.com/samber/lo"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/helpers"
)

type ConfirmFareHandler interface {
	Handle(ctx context.Context, req *domain.ConfirmFareRequest, webhookCfg *domain.WebhookCfg) (*domain.ConfirmFareResponse, error)
}

type confirmFareHandler struct {
	sessionService  service.SessionService
	fareService     service.FareService
	bookingRepo     repositories.BookingRepository
	pnrRepo         repositories.PNRRepository
	bookingService  service.BookingService
	currencyExSvc   service.CurrencyExchangeService
	supplierService service.SupplierRouteService
	l2bSvc          service.L2bService
	listFlightSvc   service.ListFlightService
	priceService    service.PriceService
}

func NewConfirmFareHandler(
	sessionService service.SessionService,
	fareService service.FareService,
	bookingRepo repositories.BookingRepository,
	pnrRepo repositories.PNRRepository,
	bookingService service.BookingService,
	currencyExSvc service.CurrencyExchangeService,
	supplierService service.SupplierRouteService,
	l2bSvc service.L2bService,
	listFlightSvc service.ListFlightService,
	priceService service.PriceService,
) ConfirmFareHandler {
	return &confirmFareHandler{
		sessionService,
		fareService,
		bookingRepo,
		pnrRepo,
		bookingService,
		currencyExSvc,
		supplierService,
		l2bSvc,
		listFlightSvc,
		priceService,
	}
}
func (h *confirmFareHandler) validatePassengerQuantity(booking *domain.BookingSession, pnr *domain.PNR) bool {
	paxRequest := booking.SearchRequest.Passengers

	mapPassengers := make(map[enum.PaxType]int)
	for _, pax := range pnr.ListPax {
		mapPassengers[pax.Type]++
	}

	if mapPassengers[enum.PaxTypeAdult] != paxRequest.ADT || mapPassengers[enum.PaxTypeChildren] != paxRequest.CHD || mapPassengers[enum.PaxTypeInfant] != paxRequest.INF {
		return false
	}

	return true
}

func (h *confirmFareHandler) Handle(ctx context.Context, req *domain.ConfirmFareRequest, webhookCfg *domain.WebhookCfg) (*domain.ConfirmFareResponse, error) {
	h.l2bSvc.Look(ctx, req.OfficeID, enum.L2bAPIConfirmFare)

	var (
		meta []*domain.Metadata
	)

	ok, err := h.sessionService.Verify(ctx, req.OfficeID, req.SessionID)
	if err != nil {
		log.Error("sessionService.Verify error", log.String("officeID", req.OfficeID), log.String("sessionID", req.SessionID))
		return nil, commonErrs.ErrSomethingOccurred
	}

	if !ok {
		return nil, domain.ErrSessionInvalidOrExpired
	}

	bkSession, err := h.bookingRepo.FindBookingBySessionID(ctx, req.SessionID)
	if err != nil {
		log.Error("bookingRepo.FindBookingBySessionID error", log.Any("error", err), log.String("sessionID", req.SessionID))
		return nil, commonErrs.ErrSomethingOccurred
	}

	if bkSession == nil {
		return nil, commonErrs.WithMsg(commonErrs.ErrNotFound, constants.ErrMsgBookingNotFound)
	}

	if bkSession.Status == enum.BookingStatusConfirmed {
		return nil, domain.ErrBookingConfirmed
	}

	pnr, err := h.pnrRepo.FindOne(ctx, req.SessionID)
	if err != nil {
		log.Error("pnrRepo.FindOne error", log.Any("error", err), log.String("sessionID", req.SessionID))
		return nil, commonErrs.ErrSomethingOccurred
	}

	if pnr == nil {
		return nil, domain.ErrPNREmpty
	}

	if ok := h.validatePassengerQuantity(bkSession, pnr); !ok {
		return nil, domain.ErrPassengerQuantityNotMatch
	}

	canBook, err := h.bookingService.CanBook(ctx, bkSession.Itineraries, pnr.ListPax)
	if err != nil {
		return nil, err
	}

	if !canBook {
		return nil, domain.ErrPaxNameExistedInAnotherBooking
	}

	var suppliers map[enum.FlightProvider][]string
	if bkSession.Provider == enum.FlightProviderTravelFusion {
		suppliers, err = h.supplierService.GetSuppliersByRoute(ctx, bkSession.Itineraries[0].DepartPlace, bkSession.Itineraries[0].ArrivalPlace)
		if err != nil {
			log.Error("supplierService.GetSuppliersByRoute error", log.Any("error", err))
			return nil, commonErrs.ErrSomethingOccurred
		}
	}

	shouldCreateInternalBooking, _, _ := h.bookingService.ShouldCreateInternalBooking(ctx, bkSession)

	if bkSession.Provider == enum.FlightProviderAmadeus && shouldCreateInternalBooking {
		bkSession.InternalBooking = true
	}

	bkSession.FareDataCf, err = h.fareService.ConfirmFare(ctx, bkSession, pnr, suppliers, webhookCfg)
	if err != nil {
		if errors.Is(err, domain.ErrItinerarySoldOut) {
			go h.setSoldOutFlight(bkSession.FlightID)
		}
		return nil, err
	}

	bkSession.FareDataCfRaw = helpers.Copy(bkSession.FareDataCf).(*domain.TotalFareInfo)

	if bkSession.FareDataCf == nil {
		log.Error("bkSession.FareDataCf nil error", log.Any("bkSession", bkSession), log.Any("pnr", pnr))
		return nil, commonErrs.ErrSomethingOccurred
	}

	multiCurrencyProvider := []enum.FlightProvider{enum.FlightProviderTravelFusion, enum.FlightProviderPkfare}
	// Currency mapping for providers
	if lo.Contains(multiCurrencyProvider, bkSession.Provider) {
		forceCur := ""

		if bkSession.Provider == enum.FlightProviderPkfare {
			forceCur = "USD"
		}

		rateMap, err := h.currencyExSvc.GetRateMapping(ctx, constants.VNCurrency, bkSession.Provider)
		if err != nil {
			return nil, err
		}

		roundFunc := utils.GetRoundFunc(bkSession.Provider)

		meta, err = h.mapCurrencyToVND(bkSession.FareDataCf, rateMap, bkSession.SearchRequest, forceCur, roundFunc)
		if err != nil {
			return nil, err
		}
	}

	//  Call Price after handle currency exchange
	bkSession.SearchRequest.OfficeID = bkSession.OfficeID
	bkSession.SearchRequest.FlightType = bkSession.FlightType

	if bkSession.SearchRequest.FlightType == enum.FlightTypeNone {
		bkSession.SearchRequest.FlightType = bkSession.SearchRequest.DetermineFlightType()
	}

	updatedFareInfo, err := h.priceService.CalculateFarePricing(ctx, bkSession.FareDataCf, bkSession.SearchRequest, bkSession.Itineraries, bkSession.FlightID, bkSession.Provider)
	if err != nil {
		log.Error("Failed to calculate fare pricing in ConfirmFare",
			log.Any("error", err),
			log.Any("provider", bkSession.Provider),
			log.String("session_id", bkSession.SessionID))
		return nil, commonErrs.ErrSomethingOccurred
	}

	bkSession.FareDataCf = updatedFareInfo

	if bkSession.FareDataCf.TotalFareBasic != bkSession.FareData.TotalFareBasic || bkSession.FareDataCf.TotalTaxAmount != bkSession.FareData.TotalTaxAmount {
		go h.updateFlightFare(bkSession.FlightID, bkSession.FareDataCf, meta)
	}

	// Update both FareDataCf and FareDataCfRaw in single transaction
	err = h.bookingRepo.UpdateFareDataCfWithRaw(ctx, req.SessionID, bkSession.FareDataCf, bkSession.FareDataCfRaw, bkSession.OriginFareData)
	if err != nil {
		log.Error("bookingRepo.UpdateFareDataCfWithRaw error",
			log.Any("error", err),
			log.String("session_id", req.SessionID))
		return nil, commonErrs.ErrSomethingOccurred
	}

	return &domain.ConfirmFareResponse{
		FlightType:  bkSession.FlightType,
		SessionID:   req.SessionID,
		Itineraries: bkSession.Itineraries,
		FareInfo:    bkSession.FareData,
		FareInfoCf:  bkSession.FareDataCf,
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
	}, nil
}

func (h *confirmFareHandler) updateFlightFare(flightID string, newFare *domain.TotalFareInfo, meta []*domain.Metadata) {
	bgCtx, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
	defer cc()

	if err := h.listFlightSvc.UpdateFlightFare(bgCtx, flightID, newFare, meta); err != nil {
		log.Error("listFlightSvc.UpdateFlightFare error", log.Any("error", err), log.String("flightID", flightID), log.Any("newFare", newFare), log.Any("meta", meta))
		return
	}
}

func (h *confirmFareHandler) mapCurrencyToVND(target *domain.TotalFareInfo, currencyMapping domain.CurrencyRateMapping, req *domain.SearchFlightsRequest, forceCurrency string, roundFunc func(in float64) float64) ([]*domain.Metadata, error) {
	const VNCurrency = "VND"
	var metadata []*domain.Metadata

	if currencyMapping == nil || target == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	mapPaxQuan := map[enum.PaxType]int{
		enum.PaxTypeAdult:    req.Passengers.ADT,
		enum.PaxTypeChildren: req.Passengers.CHD,
		enum.PaxTypeInfant:   req.Passengers.INF,
	}
	rate := currencyMapping[target.Currency]

	if rate != 0 && (forceCurrency == "" || forceCurrency == target.Currency) {
		metadata = []*domain.Metadata{
			{Key: domain.MetaKeyBaseTotalFareAmount + "_cf", Value: target.BaseTotalFareAmount},
			{Key: domain.MetaKeyTotalTaxAmount + "_cf", Value: target.TotalTaxAmount},
			{Key: domain.MetaKeyCurrency + "_cf", Value: target.Currency},
		}

		target.Currency = VNCurrency

		var ttb, ttt float64
		for _, paxFare := range target.TotalPaxFares {
			paxFare.TaxAmount = roundFunc(rate * paxFare.TaxAmount)
			paxFare.FareBasic = roundFunc(rate * paxFare.FareBasic)
			paxFare.Currency = VNCurrency

			paxFare.FareAmount = commonHelpers.RoundFloat(paxFare.FareBasic+paxFare.TaxAmount, 2)

			ttb += paxFare.FareBasic * float64(mapPaxQuan[paxFare.PaxType])
			ttt += paxFare.TaxAmount * float64(mapPaxQuan[paxFare.PaxType])
		}

		target.BaseTotalFareAmount = ttb + ttt
		target.TotalTaxAmount = ttt
		target.TotalFareBasic = ttb
		target.ExchangeRate = rate
	} else {
		log.Error("Unsupported Travel-Fusion currency error", log.String("currency", target.Currency))
		return nil, commonErrs.ErrInvalidInput
	}

	return metadata, nil
}

func (h *confirmFareHandler) setSoldOutFlight(flightID string) {
	ctx, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
	defer cc()
	_ = h.listFlightSvc.SetFlightSoldOut(ctx, flightID)
}
