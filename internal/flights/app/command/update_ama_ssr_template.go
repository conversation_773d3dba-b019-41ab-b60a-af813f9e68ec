package command

import (
	"context"
	"time"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	amaConverts "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

type UpdateAmaSSRTemplateHandler interface {
	Handle(ctx context.Context, ID string, item *domain.SSRTemplate, exampleMsg string) error
}

type updateAmaSSRTemplateHandler struct {
	templateRepo repositories.AmaSSRTemplateRepository
}

func NewUpdateAmaSSRTemplateHandler(templateRepo repositories.AmaSSRTemplateRepository) UpdateAmaSSRTemplateHandler {
	return &updateAmaSSRTemplateHandler{templateRepo}
}

func (h *updateAmaSSRTemplateHandler) Handle(ctx context.Context, ID string, item *domain.SSRTemplate, exampleMsg string) error {
	if item == nil {
		return errors.ErrInvalidInput
	}

	res, err := amaConverts.ParseSSRLastTicketDate(exampleMsg, []*domain.SSRTemplate{item})
	if err != nil {
		log.Error("[UpdateAmaSSRTemplateHandler] ParseSSRLastTicketDate error", log.Any("error", err), log.Any("item", item))
		return err
	}

	_, err = time.Parse(res.Format, res.DateStr)
	if err != nil {
		log.Error("[UpdateAmaSSRTemplateHandler] error parsing time", log.Any("err", err), log.String("dateStr", res.DateStr))
		return domain.ErrSSRDateFormatInvalid
	}

	err = h.templateRepo.Update(ctx, ID, item)
	if err != nil {
		log.Error("templateRepo.Update error", log.Any("error", err), log.Any("item", item))
		return err
	}

	return nil
}
