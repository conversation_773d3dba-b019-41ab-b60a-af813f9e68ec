// CheckFareV2 allows fare check flights from the search results of search version 2 <mixed rec flights and single flights>

package command

import (
	"context"
	"errors"

	"github.com/samber/lo"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	contextbinding "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/context_binding"
)

type CheckFareV2Handler interface {
	Handle(ctx context.Context, req *domain.CheckFareRequestV2) (*domain.CheckFareResponse, error)
}

type checkFareV2Handler struct {
	fareService         service.FareService
	sessionService      service.SessionService
	searchFlightService service.SearchFlightsService
	bookingService      service.BookingService
	supplierService     service.SupplierRouteService
	l2bSvc              service.L2bService
	searchRedisRepo     redisrepo.SearchFlightsRepository
	listFlightSvc       service.ListFlightService
	priceService        service.PriceService
	cfg                 config.Schema
}

func NewCheckFareV2Handler(
	fareService service.FareService,
	sessionService service.SessionService,
	searchFlightService service.SearchFlightsService,
	bookingService service.BookingService,
	supplierService service.SupplierRouteService,
	l2bSvc service.L2bService,
	searchRedisRepo redisrepo.SearchFlightsRepository,
	listFlightSvc service.ListFlightService,
	priceService service.PriceService,
	cfg config.Schema,
) CheckFareV2Handler {
	return &checkFareV2Handler{
		fareService,
		sessionService,
		searchFlightService,
		bookingService,
		supplierService,
		l2bSvc,
		searchRedisRepo,
		listFlightSvc,
		priceService,
		cfg,
	}
}

func (h *checkFareV2Handler) Handle(ctx context.Context, req *domain.CheckFareRequestV2) (*domain.CheckFareResponse, error) {
	var (
		sessionID     string
		err           error
		totalFareInfo domain.TotalFareInfo
	)

	h.l2bSvc.Look(ctx, req.OfficeID, enum.L2bAPICheckFare) // V2?

	if err := h.searchRedisRepo.WaitCachingLockRelease(req.SearchKey); err != nil {
		log.Error("h.searchRedisRepo.WaitCachingLockRelease error", log.Any("error", err), log.String("searchKey", req.SearchKey))
		return nil, err
	}

	req.FlightIds = lo.Uniq(req.FlightIds)

	flights, record, err := h.searchFlightService.RetrieveFlightsByIds(ctx, req.SearchKey, req.FlightIds, false)
	if err != nil {
		return nil, err
	}

	if len(record.Request.Itineraries) != len(req.FlightIds) && record.FlightType == enum.FlightTypeDomestic {
		return nil, commonErrors.ErrInvalidInput
	}

	if err := h.listFlightSvc.ValidateFlights(flights); err != nil {
		return nil, err
	}

	flight := h.listFlightSvc.ComnbineFlights(ctx, flights)
	if flight == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	for _, miniRule := range flight.MiniRules {
		for _, penaltyRule := range miniRule.PenaltyRules {
			if penaltyRule.Amount == nil {
				if penaltyRule.Percent != nil {
					var amount float64
					switch penaltyRule.BaseType {
					case enum.BaseTypeFareAmount:
						amount = *penaltyRule.Percent * flight.TotalFareAmount
					case enum.BaseTypeFareBasic:
						amount = *penaltyRule.Percent * flight.TotalFareBasic
					}
					penaltyRule.Amount = &amount
				}
			}
		}
	}

	record.Request.EndUserBrowserAgent = req.EndUserBrowserAgent
	record.Request.EndUserIPAddress = req.EndUserIPAddress
	record.Request.OfficeID = req.OfficeID

	searchFreeBagProviders := []enum.FlightProvider{
		enum.FlightProviderAmadeus,
		enum.FlightProviderTravelFusion,
		enum.FlightProviderVNA1A,
		enum.FlightProviderTongCheng,
	}

	checkFareInfo := &domain.CheckFareInfo{}

	isFareBasicChanged := false
	if !lo.Contains(searchFreeBagProviders, record.Provider) {
		partyCtx, cc := context.WithTimeout(context.Background(), constants.LongThirdPartyRequestTimeout)
		defer cc()

		partyCtx = contextbinding.CopyDCPs(partyCtx, ctx)

		info, err := h.fareService.CheckFare(partyCtx, record.Provider, record.Request, flight)
		if err != nil {
			if errors.Is(err, domain.ErrItinerarySoldOut) || errors.Is(err, domain.ErrFareBasisChanged) {
				go func() {
					ctx, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
					defer cc()
					_ = h.listFlightSvc.SetFlightSoldOut(ctx, flight.FlightID)
				}()

				if errors.Is(err, domain.ErrItinerarySoldOut) {
					log.Warn("Itinerary sold out", log.Any("flightIds", req.FlightIds), log.String("searchKey", req.SearchKey))
					return nil, domain.ErrItinerarySoldOut
				}

				if errors.Is(err, domain.ErrFareBasisChanged) {
					// If fare basic changed, we need to update the flight
					isFareBasicChanged = true
					log.Warn("Fare basic changed", log.Any("flightIds", req.FlightIds), log.String("searchKey", req.SearchKey))
				}
			} else {
				return nil, err
			}
		}

		if info != nil {
			checkFareInfo = info
		}
	}

	totalFareInfo = domain.TotalFareInfo{
		TotalPaxFares:       flight.TotalPaxFares,
		BaseTotalFareAmount: flight.BaseTotalFareAmount,
		TotalFareAmount:     flight.BaseTotalFareAmount,
		TotalFareBasic:      flight.TotalFareBasic,
		TotalTaxAmount:      flight.TotalTaxAmount,
		Currency:            flight.Currency,
	}

	updatedFareInfo, err := h.priceService.CalculateFarePricing(ctx, &totalFareInfo, record.Request, flight.Itineraries, flight.FlightID, flight.Provider)
	if err != nil {
		log.Error("Failed to calculate fare pricing in CheckFareV2",
			log.Any("error", err),
			log.Any("provider", record.Provider),
			log.String("flight_id", flight.FlightID))
		return nil, err
	}

	err = h.priceService.CalculatePenaltySearchFlight(ctx, h.cfg.HubPartnershipID, req.OfficeID, []*domain.ResponseFlight{flight})
	if err != nil {
		log.Error("Failed to calculate penalty hidden fee in CheckFareV2",
			log.Any("error", err),
			log.Any("provider", record.Provider),
			log.String("flight_id", flight.FlightID))
		return nil, err
	}

	// Start session?
	if req.Stateful {
		sessionID, err = h.sessionService.New(ctx, req.OfficeID, record.Provider)

		if err != nil {
			return nil, err
		}

		bkReq := &domain.BookingSession{
			OfficeID:            req.OfficeID,
			SessionID:           sessionID,
			Provider:            record.Provider,
			Itineraries:         flight.Itineraries,
			FareData:            updatedFareInfo,
			FareDataRaw:         &totalFareInfo,
			PassengerInfo:       &record.Request.Passengers,
			FlightType:          record.FlightType,
			SearchRequest:       record.Request,
			SearchKey:           record.Key,
			FlightID:            flight.FlightID,
			Status:              enum.BookingStatusDraft,
			EndUserIPAddress:    req.EndUserIPAddress,
			EndUserBrowserAgent: req.EndUserBrowserAgent,
			EndUserCountryCode:  req.EndUserCountryCode,
			AirlineSystem:       flight.AirlineSystem,
			VAT:                 flight.VAT,
			Metadata:            flight.Metadata,
		}

		err := h.bookingService.CreateBookingSession(ctx, bkReq)
		if err != nil {
			return nil, err
		}
	}

	result := &domain.CheckFareResponse{
		SessionID:   sessionID,
		FlightType:  record.FlightType,
		Itineraries: flight.Itineraries,
		FareInfo:    totalFareInfo,
		VAT:         flight.VAT,
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		MiniRules: checkFareInfo.MiniRules,
	}

	if isFareBasicChanged {
		return result, domain.ErrFareBasisChanged
	}

	return result, nil
}
