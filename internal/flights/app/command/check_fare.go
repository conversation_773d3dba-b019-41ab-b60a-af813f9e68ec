package command

import (
	"context"
	"errors"

	"github.com/samber/lo"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	redisrepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
	contextbinding "gitlab.deepgate.io/skyhub/skyhub-flights/pkg/context_binding"
)

type CheckFareHandler interface {
	Handle(ctx context.Context, req *domain.CheckFareRequest) (*domain.CheckFareResponse, error)
}

type checkFareHandler struct {
	fareService         service.FareService
	sessionService      service.SessionService
	searchFlightService service.SearchFlightsService
	bookingService      service.BookingService
	supplierService     service.SupplierRouteService
	l2bSvc              service.L2bService
	searchRedisRepo     redisrepo.SearchFlightsRepository
	listFlightRepo      repositories.ListFlightRepository
	listFlightSvc       service.ListFlightService
	currencyExSvc       service.CurrencyExchangeService
	priceService        service.PriceService
	cfg                 config.Schema
}

func NewCheckFareHandler(
	fareService service.FareService,
	sessionService service.SessionService,
	searchFlightService service.SearchFlightsService,
	bookingService service.BookingService,
	supplierService service.SupplierRouteService,
	l2bSvc service.L2bService,
	searchRedisRepo redisrepo.SearchFlightsRepository,
	listFlightRepo repositories.ListFlightRepository,
	listFlightSvc service.ListFlightService,
	currencyExSvc service.CurrencyExchangeService,
	priceService service.PriceService,
	cfg config.Schema,
) CheckFareHandler {
	return &checkFareHandler{
		fareService,
		sessionService,
		searchFlightService,
		bookingService,
		supplierService,
		l2bSvc,
		searchRedisRepo,
		listFlightRepo,
		listFlightSvc,
		currencyExSvc,
		priceService,
		cfg,
	}
}

func (h *checkFareHandler) Handle(ctx context.Context, req *domain.CheckFareRequest) (*domain.CheckFareResponse, error) {
	var (
		sessionID     string
		err           error
		totalFareInfo domain.TotalFareInfo
	)

	h.l2bSvc.Look(ctx, req.OfficeID, enum.L2bAPICheckFare)

	if err := h.searchRedisRepo.WaitCachingLockRelease(req.SearchKey); err != nil {
		log.Error("h.searchRedisRepo.WaitCachingLockRelease error", log.Any("error", err), log.String("searchKey", req.SearchKey))
		return nil, err
	}

	flight, record, err := h.searchFlightService.RetrieveFlightByID(ctx, req.SearchKey, req.FlightID)
	if err != nil {
		return nil, err
	}

	for _, miniRule := range flight.MiniRules {
		for _, penaltyRule := range miniRule.PenaltyRules {
			if penaltyRule.Amount == nil {
				if penaltyRule.Percent != nil {
					var amount float64
					switch penaltyRule.BaseType {
					case enum.BaseTypeFareAmount:
						amount = *penaltyRule.Percent * flight.TotalFareAmount
					case enum.BaseTypeFareBasic:
						amount = *penaltyRule.Percent * flight.TotalFareBasic
					}
					penaltyRule.Amount = &amount
				}
			}
		}
	}

	record.Request.EndUserBrowserAgent = req.EndUserBrowserAgent
	record.Request.EndUserIPAddress = req.EndUserIPAddress
	record.Request.OfficeID = req.OfficeID

	searchFreeBagProviders := []enum.FlightProvider{
		enum.FlightProviderAmadeus,
		enum.FlightProviderTravelFusion,
		enum.FlightProviderVNA1A,
		enum.FlightProviderHNH,
		enum.FlightProviderHNH_HPL,
	}

	isFareBasicChanged := false
	fareMetaData := []*domain.Metadata{}
	if !lo.Contains(searchFreeBagProviders, record.Provider) {
		isLock, err := h.searchRedisRepo.GetFareCheckClientLock(flight.FlightID)
		if err != nil {
			return nil, err
		}

		if !isLock || record.Provider == enum.FlightProviderTongCheng {
			partyCtx, cc := context.WithTimeout(context.Background(), constants.LongThirdPartyRequestTimeout)
			defer cc()

			partyCtx = contextbinding.CopyDCPs(partyCtx, ctx)

			fareRes, err := h.fareService.CheckFare(partyCtx, record.Provider, record.Request, flight)

			if err != nil {
				if errors.Is(err, domain.ErrItinerarySoldOut) || errors.Is(err, domain.ErrFareBasisChanged) {
					go h.setSoldOutFlight(req.FlightID)

					if errors.Is(err, domain.ErrItinerarySoldOut) {
						log.Warn("Itinerary sold out", log.Any("FlightID", req.FlightID), log.String("searchKey", req.SearchKey))
						return nil, domain.ErrItinerarySoldOut
					}

					if errors.Is(err, domain.ErrFareBasisChanged) {
						// If fare basic changed, we need to update the flight
						isFareBasicChanged = true
						log.Warn("Fare basic changed", log.String("flightID", req.FlightID), log.Any("error", err))
					}
				} else {
					log.Error("CheckFare error", log.Any("error", err), log.String("flightID", req.FlightID))
					return nil, err
				}
			}

			if fareRes != nil {
				fareMetaData = fareRes.MetaData
				if fareRes.ShouldUpdateFlight {
					// Map check fare
					if err := h.mapCheckFareFromProvider(ctx, flight, *fareRes); err != nil {
						log.Error("mapCheckFareFromProvider error", log.Any("error", err), log.String("flightID", flight.FlightID))
						return nil, err
					}

					if err := h.listFlightRepo.UpdateOneByFlightID(ctx, flight.FlightID, flight); err != nil {
						log.Error("listFlightRepo.UpdateOneByFlightID error", log.Any("error", err), log.String("flightID", flight.FlightID))
						return nil, err
					}

					_, err := h.searchRedisRepo.AccquireFareCheckClientLock(flight.FlightID)
					if err != nil {
						return nil, err
					}
				}
			}
		}
	}

	totalFareInfo = domain.TotalFareInfo{
		TotalPaxFares:       flight.TotalPaxFares,
		BaseTotalFareAmount: flight.BaseTotalFareAmount,
		TotalFareAmount:     flight.BaseTotalFareAmount,
		TotalFareBasic:      flight.TotalFareBasic,
		TotalTaxAmount:      flight.TotalTaxAmount,
		Currency:            flight.Currency,
	}

	updatedFareInfo, err := h.priceService.CalculateFarePricing(ctx, &totalFareInfo, record.Request, flight.Itineraries, req.FlightID, flight.Provider)
	if err != nil {
		log.Error("Failed to calculate fare pricing in CheckFare",
			log.Any("error", err),
			log.Any("provider", record.Provider),
			log.String("flight_id", req.FlightID))
		return nil, err
	}

	err = h.priceService.CalculatePenaltySearchFlight(ctx, h.cfg.HubPartnershipID, req.OfficeID, []*domain.ResponseFlight{flight})
	if err != nil {
		log.Error("Failed to calculate penalty hidden fee in CheckFareV2",
			log.Any("error", err),
			log.Any("provider", record.Provider),
			log.String("flight_id", flight.FlightID))
		return nil, err
	}

	// Start session?
	if req.Stateful {
		sessionID, err = h.sessionService.New(ctx, req.OfficeID, record.Provider)

		if err != nil {
			return nil, err
		}

		bkReq := &domain.BookingSession{
			OfficeID:            req.OfficeID,
			SessionID:           sessionID,
			Provider:            record.Provider,
			Itineraries:         flight.Itineraries,
			FareData:            updatedFareInfo,
			FareDataRaw:         &totalFareInfo,
			PassengerInfo:       &record.Request.Passengers,
			FlightType:          record.FlightType,
			SearchRequest:       record.Request,
			SearchKey:           record.Key,
			FlightID:            req.FlightID,
			Status:              enum.BookingStatusDraft,
			EndUserIPAddress:    req.EndUserIPAddress,
			EndUserBrowserAgent: req.EndUserBrowserAgent,
			EndUserCountryCode:  req.EndUserCountryCode,
			AirlineSystem:       flight.AirlineSystem,
			VAT:                 flight.VAT,
			Metadata:            fareMetaData,
		}

		err := h.bookingService.CreateBookingSession(ctx, bkReq)
		if err != nil {
			return nil, err
		}
	}

	result := &domain.CheckFareResponse{
		SessionID:   sessionID,
		FlightType:  record.FlightType,
		Itineraries: flight.Itineraries,
		FareInfo:    *updatedFareInfo,
		VAT:         flight.VAT,
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		MiniRules: flight.MiniRules,
	}

	if isFareBasicChanged {
		return result, domain.ErrFareBasisChanged
	}

	return result, nil
}

func (h *checkFareHandler) setSoldOutFlight(flightID string) {
	ctx, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
	defer cc()
	_ = h.listFlightSvc.SetFlightSoldOut(ctx, flightID)
}

func (h *checkFareHandler) mapCheckFareFromProvider(ctx context.Context, currentFlight *domain.ResponseFlight, fare domain.CheckFareInfo) error {
	rateMap, err := h.currencyExSvc.GetRateMapping(ctx, constants.VNCurrency, enum.FlightProviderPkfare)
	if err != nil {
		log.Error("currencyExSvc.GetRateMapping error", log.Any("error", err))
		return commonErrs.ErrSomethingOccurred
	}
	if len(fare.MiniRules) > 0 {
		currentFlight.MiniRules = fare.MiniRules
	}

	err = h.mapCurrencyToVND(currentFlight, rateMap)
	if err != nil {
		return err
	}

	return nil
}

func (h *checkFareHandler) mapCurrencyToVND(flight *domain.ResponseFlight, currencyMapping domain.CurrencyRateMapping) error {
	const VNCurrency = "VND"

	const ForceCurrency = "USD"

	if currencyMapping == nil {
		return commonErrs.ErrInvalidInput
	}

	roundFunc := utils.GetRoundFunc(enum.FlightProviderPkfare)

	// Map minirule amount to VND
	for _, miniRule := range flight.MiniRules {
		if miniRule == nil {
			continue
		}
		for _, rule := range miniRule.PenaltyRules {
			rate := currencyMapping[rule.Currency]
			if rate != 0 && rule.Currency == ForceCurrency {
				if rule.Amount == nil {
					continue
				}

				*rule.Amount = roundFunc(*rule.Amount * rate)
				rule.Currency = VNCurrency
			} else {
				log.Error("Unsupported Pkfare currency error", log.String("currency", rule.Currency))
			}
		}
	}

	return nil
}
