package command

import (
	"context"

	"github.com/gammazero/workerpool"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type ProcessPendingTicketHandler interface {
	Handle(ctx context.Context, req *ProcessPendingTicketRequest) error
}

type processPendingTicketHandler struct {
	bookingRepo    repositories.BookingRepository
	ticketService  service.IssueTicketService
	interClient    internal_client.InternalClient
	bookingService service.BookingService
}

type ProcessPendingTicketRequest struct {
	Bookings []*domain.BookingSession
}

func NewProcessPendingTicketHandler(
	bookingRepo repositories.BookingRepository,
	ticketService service.IssueTicketService,
	interClient internal_client.InternalClient,
	bookingService service.BookingService,
) ProcessPendingTicketHandler {
	return &processPendingTicketHandler{bookingRepo, ticketService, interClient, bookingService}
}

func (h *processPendingTicketHandler) Handle(ctx context.Context, req *ProcessPendingTicketRequest) error {
	wp := workerpool.New(5)

	for _, booking := range req.Bookings {
		booking := booking

		wp.Submit(func() {
			bgCtx, cc := context.WithTimeout(ctx, constants.ThirdPartyRequestTimeout)
			defer cc()

			tktStatus, err := h.ticketService.ResolvePendingTicket(bgCtx, booking)
			if err != nil {
				log.Error("ResolvePendingTicket error", log.Any("error", err), log.Any("booking", booking))
			}

			// Refund on failed ticket
			if tktStatus == enum.TicketStatusFailed {
				if err := h.bookingService.CancelBooking(ctx, booking.BookingCode, booking.SessionID, booking.OfficeID); err != nil {
					log.Error("CancelBooking error", log.Any("error", err), log.Any("booking", booking))
					return
				}

				err := h.ticketService.RefundFailedTicket(ctx, nil, "", nil, booking.OfficeID, booking.LastTransactionID, booking.BookingCode, enum.FlightProviderName[booking.Provider])
				if err != nil {
					log.Error("RefundFailedTicket error", log.Any("error", err), log.Any("booking", booking))
				}

			}
		})
	}

	wp.StopWait()

	return nil
}
