package command

import (
	"context"
	"errors"

	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type PrepareBookingHandler interface {
	Handle(ctx context.Context, req *domain.PrepareBookingRequest) (*domain.PrepareBookingResponse, error)
}

func NewPrepareBookingHandler(
	bookingService service.BookingService,
	bookingRepo repositories.BookingRepository,
	pnrRepo repositories.PNRRepository,
	listFlightSvc service.ListFlightService,
) PrepareBookingHandler {
	return &prepareBookingHandler{bookingService, bookingRepo, pnrRepo, listFlightSvc}
}

type prepareBookingHandler struct {
	bookingService service.BookingService
	bookingRepo    repositories.BookingRepository
	pnrRepo        repositories.PNRRepository
	listFlightSvc  service.ListFlightService
}

func (h *prepareBookingHandler) Handle(ctx context.Context, req *domain.PrepareBookingRequest) (*domain.PrepareBookingResponse, error) {
	booking, err := h.bookingRepo.FindOneByBookingCode(ctx, req.OfficeID, req.BookingCode)
	if err != nil {
		log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err),
			log.String("officeID", req.OfficeID), log.String("bookingCode", req.BookingCode))
		return nil, commonError.ErrSomethingOccurred
	}

	if err := booking.CanIssue(); err != nil {
		return nil, err
	}

	if booking.IsExpired() {
		go func() {
			bCtx, cc := context.WithTimeout(context.Background(), constants.ThirdPartyRequestTimeout)
			defer cc()
			err := h.bookingService.CancelBooking(bCtx, booking.BookingCode, "", req.OfficeID)
			if err != nil {
				log.Error("bookingService.CancelBooking error", log.Any("error", err),
					log.String("officeID", req.OfficeID), log.String("bookingCode", req.BookingCode))
			}
		}()

		return nil, domain.ErrBookingExpired
	}

	pnr, err := h.pnrRepo.FindOne(ctx, booking.SessionID)
	if err != nil {
		log.Error("pnrRepo.FindOne error", log.Any("error", err), log.String("session", booking.SessionID))
		return nil, commonError.ErrSomethingOccurred
	}

	bkDetail := domain.BookingDetails{
		BookingCode:       booking.BookingCode,
		Status:            booking.Status,
		EMDStatus:         booking.EMDStatus,
		FlightType:        booking.FlightType,
		Itineraries:       booking.Itineraries,
		FareDataCf:        booking.FareDataCf,
		ListPax:           pnr.ListPax,
		ContactInfo:       pnr.ContactInfo,
		LastTicketingDate: booking.LastTicketingDate,
		TicketStatus:      booking.TicketStatus,
		VAT:               booking.VAT,
		IsTransferred:     booking.IsTransferred,
		Transferable:      booking.IsTransferable(),
		PendingDeadline:   booking.PendingDeadline,
	}

	IsInVJ24h, _ := h.bookingService.IsInVJ24h(context.TODO(), booking)

	if booking.InternalBooking && !IsInVJ24h {
		res, err := h.bookingService.CreateBooking(ctx, booking.Provider, &bkDetail, booking, booking.SearchRequest, pnr, booking.SessionID, true)
		if err != nil {
			if errors.Is(err, domain.ErrItinerarySoldOut) {
				go h.setSoldOutFlight(booking.FlightID)
			}

			if errors.Is(err, domain.ErrSeatNotAvailable) {
				_ = h.bookingRepo.UpdateBooking(ctx, booking.SessionID, &domain.UpdateBookingRepoRequest{
					Status: enum.BookingStatusCancelled,
				})
			}

			log.Error("bookingService.CreateBooking error", log.Any("error", err), log.String("bookingCode", booking.BookingCode))
			return nil, err
		}

		booking.BookingRef = res.BookingRef
		booking.LastTicketingDate = res.LastTicketingDate
		bkDetail.LastTicketingDate = res.LastTicketingDate
		booking.InternalBooking = false

		updateBookingRepoReq := &domain.UpdateBookingRepoRequest{
			BookingRef:        res.BookingRef,
			LastTicketingDate: booking.LastTicketingDate,
			FareExpiredDate:   &res.FareExpiredDate,
			TicketExpiredDate: &res.TicketExpiredDate,
			ExpectedPrice:     res.ExpectedPrice,
			CommissionRate:    res.CommRate,
			ReservationCode:   &res.BookingRef,
			OrderNumRef:       res.OrderNumRef,
			InternalBooking:   &booking.InternalBooking,
			AirlineSystem:     res.AirlineSystem,
		}

		err = h.bookingRepo.UpdateBooking(ctx, booking.SessionID, updateBookingRepoReq)
		if err != nil {
			log.Error("bookingRepo.UpdateBooking error", log.Any("error", err), log.String("sessionID", booking.SessionID))
			return nil, commonError.ErrSomethingOccurred
		}
	}

	return &domain.PrepareBookingResponse{
		BookingDetails: bkDetail,
	}, nil
}

func (h *prepareBookingHandler) setSoldOutFlight(flightID string) {
	ctx, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
	defer cc()
	_ = h.listFlightSvc.SetFlightSoldOut(ctx, flightID)
}
