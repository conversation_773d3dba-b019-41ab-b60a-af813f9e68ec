package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/amadeus_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/ev_client"
	hnh_client "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/hnh_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/pkfare_client"
	redisRepo "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/redis/redis_repo"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/telegram"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/tongcheng_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/travel_fusion"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vietjet_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna1a_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/vna_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/webhook"
	convert "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/utils"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-flights/pkg/constants"
)

type IssueTicketService interface {
	IssueTicket(ctx context.Context, provider enum.FlightProvider, booking *domain.BookingSession, pnr *domain.PNR) (*domain.IssueTicketServiceResponse, error)
	ResolvePendingTicket(ctx context.Context, bkDetail *domain.BookingSession) (enum.TicketStatus, error)
	RefundFailedTicket(ctx context.Context, pm *domain.PaymentMethod, ownerID string, cf *domain.WebhookCfg, officeID, transID, bkCode string, provider string) error
}

type issueTicketService struct {
	cfg              *config.Schema
	vnaAdapter       vna_client.VNAAdapter
	tfAdapter        travel_fusion.TravelFusionAdapter
	amadeusAdapter   amadeus_client.AmadeusAdapter
	vjAdapter        vietjet_client.VietjetAdapter
	bookingRepo      repositories.BookingRepository
	pnrRepo          repositories.PNRRepository
	bookingRedisRepo redisRepo.BookingRepository
	telegramBotRepo  telegram.EVIssueTicketTelegramBotRepository
	walletClient     wallet.WalletClient
	paymentClient    payment.PaymentClient
	interClient      internal_client.InternalClient
	webhookClient    webhook.WebhookAdapter
	commSvc          CommissionService
	l2bSvc           L2bService
	evAdapter        ev_client.EVAdapter
	ancillarySvc     AncillaryService
	amadeusPNRRepo   repositories.AmadeusPNRRepository
	issueEMDTelegram telegram.IssueEMDRepository
	vna1aAdapter     vna1a_client.VNA1AAdapter
	hnhAdapter       hnh_client.HNHAdapter
	reportService    ReportService
	pkfareAdapter    pkfare_client.PkfareAdapter
	tongchengAdapter tongcheng_client.TongChengAdapter
}

func NewIssueTicketService(
	cfg *config.Schema,
	vnaAdapter vna_client.VNAAdapter,
	tfAdapter travel_fusion.TravelFusionAdapter,
	amadeusAdapter amadeus_client.AmadeusAdapter,
	vjAdapter vietjet_client.VietjetAdapter,
	bookingRepo repositories.BookingRepository,
	pnrRepo repositories.PNRRepository,
	bookingRedisRepo redisRepo.BookingRepository,
	telegramBotRepo telegram.EVIssueTicketTelegramBotRepository,
	walletClient wallet.WalletClient,
	paymentClient payment.PaymentClient,
	interClient internal_client.InternalClient,
	webhookClient webhook.WebhookAdapter,
	commSvc CommissionService,
	l2bSvc L2bService,
	evAdapter ev_client.EVAdapter,
	ancillarySvc AncillaryService,
	amadeusPNRRepo repositories.AmadeusPNRRepository,
	issueEMDTelegram telegram.IssueEMDRepository,
	vna1aAdapter vna1a_client.VNA1AAdapter,
	hnhAdapter hnh_client.HNHAdapter,
	reportService ReportService,
	pkfareAdapter pkfare_client.PkfareAdapter,
	tongchengAdapter tongcheng_client.TongChengAdapter,
) IssueTicketService {
	return &issueTicketService{
		cfg:              cfg,
		vnaAdapter:       vnaAdapter,
		tfAdapter:        tfAdapter,
		amadeusAdapter:   amadeusAdapter,
		vjAdapter:        vjAdapter,
		bookingRepo:      bookingRepo,
		pnrRepo:          pnrRepo,
		bookingRedisRepo: bookingRedisRepo,
		telegramBotRepo:  telegramBotRepo,
		paymentClient:    paymentClient,
		interClient:      interClient,
		webhookClient:    webhookClient,
		walletClient:     walletClient,
		commSvc:          commSvc,
		l2bSvc:           l2bSvc,
		evAdapter:        evAdapter,
		ancillarySvc:     ancillarySvc,
		amadeusPNRRepo:   amadeusPNRRepo,
		issueEMDTelegram: issueEMDTelegram,
		vna1aAdapter:     vna1aAdapter,
		reportService:    reportService,
		hnhAdapter:       hnhAdapter,
		pkfareAdapter:    pkfareAdapter,
		tongchengAdapter: tongchengAdapter,
	}
}

func (s *issueTicketService) issueTicketInternalBooking(ctx context.Context, provider enum.FlightProvider, booking *domain.BookingSession, pnr *domain.PNR) (*domain.IssueTicketServiceResponse, error) {
	res := &domain.IssueTicketServiceResponse{}
	var err error

	switch provider {
	case enum.FlightProviderVietJet, enum.FlightProviderVietJetAT:
		{
			// Issue
			pnr, paxFareInfos, err := s.vjAdapter.BookingAutoIssue(ctx, booking, booking.SearchRequest, pnr, provider, booking.SessionID)
			if err != nil {
				log.Error("vjAdapter.BookingAutoIssue error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
				return res, domain.ErrIssueTicketFailed
			}

			res.ReservationInfo = s.makeReservationInfo(pnr, len(booking.Itineraries))

			// Report
			if len(paxFareInfos) > 0 {
				booking.FareDataCf.PaxFareInfos = paxFareInfos
				err = s.bookingRepo.UpdateFareDataCf(ctx, booking.SessionID, booking.FareDataCf, booking.OriginFareData)
				if err != nil {
					log.Error("s.bookingRepo.UpdateFareDataCf error", log.Any("err", err), log.Any("fareDataCf", booking.FareDataCf))
				}
			}

			// Verify booking flight data
			hasChanges, err := s.vjAdapter.VerifyBookingFlightData(ctx, booking, provider)
			if err != nil {
				log.Error("vjAdapter.BookingAutoIssue error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
			}

			if hasChanges {
				log.Error("[24H] Booking changed must verify booking flight data error", log.String("bookingRef", booking.BookingRef))
			}
		}
	case enum.FlightProviderHNH, enum.FlightProviderHNH_HPL:
		{
			res, err = s.hnhAdapter.BookingAutoIssue(ctx, booking, pnr, booking.BookingCode, provider)
			if err != nil {
				log.Error("hnhAdapter.BookingAutoIssue error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
				return res, err
			}
		}
	case enum.FlightProviderAmadeus:
		{
			shouldManualIssue, err := s.shouldManualIssuer(booking.Itineraries[0].CarrierMarketing)
			if err != nil {
				shouldManualIssue = false
				log.Error("shouldManualIssuer error", log.Any("error", err), log.String("ManualIssuingAirlines", s.cfg.ManualIssuingAirlines), log.String("ManualIssuingActiveTime", s.cfg.ManualIssuingActiveTime))
			}

			if shouldManualIssue {
				cfFareRes, err := s.amadeusAdapter.ConfirmFare(ctx, booking, pnr, true, booking.BookingCode)
				if err != nil {
					log.Error("amadeusAdapter.ConfirmFare error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
					return nil, err
				}

				booking.BookingRef = cfFareRes.RecordLocator
				booking.FareDataIss = cfFareRes.FareCf

				res.ManualIssuing = true

				message := fmt.Sprintf("Khắc Xuất Khắc Xuất *PNR Number : %s*", booking.BookingRef) // HACK: Style Messsage can define khi len prd
				err = s.telegramBotRepo.SendMessage(ctx, message, false)
				if err != nil {
					log.Error("telegramBotRepo.SendMessage error", log.Any("error", err), log.String("message", message))
				}
			} else {
				defaultCommRate, err := s.getFinalCommissionRateForBooking(ctx, booking)
				if err != nil {
					log.Error("getFinalCommissionRateForBooking error", log.Any("error", err), log.Any("booking", booking))
					return nil, err
				}

				booking.CommissionRate = &defaultCommRate

				reser, etickets, emdInfo, fareCf, emdErr, err := s.amadeusAdapter.BookingAutoIssue(ctx, booking, pnr)
				if err != nil {
					res.FareCf = fareCf
					log.Error("amadeusAdapter.BookingAutoIssue error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
					return res, err
				}

				domainReser := convert.FromAmaReservationInfos(booking.Itineraries, reser)
				emdStatus := enum.EMDStatusEmpty
				res.ReservationInfo = domainReser
				res.Etickets = convert.FromAmaETicketInfos(etickets, booking.Itineraries, emdInfo, pnr.ListPax)

				if emdErr != nil {
					emdStatus = enum.EMDStatusFailed
				} else if emdInfo != nil {
					emdStatus = enum.EMDStatusOK
				}

				res.EMDStatus = emdStatus
				res.FareCf = fareCf
			}
		}
	case enum.FlightProviderPkfare:
		{
			if pnr == nil {
				log.Error("pnr is nil", log.String("bookingRef", booking.BookingRef))
				return res, errors.WithMessage(domain.ErrPNREmpty, "pnr nil")
			}

			bookingDetails := &domain.BookingDetails{
				OfficeID:        booking.OfficeID,
				FlightType:      booking.FlightType,
				Itineraries:     booking.Itineraries,
				FareDataCf:      booking.FareDataCf,
				ListPax:         pnr.ListPax,
				ContactInfo:     pnr.ContactInfo,
				Status:          enum.BookingStatusOK,
				AirlineSystem:   booking.AirlineSystem,
				VAT:             booking.VAT,
				PendingDeadline: booking.PendingDeadline,
				OriginFareData:  booking.OriginFareData,
				Tag:             enum.TagBZT,
			}

			createBookingRes, err := s.pkfareAdapter.CreateBooking(ctx, bookingDetails, booking.SessionID)
			if err != nil {
				log.Error("pkfareAdapter.CreateBooking error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
				return res, domain.ErrIssueTicketFailed
			}

			if createBookingRes == nil {
				log.Error("pkfareAdapter.CreateBooking empty value", log.String("bookingRef", booking.BookingRef))
				return res, domain.ErrIssueTicketFailed
			}

			booking.BookingRef = createBookingRes.BookingRef
			booking.OrderNumRef = createBookingRes.OrderNumRef

			res.ReservationInfo, res.Etickets, res.EMDStatus, res.Pending, res.PendingDeadlineTime, err = s.pkfareAdapter.IssueTicket(ctx, booking, pnr.ListPax, booking.BookingRef)
			if err != nil {
				log.Error("pkfareAdapter.IssueTicket error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
				return res, domain.ErrIssueTicketFailed
			}
		}
	case enum.FlightProviderTongCheng:
		{
			return res, commonErrors.ErrSomethingOccurred
		}
	default:
		{
			log.Error("Unsupported provider internal booking issuing error", log.Int("type", int(provider)))
			return res, domain.ErrInvalidValue
		}
	}

	// if res.EMDStatus == enum.EMDStatusOK {
	// 	_ = s.ancillarySvc.ClearSeatMap(ctx, booking.Itineraries)
	// }

	// if res.EMDStatus == enum.EMDStatusFailed {
	// 	go func() {
	// 		teleCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
	// 		defer cancel()
	// 		s.issueEMDTelegram.SendMessage(teleCtx, fmt.Sprintf("Issue EMD Failed [*Booking Code : %s , PNR Ref : %s , Provider : %s*]", booking.BookingCode, booking.BookingRef, enum.FlightProviderName[provider]))
	// 	}()
	// }

	return res, nil
}

func (s *issueTicketService) getFinalCommissionRateForBooking(ctx context.Context, booking *domain.BookingSession) (float64, error) {
	airlines := lo.Map[*domain.FlightItinerary](booking.Itineraries, func(item *domain.FlightItinerary, _ int) string {
		return item.CarrierMarketing
	})

	airlines = lo.Uniq[string](airlines)
	minComm := float64(0)
	commMap := map[string]*float64{}
	isRoundTrip := booking.SearchRequest.IsRoundTrip()

	commRules, err := s.commSvc.GetRuleSets(ctx, airlines)
	if err != nil {
		log.Error("commSvc.GetRuleSets error", log.Any("error", err), log.Any("airlines", airlines))
		return 0, err
	}

	if len(commRules) == 0 {
		return 0, nil
	}

	for i, iti := range booking.Itineraries {
		var commRate float64
		cachedRate := commMap[iti.ID]

		if cachedRate == nil {
			commRate, err = s.commSvc.ApplyRules(ctx, iti, commRules[iti.CarrierMarketing])
			if err != nil {
				log.Error("commSvc.ApplyRules error", log.Any("error", err), log.Any("iti", iti), log.Any("rules", commRules[iti.CarrierMarketing]))
				return 0, err
			}

			commMap[iti.ID] = &commRate
		} else {
			commRate = *cachedRate
		}

		if isRoundTrip && i == 0 {
			minComm = commRate
			break
		}

		if commRate < minComm || i == 0 {
			minComm = commRate
		}
	}

	return minComm, nil
}

func (s *issueTicketService) RefundFailedTicket(refundCtx context.Context, pm *domain.PaymentMethod, ownerID string, cf *domain.WebhookCfg, officeID, transID, bkCode string, provider string) error {
	if officeID == "" || transID == "" || bkCode == "" {
		return commonErrors.ErrInvalidInput
	}

	const defaultPM = commonEnum.PaymentMethod_Wallet
	var err error

	if pm == nil {
		pm, err = s.paymentClient.GetPaymentMethod(refundCtx, defaultPM)
		if err != nil {
			log.Error("paymentClient.GetPaymentMethod error", log.Any("error", err))
			return err
		}
	}
	shopInfo, err := s.interClient.GetOfficeInfo(refundCtx, "", officeID)
	if err != nil {
		log.Error("GetOfficeInfo error", log.Any("error", err), log.String("officeID", officeID))
		return err
	}

	if ownerID == "" {
		shopInfo, err := s.interClient.GetOfficeInfo(refundCtx, "", officeID)
		if err != nil {
			log.Error("GetOfficeInfo error", log.Any("error", err), log.String("officeID", officeID))
			return err
		}

		ownerID = shopInfo.OwnerID
		cf = shopInfo.WebhookCfg
	}

	refundTransInfo, refundErr := s.walletClient.RequestRefund(refundCtx, ownerID, transID, shopInfo, provider)
	if refundErr != nil {
		log.Error(" h.walletClient.RequestRefund error", log.Any("error", refundErr), log.String("userID", ownerID), log.String("transID", transID))
		return err
	}

	go func() {
		webhookCfg := cf

		if webhookCfg == nil || webhookCfg.WebhookKey == "" || webhookCfg.WebhookURLCfg.Transaction == "" {
			return
		}

		webhookCtx, cc := context.WithTimeout(context.Background(), constants.ClientRequestTimeout)
		defer cc()

		reqTrans := &domain.Transaction{
			BookingCode:   bkCode,
			TransactionID: refundTransInfo.ID,
			Type:          refundTransInfo.Type,
			Amount:        refundTransInfo.Amount,
			PaymentMethod: pm,
			OfficeID:      officeID,
			CreatedAt:     refundTransInfo.CreatedAt,
			Currency:      constants.VNCurrency,
		}

		if err := s.webhookClient.SendTransaction(webhookCtx, reqTrans, webhookCfg.WebhookKey, webhookCfg.WebhookURLCfg.Transaction); err != nil {
			log.Error("webhookClient.SendTransaction error", log.Any("error", err), log.Any("reqTrans", reqTrans))
		}
	}()

	return nil
}

func (s *issueTicketService) ResolvePendingTicket(ctx context.Context, bk *domain.BookingSession) (enum.TicketStatus, error) {
	if bk.Status != enum.BookingStatusTicketed || bk.TicketStatus != enum.TicketStatusPending || bk == nil || bk.BookingCode == "" {
		return bk.TicketStatus, nil
	}

	err := s.bookingRedisRepo.AcquireResolvePendingLock(bk.BookingCode)
	if err != nil {
		return bk.TicketStatus, nil
	}
	defer s.bookingRedisRepo.ReleaseResolvePendingLock(bk.BookingCode)

	pnr, err := s.pnrRepo.FindOne(ctx, bk.SessionID)
	if err != nil || pnr == nil {
		log.Error("pnrRepo.FindOne error", log.Any("error", err), log.String("sessionID", bk.SessionID))
		return enum.TicketStatusEmpty, commonErrors.ErrSomethingOccurred
	}

	pass := false

	forceFail := false
	if !forceFail && !bk.ManualIssuing {
		switch bk.Provider {
		case enum.FlightProviderAmadeus:
			{
				const retryTimes = 3
				var entityTickets []*models.EticketInfo
				var mReserInfos []*models.ReservationInfo
				var emdInfo []*models.EMDInfo
				var err error

				for i := 0; i < retryTimes; i++ {
					mReserInfos, entityTickets, emdInfo, err = s.amadeusAdapter.FetchEticketInfo(ctx, bk.BookingRef)
					if err != nil {
						log.Error("FetchEticketInfo error", log.Any("error", err), log.String("bkRef", bk.BookingRef))
					}

					if len(entityTickets) > 0 {
						break
					}

					time.Sleep(time.Second)
				}

				etickets := convert.FromAmaETicketInfos(entityTickets, bk.Itineraries, emdInfo, pnr.ListPax)
				if len(etickets) > 0 {
					bk.MapEtickets(etickets)

					if len(mReserInfos) > 0 {
						reserInfos := convert.FromAmaReservationInfos(bk.Itineraries, mReserInfos)

						bk.MapReservationCodeV2(reserInfos)
					}

					pass = true
				} else {
					go func() {
						bgCtx, cc := context.WithTimeout(context.Background(), constants.ThirdPartyRequestTimeout)
						defer cc()

						err := s.amadeusAdapter.PlaceQueue(bgCtx, bk.BookingRef)
						if err != nil {
							log.Error("PlaceQueue error", log.Any("error", err), log.String("bkRef", bk.BookingRef))
						}
					}()

					forceFail = true
				}
			}
		case enum.FlightProviderTravelFusion:
			{
				reservationCode, err := s.tfAdapter.FetchReservationCode(ctx, bk.BookingRef)
				if err != nil {
					if errors.Is(err, domain.ErrIssueTicketFailed) {
						forceFail = true
					} else {
						log.Error("tfAdapter.FetchReservationCode error", log.Any("error", err), log.String("bkRef", bk.BookingRef))
						return enum.TicketStatusEmpty, commonErrors.ErrSomethingOccurred
					}
				}

				if reservationCode != "" {
					bk.MapReservationCode(reservationCode)
					pass = true
				}
			}
		case enum.FlightProviderVNA:
			{
				entityEtickets, err := s.vnaAdapter.FetchEtickets(ctx, bk.BookingRef)
				if err != nil {
					log.Error("vnaAdapter.FetchEtickets error", log.Any("error", err), log.String("bkRef", bk.BookingRef))
					return enum.TicketStatusEmpty, commonErrors.ErrSomethingOccurred
				}

				etickets := convert.ToETickets(entityEtickets, bk.Itineraries, pnr.ListPax)

				if len(etickets) > 0 {
					bk.MapEtickets(etickets)
					bk.MapReservationCode(bk.BookingRef)
					pass = true
				}
			}
		case enum.FlightProviderEVInternational:
			{
				pass = false
			}
		case enum.FlightProviderVNA1A:
			{
				forceFail = true
			}
		case enum.FlightProviderEV:
			{
				eTickets, isPass, err := s.evAdapter.FetchEticketInfo(ctx, bk, pnr.ListPax)
				if err != nil {
					log.Error("evAdapter.FetchEticketInfo error", log.Any("error", err), log.Any("bk", bk))
					return enum.TicketStatusEmpty, err
				}

				if isPass {
					pass = true
					bk.MapEtickets(eTickets)
				}
			}
		case enum.FlightProviderPkfare:
			{
				//Case pending deadline expired
				if time.Now().UnixMilli() > bk.PendingDeadline {
					forceFail = true
				}

				reservationInfos, eTickets, isPass, err := s.pkfareAdapter.FetchEticketInfo(ctx, bk, pnr.ListPax)
				if err != nil {
					if errors.Is(err, domain.ErrIssueTicketFailed) {
						forceFail = true
					} else {
						log.Error("pkfareAdapter.FetchEticketInfo error", log.Any("error", err), log.Any("bk", bk))
						return enum.TicketStatusEmpty, err
					}
				}

				if isPass {
					pass = true
					bk.MapEtickets(eTickets)
					bk.MapReservationCodeV2(reservationInfos)
				}
			}
		case enum.FlightProviderTongCheng:
			{
				reservationInfos, eTickets, isPass, err := s.tongchengAdapter.FetchEticketInfo(ctx, bk, pnr.ListPax)
				if err != nil {
					if errors.Is(err, domain.ErrIssueTicketFailed) {
						forceFail = true
					} else {
						log.Error("tongchengAdapter.FetchEticketInfo error", log.Any("error", err), log.Any("bk", bk))
						return enum.TicketStatusEmpty, err
					}
				}

				if isPass {
					pass = true
					bk.MapEtickets(eTickets)
					bk.MapReservationCodeV2(reservationInfos)
				}
			}
		default:
			return bk.TicketStatus, nil
		}
	}

	// if !pass && time.Now().UnixMilli() > bk.PendingDeadline {
	// 	forceFail = true
	// }

	if forceFail {
		pass = false
		bk.TicketStatus = enum.TicketStatusFailed

		if bk.HasEMD() {
			bk.EMDStatus = enum.EMDStatusFailed
		} else {
			bk.EMDStatus = enum.EMDStatusEmpty
		}
	}

	if pass {
		bk.TicketStatus = enum.TicketStatusOK

		if bk.HasEMD() {
			bk.EMDStatus = enum.EMDStatusOK
		} else {
			bk.EMDStatus = enum.EMDStatusEmpty
		}

		err := s.bookingRepo.UpdateOne(ctx, bk.ID, bk)
		if err != nil {
			log.Error("bookingRepo.UpdateBooking error", log.Any("error", err), log.String("bkID", bk.ID), log.Any("bk", bk))
			return enum.TicketStatusEmpty, commonErrors.ErrSomethingOccurred
		}

		go func() {
			if bk.Status == enum.BookingStatusTicketed && bk.TicketStatus == enum.TicketStatusOK {
				ctxB, cancel := context.WithTimeout(context.Background(), constants.DefaultCtxTimeout)
				defer cancel()
				err = s.reportService.SendReportBooking(ctxB, bk, pnr)
				if err != nil {
					log.Error("reportService.SendReportBooking error", log.Any("error", err))
				}
			}
		}()

		s.l2bSvc.Book(ctx, bk.OfficeID)
	}

	return bk.TicketStatus, nil
}

func (s *issueTicketService) shouldManualIssuer(airline string) (bool, error) {
	manualAirlines := strings.Split(s.cfg.ManualIssuingAirlines, ",")
	for i := 0; i < len(manualAirlines); i++ {
		manualAirlines[i] = strings.TrimSpace(manualAirlines[i])
	}

	manualTimeRange := s.cfg.ManualIssuingActiveTime

	if len(manualAirlines) == 0 && airline != "" || manualTimeRange == "" {
		return false, nil
	}

	saigonLoc, err := time.LoadLocation(constants.HCMTimezone)
	if err != nil {
		return false, err
	}

	nowInSaigon := time.Now().In(saigonLoc)
	ok, err := utils.CheckTimeInRange(manualTimeRange, nowInSaigon)
	if err != nil {
		return false, err
	}

	if !ok {
		return false, nil
	}

	if !lo.Contains(manualAirlines, strings.TrimSpace(airline)) && airline != "" {
		return false, nil
	}

	return true, nil
}

func (s *issueTicketService) IssueTicket(ctx context.Context, provider enum.FlightProvider, booking *domain.BookingSession, pnr *domain.PNR) (*domain.IssueTicketServiceResponse, error) {
	if booking.InternalBooking {
		return s.issueTicketInternalBooking(ctx, provider, booking, pnr)
	}

	res := &domain.IssueTicketServiceResponse{}
	var err error

	switch provider {
	case enum.FlightProviderTravelFusion:
		res.ReservationInfo, res.EMDStatus, err = s.tfIssueTicket(ctx, booking)
	case enum.FlightProviderAmadeus:
		{
			shouldManualIssue, err := s.shouldManualIssuer(booking.Itineraries[0].CarrierMarketing)
			if err != nil {
				shouldManualIssue = false
				log.Error("shouldManualIssuer error", log.Any("error", err), log.String("ManualIssuingAirlines", s.cfg.ManualIssuingAirlines), log.String("ManualIssuingActiveTime", s.cfg.ManualIssuingActiveTime))
			}

			if shouldManualIssue {
				res.ManualIssuing = true

				message := fmt.Sprintf("Khắc Xuất Khắc Xuất *PNR Number : %s*", booking.BookingRef) // HACK: Style Messsage can define khi len prd
				err = s.telegramBotRepo.SendMessage(ctx, message, false)
				if err != nil {
					log.Error("telegramBotRepo.SendMessage error", log.Any("error", err), log.String("message", message))
				}
			} else {
				res.ReservationInfo, res.Etickets, res.EMDStatus, err = s.amadeusIssueTicket(ctx, booking, pnr)
				if err != nil {
					return nil, err
				}
			}
		}
	case enum.FlightProviderVNA:
		res.ReservationInfo, res.Etickets, res.EMDStatus, err = s.vnaIssueTicket(ctx, booking, pnr)
	case enum.FlightProviderVietJet, enum.FlightProviderVietJetAT:
		res.ReservationInfo, err = s.vjIssueTicket(ctx, booking, provider)
	case enum.FlightProviderEVInternational:
		{
			message := fmt.Sprintf("Khắc Xuất Khắc Xuất *PNR Number : %s*", booking.BookingRef) // HACK: Style Messsage can define khi len prd
			err = s.telegramBotRepo.SendMessage(ctx, message, false)
			if err != nil {
				log.Error("telegramBotRepo.SendMessage error", log.Any("error", err), log.String("message", message))
			}

			res.ManualIssuing = true
		}
	case enum.FlightProviderEV:
		res, err = s.evIssueTicket(ctx, booking, pnr)
	case enum.FlightProviderVNA1A:
		{
			res.ReservationInfo, res.Etickets, res.EMDStatus, err = s.vna1aAdapter.IssueTicket(ctx, booking.BookingRef, booking, pnr.ListPax)
			if err != nil {
				log.Error("vna1aAdapter.IssueTicket error", log.Any("err", err), log.String("bkRef", booking.BookingRef))
			}
		}
	case enum.FlightProviderHNH, enum.FlightProviderHNH_HPL:
		res.ReservationInfo, res.Etickets, res.EMDStatus, err = s.hnhIssueTicket(ctx, booking, pnr, provider)
	case enum.FlightProviderPkfare:
		{
			// shouldManualIssue, err := s.shouldManualIssuer("")
			// if err != nil {
			// 	shouldManualIssue = false
			// 	log.Error("shouldManualIssuer error", log.Any("error", err), log.String("ManualIssuingAirlines", s.cfg.ManualIssuingAirlines), log.String("ManualIssuingActiveTime", s.cfg.ManualIssuingActiveTime))
			// }

			shouldManualIssue := false

			if shouldManualIssue {
				t := time.UnixMilli(booking.LastTicketingDate).In(time.FixedZone("+07", 7*60*60)).Format("02/01/2006 15:04") // set timezone to +7

				message := fmt.Sprintf("OrderNum: *%s* PNR: *%s* Last ticket date: *%s*", booking.OrderNumRef, booking.BookingRef, t) // HACK: Style Messsage can define khi len prd
				// message := fmt.Sprintf("*PNR Number : %s*", booking.BookingRef)
				err = s.telegramBotRepo.SendMessage(ctx, message, true)
				if err != nil {
					log.Error("telegramBotRepo.SendMessage error", log.Any("error", err), log.String("message", message))
				}

				res.ManualIssuing = true
				res.PendingDeadlineTime = booking.LastTicketingDate
			} else {
				res.ReservationInfo, res.Etickets, res.EMDStatus, res.Pending, res.PendingDeadlineTime, err = s.pkfareAdapter.IssueTicket(ctx, booking, pnr.ListPax, booking.BookingRef)
				if err != nil {
					log.Error("pkfareAdapter.IssueTicket error", log.Any("err", err), log.String("bkRef", booking.BookingRef))
				}
			}
		}
	case enum.FlightProviderTongCheng:
		{
			res.ReservationInfo, res.Etickets, res.EMDStatus, res.Pending, res.PendingDeadlineTime, err = s.tongchengAdapter.IssueTicket(ctx, booking, pnr.ListPax, booking.BookingRef)
			if err != nil {
				log.Error("tongchengAdapter.IssueTicket error", log.Any("err", err), log.String("bkRef", booking.BookingRef))
			}
		}
	default:
		log.Error("Unsupported provider error", log.Int("type", int(provider)))
		return res, commonErrors.ErrInvalidInput
	}

	if res.EMDStatus == enum.EMDStatusOK {
		_ = s.ancillarySvc.ClearSeatMap(ctx, booking.Itineraries)
	}

	if res.EMDStatus == enum.EMDStatusFailed {
		go func() {
			teleCtx, cancel := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
			defer cancel()
			s.issueEMDTelegram.SendMessage(teleCtx, fmt.Sprintf("Issue EMD Failed [*Booking Code : %s , PNR Ref : %s , Provider : %s*]", booking.BookingCode, booking.BookingRef, enum.FlightProviderName[provider]))
		}()
	}

	return res, err
}

func (s *issueTicketService) makeReservationInfo(reservationCode string, len int) []*domain.IssueTicketSvcReservationInfo {
	out := make([]*domain.IssueTicketSvcReservationInfo, 0, len)

	for i := 0; i < len; i++ {
		out = append(out, &domain.IssueTicketSvcReservationInfo{
			ItineraryIndex:  i + 1,
			ReservationCode: reservationCode,
		})
	}

	return out
}

func (s *issueTicketService) tfIssueTicket(ctx context.Context, booking *domain.BookingSession) ([]*domain.IssueTicketSvcReservationInfo, enum.EMDStatus, error) {
	var emdStatus enum.EMDStatus
	supplierReference, err := s.tfAdapter.IssueTicket(ctx, booking.BookingRef, booking.ExpectedPrice)
	if err != nil {
		log.Error("(s *issueTicketService) tfIssueTicket error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
		return nil, emdStatus, err
	}

	if booking.HasEMD() {
		emdStatus = enum.EMDStatusOK
	}

	return s.makeReservationInfo(supplierReference, len(booking.Itineraries)), emdStatus, nil
}

func (s *issueTicketService) amadeusIssueTicket(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, enum.EMDStatus, error) {
	var commRate *float64
	emdStatus := enum.EMDStatusEmpty

	if booking.CommissionRate == nil {
		udCommRate, err := s.getFinalCommissionRateForBooking(ctx, booking)
		if err != nil {
			log.Error("getFinalCommissionRateForBooking error", log.Any("error", err), log.Any("booking", booking))
			return nil, nil, emdStatus, err
		}

		commRate = &udCommRate
	}

	hasINF := booking.SearchRequest.Passengers.INF > 0
	hasEMD := booking.HasEMD() && booking.FareDataCf != nil && (booking.FareDataCf.TotalSeatAmount > 0 || booking.FareDataCf.TotalBaggageAmount > 0)

	amadeusPNR, err := s.amadeusPNRRepo.FindOne(ctx, booking.SessionID)
	if err != nil {
		log.Error("s.amadeusPNRRepo.FindOne error", log.Any("err", err), log.String("sessionID", booking.SessionID))
		return nil, nil, emdStatus, commonErrors.ErrSomethingOccurred
	}

	if amadeusPNR == nil {
		log.Error("amadeusPNR nil", log.String("sessionID", booking.SessionID))
		return nil, nil, emdStatus, commonErrors.ErrSomethingOccurred
	}

	loc, err := time.LoadLocation(constants.HCMTimezone)
	if err != nil {
		fmt.Println("amadeusPNR Error loading location:", err)
		return nil, nil, emdStatus, commonErrors.ErrSomethingOccurred
	}

	gmt7 := time.Now().In(loc)
	bookingCreateTime := time.UnixMilli(amadeusPNR.Created_at).In(loc)
	isRetry := (gmt7.Day() != bookingCreateTime.Day()) || (gmt7.Day() == bookingCreateTime.Day() && (gmt7.Month() != bookingCreateTime.Month() || gmt7.Year() != bookingCreateTime.Year()))
	reservationInfo, etickets, emdInfo, emdError, err := s.amadeusAdapter.IssueTicket(ctx, booking.BookingRef, commRate, hasINF, hasEMD, amadeusPNR.TSMValues, booking, pnr, isRetry, booking.BookingRef)
	if err != nil {
		log.Error("(s *issueTicketService) amadeusIssueTicket error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
		return nil, nil, emdStatus, err
	}

	if emdError != nil {
		emdStatus = enum.EMDStatusFailed
	} else if hasEMD {
		emdStatus = enum.EMDStatusOK
	}

	return convert.FromAmaReservationInfos(booking.Itineraries, reservationInfo), convert.FromAmaETicketInfos(etickets, booking.Itineraries, emdInfo, pnr.ListPax), emdStatus, nil
}

func (s *issueTicketService) vnaIssueTicket(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, enum.EMDStatus, error) {
	paxInfo := convert.ToVNAIssuePaxInfo(pnr.ListPax)
	paxFare := convert.ToVNATotalFareInfo(booking.FareDataCf)
	var emdStatus enum.EMDStatus
	locator, eTickets, err := s.vnaAdapter.IssueTicket(ctx, booking.BookingRef, paxInfo, paxFare)
	if err != nil {
		log.Error("(s *issueTicketService) vnaIssueTicket error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
		return nil, []*domain.ETicketInfo{}, emdStatus, err
	}

	if booking.HasEMD() {
		if locator.EMDError != nil {
			emdStatus = enum.EMDStatusFailed
		} else {
			emdStatus = enum.EMDStatusOK
		}
	}

	return s.makeReservationInfo(locator.LocatorCode, len(booking.Itineraries)), convert.ToETickets(eTickets, booking.Itineraries, pnr.ListPax), emdStatus, nil
}

func (s *issueTicketService) vjIssueTicket(ctx context.Context, booking *domain.BookingSession, provider enum.FlightProvider) ([]*domain.IssueTicketSvcReservationInfo, error) {
	locator, err := s.vjAdapter.IssueTicket(ctx, booking, provider)
	if err != nil {
		log.Error("(s *issueTicketService) vjAdapter error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
		return nil, err
	}

	return s.makeReservationInfo(locator, len(booking.Itineraries)), nil
}

func (s *issueTicketService) evIssueTicket(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR) (*domain.IssueTicketServiceResponse, error) {
	response, err := s.evAdapter.IssueTicket(ctx, booking, pnr.ListPax, booking.BookingRef)
	if err != nil {
		log.Error("(s *issueTicketService) evAdapter error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
		return nil, domain.ErrIssueTicketFailed
	}

	return response, nil
}

func (s *issueTicketService) hnhIssueTicket(ctx context.Context, booking *domain.BookingSession, pnr *domain.PNR, provider enum.FlightProvider) ([]*domain.IssueTicketSvcReservationInfo, []*domain.ETicketInfo, enum.EMDStatus, error) {
	response, err := s.hnhAdapter.IssueTicket(ctx, booking, pnr.ListPax, booking.BookingRef, provider)
	if err != nil {
		log.Error("(s *issueTicketService) issueTicketService error", log.Any("error", err), log.String("bookingRef", booking.BookingRef))
		if response != nil {
			return nil, nil, response.EMDStatus, domain.ErrIssueTicketFailed
		}
		return nil, nil, enum.EMDStatusEmpty, domain.ErrIssueTicketFailed
	}

	return response.ReservationInfo, response.Etickets, response.EMDStatus, nil
}
