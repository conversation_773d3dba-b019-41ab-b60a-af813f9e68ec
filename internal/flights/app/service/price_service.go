package service

import (
	"context"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/price"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

// PriceService định nghĩa interface cho service tính giá flight
// Sử dụng proto definitions từ backendPb.CalculateFlightPricingRequest/Response
type PriceService interface {
	// CalculateSearchFlightsPricing tính giá cho search flights results
	// Sử dụng backendPb.CalculateFlightPricingRequest/Response
	CalculateSearchFlightsPricing(ctx context.Context, flights []*domain.ResponseFlight, passengers *domain.PaxRequest, flightType enum.FlightType, officeID, partnershipID string) error
	// CalculateFarePricing tính giá cho fare check/confirm với TotalFareInfo
	// Sử dụng backendPb.CalculateFlightPricingRequest/Response
	CalculateFarePricing(ctx context.Context, fareInfo *domain.TotalFareInfo, req *domain.SearchFlightsRequest, itineraries []*domain.FlightItinerary, flightID string, provider enum.FlightProvider) (*domain.TotalFareInfo, error)

	CalculatePenaltySearchFlight(ctx context.Context, pID, officeID string, flights []*domain.ResponseFlight) error
}

// priceService implement PriceService interface
// Sử dụng price.PriceClient để gọi gRPC service với proto mới
type priceService struct {
	priceClient price.PriceClient
}

// NewPriceService tạo instance mới của PriceService
// priceClient sử dụng backendPb proto definitions
func NewPriceService(priceClient price.PriceClient) PriceService {
	return &priceService{
		priceClient: priceClient,
	}
}

// CalculateSearchFlightsPricing tính giá cho search flights results và update trực tiếp vào flights
// Sử dụng backendPb.CalculateFlightPricingBatchRequest/Response để gọi pricing service
func (s *priceService) CalculateSearchFlightsPricing(ctx context.Context, flights []*domain.ResponseFlight, passengers *domain.PaxRequest, flightType enum.FlightType, officeID, partnershipID string) error {
	if len(flights) == 0 {
		return fmt.Errorf("no flights to calculate pricing")
	}

	if passengers == nil {
		return fmt.Errorf("passengers information is required")
	}

	// Prepare batch requests for all flights with FlightID mapping
	batchRequests := make([]*domain.PricingServiceRequest, 0, len(flights))
	flightIDToIndexMap := make(map[string]int) // Map FlightID to flight index

	for i, flight := range flights {
		if flight == nil || len(flight.Itineraries) == 0 {
			continue
		}

		// Convert ResponseFlight to FlightPricingData
		flightData := s.convertResponseFlightToFlightPricingData(flight, flightType)

		pricingReq := &domain.PricingServiceRequest{
			FlightData:    flightData,
			Passengers:    passengers,
			OfficeID:      officeID,
			PartnershipID: partnershipID,
			Currency:      flight.Currency,
			FlightID:      flight.FlightID, // Add FlightID for matching
		}

		batchRequests = append(batchRequests, pricingReq)
		flightIDToIndexMap[flight.FlightID] = i // Store mapping
	}

	if len(batchRequests) == 0 {
		return fmt.Errorf("no valid flights for pricing calculation")
	}

	// Execute batch pricing (uses backendPb.CalculateFlightPricingBatchResponse)
	batchReq := &domain.PricingBatchServiceRequest{
		Requests:      batchRequests,
		OfficeID:      officeID,
		PartnershipID: partnershipID,
	}

	batchRes, err := s.priceClient.CalculateFlightPricingBatch(ctx, batchReq)
	if err != nil {
		return fmt.Errorf("failed to calculate batch pricing: %w", err)
	}

	// Update flights with pricing results using FlightID matching
	if batchRes.Success && len(batchRes.Responses) > 0 {
		// Create a map of FlightID to ResponseFlight for efficient lookup
		flightMap := make(map[string]*domain.ResponseFlight)
		for _, flight := range flights {
			if flight != nil {
				flightMap[flight.FlightID] = flight
			}
		}

		// Match responses to flights using FlightID
		for _, response := range batchRes.Responses {
			if response.Success && response.PricingData != nil && response.PricingData.FlightID != "" {
				if flight, exists := flightMap[response.PricingData.FlightID]; exists {
					s.updateFlightWithPricingResult(flight, response.PricingData)
				} else {
					log.Warn("No matching flight found for pricing response",
						log.String("flight_id", response.FlightID))
				}
			}
		}
	}

	return nil
}

// CalculateFarePricing tính giá cho fare check/confirm với TotalFareInfo
// Sử dụng backendPb.CalculateFlightPricingRequest/Response để gọi pricing service
func (s *priceService) CalculateFarePricing(ctx context.Context, fareInfo *domain.TotalFareInfo, req *domain.SearchFlightsRequest, itineraries []*domain.FlightItinerary, flightID string, provider enum.FlightProvider) (*domain.TotalFareInfo, error) {
	if fareInfo == nil {
		return nil, fmt.Errorf("fareInfo is required")
	}

	if req == nil {
		return nil, fmt.Errorf("search request is required")
	}

	if len(itineraries) == 0 {
		return nil, fmt.Errorf("itineraries are required")
	}

	log.Info("Starting fare pricing calculation",
		log.String("office_id", req.OfficeID),
		log.Int("itinerary_count", len(itineraries)),
		log.Float64("original_total", fareInfo.TotalFareAmount))

	// Convert TotalFareInfo to SearchTotalFareInfo for FlightPricingData
	searchFareInfo := fareInfo.ConvertToSearch()
	if searchFareInfo == nil {
		return nil, fmt.Errorf("failed to convert TotalFareInfo to SearchTotalFareInfo")
	}

	// Create FlightPricingData from input parameters
	flightData := &domain.FlightPricingData{
		Itineraries:         itineraries,
		SearchTotalFareInfo: *searchFareInfo,
		Provider:            provider,
		FlightType:          req.FlightType,
	}

	if flightData.FlightType == enum.FlightTypeNone {
		flightData.FlightType = req.DetermineFlightType()
	}

	// Convert PaxRequest to domain passengers format
	passengers := &req.Passengers

	// Create pricing calculation request (maps to backendPb.CalculateFlightPricingRequest)
	pricingReq := &domain.PricingCalculationRequest{
		FlightID:      flightID,
		PartnershipID: s.getPartnershipIDFromRequest(req),
		OfficeID:      req.OfficeID,
		FlightData:    flightData,
		Passengers:    passengers,
		Currency:      fareInfo.Currency,
	}

	log.Info("Calling price client for fare calculation",
		log.String("flight_id", pricingReq.FlightID),
		log.Any("provider", flightData.Provider))

	// Call price client to calculate pricing (uses backendPb.CalculateFlightPricingResponse)
	pricingRes, err := s.priceClient.CalculateFlightPricing(ctx, pricingReq)
	if err != nil {
		log.Error("Failed to calculate fare pricing",
			log.String("flight_id", pricingReq.FlightID),
			log.Any("error", err))
		return nil, fmt.Errorf("failed to calculate fare pricing: %w", err)
	}

	if !pricingRes.Success {
		log.Error("Fare pricing calculation failed",
			log.String("flight_id", pricingReq.FlightID),
			log.String("error_code", pricingRes.ErrorCode))
		return nil, fmt.Errorf("fare pricing calculation failed: %s", pricingRes.ErrorCode)
	}

	if pricingRes.PricingData == nil {
		return nil, fmt.Errorf("pricing data is nil in response")
	}

	// Convert pricing result back to TotalFareInfo
	updatedFareInfo, err := s.convertPricingResultToTotalFareInfo(pricingRes.PricingData, fareInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to convert pricing result: %w", err)
	}

	return updatedFareInfo, nil
}

func (s *priceService) CalculatePenaltySearchFlight(ctx context.Context, pID, officeID string, flights []*domain.ResponseFlight) error {
	reqFlights := []*domain.MiniRuleFlight{}
	for _, flight := range flights {
		reqFlights = append(reqFlights, &domain.MiniRuleFlight{
			FlightID:  flight.FlightID,
			Provider:  flight.Provider,
			Airline:   flight.AirlineSystem,
			MiniRules: flight.MiniRules,
		})
	}

	req := &domain.CalculatePenaltyPricingRequest{
		PartnershipID: pID,
		OfficeID:      officeID,
		Flights:       reqFlights,
	}

	resFlights, err := s.priceClient.CalculatePenaltyPricing(ctx, req)
	if err != nil {
		log.Error("priceClient.CalculatePenaltyPricing failed", log.Any("req", req))
		return err
	}

	resFlightMap := map[string]*domain.MiniRuleFlight{}
	for _, flight := range resFlights {
		resFlightMap[flight.FlightID] = flight
	}

	for _, flight := range flights {
		if _, ok := resFlightMap[flight.FlightID]; ok {
			flight.MiniRules = resFlightMap[flight.FlightID].MiniRules
		}
	}

	return nil
}

// ===== HELPER METHODS =====

// convertResponseFlightToFlightPricingData converts ResponseFlight to FlightPricingData
func (s *priceService) convertResponseFlightToFlightPricingData(flight *domain.ResponseFlight, flightType enum.FlightType) *domain.FlightPricingData {
	if flight == nil {
		return nil
	}

	return &domain.FlightPricingData{
		Itineraries:         flight.Itineraries,
		SearchTotalFareInfo: flight.SearchTotalFareInfo,
		Provider:            flight.Provider,
		FlightType:          flightType,
	}
}

// updateFlightWithPricingResult updates ResponseFlight with pricing result
func (s *priceService) updateFlightWithPricingResult(flight *domain.ResponseFlight, pricingResult *domain.FlightPricingResult) {
	if flight == nil || pricingResult == nil {
		return
	}

	// Update flight with new pricing information
	if pricingResult.IsSuccess {
		// Update SearchTotalFareInfo with pricing results

		// Calculate TotalFareBasic and TotalTaxAmount from ItineraryResults
		var totalFareBasic, totalTaxAmount, totalFareAmount float64
		var totalPaxFares []*domain.ItineraryPaxFare

		// Aggregate passenger fares from PassengerResults (top-level)
		paxFareMap := make(map[enum.PaxType]*domain.ItineraryPaxFare) // Map by PaxType

		// Use PassengerResults from top-level response if available
		if len(pricingResult.PassengerResults) > 0 {
			for _, passengerResult := range pricingResult.PassengerResults {
				paxType := passengerResult.PassengerType

				// Calculate total amounts for this passenger type (per passenger * quantity)
				totalPassengerFareAmount := passengerResult.FinalFareAmount * float64(passengerResult.Quantity)
				totalPassengerFareBasic := passengerResult.FinalFareBasic * float64(passengerResult.Quantity)
				totalPassengerTaxAmount := passengerResult.TaxAmount * float64(passengerResult.Quantity)

				if _, exists := paxFareMap[paxType]; !exists {
					paxFareMap[paxType] = &domain.ItineraryPaxFare{
						PaxType:    paxType,
						FareAmount: passengerResult.FinalFareAmount,
						FareBasic:  passengerResult.FinalFareBasic,
						TaxAmount:  passengerResult.TaxAmount,
						Currency:   flight.Currency,
					}
				}

				totalFareBasic += totalPassengerFareBasic
				totalTaxAmount += totalPassengerTaxAmount
				totalFareAmount += totalPassengerFareAmount
			}
		}

		flight.TotalFareAmount = totalFareAmount

		// Convert map to slice
		for _, fare := range paxFareMap {
			totalPaxFares = append(totalPaxFares, fare)
		}

		// Update SearchTotalFareInfo
		flight.TotalPaxFares = totalPaxFares
		flight.TotalFareBasic = totalFareBasic
		flight.TotalTaxAmount = totalTaxAmount

		// Keep existing currency if not empty, otherwise use flight currency
		if flight.Currency == "" && len(totalPaxFares) > 0 {
			flight.Currency = totalPaxFares[0].Currency
		}
	}
}

// getProviderFromItineraries determines provider from itineraries
func (s *priceService) getProviderFromItineraries(itineraries []*domain.FlightItinerary) enum.FlightProvider {
	if len(itineraries) == 0 {
		return enum.FlightProviderNone
	}

	// For now, return a default provider
	// In real implementation, this should be determined from itinerary data
	return enum.FlightProviderVNA
}

// convertPricingResultToTotalFareInfo converts pricing result back to TotalFareInfo
func (s *priceService) convertPricingResultToTotalFareInfo(pricingData *domain.FlightPricingResult, originalFareInfo *domain.TotalFareInfo) (*domain.TotalFareInfo, error) {
	if pricingData == nil {
		return nil, fmt.Errorf("pricing data is nil")
	}

	if originalFareInfo == nil {
		return nil, fmt.Errorf("original fare info is nil")
	}

	// Calculate totals from pricing result
	var totalFareBasic, totalTaxAmount, totalFareAmount float64
	var totalPaxFares []*domain.ItineraryPaxFare

	// Aggregate passenger fares from all itinerary results
	paxFareMap := make(map[enum.PaxType]*domain.ItineraryPaxFare) // Map by PaxType

	// Use PassengerResults from top-level response if available
	if len(pricingData.PassengerResults) > 0 {
		for _, passengerResult := range pricingData.PassengerResults {
			paxType := passengerResult.PassengerType

			// Calculate total amounts for this passenger type (per passenger * quantity)
			totalPassengerFareAmount := passengerResult.FinalFareAmount * float64(passengerResult.Quantity)
			totalPassengerFareBasic := passengerResult.FinalFareBasic * float64(passengerResult.Quantity)
			totalPassengerTaxAmount := passengerResult.TaxAmount * float64(passengerResult.Quantity)

			if _, exists := paxFareMap[paxType]; !exists {
				// Create new fare entry
				paxFareMap[paxType] = &domain.ItineraryPaxFare{
					PaxType:    paxType,
					FareAmount: passengerResult.FinalFareAmount,
					FareBasic:  passengerResult.FinalFareBasic,
					TaxAmount:  passengerResult.TaxAmount,
					Currency:   originalFareInfo.Currency,
				}
			}

			totalFareBasic += totalPassengerFareBasic
			totalTaxAmount += totalPassengerTaxAmount
			totalFareAmount += totalPassengerFareAmount
		}
	}
	// Convert map to slice
	for _, fare := range paxFareMap {
		totalPaxFares = append(totalPaxFares, fare)
	}

	// Create updated TotalFareInfo
	updatedFareInfo := &domain.TotalFareInfo{
		TotalPaxFares:       totalPaxFares,
		BaseTotalFareAmount: totalFareBasic + totalTaxAmount, // Use final calculated values
		TotalFareAmount:     totalFareAmount,                 // Use final calculated values
		TotalFareBasic:      totalFareBasic,                  // Use final calculated values
		TotalTaxAmount:      totalTaxAmount,
		TotalSeatAmount:     originalFareInfo.TotalSeatAmount,    // Keep original seat amount
		TotalBaggageAmount:  originalFareInfo.TotalBaggageAmount, // Keep original baggage amount
		Currency:            originalFareInfo.Currency,
		PaxFareInfos:        originalFareInfo.PaxFareInfos, // Keep original pax fare infos
		ExchangeRate:        originalFareInfo.ExchangeRate, // Keep original exchange rate

		// Set tracking pricing data using embedded struct
		PricingTracking: &domain.PricingTracking{
			ItineraryPricingResults: pricingData.ItineraryResults,
			PassengerResults:        pricingData.PassengerResults,
			TotalHiddenFeeAmount:    pricingData.TotalHiddenFeeAmount,
			TotalDiscountAmount:     pricingData.TotalDiscountAmount,
			OriginalTotalFareAmount: pricingData.OriginalTotalFareAmount,
			FinalTotalFareAmount:    pricingData.FinalTotalFareAmount,
			OriginalTotalFareBasic:  pricingData.OriginalTotalFareBasic,
			FinalTotalFareBasic:     pricingData.FinalTotalFareBasic,
		},
	}

	return updatedFareInfo, nil
}

// getPartnershipIDFromRequest extracts partnership ID from request
func (s *priceService) getPartnershipIDFromRequest(_ *domain.SearchFlightsRequest) string {
	// For now, return empty string as partnership ID should be provided by the caller
	// In real implementation, this could be extracted from request context or user session
	return ""
}
