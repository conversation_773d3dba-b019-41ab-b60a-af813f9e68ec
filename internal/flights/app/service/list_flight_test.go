package service

import (
	"context"
	"fmt"
	"testing"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func TestComnbineFlights(t *testing.T) {
	handler := &listFlightService{}

	testData := [][]*domain.ResponseFlight{
		// Rec
		{
			{
				FlightID: "1",
				Itineraries: []*domain.FlightItinerary{
					{DepartPlace: "SGN", ArrivalPlace: "HAN"},
					{DepartPlace: "HAN", ArrivalPlace: "SGN"},
				},

				OptionType: enum.FlightOptionTypeRecommend,
			},
		},
		// Inbound outboud
		{
			{
				FlightID: "1",
				Itineraries: []*domain.FlightItinerary{
					{DepartPlace: "HAN", ArrivalPlace: "SGN"},
				},

				OptionType: enum.FlightOptionSingleTrip,
			},
			{
				FlightID: "1",
				Itineraries: []*domain.FlightItinerary{
					{DepartPlace: "SGN", ArrivalPlace: "HAN"},
				},

				OptionType: enum.FlightOptionSingleTrip,
			},
		},
		// Multi
		{
			{
				FlightID: "1",
				Itineraries: []*domain.FlightItinerary{
					{DepartPlace: "DAD", ArrivalPlace: "SGN"},
				},
				Leg:        3,
				OptionType: enum.FlightOptionSingleTrip,
			},
			{
				FlightID: "2",
				Itineraries: []*domain.FlightItinerary{
					{DepartPlace: "SGN", ArrivalPlace: "HAN"},
				},
				Leg:        1,
				OptionType: enum.FlightOptionSingleTrip,
			},
			{
				FlightID: "3",
				Itineraries: []*domain.FlightItinerary{
					{DepartPlace: "HAN", ArrivalPlace: "DAD"},
				},
				Leg:        2,
				OptionType: enum.FlightOptionSingleTrip,
			},
		},
	}

	for _, testDataItem := range testData {
		option := enum.FlightOptionTypeName[testDataItem[0].OptionType]

		t.Run(fmt.Sprintf("TEST OPTION DATA %v", option), func(t *testing.T) {
			out := handler.ComnbineFlights(context.Background(), testDataItem)

			if rs := validateTestResult(out, out.OptionType); rs == "" {
				t.Error("FAILED WITH OPTION DATA ", option)
			} else {
				t.Log(rs)
			}
		})
	}

}

func validateTestResult(ins *domain.ResponseFlight, opt enum.FlightOptionType) string {
	ept := "SGNHAN/HANSGN/"
	if opt == enum.FlightOptionSingleTrip {
		ept = "SGNHAN/HANDAD/DADSGN/"
	}

	have := ""

	for _, iti := range ins.Itineraries {
		have += iti.DepartPlace + iti.ArrivalPlace + "/"
	}

	if ept == have {
		return ept
	}

	return ""
}
