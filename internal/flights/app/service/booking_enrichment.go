package service

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/internal_client"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

type BookingEnrichmentService interface {
	EnrichBookingListWithPaxAndAgent(ctx context.Context, bookings []*domain.BookingSession, partnershipID string) ([]*domain.EnrichedBookingSession, error)
	EnrichBookingDetailWithAgent(ctx context.Context, booking *domain.BookingDetails, partnershipID string) (*domain.BookingDetails, error)
}

type bookingEnrichmentService struct {
	pnrRepo       repositories.PNRRepository
	internalClient internal_client.InternalClient
}

func NewBookingEnrichmentService(
	pnrRepo repositories.PNRRepository,
	internalClient internal_client.InternalClient,
) BookingEnrichmentService {
	return &bookingEnrichmentService{
		pnrRepo:       pnrRepo,
		internalClient: internalClient,
	}
}

func (s *bookingEnrichmentService) EnrichBookingListWithPaxAndAgent(ctx context.Context, bookings []*domain.BookingSession, partnershipID string) ([]*domain.EnrichedBookingSession, error) {
	enrichedBookings := make([]*domain.EnrichedBookingSession, 0, len(bookings))

	for _, booking := range bookings {
		enriched := &domain.EnrichedBookingSession{
			BookingSession: *booking,
			AgentCode:      booking.OfficeID, // Default to OfficeID
		}

		// Get PNR data for passengers
		if booking.SessionID != "" {
			pnr, err := s.pnrRepo.FindOne(ctx, booking.SessionID)
			if err != nil {
				log.Error("Failed to get PNR for booking", 
					log.String("sessionID", booking.SessionID),
					log.Any("error", err))
				// Continue without PNR data
			} else if pnr != nil {
				enriched.ListPax = pnr.ListPax
			}
		}

		// Get agent code from office info
		if booking.OfficeID != "" {
			officeInfo, err := s.internalClient.GetOfficeInfo(ctx, partnershipID, booking.OfficeID)
			if err != nil {
				log.Error("Failed to get office info", 
					log.String("officeID", booking.OfficeID),
					log.String("partnershipID", partnershipID),
					log.Any("error", err))
				// Continue with OfficeID as agent code
			} else if officeInfo != nil {
				enriched.AgentCode = officeInfo.Code
			}
		}

		enrichedBookings = append(enrichedBookings, enriched)
	}

	return enrichedBookings, nil
}

func (s *bookingEnrichmentService) EnrichBookingDetailWithAgent(ctx context.Context, booking *domain.BookingDetails, partnershipID string) (*domain.BookingDetails, error) {
	// Get agent code from office info
	if booking.OfficeID != "" && partnershipID != "" {
		officeInfo, err := s.internalClient.GetOfficeInfo(ctx, partnershipID, booking.OfficeID)
		if err != nil {
			log.Error("Failed to get office info for booking detail",
				log.String("officeID", booking.OfficeID),
				log.String("partnershipID", partnershipID),
				log.Any("error", err))
			// Continue without agent code
		} else if officeInfo != nil {
			// Add agent code to booking details
			// Note: BookingDetails struct may need to be extended with AgentCode field
			// For now, we can't modify the struct, so this is a placeholder
			log.Info("Got agent code for booking",
				log.String("agentCode", officeInfo.Code),
				log.String("officeID", booking.OfficeID))
		}
	}

	return booking, nil
}
