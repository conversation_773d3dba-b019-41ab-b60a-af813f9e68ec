package query

import (
	"context"

	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

type GetCurrencyExchangeDetailHandler interface {
	Handle(ctx context.Context, req *domain.GetCurrencyExchangeDetailReq) (*domain.CurrencyExchange, error)
}

type getCurrencyExchangeDetailHandler struct {
	currencyExchangeRepo repositories.CurrencyExchangeRepository
}

func NewGetCurrencyExchangeDetailHandler(currencyExchangeRepo repositories.CurrencyExchangeRepository) GetCurrencyExchangeDetailHandler {
	return &getCurrencyExchangeDetailHandler{currencyExchangeRepo}
}

func (h *getCurrencyExchangeDetailHandler) Handle(ctx context.Context, req *domain.GetCurrencyExchangeDetailReq) (*domain.CurrencyExchange, error) {
	currencyExchange, err := h.currencyExchangeRepo.FindByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	return currencyExchange, nil
}
