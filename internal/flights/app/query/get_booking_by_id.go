package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

type GetBookingByIDHandler interface {
	Handle(ctx context.Context, req *domain.GetBookingByIDRequest) (*domain.GetBookingByIDResponse, error)
}

type getBookingByIDHandler struct {
	bookingRepo repositories.BookingRepository
	pnrRepo     repositories.PNRRepository
}

func NewGetBookingByIDHandler(
	bookingRepo repositories.BookingRepository,
	pnrRepo repositories.PNRRepository,
) GetBookingByIDHandler {
	return &getBookingByIDHandler{
		bookingRepo: bookingRepo,
		pnrRepo:     pnrRepo,
	}
}

func (h *getBookingByIDHandler) Handle(ctx context.Context, req *domain.GetBookingByIDRequest) (*domain.GetBookingByIDResponse, error) {
	// Validate request
	if req == nil {
		return &domain.GetBookingByIDResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: errors.ErrInvalidInput.Error(),
			},
		}, nil
	}

	if req.ID == "" {
		return &domain.GetBookingByIDResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: "BOOKING_ID_REQUIRED",
			},
		}, nil
	}

	// Find booking by ID using existing repository method
	booking, err := h.findBookingByID(ctx, req.ID)
	if err != nil {
		log.Error("Failed to find booking by ID",
			log.Any("error", err),
			log.String("id", req.ID),
			log.String("officeID", req.OfficeID))
		return &domain.GetBookingByIDResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: errors.ErrSomethingOccurred.Error(),
			},
		}, nil
	}

	if booking == nil {
		return &domain.GetBookingByIDResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: errors.ErrNotFound.Error(),
			},
		}, nil
	}

	// Check office permission if provided
	if req.OfficeID != "" && booking.OfficeID != req.OfficeID {
		return &domain.GetBookingByIDResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: errors.ErrPermissionDenied.Error(),
			},
		}, nil
	}

	// Get PNR information
	pnr, err := h.pnrRepo.FindOne(ctx, booking.SessionID)
	if err != nil {
		log.Error("pnrRepo.FindOne error",
			log.Any("error", err),
			log.String("sessionID", booking.SessionID))
		return &domain.GetBookingByIDResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: errors.ErrSomethingOccurred.Error(),
			},
		}, nil
	}

	// Build booking details
	bkDetail := domain.BookingDetails{
		OfficeID:          booking.OfficeID,
		OrderID:           booking.OrderID,
		TicketStatus:      booking.TicketStatus,
		EMDStatus:         booking.EMDStatus,
		Status:            booking.Status,
		BookingCode:       booking.BookingCode,
		FlightType:        booking.FlightType,
		Itineraries:       booking.Itineraries,
		FareDataCf:        booking.FareDataCf,
		LastTicketingDate: booking.LastTicketingDate,
		AirlineSystem:     booking.AirlineSystem,
		VAT:               booking.VAT,
		IsTransferred:     booking.IsTransferred,
		Transferable:      booking.IsTransferable(),
		PendingDeadline:   booking.PendingDeadline,
	}

	// Add PNR information if available
	if pnr != nil {
		bkDetail.ListPax = pnr.ListPax
		bkDetail.ContactInfo = pnr.ContactInfo
	}

	return &domain.GetBookingByIDResponse{
		BookingDetails: bkDetail,
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
	}, nil
}

// findBookingByID attempts to find booking by ID using different methods
func (h *getBookingByIDHandler) findBookingByID(ctx context.Context, id string) (*domain.BookingSession, error) {
	// First try to find by MongoDB ObjectID (most likely case for ID)
	booking, err := h.bookingRepo.FindBookingByID(ctx, id)
	if err != nil {
		log.Error("bookingRepo.FindBookingByID error",
			log.Any("error", err),
			log.String("id", id))
		// If error is due to invalid ObjectID format, try session ID
		if err.Error() != "ObjectIDFromHex id: encoding/hex: invalid byte: U+002D '-'" {
			return nil, err
		}
	}

	if booking != nil {
		return booking, nil
	}

	// If not found by ObjectID, try to find by session ID as fallback
	booking, err = h.bookingRepo.FindBookingBySessionID(ctx, id)
	if err != nil {
		log.Error("bookingRepo.FindBookingBySessionID error",
			log.Any("error", err),
			log.String("id", id))
		return nil, err
	}

	return booking, nil
}
