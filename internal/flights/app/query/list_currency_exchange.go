package query

import (
	"context"

	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
)

type ListCurrencyExchangeHandler interface {
	Handle(ctx context.Context, req *domain.ListCurrencyExchangeReq) ([]*domain.CurrencyExchange, error)
}

type listCurrencyExchangeHandler struct {
	currencyExchangeRepo repositories.CurrencyExchangeRepository
}

func NewListCurrencyExchangeHandler(currencyExchangeRepo repositories.CurrencyExchangeRepository) ListCurrencyExchangeHandler {
	return &listCurrencyExchangeHandler{currencyExchangeRepo}
}

func (h *listCurrencyExchangeHandler) Handle(ctx context.Context, req *domain.ListCurrencyExchangeReq) ([]*domain.CurrencyExchange, error) {
	res, err := h.currencyExchangeRepo.FindWithFilter(ctx, req)
	if err != nil {
		log.Error("currencyExchangeRepo.FindWithFilter error", log.Any("error", err), log.Any("req", req))
		return nil, commonErrors.ErrSomethingOccurred
	}

	return res, nil
}
