package utils

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	"github.com/mitchellh/mapstructure"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DecodeLegacyMetadata: giải mã "metadata cũ" từ nhiều dạng Mongo về struct đích.
//   - Hỗ trợ: primitive.D, bson.M, bson.Raw, primitive.A, []any, map[string]any, []byte (BSON), string (JSON),
//     và cả struct/pointer-to-struct.
//   - Bước 1: normalize -> map[string]any hoặc []any các map
//   - Bước 2: nếu là slice, lấy phần tử đầu tiên không nil
//   - Bước 3: thử fast-path bằng BSON (tôn trọng bson tags); nếu fail, fallback sang mapstructure + remap hook.
func DecodeLegacyMetadata(source any, target any) error {
	if source == nil {
		return fmt.Errorf("source data is nil")
	}

	norm, err := normalizeToGeneric(source)
	if err != nil {
		return fmt.Errorf("normalize source failed: %w", err)
	}

	// Chọn dữ liệu để decode (map hoặc phần tử đầu của slice)
	dataMap, err := pickFirstMap(norm)
	if err != nil {
		return err
	}

	// --- Fast-path: BSON marshal/unmarshal trực tiếp (ưu tiên vì nhanh & đúng bson tag) ---
	if b, err := bson.Marshal(dataMap); err == nil {
		if err := bson.Unmarshal(b, target); err == nil {
			return nil
		}
	}

	// --- Fallback: mapstructure với hook remap khoá legacy → field name/tag ---
	decoder, err := mapstructure.NewDecoder(&mapstructure.DecoderConfig{
		Result:  target,
		TagName: "json", // vẫn đặt "json"; remap hook sẽ đưa key → field name nên tagName không quá quan trọng
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			remapLegacyKeysHookFunc, // nâng cấp: hiểu cả json/bson tag và biến thể khoá
		),
	})
	if err != nil {
		return fmt.Errorf("failed to create mapstructure decoder: %w", err)
	}
	if err := decoder.Decode(dataMap); err != nil {
		return fmt.Errorf("mapstructure failed to decode remapped data: %w", err)
	}

	return nil
}

// ------------------------- Helpers -------------------------

// normalizeToGeneric chuẩn hoá mọi loại input Mongo/Go về:
//   - map[string]any
//   - hoặc []any (mỗi phần tử rồi cũng sẽ là map[string]any sau chuẩn hoá đệ quy)
func normalizeToGeneric(v any) (any, error) {
	switch x := v.(type) {

	case bson.Raw:
		var tmp any
		if err := bson.Unmarshal(x, &tmp); err != nil {
			return nil, err
		}
		return normalizeToGeneric(tmp)

	case primitive.D:
		// D.Map() → bson.M (map). Mất order nhưng không ảnh hưởng decode struct.
		return map[string]any(x.Map()), nil

	case bson.M:
		return map[string]any(x), nil

	case map[string]any:
		return x, nil

	case primitive.A:
		out := make([]any, 0, len(x))
		for _, el := range x {
			normEl, err := normalizeToGeneric(el)
			if err != nil {
				return nil, err
			}
			out = append(out, normEl)
		}
		return out, nil

	case []any:
		out := make([]any, 0, len(x))
		for _, el := range x {
			normEl, err := normalizeToGeneric(el)
			if err != nil {
				return nil, err
			}
			out = append(out, normEl)
		}
		return out, nil

	case []byte: // giả định là BSON bytes
		var tmp any
		if err := bson.Unmarshal(x, &tmp); err != nil {
			return nil, err
		}
		return normalizeToGeneric(tmp)

	case string: // có thể là JSON legacy
		var tmp any
		// Ưu tiên Extended JSON của Mongo nếu cần, ở đây dùng JSON thường:
		if err := json.Unmarshal([]byte(x), &tmp); err != nil {
			// Nếu muốn chắc chắn, có thể thử bson.UnmarshalExtJSON ở đây.
			return nil, fmt.Errorf("failed to unmarshal string JSON: %w", err)
		}
		return normalizeToGeneric(tmp)

	default:
		// Nếu là struct hoặc *struct thì marshal BSON rồi đưa về map
		rv := reflect.ValueOf(v)
		if rv.Kind() == reflect.Ptr {
			rv = rv.Elem()
		}
		if rv.IsValid() && rv.Kind() == reflect.Struct {
			b, err := bson.Marshal(v)
			if err != nil {
				return nil, err
			}
			var m map[string]any
			if err := bson.Unmarshal(b, &m); err != nil {
				return nil, err
			}
			return m, nil
		}

		return nil, fmt.Errorf("unsupported source data type: %T", v)
	}
}

// pickFirstMap chọn ra map[string]any để decode:
// - Nếu input là map → trả về luôn
// - Nếu input là slice → lấy phần tử đầu tiên là map (bỏ qua nil/kiểu khác)
func pickFirstMap(v any) (map[string]any, error) {
	switch t := v.(type) {
	case map[string]any:
		return t, nil
	case []any:
		for _, el := range t {
			if el == nil {
				continue
			}
			if m, ok := el.(map[string]any); ok {
				return m, nil
			}
		}
		return nil, fmt.Errorf("source data is a slice but contains no map elements to decode")
	default:
		return nil, fmt.Errorf("unsupported normalized data type: expected map or slice, got %T", v)
	}
}

// remapLegacyKeysHookFunc: remap key của map → đúng tên field struct,
// nhận biết cả json/bson tag, tên field gốc, và các biến thể lowercase/underscore/hyphen.
func remapLegacyKeysHookFunc(from reflect.Type, to reflect.Type, data any) (any, error) {
	if from.Kind() != reflect.Map || to.Kind() != reflect.Struct {
		return data, nil
	}

	// Chỉ xử lý map[string]any
	src, ok := data.(map[string]any)
	if !ok {
		return data, nil
	}

	// Xây bảng alias key → FieldName
	aliases := make(map[string]string)
	for i := 0; i < to.NumField(); i++ {
		f := to.Field(i)
		if f.PkgPath != "" { // unexported
			continue
		}
		// Candidate names: FieldName, jsonTag, bsonTag
		cands := []string{f.Name}

		if tag := f.Tag.Get("json"); tag != "" && tag != "-" {
			if name := strings.Split(tag, ",")[0]; name != "" {
				cands = append(cands, name)
			}
		}
		if tag := f.Tag.Get("bson"); tag != "" && tag != "-" {
			if name := strings.Split(tag, ",")[0]; name != "" {
				cands = append(cands, name)
			}
		}

		for _, c := range cands {
			if c == "" {
				continue
			}
			aliases[canonKey(c)] = f.Name
		}
		// Thêm các biến thể thông dụng
		aliases[canonKey(strings.ToLower(f.Name))] = f.Name
	}

	remapped := make(map[string]any, len(src))
	for k, v := range src {
		if field, ok := aliases[canonKey(k)]; ok {
			remapped[field] = v
		} else {
			// Không map được thì để nguyên (tránh mất dữ liệu)
			remapped[k] = v
		}
	}

	return remapped, nil
}

func canonKey(s string) string {
	// chuẩn hoá khoá: lowercase + bỏ "_" và "-"
	s = strings.ToLower(s)
	s = strings.ReplaceAll(s, "_", "")
	s = strings.ReplaceAll(s, "-", "")
	return s
}
