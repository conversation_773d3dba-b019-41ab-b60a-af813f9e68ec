package enum

type Permitted string

const (
	PermittedNotAllowed Permitted = "not_allowed"
	PermittedAllowed    Permitted = "allowed"
	PermittedUnknown    Permitted = "unknown"
)

const (
	PermittedNotAllowedInt = iota
	PermittedAllowedInt
	PermittedUnknownInt
)

var PermittedMap = map[int]Permitted{
	PermittedAllowedInt:    PermittedAllowed,
	PermittedNotAllowedInt: PermittedNotAllowed,
	PermittedUnknownInt:    PermittedUnknown,
}

var PermittedReverseMap = map[Permitted]int{
	PermittedAllowed:    PermittedAllowedInt,
	PermittedNotAllowed: PermittedNotAllowedInt,
	PermittedUnknown:    PermittedUnknownInt,
}
