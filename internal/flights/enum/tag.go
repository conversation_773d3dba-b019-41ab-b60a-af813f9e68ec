package enum

import (
	"bytes"
	"errors"
	"fmt"
)

type Tag uint

const (
	TagNone Tag = iota
	TagBZT
	TagLLD
)

var TagName = map[Tag]string{
	TagNone: "",
	TagBZT:  "BZT",
	TagLLD:  "LLD",
}

var TagValue = func() map[string]Tag {
	value := map[string]Tag{}

	for k, v := range TagName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e Tag) MarshalJSON() ([]byte, error) {
	v, ok := TagName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *Tag) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := TagValue[string(data)]

	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (*Tag) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range TagName {
		vals = append(vals, name)
	}

	return vals
}
