package enum

type Situation string

const (
	SituationBeforeDeparture Situation = "before_departure"
	SituationAfterDeparture  Situation = "after_departure"
	SituationAll             Situation = "all"
)

const (
	SituationBeforeDepartureInt = iota
	SituationAfterDepartureInt
	SituationAllInt
)

var SituationMap = map[int]Situation{
	SituationBeforeDepartureInt: SituationBeforeDeparture,
	SituationAfterDepartureInt:  SituationAfterDeparture,
	SituationAllInt:             SituationAll,
}

var SituationReverseMap = map[Situation]int{
	SituationBeforeDeparture: SituationBeforeDepartureInt,
	SituationAfterDeparture:  SituationAfterDepartureInt,
	SituationAll:             SituationAllInt,
}
