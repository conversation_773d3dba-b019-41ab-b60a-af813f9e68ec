package enum

import (
	"bytes"
	"errors"
	"fmt"
)

type FilterMode uint

const (
	FilterModeNone FilterMode = iota
	FilterModeInclude
	FilterModeExclude
)

var FilterModeName = map[FilterMode]string{
	FilterModeNone:    "",
	FilterModeInclude: "Include",
	FilterModeExclude: "Exclude",
}

var FilterModeValue = func() map[string]FilterMode {
	value := map[string]FilterMode{}

	for k, v := range FilterModeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e FilterMode) MarshalJSON() ([]byte, error) {
	v, ok := FilterModeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *FilterMode) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := FilterModeValue[string(data)]

	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (*FilterMode) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range FilterModeName {
		vals = append(vals, name)
	}

	return vals
}
