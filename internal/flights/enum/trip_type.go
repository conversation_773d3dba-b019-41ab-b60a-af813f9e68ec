package enum

import (
	"bytes"
	"errors"
	"fmt"
)

type TripType uint

const (
	TripTypeNone TripType = iota
	TripTypeSITI
	TripTypeSOTO
	TripTypeGLOBAL
)

var TripTypeName = map[TripType]string{
	TripTypeNone:   "",
	TripTypeSITI:   "SITI",
	TripTypeSOTO:   "SOTO",
	TripTypeGLOBAL: "GLOBAL",
}

var TripTypeValue = func() map[string]TripType {
	value := map[string]TripType{}

	for k, v := range TripTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e TripType) MarshalJSON() ([]byte, error) {
	v, ok := TripTypeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *TripType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := TripTypeValue[string(data)]

	if !ok {
		return errors.New(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
	}

	*e = v

	return nil
}

func (*TripType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range TripTypeName {
		vals = append(vals, name)
	}

	return vals
}
