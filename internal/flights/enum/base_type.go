package enum

type BaseType string

const (
	BaseTypeFareNone    BaseType = ""
	BaseTypeFareBasic   BaseType = "fare_basic"
	BaseTypeFareAmount  BaseType = "fare_amount"
	BaseTypeFareUnknown BaseType = "unknown"
)

const (
	BaseTypeFareNoneInt = iota - 1
	BaseTypeFareBasicInt
	BaseTypeFareAmountInt
	BaseTypeUnknownInt
)

var BaseTypeMap = map[int]BaseType{
	BaseTypeFareNoneInt:   BaseTypeFareNone,
	BaseTypeFareBasicInt:  BaseTypeFareBasic,
	BaseTypeFareAmountInt: BaseTypeFareAmount,
	BaseTypeUnknownInt:    BaseTypeFareUnknown,
}

var BaseTypeReverseMap = map[BaseType]int{
	BaseTypeFareNone:    BaseTypeFareNoneInt,
	BaseTypeFareBasic:   BaseTypeFareBasicInt,
	BaseTypeFareAmount:  BaseTypeFareAmountInt,
	BaseTypeFareUnknown: BaseTypeUnknownInt,
}
