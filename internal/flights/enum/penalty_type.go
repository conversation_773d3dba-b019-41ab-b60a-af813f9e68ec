package enum

type PenaltyType string

const (
	PenaltyTypeNone         PenaltyType = ""
	PenaltyTypeRefund       PenaltyType = "refund"
	PenaltyTypeRefundNoShow PenaltyType = "refund_noshow"
	PenaltyTypeChange       PenaltyType = "change"
	PenaltyTypeChangeNoShow PenaltyType = "change_noshow"
)

const (
	PenaltyTypeRefundInt = iota
	PenaltyTypeRefundNoShowInt
	PenaltyTypeChangeInt
	PenaltyTypeChangeNoShowInt
	PenalTyTypeNoneInt
)

var PenaltyTypeMap = map[int]PenaltyType{
	PenaltyTypeRefundInt:       PenaltyTypeRefund,
	PenaltyTypeRefundNoShowInt: PenaltyTypeRefundNoShow,
	PenaltyTypeChangeInt:       PenaltyTypeChange,
	PenaltyTypeChangeNoShowInt: PenaltyTypeChangeNoShow,
	PenalTyTypeNoneInt:         PenaltyTypeNone,
}

var PenaltyReverseMap = map[PenaltyType]int{
	PenaltyTypeRefund:       PenaltyTypeRefundInt,
	PenaltyTypeRefundNoShow: PenaltyTypeRefundNoShowInt,
	PenaltyTypeChange:       PenaltyTypeChangeInt,
	PenaltyTypeChangeNoShow: PenaltyTypeChangeNoShowInt,
	PenaltyTypeNone:         PenalTyTypeNoneInt,
}
