package webpartnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) GetFlightCurrencyExchange(ctx context.Context, req *web_partnership.GetFlightCurrencyExchangeReq) (*web_partnership.GetFlightCurrencyExchangeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	//Only super admin can list currency exchange
	if isContain := commonHelpers.Contains(user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSuperAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainGetCurrencyExchangeReq(req)

	res, err := s.app.Queries.GetCurrencyExchangeHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.GetFlightCurrencyExchangeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.GetFlightCurrencyExchangeRes{
		IsSuccess:        true,
		CurrencyExchange: partnershipConverts.ToProtoCurrencyExchange(res),
	}, nil
}
