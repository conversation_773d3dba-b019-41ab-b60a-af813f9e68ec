package webpartnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) ListCurrencyExchange(ctx context.Context, req *web_partnership.ListFlightCurrencyExchangeReq) (*web_partnership.ListFlightCurrencyExchangeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	//Only super admin can list currency exchange
	if isContain := commonHelpers.Contains(user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSuperAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainListCurrencyExchangeReq(req.Filter)
	reqFilter.Pagination = commonDomain.ToPagingDomain(req.Pagination)

	res, err := s.app.Queries.ListCurrencyExchanggeHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.ListFlightCurrencyExchangeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.ListFlightCurrencyExchangeRes{
		IsSuccess:  true,
		Pagination: commonDomain.ToPagingProto(reqFilter.Pagination),
		Items:      partnershipConverts.ToProtoCurrencyExchanges(res),
	}, nil
}
