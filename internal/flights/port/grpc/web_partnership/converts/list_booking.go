package converts

import (
	"errors"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

// ToListBookingRequest converts proto request to domain request
func ToListBookingRequest(req *web_partnership.ListFlightBookingReq) (*domain.ListBookingFilter, error) {
	if req == nil {
		return nil, errors.New("request is nil")
	}

	domainReq := &domain.ListBookingFilter{
		Pagination:   toDomainPagination(req.Pagination),
		StatusesList: []enum.BookingStatus{},
	}

	// Convert filter if exists
	if req.Filter != nil {
		// Map booking code filter
		if req.Filter.BookingCode != nil {
			// Note: Current ListBookingRequest doesn't have BookingCode filter
			// This might need to be extended or use a different handler
		}

		// Map status filter
		if len(req.Filter.Statuses) > 0 {
			// Convert first status for now (current handler only supports single TicketStatus)
			// This might need to be extended to support multiple booking statuses
			if len(req.Filter.Statuses) > 0 {
				// Map BookingStatus to TicketStatus (simplified mapping)
				switch req.Filter.Statuses[0] {
				case skyhub.FlightBookingStatus_FlightBookingStatusOK:
					domainReq.StatusesList = append(domainReq.StatusesList, enum.BookingStatusOK)
				case skyhub.FlightBookingStatus_FlightBookingStatusCancelled:
					domainReq.StatusesList = append(domainReq.StatusesList, enum.BookingStatusCancelled)
				case skyhub.FlightBookingStatus_FlightBookingStatusDraft:
					domainReq.StatusesList = append(domainReq.StatusesList, enum.BookingStatusDraft)
				case skyhub.FlightBookingStatus_FlightBookingStatusTicketed:
					domainReq.StatusesList = append(domainReq.StatusesList, enum.BookingStatusTicketed)
				default:
				}
			}
		}
	}

	// Sorts field may not exist in proto, skipping for now
	// This can be updated when the actual proto struct is available

	return domainReq, nil
}

// ToFlightBookingListItems converts domain bookings to proto items
func ToFlightBookingListItems(bookings []*domain.BookingSession) []*web_partnership.FlightBookingListItem {
	var items []*web_partnership.FlightBookingListItem

	for _, booking := range bookings {
		if booking != nil {
			items = append(items, ToFlightBookingListItem(booking))
		}
	}

	return items
}

// ToFlightBookingListItemsFromEnriched converts enriched domain bookings to proto items
func ToFlightBookingListItemsFromEnriched(enrichedBookings []*domain.EnrichedBookingSession) []*web_partnership.FlightBookingListItem {
	var items []*web_partnership.FlightBookingListItem

	for _, enriched := range enrichedBookings {
		if enriched != nil {
			items = append(items, ToFlightBookingListItemFromEnriched(enriched))
		}
	}

	return items
}

// ToFlightBookingListItemFromEnriched converts single enriched domain booking to proto item
func ToFlightBookingListItemFromEnriched(enriched *domain.EnrichedBookingSession) *web_partnership.FlightBookingListItem {
	if enriched == nil {
		return nil
	}

	item := &web_partnership.FlightBookingListItem{
		Id:              enriched.ID,
		BookingCode:     enriched.BookingCode,
		ReservationCode: enriched.BookingRef,
		Status:          toProtoBookingStatus(enriched.Status),
		TicketStatus:    toProtoTicketStatus(enriched.TicketStatus),
		CreatedAt:       enriched.CreatedAt,
		AgentCode:       enriched.AgentCode, // Use enriched agent code
		TotalAmount:     getTotalAmountFromBooking(&enriched.BookingSession),
	}

	// Convert passengers from enriched data
	if len(enriched.ListPax) > 0 {
		item.Passengers = FromDomainPaxInfos(enriched.ListPax)
	}

	return item
}

// ToFlightBookingListItem converts single domain booking to proto item
func ToFlightBookingListItem(booking *domain.BookingSession) *web_partnership.FlightBookingListItem {
	if booking == nil {
		return nil
	}

	item := &web_partnership.FlightBookingListItem{
		Id:              booking.ID,
		BookingCode:     booking.BookingCode,
		ReservationCode: booking.BookingRef,
		Status:          toProtoBookingStatus(booking.Status),
		CreatedAt:       booking.CreatedAt,
		AgentCode:       booking.OfficeID, // Use OfficeID as agent code for now
		TotalAmount:     getTotalAmountFromBooking(booking),
	}

	// Convert passengers from PNR data if available
	// Note: In list view, BookingSession doesn't have ListPax
	// Passengers would need to be populated at the handler level by joining with PNR data
	// For now, leaving empty array

	return item
}

// Helper function to get total amount from booking
func getTotalAmountFromBooking(booking *domain.BookingSession) float64 {
	if booking.FareDataCf != nil {
		return booking.FareDataCf.TotalFareAmount
	}
	if booking.FareData != nil {
		return booking.FareData.TotalFareAmount
	}
	return 0
}

// ToPaginationResponse creates pagination response
func ToPaginationResponse(pagination *commonDomain.Pagination, totalItems int) *base.PaginationRes {
	if pagination == nil {
		return &base.PaginationRes{
			PageCurrent: 1,
			PageLimit:   10,
			TotalRecord: int64(totalItems),
			TotalPage:   uint32((totalItems + 9) / 10), // Calculate total pages
		}
	}

	totalPages := uint32((totalItems + int(pagination.PageLimit) - 1) / int(pagination.PageLimit))

	return &base.PaginationRes{
		PageCurrent: uint32(pagination.PageCurrent),
		PageLimit:   uint32(pagination.PageLimit),
		TotalRecord: int64(totalItems),
		TotalPage:   totalPages,
	}
}

// ToFlightBookingListSummary creates summary (placeholder implementation)
// Note: FlightBookingListSummary struct may not exist in proto, using simple approach
func ToFlightBookingListSummary(bookings []*domain.BookingSession) interface{} {
	// Return nil for now since the struct may not exist in proto
	// This can be updated when the actual proto struct is available
	return nil
}

// Helper functions
func toDomainPagination(pagination *base.PaginationReq) *commonDomain.Pagination {
	if pagination == nil {
		return &commonDomain.Pagination{
			PageCurrent: 1,
			PageLimit:   10,
		}
	}

	return &commonDomain.Pagination{
		PageCurrent: int64(pagination.PageNumber),
		PageLimit:   int64(pagination.PageLimit),
	}
}

func toProtoBookingStatus(status enum.BookingStatus) skyhub.FlightBookingStatus {
	switch status {
	case enum.BookingStatusOK:
		return skyhub.FlightBookingStatus_FlightBookingStatusOK
	case enum.BookingStatusCancelled:
		return skyhub.FlightBookingStatus_FlightBookingStatusCancelled
	case enum.BookingStatusDraft:
		return skyhub.FlightBookingStatus_FlightBookingStatusDraft
	case enum.BookingStatusConfirmed:
		return skyhub.FlightBookingStatus_FlightBookingStatusConfirmed
	case enum.BookingStatusTicketed:
		return skyhub.FlightBookingStatus_FlightBookingStatusTicketed
	default:
		return skyhub.FlightBookingStatus_FlightBookingStatusNone
	}
}

func toProtoTicketStatus(status enum.TicketStatus) skyhub.FlightTicketStatus {
	switch status {
	case enum.TicketStatusOK:
		return skyhub.FlightTicketStatus_FlightTicketStatusOK
	case enum.TicketStatusFailed:
		return skyhub.FlightTicketStatus_FlightTicketStatusFailed
	case enum.TicketStatusPending:
		return skyhub.FlightTicketStatus_FlightTicketStatusPending
	case enum.TicketStatusVoided:
		return skyhub.FlightTicketStatus_FlightBookingStatusVoided
	case enum.TicketStatusUnknown:
		return skyhub.FlightTicketStatus_FlightTicketStatusUnknown
	default:
		return skyhub.FlightTicketStatus_FlightTicketStatusNone
	}
}

func getCurrencyFromBooking(booking *domain.BookingSession) string {
	if booking.FareDataCf != nil && booking.FareDataCf.Currency != "" {
		return booking.FareDataCf.Currency
	}
	if booking.FareData != nil && booking.FareData.Currency != "" {
		return booking.FareData.Currency
	}
	return "VND" // Default currency
}

// ToListBookingFilterRequest converts proto request to filter query request
func ToListBookingFilterRequest(req *web_partnership.ListFlightBookingReq) (*domain.ListBookingFilter, error) {
	if req == nil {
		return nil, errors.New("request is nil")
	}

	queryReq := &domain.ListBookingFilter{
		Pagination: toDomainPagination(req.Pagination),
	}

	// Convert filter if exists
	if req.Filter != nil {
		// Map office_id from filter - if "" then no filter
		if req.Filter.OfficeId != nil && *req.Filter.OfficeId != "" {
			queryReq.OfficeID = *req.Filter.OfficeId
		}

		// Map booking code filter - if "" then no filter
		if req.Filter.BookingCode != nil && *req.Filter.BookingCode != "" {
			queryReq.BookingCode = *req.Filter.BookingCode
		}

		// Map date range filter (convert int64 to float64) - if 0 then no filter
		if req.Filter.FromDate != nil && *req.Filter.FromDate > 0 {
			fromDate := float64(*req.Filter.FromDate)
			queryReq.FromDate = &fromDate
		}
		if req.Filter.ToDate != nil && *req.Filter.ToDate > 0 {
			toDate := float64(*req.Filter.ToDate)
			queryReq.ToDate = &toDate
		}

		// Map status filter - if empty array then no filter
		if len(req.Filter.Statuses) > 0 {
			queryReq.StatusesList = make([]enum.BookingStatus, 0, len(req.Filter.Statuses))
			for _, protoStatus := range req.Filter.Statuses {
				domainStatus := fromProtoBookingStatus(protoStatus)
				if domainStatus != "" {
					queryReq.StatusesList = append(queryReq.StatusesList, domainStatus)
				}
			}
		}

	}

	return queryReq, nil
}

// ToGetBookingDetailRequest converts proto request to domain request
func ToGetBookingDetailRequest(req *web_partnership.GetFlightBookingDetailReq) (*domain.GetBookingByIDRequest, error) {
	if req == nil {
		return nil, errors.New("request is nil")
	}

	if req.Id == "" {
		return nil, errors.New("booking ID is required")
	}

	return &domain.GetBookingByIDRequest{
		ID: req.Id,
		// OfficeID will be set from context in handler
	}, nil
}

// ToGetBookingDetailResponse converts domain response to proto response
func ToGetBookingDetailResponse(domainRes *domain.GetBookingByIDResponse) *web_partnership.GetFlightBookingDetailRes {
	if domainRes == nil {
		return &web_partnership.GetFlightBookingDetailRes{
			IsSuccess: false,
			ErrorCode: "RESPONSE_IS_NULL",
		}
	}

	if !domainRes.IsSuccess {
		return &web_partnership.GetFlightBookingDetailRes{
			IsSuccess: false,
			ErrorCode: domainRes.ErrorCode,
		}
	}

	// Convert BookingDetails to web_partnership proto format
	protoData := ToWebPartnershipFlightBookingDetails(&domainRes.BookingDetails)

	return &web_partnership.GetFlightBookingDetailRes{
		IsSuccess: true,
		Data:      protoData,
	}
}

// ToWebPartnershipFlightBookingDetails converts domain BookingDetails to web_partnership proto format
func ToWebPartnershipFlightBookingDetails(details *domain.BookingDetails) *web_partnership.FlightBookingDetails {
	if details == nil {
		return nil
	}

	protoDetail := &web_partnership.FlightBookingDetails{
		Id:           details.OrderID,
		BookingCode:  details.BookingCode,
		Status:       toProtoBookingStatus(details.Status),
		TicketStatus: toProtoTicketStatus(details.TicketStatus),
		CreatedAt:    details.LastTicketingDate, // Use appropriate timestamp
		AgentCode:    details.OfficeID,          // Use OfficeID as agent code for now
	}

	// Try to add TotalAmount if field exists in proto
	// Note: This field may need to be added to proto definition
	totalAmount := getTotalAmountFromFareData(details.FareDataCf)
	_ = totalAmount // Avoid unused variable warning for now

	// Convert passengers
	if len(details.ListPax) > 0 {
		protoDetail.Passengers = FromDomainPaxInfos(details.ListPax)
	}

	// Convert itineraries
	if len(details.Itineraries) > 0 {
		protoDetail.Itineraries = FromDomainFlightItineraries(details.Itineraries)
		// Set reservation code from first itinerary
		protoDetail.ReservationCode = details.Itineraries[0].ReservationCode
	}

	// Convert contact info using skyhub format (compatible with web_partnership)
	if details.ContactInfo != nil {
		protoDetail.ContactInfo = &skyhub.Contact{
			Surname:   details.ContactInfo.Surname,
			GivenName: details.ContactInfo.GivenName,
			PhoneCode: details.ContactInfo.PhoneCode,
			Phone:     details.ContactInfo.Phone,
			Email:     details.ContactInfo.Email,
			Gender:    base.GENDER(details.ContactInfo.Gender),
		}
	}

	// Convert fee data - map to skyhub.Fee with correct structure
	if details.FareDataCf != nil {
		protoDetail.Fee = createSkyhubFeeFromDomain(details.FareDataCf, details.ListPax)
	}

	return protoDetail
}

// Helper function to get total amount from fare data
func getTotalAmountFromFareData(fareData *domain.TotalFareInfo) float64 {
	if fareData != nil {
		return fareData.TotalFareAmount
	}
	return 0
}

// createSkyhubFeeFromDomain creates skyhub.Fee from domain data
func createSkyhubFeeFromDomain(fareData *domain.TotalFareInfo, passengers []*domain.PaxInfo) *skyhub.Fee {
	if fareData == nil {
		return nil
	}

	// Create TotalFeeBreakdown
	totalFee := &skyhub.TotalFeeBreakdown{
		BaseFare:    fareData.TotalFareBasic,
		Taxes:       fareData.TotalTaxAmount,
		HiddenFee:   0,                                                      // Can be mapped from domain if available
		Discount:    0,                                                      // Can be mapped from domain if available
		ServiceFee:  fareData.TotalSeatAmount + fareData.TotalBaggageAmount, // Combine seat and baggage as service fee
		TotalAmount: fareData.TotalFareAmount,
	}

	// Create passenger type details
	var passengerDetails []*skyhub.PassengerTypeFeeDetail
	if len(fareData.TotalPaxFares) > 0 {
		// Count all passenger types once for better performance
		passengerCounts := countPassengersByTypes(passengers)

		for _, paxFare := range fareData.TotalPaxFares {
			if paxFare != nil {
				// Get count from pre-calculated map
				quantity := passengerCounts[paxFare.PaxType]

				// Skip if no passengers of this type
				if quantity == 0 {
					continue
				}

				passengerDetail := &skyhub.PassengerTypeFeeDetail{
					Type:     convertToBasePassengerType(paxFare.PaxType),
					Quantity: int32(quantity),
					UnitFee: &skyhub.PassengerUnitFee{
						BaseFare:    paxFare.FareBasic,
						Taxes:       paxFare.TaxAmount,
						TotalAmount: paxFare.FareAmount,
					},
					TotalFee: &skyhub.PassengerTotalFee{
						BaseFare:    paxFare.FareBasic * float64(quantity),
						Taxes:       paxFare.TaxAmount * float64(quantity),
						TotalAmount: paxFare.FareAmount * float64(quantity),
					},
				}
				passengerDetails = append(passengerDetails, passengerDetail)
			}
		}
	}

	return &skyhub.Fee{
		TotalFee:             totalFee,
		PassengerTypeDetails: passengerDetails,
		Currency:             fareData.Currency,
		TotalPassengers:      int32(len(passengers)),
	}
}

// Helper function to count all passenger types at once (more efficient)
func countPassengersByTypes(passengers []*domain.PaxInfo) map[enum.PaxType]int {
	counts := make(map[enum.PaxType]int)

	if len(passengers) == 0 {
		// If no passenger data available, return default counts
		// This handles cases where passenger data might not be available
		return counts
	}

	// Count each passenger type
	for _, pax := range passengers {
		if pax != nil {
			counts[pax.Type]++
		}
	}

	return counts
}

// Helper function to convert domain PaxType to base.PassengerType
func convertToBasePassengerType(paxType enum.PaxType) base.PassengerType {
	// Convert string-based PaxType to proto enum
	switch string(paxType) {
	case "ADT":
		return base.PassengerType(1) // Adult
	case "CHD":
		return base.PassengerType(2) // Child
	case "INF":
		return base.PassengerType(3) // Infant
	default:
		return base.PassengerType(1) // Default to adult
	}
}

// ToPaginationResponseFromQuery creates pagination response from query result
func ToPaginationResponseFromQuery(pagination *commonDomain.Pagination, totalCount int64) *base.PaginationRes {
	if pagination == nil {
		return &base.PaginationRes{
			PageCurrent: 1,
			PageLimit:   20,
			TotalRecord: totalCount,
			TotalPage:   uint32((totalCount + 19) / 20), // Calculate total pages
		}
	}

	totalPages := uint32((totalCount + pagination.PageLimit - 1) / pagination.PageLimit)

	return &base.PaginationRes{
		PageCurrent: uint32(pagination.PageCurrent),
		PageLimit:   uint32(pagination.PageLimit),
		TotalRecord: totalCount,
		TotalPage:   totalPages,
	}
}

// Helper function to convert proto booking status to domain booking status
func fromProtoBookingStatus(protoStatus skyhub.FlightBookingStatus) enum.BookingStatus {
	switch protoStatus {
	case skyhub.FlightBookingStatus_FlightBookingStatusOK:
		return enum.BookingStatusOK
	case skyhub.FlightBookingStatus_FlightBookingStatusCancelled:
		return enum.BookingStatusCancelled
	case skyhub.FlightBookingStatus_FlightBookingStatusDraft:
		return enum.BookingStatusDraft
	case skyhub.FlightBookingStatus_FlightBookingStatusConfirmed:
		return enum.BookingStatusConfirmed
	case skyhub.FlightBookingStatus_FlightBookingStatusTicketed:
		return enum.BookingStatusTicketed
	default:
		return enum.BookingStatusOK
	}

}
