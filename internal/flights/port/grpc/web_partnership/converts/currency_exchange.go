package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/domain"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func FromDomainGetCurrencyExchangeReq(info *web_partnership.GetFlightCurrencyExchangeReq) *domain.GetCurrencyExchangeDetailReq {
	if info == nil {
		return nil
	}

	return &domain.GetCurrencyExchangeDetailReq{
		ID: info.Id,
	}
}

func FromDomainCreateCurrencyExchangeReq(info *skyhub.FlightCurrencyExchange) *domain.CurrencyExchange {
	if info == nil {
		return nil
	}

	return &domain.CurrencyExchange{
		From:     info.From,
		To:       info.To,
		Rate:     info.Rate,
		Provider: enum.FlightProvider(info.Provider),
	}
}

func FromDomainUpdateCurrencyExchangeReq(info *skyhub.FlightCurrencyExchange, id string) *domain.CurrencyExchange {
	if info == nil {
		return nil
	}

	return &domain.CurrencyExchange{
		Base: domain.Base{
			ID: id,
		},
		From:     info.From,
		To:       info.To,
		Rate:     info.Rate,
		Provider: enum.FlightProvider(info.Provider),
	}
}

func FromDomainListCurrencyExchangeReq(info *web_partnership.GetFlightCurrencyExchangeReqFilter) *domain.ListCurrencyExchangeReq {
	if info == nil {
		return nil
	}

	return &domain.ListCurrencyExchangeReq{
		From:     info.From,
		To:       info.To,
		Provider: enum.FlightProvider(info.Provider),
	}
}

func ToProtoCurrencyExchanges(info []*domain.CurrencyExchange) []*skyhub.FlightCurrencyExchange {
	res := make([]*skyhub.FlightCurrencyExchange, 0, len(info))
	for _, item := range info {
		res = append(res, ToProtoCurrencyExchange(item))
	}
	return res
}

func ToProtoCurrencyExchange(info *domain.CurrencyExchange) *skyhub.FlightCurrencyExchange {
	if info == nil {
		return nil
	}

	return &skyhub.FlightCurrencyExchange{
		Id:       info.ID,
		From:     info.From,
		To:       info.To,
		Rate:     info.Rate,
		Provider: skyhub.FlightProvider(info.Provider),
	}
}
