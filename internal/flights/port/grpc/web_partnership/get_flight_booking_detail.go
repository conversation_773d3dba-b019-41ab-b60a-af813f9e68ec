package webpartnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/grpc/web_partnership/converts"
)

// GetFlightBookingDetail implements FlightBookingServiceServer.GetFlightBookingDetail
// Lấy chi tiết booking chuyến bay theo booking code
func (s *WebPartnershipServer) GetFlightBookingDetail(ctx context.Context, req *web_partnership.GetFlightBookingDetailReq) (*web_partnership.GetFlightBookingDetailRes, error) {
	// Validate request
	if req == nil {
		return &web_partnership.GetFlightBookingDetailRes{
			IsSuccess: false,
			ErrorCode: "REQUEST_IS_NULL",
		}, nil
	}

	// Validate booking ID - nếu "" thì return error
	if req.Id == "" {
		return &web_partnership.GetFlightBookingDetailRes{
			IsSuccess: false,
			ErrorCode: "BOOKING_ID_REQUIRED",
		}, nil
	}

	// Get partnership ID from context
	_, ok := auth.GetContextPartnershipId(ctx)
	if !ok {
		return &web_partnership.GetFlightBookingDetailRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		}, nil
	}

	// Convert proto request to domain request
	domainReq, err := converts.ToGetBookingDetailRequest(req)
	if err != nil {
		log.Error("Failed to convert proto request to domain request",
			log.Any("error", err),
			log.Any("req", req))
		return &web_partnership.GetFlightBookingDetailRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	// Call application layer to get booking by ID
	domainRes, err := s.app.Queries.GetBookingByID.Handle(ctx, domainReq)
	if err != nil {
		log.Error("Failed to get booking by ID",
			log.Any("error", err),
			log.Any("domainReq", domainReq))
		return &web_partnership.GetFlightBookingDetailRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	// Convert domain response to proto response
	protoRes := converts.ToGetBookingDetailResponse(domainRes)

	return protoRes, nil
}
