package webpartnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) UpdateCurrencyExchange(ctx context.Context, req *web_partnership.UpdateFlightCurrencyExchangeReq) (*web_partnership.FlightCurrencyExchangeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	//Only super admin can list currency exchange
	if isContain := commonHelpers.Contains(user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSuperAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainUpdateCurrencyExchangeReq(req.CurrencyExchange, req.Id)

	err := s.app.Commands.UpdateCurrencyExchangeHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.FlightCurrencyExchangeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.FlightCurrencyExchangeRes{
		IsSuccess: true,
		ErrorCode: "",
	}, nil
}
