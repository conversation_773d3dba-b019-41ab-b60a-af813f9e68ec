package background

import (
	"context"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
)

func (c *cronjob) registerProcessExpiredBooking(s *gocron.Scheduler, scheduled string) {
	const jobTimeout = time.Minute * 5

	_, err := s.<PERSON>(scheduled).Do(func() {
		ctx, cancel := context.WithTimeout(context.Background(), jobTimeout)
		defer cancel()

		c.processExpiredBooking(ctx)
	})
	if err != nil {
		log.Fatal("cronjob do error", log.Any("error", err))
		return
	}
}

func (c *cronjob) processExpiredBooking(ctx context.Context) {
	log.Info("[cronjob] start processExpiredBooking...")

	res, err := c.app.Queries.ListExpiredBooking.Handle(ctx)
	if err != nil {
		return
	}

	err = c.app.Commands.ProcessExpiredBookingHandler.Handle(ctx, &command.ProcessExpiredBookingRequest{
		Bookings: res,
	})
	if err != nil {
		return
	}

}
