package background

import (
	"context"
	"strings"
	"time"

	"github.com/go-co-op/gocron"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-flights/internal/flights/enum"
)

func (c *cronjob) registerProcessTicketPendingBooking(s *gocron.Scheduler, scheduled string) {
	const jobTimeout = time.Minute * 5

	_, err := s.Cron(scheduled).Do(func() {
		ctx, cancel := context.WithTimeout(context.Background(), jobTimeout)
		defer cancel()

		c.processTicketPendingBooking(ctx)
	})
	if err != nil {
		log.Fatal("cronjob do error", log.Any("error", err))
		return
	}
}

func (c *cronjob) processTicketPendingBooking(ctx context.Context) {
	log.Info("[cronjob] start processTicketPendingBooking...")

	listReq := &query.ListBookingRequest{
		Pagi: &commonDomain.Pagination{
			PageLimit:   30,
			PageCurrent: 1,
		},
		TicketStatus: enum.TicketStatusPending,
		OrderKey:     "created_at",
		OrderVal:     1,
	}

	res, err := c.app.Queries.ListBooking.Handle(ctx, listReq)
	if err != nil {
		return
	}

	processReq := &command.ProcessPendingTicketRequest{
		Bookings: res,
	}

	err = c.app.Commands.ProcessPendingTicket.Handle(ctx, processReq)
	if err != nil {
		return
	}

	tempArr := []string{}

	for _, item := range res {
		tempArr = append(tempArr, item.BookingRef)
	}

	log.Info("[cronjob] done processTicketPendingBooking", log.Int("scanned", len(res)), log.String("info", strings.Join(tempArr, ",")))
}
